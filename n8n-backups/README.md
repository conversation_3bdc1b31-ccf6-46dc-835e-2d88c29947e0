# 🔄 Backup e Restauração n8n

Este diretório contém os backups dos workflows e configurações do n8n.

## 📋 Como Usar

### 🔐 Fazer Backup

Execute o script de backup para salvar seus workflows:

```bash
./backup-n8n.sh
```

Isso irá criar:

- `n8n-backup-YYYYMMDD_HHMMSS.tar.gz` - Backup completo dos dados
- `workflows-YYYYMMDD_HHMMSS.json` - Backup dos workflows via API

### 🔄 Restaurar Backup

Para restaurar um backup anterior:

```bash
# Listar backups disponíveis
./restore-n8n.sh

# Restaurar um backup específico
./restore-n8n.sh n8n-backups/n8n-backup-20240613_205030.tar.gz
```

## ⚠️ Importante

### Para Evitar Perda de Dados

1. **NUNCA use `docker-compose down -v`** se quiser manter os dados

   ```bash
   # ❌ Remove volumes (perde dados)
   docker-compose down -v

   # ✅ Para containers mas mantém dados
   docker-compose down
   ```

2. **Faça backup regularmente**

   ```bash
   # Adicionar ao seu workflow diário
   ./backup-n8n.sh
   ```

3. **Backup antes de mudanças importantes**
   - Antes de atualizar versões
   - Antes de modificar configurações
   - Antes de fazer manutenção no Docker

## 🔄 Backup Automático (Opcional)

Para backup automático diário, adicione ao cron:

```bash
# Editar crontab
crontab -e

# Adicionar linha para backup diário às 2h da manhã
0 2 * * * cd /caminho/para/projeto && ./backup-n8n.sh
```

## 📁 Estrutura dos Arquivos

- `*.tar.gz` - Backup completo (dados + configurações)
- `*.json` - Backup apenas dos workflows
- Arquivos mais antigos podem ser removidos manualmente
