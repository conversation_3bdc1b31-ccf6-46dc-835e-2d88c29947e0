#!/bin/bash

# Script de restauração para n8n workflows
# Uso: ./restore-n8n.sh [arquivo-backup.tar.gz]

set -e

BACKUP_DIR="./n8n-backups"
CONTAINER_NAME="n8n-dev"

echo "🔄 Script de Restauração n8n"

# Verificar se foi fornecido um arquivo de backup
if [ $# -eq 0 ]; then
    echo "📚 Backups disponíveis:"
    ls -la "$BACKUP_DIR"/*.tar.gz 2>/dev/null | awk '{print "   - " $9}' || echo "   Nenhum backup encontrado"
    echo ""
    echo "Uso: $0 <arquivo-backup.tar.gz>"
    echo "Exemplo: $0 n8n-backups/n8n-backup-20240613_205030.tar.gz"
    exit 1
fi

BACKUP_FILE="$1"

# Verificar se o arquivo existe
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Arquivo de backup não encontrado: $BACKUP_FILE"
    exit 1
fi

# Verificar se o container está rodando
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "❌ Container $CONTAINER_NAME não está rodando!"
    echo "Execute: docker-compose -f docker-compose.dev.yml up -d"
    exit 1
fi

echo "⚠️  ATENÇÃO: Esta operação irá sobrescrever todos os dados atuais do n8n!"
read -p "Deseja continuar? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operação cancelada."
    exit 1
fi

echo "🛑 Parando container n8n..."
docker-compose -f docker-compose.dev.yml stop n8n

echo "🗑️  Limpando dados atuais..."
docker run --rm -v n8n_data:/data alpine sh -c "rm -rf /data/*"

echo "📦 Restaurando backup..."
docker run --rm -v n8n_data:/data -v "$(pwd):/backup" alpine sh -c "
    cd /data && 
    tar xzf /backup/$BACKUP_FILE
"

echo "🚀 Reiniciando container n8n..."
docker-compose -f docker-compose.dev.yml up -d n8n

echo "⏳ Aguardando n8n inicializar..."
sleep 10

echo "✅ Restauração concluída!"
echo "🌐 Acesse: http://localhost:5678" 