FROM node:18.17-alpine AS builder
WORKDIR /usr/src/api
COPY package.json ./
RUN yarn install --network-timeout 1000000000
COPY . .
ENV NODE_ENV production
RUN yarn migration:run:prod 
RUN yarn build:prod

FROM node:18.17-alpine AS api-production
WORKDIR /usr/src/api
COPY package.json ./
RUN yarn install --prod --network-timeout 1000000000 
ENV NODE_ENV production 
COPY --from=builder /usr/src/api/dist ./dist
COPY --from=builder /usr/src/api/prisma/schema.prisma ./prisma/schema.prisma
RUN yarn prisma:generate
EXPOSE 4000
CMD [ "yarn", "start:prod" ]
