# Backend

Aqui será listado detalhes de como executar o projeto, executar ações utilizadas durante desenvolvimento e deploy do projeto.

## Executando o projeto localmente

Para executar o projeto, é necessário ter instalado o NodeJS 18.x e o Yarn. Com isso podemos executar os seguintes comandos:

```powershell
yarn install
yarn build ## Build do projeto usando arquivo development.env
yarn start ## Executar o projeto usando arquivo development.env
yarn build:staging ## Build do projeto usando arquivo staging.env
yarn start:staging ## Executar o projeto usando arquivo staging.env
yarn dev ## Executar o projeto em modo de desenvolvimento usando arquivo development.env
```

Tenha em mente que o projeto utiliza variáveis de ambiente para definir algumas configurações, como por exemplo a conexão com o banco de dados. Para isso, é necessário criar um arquivo development.env (ou staging.env, production.env) baseado no arquivo .env.example

Após isso podemos executar o comando que executa as migrations:

```powershell
yarn migration:run
```

## Deploy

O nosso projeto é hospedado na AWS, utilizando da nossa infraestrutura que é construída usando CloudFormation (será disponibilizado um repositório contendo os arquivos de criação/configuração). Para o deploy, utilizamos o Azure DevOps, que é responsável por realizar o build e deploy da aplicação. Será necessário uma chave SSH para acessar a máquina que hospeda a aplicação e que deve ser gerada pelo console da AWS.

Uma vez com o projeto em mãos, você deve seguir os seguintes passos para realizar o deploy:

1. Preencher os arquivos .env com os dados necessários para o deploy (alguns detalhes serão abordados mais a frente).
2. Acessar o console da AWS e ter em mãos o IP da máquina que hospeda as aplicações.
3. Definir o IP da máquina e a chave SSH no Azure DevOps, na seção Service Connections.
4. Executar o pipeline de deploy.

Note que o deploy é feito em duas etapas, sendo a primeira a criação da infraestrutura e a segunda o deploy da aplicação. O deploy da aplicação é feito em um container Docker, que é instalado na máquina durante o deploy da infraestrutura. O container é criado a partir de uma imagem que é gerada durante o build da aplicação, que é feito pelo Azure DevOps e os detalhes podem ser consultados na seção Releases do mesmo. O banco de dados também é instalado durante o deploy da infraestrutura, sendo que o mesmo é executado na mesma rede do container da aplicação, por isso o IP preenchido no .env deve ser o IP que foi atribuido aquela máquina durante a criação da infraestrutura.

Com isso criado, será necessário a realização da configuração que permite os containers Docker se comunicarem com o ambiente externo cuja implementação deverá ser feita pelo time de infraestrutura.

## Serviços utilizados

O projeto possui algumas dependências que devem ser configuradas para que o mesmo funcione corretamente. São elas:

- **AWS S3**: utilizado para armazenar os arquivos de imagem
- **AWS CloudFront**: utilizado para servir os arquivos de imagem
- **AWS SES**: utilizado para envio de e-mails
- **AWS SNS**: utilizado para envio de notificações
- **AWS Cognito**: utilizado para autenticação
- **Google OAuth**: utilizado para autenticação
- **Pagseguro**: utilizado para pagamentos
- **Google Cloud Platform**: utilizado para serviços de localização
