#!/bin/bash
FQDN=$1

# make directories to work from
mkdir -p cert/ cert/server/ cert/client/ cert/CA/

# Create your very own Root Certificate Authority
openssl genrsa \
  -out cert/CA/my-private-root-ca.privkey.pem \
  2048

# Self-sign your Root Certificate Authority
# Since this is private, the details can be as bogus as you like
openssl req \
  -x509 \
  -new \
  -nodes \
  -key cert/CA/my-private-root-ca.privkey.pem \
  -days 1024 \
  -out cert/CA/my-private-root-ca.cert.pem \
  -subj "/C=BR/ST=MG/L=*/O=Signing Authority/CN=selfsigned"

# Create a Device Certificate for each domain,
# such as example.com, *.example.com, awesome.example.com
# NOTE: You MUST match CN to the domain name or ip address you want to use
openssl genrsa \
  -out cert/server/privkey.pem \
  2048

# Create a request from your Device, which your Root CA will sign
openssl req -new \
  -key cert/server/privkey.pem \
  -out cert/client/csr.pem \
  -subj "/C=BR/ST=MG/L=*/O=Signing Authority/CN=${FQDN}"

# Sign the request from <PERSON><PERSON> with your Root CA
openssl x509 \
  -req -in cert/client/csr.pem \
  -CA cert/CA/my-private-root-ca.cert.pem \
  -CAkey cert/CA/my-private-root-ca.privkey.pem \
  -CAcreateserial \
  -out cert/server/cert.pem \
  -days 500

# Put things in their proper place
# rsync -a {privkey,cert}.pem server/
cat cert/server/cert.pem > cert/server/fullchain.pem         # we have no intermediates in this case
rsync -a cert/CA/my-private-root-ca.cert.pem cert/server/
rsync -a cert/CA/my-private-root-ca.cert.pem cert/client/

# create DER format crt for iOS Mobile Safari, etc
openssl x509 -outform der -in cert/CA/my-private-root-ca.cert.pem -out cert/client/my-private-root-ca.crt