#!/bin/bash

# Script de backup para n8n workflows
# Uso: ./backup-n8n.sh

set -e

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./n8n-backups"
CONTAINER_NAME="n8n-dev"

echo "🔄 Iniciando backup do n8n..."

# Criar diretório de backup se não existir
mkdir -p "$BACKUP_DIR"

# Verificar se o container está rodando
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "❌ Container $CONTAINER_NAME não está rodando!"
    exit 1
fi

# Fazer backup do diretório completo do n8n
echo "📦 Criando backup dos dados do n8n..."
docker exec "$CONTAINER_NAME" tar czf "/backups/n8n-backup-$TIMESTAMP.tar.gz" -C /home/<USER>/.n8n .

# Fazer backup via API (workflows apenas)
echo "📋 Exportando workflows via API..."
docker exec "$CONTAINER_NAME" sh -c "
    if [ -f /home/<USER>/.n8n/config ]; then
        # Tentar exportar via API do n8n (se disponível)
        curl -s 'http://localhost:5678/api/v1/workflows' \
             -H 'Accept: application/json' \
             > /backups/workflows-$TIMESTAMP.json 2>/dev/null || echo '[]' > /backups/workflows-$TIMESTAMP.json
    else
        echo '[]' > /backups/workflows-$TIMESTAMP.json
    fi
"

echo "✅ Backup concluído!"
echo "📁 Arquivos criados:"
echo "   - n8n-backups/n8n-backup-$TIMESTAMP.tar.gz (backup completo)"
echo "   - n8n-backups/workflows-$TIMESTAMP.json (workflows via API)"

# Listar backups existentes
echo ""
echo "📚 Backups disponíveis:"
ls -la "$BACKUP_DIR" | grep -E "\.(tar\.gz|json)$" | tail -5 