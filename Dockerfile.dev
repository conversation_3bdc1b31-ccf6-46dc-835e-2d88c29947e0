FROM node:20-slim AS builder
WORKDIR /usr/src/api
COPY package.json ./
COPY development.env ./
RUN yarn install --network-timeout 1000000000
COPY . .
COPY prisma ./prisma
ENV NODE_ENV development
RUN yarn prisma generate
RUN yarn build

FROM node:20-slim AS api-dev
WORKDIR /usr/src/api
COPY package.json ./
COPY development.env ./
RUN yarn install --prod --network-timeout 1000000000 
ENV NODE_ENV development
ENV NODE_OPTIONS="--max-old-space-size=2048 --no-experimental-fetch"
ENV MALLOC_ARENA_MAX=2
COPY --from=builder /usr/src/api/dist ./dist
COPY --from=builder /usr/src/api/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /usr/src/api/prisma ./prisma
EXPOSE 4000
COPY entrypoint.sh ./
RUN chmod +x entrypoint.sh
CMD ["./entrypoint.sh"]