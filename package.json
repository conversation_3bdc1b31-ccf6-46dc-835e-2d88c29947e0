{"name": "backend", "version": "1.0.0", "main": "index.js", "repository": "https://gitlab.cepedi.org.br/cepedi/template-mobile/backend.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"build": "rm -rf dist && ttsc && cp development.env dist/", "build:staging": "rm -rf dist && ttsc && cp staging.env dist/", "build:prod": "rm -rf dist && ttsc && cp production.env dist/", "start": "dotenv -e dist/development.env -- node --require ./dist/tsconfig-paths-bootstrap.js dist/src/api/app.js", "start:staging": "dotenv -e dist/staging.env -- node --require ./dist/tsconfig-paths-bootstrap.js dist/src/api/app.js", "start:prod": "dotenv -e dist/production.env -- node --require ./dist/tsconfig-paths-bootstrap.js dist/src/api/app.js", "predev": "set NODE_ENV=development& ts-node-dev src/business/Configs/TsoaSpecSwagger.ts", "predev:staging": "set NODE_ENV=staging& ts-node-dev src/business/Configs/TsoaSpecSwagger.ts", "predev:prod": "set NODE_ENV=production& ts-node-dev src/business/Configs/TsoaSpecSwagger.ts", "dev": "dotenv -e development.env -- tsnd  -C ttypescript --inspect -r tsconfig-paths/register --files src/api/app.ts", "dev:staging": "dotenv -e staging.env -- tsnd -C ttypescript --inspect -r tsconfig-paths/register --files src/api/app.ts", "dev:stagingLocal": "dotenv -e stagingLocal.env -- tsnd -C ttypescript --inspect -r tsconfig-paths/register --files src/api/app.ts", "dev:prod": "dotenv -e production.env -- tsnd -C ttypescript --inspect -r tsconfig-paths/register --files src/api/app.ts", "dev:staging:linux": "export NODE_ENV=staging& tsnd -C ttypescript -r tsconfig-paths/register src/api/app.ts", "migration:run": "dotenv -e development.env -- prisma migrate dev", "migration:create": "dotenv -e development.env -- prisma migrate dev --create-only", "migration:run:staging": "dotenv -e staging.env -- prisma migrate dev", "migration:run:prod": "dotenv -e production.env -- prisma migrate dev", "generate": "ts-node-dev src/business/Configs/TsoaSpecSwagger.ts", "prepare": "husky install", "prisma:generate": "npx prisma generate", "prisma:studio": "dotenv -e development.env -- npx prisma studio"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.300.0", "@aws-sdk/client-s3": "^3.300.0", "@aws-sdk/client-ses": "^3.300.0", "@aws-sdk/client-sns": "^3.300.0", "@dynamic-mapper/mapper": "1.10.2", "@prisma/client": "4.16.2", "@types/node-fetch": "^2.6.3", "@types/node-schedule": "^2.1.2", "aws-jwt-verify": "^3.1.0", "axios": "^1.1.3", "browser-env": "^3.3.0", "class-validator": "^0.13.2", "cookie": "^0.5.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.0.1", "dotenv-cli": "^7.2.1", "exceljs": "^4.3.0", "express": "^4.17.1", "express-async-errors": "^3.1.1", "inversify": "^6.0.1", "inversify-express-utils": "6.4.3", "inversify-inject-decorators": "^3.1.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "mime-types": "^2.1.35", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "2", "node-schedule": "^2.1.1", "nodejs-jsencrypt": "^3.0.0-rc.1", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "pg": "^8.7.1", "prisma-field-encryption": "^1.5.0", "reflect-metadata": "^0.1.13", "socket.io": "^4.6.2", "swagger-ui-express": "^4.4.0", "tsconfig-paths": "^4.2.0", "tsoa": "4.1.0", "uuid": "^8.3.2", "winston": "^3.10.0", "winston-transport": "^4.5.0", "xml2js": "^0.6.2", "zod": "^3.21.4"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.12", "@types/express": "^4.17.13", "@types/jsonwebtoken": "^8.5.6", "@types/lodash": "^4.14.194", "@types/mime-types": "^2.1.1", "@types/morgan": "^1.9.3", "@types/multer": "^1.4.7", "@types/node": "^18.16.0", "@types/passport": "^1.0.7", "@types/passport-jwt": "^3.0.6", "@types/swagger-ui-express": "^4.1.3", "@types/uuid": "^8.3.4", "@types/xml2js": "^0.4.11", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "eslint": "^7.32.0 || ^8.2.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.25.2", "husky": "^8.0.3", "jest": "^28.1.2", "prettier": "^2.7.1", "prisma": "4.16.2", "ts-node": "^10.8.2", "ts-node-dev": "^2.0.0", "ttypescript": "^1.5.13", "typescript": "4.7.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}