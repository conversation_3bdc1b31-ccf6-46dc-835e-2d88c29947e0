{"compilerOptions": {"target": "esnext", "experimentalDecorators": true, "emitDecoratorMetadata": true, "module": "commonjs", "moduleResolution": "node", "rootDir": ".", "baseUrl": ".", "paths": {"src/*": ["./src/*"]}, "typeRoots": ["./src/types", "./node_modules/@types"], "types": ["reflect-metadata"], "resolveJsonModule": true, "allowJs": true, "outDir": "./dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": false, "strictNullChecks": true, "skipLibCheck": true, "sourceMap": true}}