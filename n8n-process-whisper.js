// Processar resultado do Whisper e preparar para AI
// Cole este código em um Function node após o Whisper

const whisperResult = $input.all()[0].json;
const originalData = $input.all()[0].json;

// Extrair texto transcrito
const transcribedText = whisperResult.text || '';

// Preparar dados para o AI Agent
const aiInput = {
	// Dados originais da mensagem
	remoteJid: originalData.remoteJid,
	pushName: originalData.pushName,
	messageId: originalData.messageId,
	timestamp: originalData.timestamp,

	// Conteúdo processado
	content: transcribedText,
	originalType: 'audio',

	// Metadados do áudio
	audioMetadata: {
		duration: originalData.audioData?.seconds,
		fileSize: originalData.audioData?.fileLength,
		isPTT: originalData.audioData?.ptt
	},

	// Contexto para a AI
	context: {
		isAudioMessage: true,
		transcription: transcribedText,
		confidence: whisperResult.confidence || 'unknown',
		language: whisperResult.language || 'pt'
	}
};

// Adicionar contexto especial para áudios
aiInput.systemPrompt = `
IMPORTANTE: Esta mensagem foi enviada como ÁUDIO pelo usuário e transcrita automaticamente.
- Texto transcrito: "${transcribedText}"
- Duração: ${originalData.audioData?.seconds || 'desconhecida'}s
- É mensagem de voz (PTT): ${originalData.audioData?.ptt ? 'Sim' : 'Não'}

Responda de forma natural, reconhecendo que foi um áudio. Use frases como:
"Entendi seu áudio", "Pelo que você falou", "Obrigada pela mensagem de voz", etc.
`;

return [{ json: aiInput }];
