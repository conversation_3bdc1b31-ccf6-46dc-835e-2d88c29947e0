{"name": "Audio to Text (Whisper)", "type": "HTTP Request", "configuration": {"method": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "headers": {"Authorization": "Bearer {{ $credentials.openAiApi.apiKey }}", "Content-Type": "multipart/form-data"}, "bodyType": "multipart-form-data", "multipartBody": {"file": {"value": "={{ $binary.data }}", "filename": "audio.ogg"}, "model": "whisper-1", "language": "pt", "response_format": "json", "prompt": "Esta é uma conversa em português brasileiro sobre delivery, pedidos, produtos ou atendimento ao cliente."}}, "description": "Converte áudio em texto usando OpenAI Whisper"}