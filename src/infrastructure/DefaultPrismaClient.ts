import { PrismaClient } from "@prisma/client";
import { injectable } from "inversify";
import { fieldEncryptionExtension } from "prisma-field-encryption";
import { IDefaultPrismaClient } from "src/business/Interfaces/Database/IDefault";

@injectable()
export class DefaultPrismaClient implements IDefaultPrismaClient {
  private _client: PrismaClient;

  constructor() {
    const client = new PrismaClient({
      log: ["error", "info", "warn"],
    });

    const clientWithEncryptionExtension = client.$extends(fieldEncryptionExtension()) as PrismaClient;

    this._client = clientWithEncryptionExtension;
  }

  get client() {
    return this._client;
  }
}
