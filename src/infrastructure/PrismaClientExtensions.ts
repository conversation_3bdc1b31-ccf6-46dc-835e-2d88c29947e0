import { Prisma, PrismaClient } from "@prisma/client";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";

export class PrismaClientExtensions {
  static extends(databaseClient: PrismaClient, loggerService: ILoggerService) {
    const databaseClientExtended = databaseClient.$extends(this.createCustomMethods(loggerService, databaseClient));

    return databaseClientExtended;
  }

  static createCustomMethods(loggerService: ILoggerService, databaseClient: PrismaClient) {
    return Prisma.defineExtension((prisma) =>
      prisma.$extends({
        model: {
          $allModels: {
            async $create<T, K>(
              this: T,
              data: Prisma.Args<T, "create"> & { userId?: string },
            ): Promise<Prisma.Result<T, K, "create">> {
              const context = Prisma.getExtensionContext(this);

              const { userId } = data;

              delete data.userId;

              let response: any;
              if (context?.name === "OrderStatus") {
                const result = await databaseClient.$transaction(async (client) => {
                  await client.orderStatus.updateMany({
                    data: { current: false },
                    where: { orderId: data.data.orderId },
                  });

                  loggerService.log({
                    level: "info",
                    message: `Operation updateMany on table ${context?.name}`,
                    details: JSON.stringify(data.data),
                    userId,
                    entity: context?.name,
                    entityId: data.data.orderId,
                    action: "update",
                  });

                  const operationResult = await client.orderStatus.create(data);

                  return operationResult;
                });

                response = result;
              } else {
                if (context.name === "User") {
                  data.data.firstName = data.data.firstName.toLowerCase();
                  data.data.lastName = data.data.lastName.toLowerCase();
                  data.data.email = data.data.email.toLowerCase();
                }
                if (context.name === "Address") {
                  data.data.street = data.data.street.toLowerCase();
                  data.data.district = data.data.district.toLowerCase();
                  data.data.city = data.data.district.toLowerCase();
                  data.data.state = data.data.district.toLowerCase();
                }
                response = await (context as any).create(data);
              }

              loggerService.log({
                level: "info",
                message: `Operation create on table ${context?.name}`,
                details: "",
                userId,
                entityId: response?.id,
                entity: context?.name,
                action: "create",
                currentData: JSON.stringify(data.data, null, 2),
              });

              return response;
            },
            async $createMany<T, K>(
              this: T,
              data: Prisma.Args<T, "createMany"> & { userId?: string },
            ): Promise<Prisma.Result<T, K, "createMany">> {
              const context = Prisma.getExtensionContext(this);

              const { userId } = data;

              loggerService.log({
                level: "info",
                message: `Operation createMany on table ${context?.name}`,
                details: "",
                userId,
                entity: context?.name,
                action: "create",
              });

              delete data.userId;

              if (context.name === "User") {
                data.data.firstName = data.data.firstName.toLowerCase();
                data.data.lastName = data.data.lastName.toLowerCase();
                data.data.email = data.data.email.toLowerCase();
              }
              if (context.name === "Address") {
                data.data.street = data.data.street.toLowerCase();
                data.data.district = data.data.district.toLowerCase();
                data.data.city = data.data.district.toLowerCase();
                data.data.state = data.data.district.toLowerCase();
              }

              return (context as any).createMany(data);
            },
            async $update<T, K>(
              this: T,
              args: Prisma.Args<T, "update"> & { userId?: string },
            ): Promise<Prisma.Result<T, K, "update">> {
              const context = Prisma.getExtensionContext(this);

              const { userId } = args;

              loggerService.log({
                level: "info",
                message: `Operation update on table ${context?.name}`,
                details: JSON.stringify(args),
                userId,
                entity: context?.name,
                entityId: args.id,
                action: "update",
              });

              delete args.userId;
              if (context.name === "User") {
                args.data.firstName = args.data.firstName.toLowerCase();
                args.data.lastName = args.data.lastName.toLowerCase();
                args.data.email = args.data.email.toLowerCase();
              }
              if (context.name === "Address") {
                args.data.street = args.data.street.toLowerCase();
                args.data.district = args.data.district.toLowerCase();
                args.data.city = args.data.city.toLowerCase();
                args.data.state = args.data.state.toLowerCase();
              }

              return (context as any).update(args);
            },
            async $updateMany<T, K>(
              this: T,
              args: Prisma.Args<T, "updateMany"> & { userId?: string },
            ): Promise<Prisma.Result<T, K, "updateMany">> {
              const context = Prisma.getExtensionContext(this);

              const { userId } = args;

              loggerService.log({
                level: "info",
                message: `Operation updateMany on table ${context?.name}`,
                details: JSON.stringify(args),
                userId,
                entity: context?.name,
                entityId: args.data.id,
                action: "update",
                currentData: JSON.stringify(args, null, 2),
              });

              delete args.userId;

              if (context.name === "User") {
                args.data.firstName = args.data.firstName?.toLowerCase();
                args.data.lastName = args.data.lastName?.toLowerCase();
                args.data.email = args.data.email?.toLowerCase();
              }
              if (context.name === "Address") {
                args.data.street = args.data.street.toLowerCase();
                args.data.district = args.data.district.toLowerCase();
                args.data.city = args.data.city.toLowerCase();
                args.data.state = args.data.state.toLowerCase();
              }

              return (context as any).updateMany(args);
            },
            async $delete<T, K>(
              this: T,
              args: Prisma.Args<T, "delete"> & { userId?: string },
            ): Promise<Prisma.Result<T, K, "delete">> {
              const context = Prisma.getExtensionContext(this);

              const { userId } = args;

              loggerService.log({
                level: "info",
                message: `Operation delete on table ${context?.name}`,
                details: JSON.stringify(args),
                userId,
                entity: context?.name,
                entityId: args.where?.id,
                action: "delete",
              });

              delete args.userId;

              return (context as any).delete(args);
            },
            async $deleteMany<T, K>(
              this: T,
              args: Prisma.Args<T, "deleteMany"> & { userId?: string },
            ): Promise<Prisma.Result<T, K, "deleteMany">> {
              const context = Prisma.getExtensionContext(this);

              const { userId } = args;

              loggerService.log({
                level: "info",
                message: `Operation deleteMany on table ${context?.name}`,
                details: JSON.stringify(args),
                userId,
                entity: context?.name,
                entityId: typeof args.where?.id === "string" ? args.where?.id : undefined,
                action: "delete",
              });

              delete args.userId;

              return (context as any).deleteMany(args);
            },
          },
        },
      }),
    );
  }
}
