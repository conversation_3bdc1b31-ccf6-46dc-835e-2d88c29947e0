import { <PERSON>risma } from "@prisma/client";
import { injectable } from "inversify";
import { ICreateStoreModerationDTO } from "src/business/DTOs/StoreModeration/IStoreModeration";
import { IStoreModeration } from "src/business/Interfaces/Prisma/IStoreModeration";
import {
  IStoreModerationClient,
  IStoreModerationRepository,
} from "src/business/Interfaces/Repository/IStoreModeration";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class StoreModerationRepository
  extends BaseRepository<
    IStoreModeration,
    Prisma.StoreModerationFindManyArgs,
    Prisma.StoreModerationFindUniqueOrThrowArgs,
    Prisma.StoreModerationUpdateManyArgs,
    Prisma.StoreModerationDeleteManyArgs,
    Prisma.StoreModerationInclude
  >
  implements IStoreModerationRepository
{
  database: IStoreModerationClient;

  constructor() {
    super("storeModeration");
    this.database = this.databaseModel;
  }

  async create(storeModeration: ICreateStoreModerationDTO): Promise<IStoreModeration | null> {
    const data = await this.database.$create({
      data: { ...storeModeration.moderation, store: undefined },
      userId: this.userContext.userId,
    });
    return data;
  }

  async getByStoreId(storeId: string): Promise<IStoreModeration[]> {
    const data = await this.database.findMany({
      include: { store: true },
      where: { storeId },
    });

    return data;
  }

  async getMostRecentStoreModeration(storeId: string): Promise<IStoreModeration | null> {
    const data = await this.database.findFirst({
      include: { store: true },
      where: { storeId, NOT: { reason: null } },
      orderBy: { createdAt: "desc" },
    });
    return data;
  }
}
