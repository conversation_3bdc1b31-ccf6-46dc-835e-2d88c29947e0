import { <PERSON>rism<PERSON> } from "@prisma/client";
import { injectable } from "inversify";
import { ICooperative } from "src/business/Interfaces/Prisma/ICooperative";
import { ICooperativeClient, ICooperativeRepository } from "src/business/Interfaces/Repository/ICooperative";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class CooperativeRepository
  extends BaseRepository<
    ICooperative,
    Prisma.CooperativeFindManyArgs,
    Prisma.CooperativeFindUniqueOrThrowArgs,
    Prisma.CooperativeUpdateManyArgs,
    Prisma.CooperativeDeleteManyArgs,
    Prisma.CooperativeInclude
  >
  implements ICooperativeRepository
{
  database: ICooperativeClient;

  constructor() {
    super("cooperative");
    this.database = this.databaseModel;
  }

  async create(data: ICooperative): Promise<boolean> {
    const address =
      data.address && Object.keys(data.address).length > 0
        ? {
            create: {
              ...data.address,
              userAddress: undefined,
            },
          }
        : undefined;

    const result = await this.databaseClient.cooperative.create({
      data: {
        ...data,
        addressId: undefined,
        address,
      },
    });

    return !!result;
  }

  async updateCooperative(cooperativeId: string, data: ICooperative): Promise<boolean> {
    const address =
      data.address && Object.keys(data.address).length > 0
        ? {
            update: {
              ...data.address,
              userAddress: undefined,
            },
          }
        : undefined;
    const result = await this.database.$update({
      where: {
        id: cooperativeId,
      },
      data: {
        ...data,
        addressId: undefined,
        address,
      },
    });

    return !!result;
  }
}
