import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import {
  IProductSubcategoryClient,
  IProductSubcategoryRepository,
} from "src/business/Interfaces/Repository/IProductSubcategory";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ProductSubcategoryRepository
  extends BaseRepository<
    IProductSubcategory,
    Prisma.ProductSubcategoryFindManyArgs,
    Prisma.ProductSubcategoryFindUniqueOrThrowArgs,
    Prisma.ProductSubcategoryUpdateManyArgs,
    Prisma.ProductSubcategoryDeleteManyArgs,
    Prisma.ProductSubcategoryInclude
  >
  implements IProductSubcategoryRepository
{
  database: IProductSubcategoryClient;

  constructor() {
    super("productSubcategory");
    this.database = this.databaseModel;
  }

  async createMany(productSubcategory: IProductSubcategory[], client?: IProductSubcategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IProductSubcategoryClient>(client);

    const data = await database.$createMany({
      data: productSubcategory,
      userId: this.userContext.userId,
    });
    return data.count;
  }

  async deleteByProductId(productId: string, client?: IProductSubcategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IProductSubcategoryClient>(client);

    const result = await database.$deleteMany({
      where: { productId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async deleteByStore(storeId: string, client?: IProductSubcategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IProductSubcategoryClient>(client);

    const result = await database.$deleteMany({
      where: { product: { storeId } },
      userId: this.userContext.userId,
    });
    // const products = await Product.createQueryBuilder("products")
    //   .where("products.storeId = :storeId", { storeId })
    //   .getMany();

    // if (products.length < 1) return new DeleteResult();

    // const result = await this.DBLink.createQueryBuilder()
    //   .delete()
    //   .from(ProductSubcategory)
    //   .where("productId IN (:...ids)", { ids: products.map((product) => product.id) })
    //   .execute();

    return result.count;
  }
}
