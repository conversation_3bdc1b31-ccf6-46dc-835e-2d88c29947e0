import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import {
  IProductCategoryClient,
  IProductCategoryRepository,
} from "src/business/Interfaces/Repository/IProductCategory";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ProductCategoryRepository
  extends BaseRepository<
    IProductCategory,
    Prisma.ProductCategoryFindManyArgs,
    Prisma.ProductCategoryFindUniqueOrThrowArgs,
    Prisma.ProductCategoryUpdateManyArgs,
    Prisma.ProductCategoryDeleteManyArgs,
    Prisma.ProductCategoryInclude
  >
  implements IProductCategoryRepository
{
  database: IProductCategoryClient;

  constructor() {
    super("productCategory");
    this.database = this.databaseModel;
  }

  async createMany(productCategory: IProductCategory[], client?: IProductCategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IProductCategoryClient>(client);

    const data = await database.$createMany({
      data: productCategory,
      userId: this.userContext.userId,
    });
    return data.count;
  }

  async deleteByProductId(productId: string, client?: IProductCategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IProductCategoryClient>(client);

    const result = await database.$deleteMany({
      where: { productId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async deleteByCategoryId(categoryId: string, client?: IProductCategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IProductCategoryClient>(client);

    const result = await database.$deleteMany({
      where: { categoryId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async deleteByStore(storeId: string, client?: IProductCategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IProductCategoryClient>(client);

    const result = await database.$deleteMany({
      where: { product: { storeId } },
      userId: this.userContext.userId,
    });
    // const products = await Product.createQueryBuilder("products")
    //   .where("products.storeId = :storeId", { storeId })
    //   .getMany();

    // if (products.length < 1) return new DeleteResult();

    // const result = await this.DBLink.createQueryBuilder()
    //   .delete()
    //   .from(ProductCategory)
    //   .where("productId IN (:...ids)", { ids: products.map((product) => product.id) })
    //   .execute();
    return result.count;
  }
}
