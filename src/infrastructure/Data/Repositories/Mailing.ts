import { Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IMailing } from "src/business/Interfaces/Prisma/IMailing";
import { IMailingClient, IMailingRepository } from "src/business/Interfaces/Repository/IMailing";
import { IUserRepository } from "src/business/Interfaces/Repository/IUser";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class MailingRepository
  extends BaseRepository<
  IMailing,
  Prisma.MailingFindManyArgs,
  Prisma.MailingFindUniqueArgs,
  Prisma.MailingUpdateManyArgs,
  Prisma.MailingDeleteManyArgs,
  Prisma.MailingInclude
  >
  implements IMailingRepository {
  database: IMailingClient;

  constructor(
    @inject(TOKENS.IUserRepository)
    private userRepository: IUserRepository,
  ) {
    super("Mailing");
    this.database = this.databaseModel;
  }

  async create(Mailing: IMailing, users: IUserMailingDTO[]): Promise<IMailing | null> {
    if (users && users?.length > 0) {
      const resultMailingInput = {
        create: users
          ? users.map((user) => ({
            userId: user.id,
          }))
          : undefined,
      };

      const filtersMailingInput = Mailing.filtersMailing
        ? {
          create: {
            ...Mailing.filtersMailing,
          },
        }
        : undefined;

      const result = await this.database.$create({
        data: {
          ...Mailing,
          filtersMailing: filtersMailingInput,
          resultMailing: resultMailingInput,
        },
      });

      return result;
    }

    return null;
  }

  async updateMailing(mailingId: string, users: IUserMailingDTO[]): Promise<boolean> {
    const resultMailingInput = {
      create: users
        ? users.map((user) => ({
          userId: user.id,
        }))
        : undefined,
    };

    const result = await this.database.$update({
      data: {
        resultMailing: resultMailingInput,
      },
      where: {
        id: mailingId,
      },
    });

    return !!result;
  }

  async deleteMailing(id: string): Promise<IMailing> {
    const result = await this.database.delete({
      where: {
        id,
      },
    });

    return result;
  }
}
