import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IReviewClient, IReviewRepository } from "src/business/Interfaces/Repository/IReview";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ReviewRepository
  extends BaseRepository<
    IReview,
    Prisma.ReviewFindManyArgs,
    Prisma.ReviewFindUniqueOrThrowArgs,
    Prisma.ReviewUpdateManyArgs,
    Prisma.ReviewDeleteManyArgs,
    Prisma.ReviewInclude
  >
  implements IReviewRepository
{
  database: IReviewClient;

  constructor() {
    super("review");
    this.database = this.databaseModel;
  }

  async create(review: IReview): Promise<IReview | null> {
    const result: IReview = await this.database.$create({
      data: {
        customerReview: review.customerReview,
        rate: review.rate,
        order: review.orderId
          ? {
              connect: {
                id: review.orderId,
              },
            }
          : undefined,
        user: {
          connect: {
            id: review.userId,
          },
        },
        store: review.storeId
          ? {
              connect: {
                id: review.storeId,
              },
            }
          : undefined,
        deliveryman: review.deliverymanId
          ? {
              connect: {
                id: review.deliverymanId,
              },
            }
          : undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getStoreReviews(storeId: string): Promise<IReview[]> {
    const data = await this.database.findMany({
      include: { user: true, store: true, order: true },
      where: { store: { id: storeId } },
    });

    return data;
  }

  async getUserReviews(userId: string): Promise<IReview[]> {
    const data = await this.database.findMany({
      include: { user: true, store: true },
      where: { user: { id: userId } },
    });
    return data;
  }

  async getOrderReviews(orderId: string, deliverymanReview?: boolean): Promise<IReview | null> {
    const data = await this.database.findFirst({
      where: {
        order: {
          id: orderId,
        },
        deliveryman: deliverymanReview ? { isNot: null } : null,
      },
    });

    // const data = await this.findOne({
    //   relations: { user: true, store: true, order: true, deliveryman: true },
    //   where: {
    //     order: { id: orderId },
    //     deliveryman: deliverymanReview ? Not(IsNull()) : IsNull(),
    //   },
    // });

    return data;
  }

  async updateUserFkInReview(updatedId: string, userIds: string[], client?: IReviewClient): Promise<number> {
    const database = this.getDatabaseClient<IReviewClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
