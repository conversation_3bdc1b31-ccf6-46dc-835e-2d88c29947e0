import { <PERSON>BorderoStatus, <PERSON>File, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import IBorderoListDTO from "src/business/DTOs/Bordero/IBordero";
import IBorderoPaymentDTO from "src/business/DTOs/Bordero/IBorderoPayment";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IBordero } from "src/business/Interfaces/Prisma/IBordero";
import { IBorderoClient, IBorderoRepository } from "src/business/Interfaces/Repository/IBordero";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class BorderoRepository
  extends BaseRepository<
    IBordero,
    Prisma.BorderoFindManyArgs,
    Prisma.BorderoFindUniqueOrThrowArgs,
    Prisma.BorderoUpdateManyArgs,
    Prisma.BorderoDeleteManyArgs,
    Prisma.BorderoInclude
  >
  implements IBorderoRepository
{
  database: IBorderoClient;

  constructor() {
    super("bordero");
    this.database = this.databaseModel;
  }

  async create(data: IBordero): Promise<IBordero | null> {
    const result = await this.database.$create({
      data: {
        ...data,
        userId: undefined,
        storeId: undefined,
        cooperativeId: undefined,
        userIdAdmin: undefined,
        user: data.userId
          ? {
              connect: {
                id: data.userId,
              },
            }
          : undefined,
        store: data.storeId
          ? {
              connect: {
                id: data.storeId,
              },
            }
          : undefined,
        cooperative: data.cooperativeId
          ? {
              connect: {
                id: data.cooperativeId,
              },
            }
          : undefined,
        userAdmin: {
          connect: { id: this.userContext.userId },
        },
        payout: undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getBorderosPaged(
    currentPage: number,
    pageSize: number,
    filterStatus?: EBorderoStatus,
    filterCpfCnpj?: string | undefined,
    startDateFilter?: Date | undefined,
    endDateFilter?: Date | undefined,
    orderBy?: string,
    sortDirection?: string,
  ): Promise<PagedResult<IBorderoListDTO>> {
    const skip = (currentPage - 1) * pageSize;
    const take = pageSize;

    const periodCondition: object | undefined = {
      createdAt: {
        lte: endDateFilter,
        gte: startDateFilter,
      },
    };

    const whereCondition = {
      ...periodCondition,
      AND: [
        {
          status: filterStatus,
        },
        {
          user: {
            cpf: filterCpfCnpj,
          },
        },
      ],
    };

    let orderByCondition: {} | undefined;

    if (orderBy && sortDirection && orderBy !== "") {
      switch (orderBy) {
        case "name":
          orderByCondition = { user: { firstName: sortDirection, lastName: sortDirection } };
          break;
        case "createdBy":
          orderByCondition = { userAdmin: { firstName: sortDirection, lastName: sortDirection } };
          break;
        default:
          orderByCondition = { [orderBy]: sortDirection };
      }
    } else {
      orderByCondition = { createdAt: "desc" };
    }

    const result = await this.database.findMany({
      take,
      skip,
      select: {
        id: true,
        userId: true,
        storeId: true,
        status: true,
        payoutOwner: true,
        createdAt: true,
        statusDate: true,
        quantityOrders: true,
        sumOrderValue: true,
        sumAdministrativeFeeValue: true,
        sumTransferValue: true,
        cooperative: {
          select: {
            name: true,
            cnpj: true,
            pixKey: true,
          },
        },
        user: {
          select: {
            firstName: true,
            lastName: true,
            cpf: true,
            deliveryman: {
              select: {
                pixKey: true,
              },
            },
          },
        },
        store: {
          select: {
            name: true,
            cnpj: true,
            pixKey: true,
          },
        },
        userAdmin: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: orderByCondition,
      where: whereCondition,
    });

    const totalCount = await this.database.count({
      where: whereCondition,
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };
  }

  async getBorderoDetails(borderoId: string): Promise<IBorderoPaymentDTO | null> {
    const result = await this.database.findFirst({
      where: { id: borderoId },
      select: {
        id: true,
        userId: true,
        storeId: true,
        cooperativeId: true,
        status: true,
        payoutOwner: true,
        createdAt: true,
        statusDate: true,
        quantityOrders: true,
        sumOrderValue: true,
        sumAdministrativeFeeValue: true,
        sumTransferValue: true,
        cooperative: {
          select: {
            name: true,
            cnpj: true,
            pixKey: true,
          },
        },
        user: {
          select: {
            firstName: true,
            lastName: true,
            cpf: true,
            deliveryman: {
              select: {
                pixKey: true,
              },
            },
          },
        },
        store: {
          select: {
            name: true,
            cnpj: true,
            pixKey: true,
          },
        },
        userAdmin: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (result) {
      const files = await this.databaseClient.file.findMany({
        where: {
          entityId: borderoId,
          entity: EFile.bordero,
        },
      });
      return { ...result, files };
    }

    return null;
  }
}
