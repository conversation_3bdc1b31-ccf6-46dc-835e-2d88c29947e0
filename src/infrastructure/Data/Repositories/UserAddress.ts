import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IUserAddress } from "src/business/Interfaces/Prisma/IUserAddress";
import { IUserAddressClient, IUserAddressRepository } from "src/business/Interfaces/Repository/IUserAddress";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class UserAddressRepository
  extends BaseRepository<
    IUserAddress,
    Prisma.UserAddressFindManyArgs,
    Prisma.UserAddressFindUniqueOrThrowArgs,
    Prisma.UserAddressUpdateManyArgs,
    Prisma.UserAddressDeleteManyArgs,
    Prisma.UserAddressInclude
  >
  implements IUserAddressRepository
{
  database: IUserAddressClient;

  constructor() {
    super("userAddress");
    this.database = this.databaseModel;
  }

  async updateUserFkInUserAddress(updatedId: string, userIds: string[], client?: IUserAddressClient): Promise<number> {
    const database = this.getDatabaseClient<IUserAddressClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
