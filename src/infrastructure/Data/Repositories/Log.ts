import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ILogDTO } from "src/business/DTOs/Log/ILog";
import { ILog } from "src/business/Interfaces/Prisma/ILog";
import { ILogRepository, ILogClient } from "src/business/Interfaces/Repository/ILog";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class LogRepository
  extends BaseRepository<
    ILog,
    Prisma.LogFindManyArgs,
    Prisma.LogFindUniqueArgs,
    Prisma.LogUpdateManyArgs,
    Prisma.LogDeleteManyArgs,
    null
  >
  implements ILogRepository
{
  database: ILogClient;

  constructor() {
    super("log");
    this.database = this.databaseModel;
  }

  async getLogDetailsById(
    logId: string,
    entity: string,
    entityId: string,
    createdAt: Date,
    action: string,
  ): Promise<ILogDTO | null> {
    let take = 1;

    let whereLog: Prisma.LogWhereInput = {};

    if (action === "update") {
      whereLog = {
        ...whereLog,
        entity,
        entityId,
        createdAt: {
          lte: createdAt,
        },
      };
      take = 2;
    } else {
      whereLog = {
        id: logId,
      };
    }

    const logs = await this.database.findMany({
      where: whereLog,
      include: {
        user: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take,
    });

    const previousData = logs.length > 1 ? logs[1].currentData : undefined;

    const result = {
      ...logs[0],
      previousData,
    };

    return result;
  }
}
