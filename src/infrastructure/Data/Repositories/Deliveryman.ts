import { EProfileStatus, Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import { ILocationViewModel } from "src/api/ViewModels/Location/IViewModel";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IDeliveryman } from "src/business/Interfaces/Prisma/IDeliveryman";
import { IDeliverymanClient, IDeliverymanRepository } from "src/business/Interfaces/Repository/IDeliveryman";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class DeliverymanRepository
  extends BaseRepository<
    IDeliveryman,
    Prisma.DeliverymanFindManyArgs,
    Prisma.DeliverymanFindUniqueOrThrowArgs,
    Prisma.DeliverymanUpdateManyArgs,
    Prisma.DeliverymanDeleteManyArgs,
    Prisma.DeliverymanInclude
  >
  implements IDeliverymanRepository
{
  database: IDeliverymanClient;

  constructor(
    @inject(TOKENS.IUserProfileRepository)
    private userProfileRepository: IUserProfileRepository,
  ) {
    super("deliveryman");
    this.database = this.databaseModel;
  }

  async getByUserId(userId: string): Promise<IDeliveryman | null> {
    const result = await this.database.findUnique({
      where: { userId },
    });

    if (result) {
      const filesDeliveryman = await this.databaseClient.file.findMany({
        where: { AND: [{ entity: "deliveryman" }, { entityId: result.id }] },
      });

      const newDeliveryman: IDeliveryman = {
        ...result,
        files: filesDeliveryman || undefined,
      };

      return newDeliveryman;
    }

    return null;
  }

  async create(data: IDeliveryman, client?: IDeliverymanClient): Promise<IDeliveryman> {
    const database = this.getDatabaseClient<IDeliverymanClient>(client);

    const result = await database.$create({
      data: {
        ...data,
        user: undefined,
        orders: undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getUserDeliverymanByStatus(
    currentPage: number,
    pageSize: number,
    status: EProfileStatus,
  ): Promise<PagedResult<IDeliveryman>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const result = await this.database.findMany({
      where: { status },
      include: { user: true },
      skip: skipIndex,
      take: pageSize,
      orderBy: { updatedAt: "desc" },
    });

    const totalCount = await this.database.count({ where: { status } });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };

    // TODO Validate query
    // const page = currentPage > 0 ? currentPage : 1;
    // const skipIndex = (page - 1) * pageSize;
    // const query = this.database
    //   .createQueryBuilder("deliverymen")
    //   .innerJoinAndSelect("users", "users", "users.id = deliverymen.userId")
    //   .where("deliverymen.status = :status", { status })
    //   .orderBy("deliverymen.updatedAt", "DESC")
    //   .skip(skipIndex)
    //   .take(pageSize);
  }

  async isDeliverymanApproved(userId: string) {
    const approved = await this.database.findUnique({
      where: { userId, status: EProfileStatus.approved },
    });
    return !!approved;
  }

  async updateDeliverymanProfile(userId: string, data: IDeliveryman, profileId: string): Promise<boolean> {
    const deliverymanData = await this.findOne({
      where: { userId },
    });

    const result = await this.transaction<boolean>(async (client) => {
      if (deliverymanData && deliverymanData.id) {
        await this.update(deliverymanData.id, data, client.deliveryman);
      } else {
        await this.create(data, client.deliveryman);
      }
      await this.userProfileRepository.updateUserProfile(userId, profileId, client.userProfile);

      return true;
    });

    return result;
  }

  async deleteUserDeliverymanRelation(userIds: string[], client?: IDeliverymanClient): Promise<number> {
    const database = this.getDatabaseClient<IDeliverymanClient>(client);

    const result = await database.$deleteMany({
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }

  async getDeliverymanLocationByCPF(cpf: string): Promise<IDeliveryman | null> {
    const deliveryman = await this.database.findFirst({
      include: { user: true },
      where: {
        user: {
          cpf,
        },
      },
    });

    return deliveryman;
  }

  async updateDeliverymanLocation(userId: string, location: ILocationViewModel): Promise<boolean> {
    const deliveryman = await this.database.update({
      where: { userId },
      data: {
        ...location,
      },
    });

    return !!deliveryman;
  }

  async getCheckPixKey(userId: string)
  {
    
    const deliverymanPixKey = await this.database.findUnique({
      where:{userId},
      select:{pixKey: true}
    })
    return deliverymanPixKey
  }
}
