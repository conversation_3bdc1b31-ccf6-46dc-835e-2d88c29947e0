/* eslint-disable quotes */
import { EFileType, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IChatMessageDTO } from "src/business/DTOs/Chat/IChatMessage";
import { IChat } from "src/business/Interfaces/Prisma/IChat";
import { IChatClient, IChatRepository } from "src/business/Interfaces/Repository/IChat";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ChatRepository
  extends BaseRepository<
    IChat,
    Prisma.ChatFindManyArgs,
    Prisma.ChatFindUniqueOrThrowArgs,
    Prisma.ChatUpdateManyArgs,
    Prisma.ChatDeleteManyArgs,
    Prisma.ChatInclude
  >
  implements IChatRepository
{
  database: IChatClient;

  constructor() {
    super("chat");
    this.database = this.databaseModel;
  }

  async create(chat: IChat): Promise<IChat> {
    const result = await this.databaseModel.$create({
      data: {
        ...chat,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getByOrderId(orderId: string): Promise<IChat | null> {
    const result = await this.findOne({
      where: { orderId },
    });

    return result;
  }

  async getChatMessages(orderId: string): Promise<IChatMessageDTO[]> {
    const result = await this.findOne({
      where: { orderId },
      include: {
        chatMessage: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
            profile: {
              select: {
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (result && result.chatMessage) {
      const usersOnChat = result.chatMessage.map((message) => message.userId);

      const usersOnChatNormalized = [...new Set(usersOnChat)];

      const usersProfilePicture = await this.databaseClient.file.findMany({
        where: {
          entityId: {
            in: usersOnChatNormalized,
          },
          type: EFileType.profilePhoto,
        },
      });

      const messagesWithProfilePicture = result.chatMessage.map((message) => {
        const userProfilePicture = usersProfilePicture.find(
          (profilePicture) => profilePicture.entityId === message.userId,
        );

        return {
          id: message.id,
          createdAt: message.createdAt,
          message: message.message,
          user: {
            id: message.user?.id,
            firstName: message.user?.firstName,
            lastName: message.user?.lastName,
            pictureUrl: userProfilePicture?.url,
            profile: message.profile?.name,
          },
        } as IChatMessageDTO;
      });

      return messagesWithProfilePicture;
    }

    // const resultDb = await this.DBLink.createQueryBuilder("chat")
    //   .where("chat.orderId = :id", { id: orderId })
    //   .leftJoinAndSelect("chat.chatMessages", "chatMessages")
    //   .leftJoinAndSelect("chatMessages.user", "user")
    //   .leftJoinAndSelect("chatMessages.profile", "profile")
    //   .leftJoinAndMapOne("user.profilePicture", "files", "user_file", 'user_file."entityId"= user.id')
    //   .orderBy("chatMessages.createdAt", "DESC")
    //   .getOne();
    // return resultDb?.chatMessages ? resultDb.chatMessages : null;
    return [];
  }
}
