import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IUserFavoriteStores } from "src/business/Interfaces/Prisma/IUserFavoriteStores";
import {
  IUserFavoriteStoresClient,
  IUserFavoriteStoresRepository,
} from "src/business/Interfaces/Repository/IUserFavoriteStores";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class UserFavoriteStoresRepository
  extends BaseRepository<
    IUserFavoriteStores,
    Prisma.UserFavoriteStoresFindManyArgs,
    Prisma.UserFavoriteStoresFindUniqueOrThrowArgs,
    Prisma.UserFavoriteStoresUpdateManyArgs,
    Prisma.UserFavoriteStoresDeleteManyArgs,
    Prisma.UserFavoriteStoresInclude
  >
  implements IUserFavoriteStoresRepository
{
  database: IUserFavoriteStoresClient;

  constructor() {
    super("userFavoriteStores");
    this.database = this.databaseModel;
  }

  async updateUserFkInFavStores(
    updatedId: string,
    userIds: string[],
    client?: IUserFavoriteStoresClient,
  ): Promise<number> {
    const database = this.getDatabaseClient<IUserFavoriteStoresClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
