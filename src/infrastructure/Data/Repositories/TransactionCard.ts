import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ITransactionCard } from "src/business/Interfaces/Prisma/ITransactionCard";
import {
  ITransactionCardClient,
  ITransactionCardRepository,
} from "src/business/Interfaces/Repository/ITransactionCard";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class TransactionCardRepository
  extends BaseRepository<
    ITransactionCard,
    Prisma.TransactionCardFindManyArgs,
    Prisma.TransactionCardFindUniqueOrThrowArgs,
    Prisma.TransactionCardUpdateManyArgs,
    Prisma.TransactionCardDeleteManyArgs,
    Prisma.TransactionCardInclude
  >
  implements ITransactionCardRepository
{
  database: ITransactionCardClient;

  constructor() {
    super("transactionCard");
    this.database = this.databaseModel;
  }

  async create(data: ITransactionCard): Promise<ITransactionCard> {
    const result = await this.database.$create({
      data: {
        ...data,
        card: undefined,
        transaction: undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }
}
