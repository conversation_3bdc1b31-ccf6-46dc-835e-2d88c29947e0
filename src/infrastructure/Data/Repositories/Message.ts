import { EMessageStatus, Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IListNotificationMessage } from "src/business/DTOs/Message/IListNotificationMessage";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";
import { IMessageClient, IMessageRepository } from "src/business/Interfaces/Repository/IMessage";
import ProfilesSingleton from "src/business/Singletons/Profile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class MessageRepository
  extends BaseRepository<
    IMessage,
    Prisma.MessageFindManyArgs,
    Prisma.MessageFindUniqueOrThrowArgs,
    Prisma.MessageUpdateManyArgs,
    Prisma.MessageDeleteManyArgs,
    Prisma.MessageInclude
  >
  implements IMessageRepository
{
  @inject(TOKENS.ProfilesSingleton)
  private profilesSingleton: ProfilesSingleton;

  private database: IMessageClient;

  constructor() {
    super("message");
    this.database = this.databaseModel;
  }

  async create(message: IMessage): Promise<IMessage> {
    const result = await this.database.$create({
      data: {
        ...message,
        senderId: undefined,
        content: message.content
          ? {
              createMany: { data: [...message.content] },
            }
          : undefined,
        sender: message.sender
          ? {
              connect: { id: message.sender.id },
            }
          : undefined,
        userMessage: message.userMessage
          ? {
              createMany: { data: [...message.userMessage] },
            }
          : undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getUserMessages(
    id: string,
    filterType: "received" | "sended" | "all",
    sendingType?: EMessageSendingType,
  ): Promise<IMessage[]> {
    let whereCondition: any = {};

    if (filterType === "received") {
      whereCondition = {
        userMessage: {
          some: {
            userId: id,
          },
        },
      };
    }

    if (filterType === "sended") {
      whereCondition = {
        senderId: id,
      };
    }

    if (sendingType) {
      whereCondition = {
        ...whereCondition,
        sendingType,
      };
    }

    const result = await this.database.findMany({
      where: whereCondition,
      include: {
        content: {
          include: {
            emailAttachment: true,
          },
        },
        sender: true,
        userMessage: {
          include: {
            user: true,
          },
        },
      },
    });

    return result;
    // const data = this.DBLink.createQueryBuilder("message")
    //   .leftJoinAndSelect("message.content", "content")
    //   .leftJoinAndSelect("content.emailAttachment", "emailAttachment")
    //   .leftJoinAndSelect("message.sender", "sender")
    //   .leftJoinAndSelect("message.userMessage", "userMessage")
    //   .leftJoinAndSelect("userMessage.user", "user");

    // if (filterType === "received") {
    //   data.where("userMessage.userId := userId", { userId: id });
    // }

    // if (filterType === "sended") {
    //   data.andWhere("sender.id := userId", { userId: id });
    // }

    // if (sendingType) {
    //   data.andWhere("message.sendingType := type", { type: sendingType });
    // }

    // const dataFiltered = await data.getMany();

    // return dataFiltered;
  }

  async getUserNotificationMessages(
    userId: string,
    profileId: string,
    currentPage: number,
    pageSize: number,
    filterByStatus?: EMessageStatus,
    dateToFilter?: string,
    orderBy?: "asc" | "desc",
    origin?: "web",
  ): Promise<PagedResult<IListNotificationMessage>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;
    if (!orderBy) orderBy = "desc";
    const date = dateToFilter ? new Date(dateToFilter) : undefined;

    console.log("Date to filter", dateToFilter, date);

    let whereCondition: Prisma.MessageWhereInput = {
      userMessage: {
        some: {
          userId,
          profileId: origin === "web" ? this.profilesSingleton.manager.id : profileId,
        },
      },
    };

    if (date) {
      const messagesFilteredByDate = await this.databaseClient.$queryRaw<{ id: string }[]>(
        Prisma.sql`
          SELECT messages.id
          FROM messages
          WHERE
          EXTRACT(MONTH FROM messages."createdAt") = ${date.getMonth() + 1}
          AND EXTRACT(DAY FROM messages."createdAt") = ${date.getDate()}
          AND EXTRACT(YEAR FROM messages."createdAt") = ${date.getFullYear()}
        `,
      );

      const messagesIdsFiltered = messagesFilteredByDate.map((message) => message?.id);

      whereCondition = {
        ...whereCondition,
        id: {
          in: messagesIdsFiltered,
        },
      };
    }

    let statusFilter = {};
    if (filterByStatus === EMessageStatus.read) {
      statusFilter = {
        status: {
          equals: filterByStatus,
        },
      };
    }

    if (filterByStatus === EMessageStatus.created) {
      statusFilter = {
        status: {
          equals: EMessageStatus.created,
        },
      };
    }

    whereCondition = {
      ...whereCondition,
      userMessage: {
        some: {
          ...whereCondition.userMessage?.some,
          ...statusFilter,
        },
      },
      content: {
        some: {
          sendingType: EMessageSendingType.notification,
        },
      },
    };

    console.log("Where condition", whereCondition);

    const userMessage = await this.database.findMany({
      where: whereCondition,
      select: {
        id: true,
        userMessage: {
          select: {
            status: true,
          },
          where: {
            userId,
            profileId,
          },
        },
        content: {
          select: {
            sendingType: true,
            body: true,
          },
        },
        title: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: orderBy,
      },
      skip: skipIndex,
      take: pageSize,
    });

    const totalCount = await this.database.count({
      where: whereCondition,
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: userMessage,
      totalCount,
      totalPages,
    };
  }

  async updateUserFkInMessage(updatedId: string, userIds: string[], client?: IMessageClient): Promise<number> {
    const database = this.getDatabaseClient<IMessageClient>(client);

    const result = await database.$updateMany({
      data: { senderId: updatedId },
      where: {
        senderId: { in: userIds },
      },
    });

    return result.count;
  }
}
