import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ICategorySubcategory } from "src/business/Interfaces/Prisma/ICategorySubcategory";
import {
  ICategorySubcategoryClient,
  ICategorySubcategoryRepository,
} from "src/business/Interfaces/Repository/ICategorySubcategory";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class CategorySubcategoryRepository
  extends BaseRepository<
    ICategorySubcategory,
    Prisma.CategorySubcategoryFindManyArgs,
    Prisma.CategorySubcategoryFindUniqueOrThrowArgs,
    Prisma.CategorySubcategoryUpdateManyArgs,
    Prisma.CategorySubcategoryDeleteManyArgs,
    Prisma.CategorySubcategoryInclude
  >
  implements ICategorySubcategoryRepository
{
  database: ICategorySubcategoryClient;

  constructor() {
    super("categorySubcategory");
    this.database = this.databaseModel;
  }

  async create(data: ICategorySubcategory): Promise<ICategorySubcategory | null> {
    const categorySubcategory = await this.database.$create({
      data: {
        ...data,
        subcategoryId: undefined,
        categoryId: undefined,
        category: {
          connect: {
            id: data.categoryId,
          },
        },
        subcategory: {
          connect: {
            id: data.subcategoryId,
          },
        },
      },
      userId: this.userContext.userId,
    });

    return categorySubcategory;
  }

  async createMany(data: ICategorySubcategory[], client?: ICategorySubcategoryClient): Promise<number> {
    const database = this.getDatabaseClient<ICategorySubcategoryClient>(client);

    const result = await database.$createMany({ data, userId: this.userContext.userId });

    return result.count;
  }

  async deleteByCategoryId(categoryId: string, client?: ICategorySubcategoryClient): Promise<number> {
    const database = this.getDatabaseClient<ICategorySubcategoryClient>(client);

    const result = await database.$deleteMany({
      where: { categoryId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async deleteBySubcategoryId(subcategoryId: string, client?: ICategorySubcategoryClient): Promise<number> {
    const database = this.getDatabaseClient<ICategorySubcategoryClient>(client);

    const result = await database.$deleteMany({
      where: { subcategoryId },
      userId: this.userContext.userId,
    });

    return result.count;
  }
}
