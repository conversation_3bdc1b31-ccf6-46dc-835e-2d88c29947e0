import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ILoginSessionAggregateDTO } from "src/business/DTOs/LoginSession/ILoginSession";
import { ILoginSession } from "src/business/Interfaces/Prisma/ILoginSession";
import { ILoginSessionClient, ILoginSessionRepository } from "src/business/Interfaces/Repository/ILoginSession";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class LoginSessionRepository
  extends BaseRepository<
    ILoginSession,
    Prisma.LoginSessionFindManyArgs,
    Prisma.LoginSessionFindUniqueOrThrowArgs,
    Prisma.LoginSessionUpdateManyArgs,
    Prisma.LoginSessionDeleteManyArgs,
    Prisma.LoginSessionInclude
  >
  implements ILoginSessionRepository
{
  database: ILoginSessionClient;

  constructor() {
    super("loginSession");
    this.database = this.databaseModel;
  }

  async create(session: ILoginSession): Promise<ILoginSession> {
    const result = await this.database.$create({
      data: {
        ...session,
        userId: undefined,
        user: {
          connect: {
            id: session.userId,
          },
        },
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getActiveLoginSession(userId: string, limitDate: Date): Promise<ILoginSession | null> {
    const result = await this.database.findFirst({
      where: {
        userId,
        endAccess: {
          gte: limitDate,
        },
      },
    });

    return result;
  }

  async updateLastAccess(userId: string): Promise<boolean> {
    const lastAccess = await this.database.findFirst({ where: { userId }, orderBy: { endAccess: "desc" } });
    const result = await this.database.$update({
      where: { id: lastAccess?.id },
      data: { endAccess: new Date() },
      userId: this.userContext.userId,
    });

    return !!result;
  }

  async getAllLoginSessionsInAWeek(firstWeekDay: Date, lastWeekDay: Date): Promise<ILoginSession[]> {
    const result = await this.database.findMany({
      where: {
        endAccess: {
          gte: firstWeekDay,
          lte: lastWeekDay,
        },
      },
      orderBy: {
        endAccess: "desc",
      },
    });

    return result;
  }

  async getTotalNumberOfAccess(firstWeekDay: Date, lastWeekDay: Date): Promise<ILoginSessionAggregateDTO> {
    const result = await this.database.aggregate({
      where: {
        startAccessDate: {
          gte: firstWeekDay,
          lte: lastWeekDay,
        },
      },
      _count: {
        startAccessDate: true,
      },
    });

    return result;
  }

  async getHourWithMostAccess(firstWeekDay: Date, lastWeekDay: Date): Promise<ILoginSessionAggregateDTO[]> {
    const result = await this.database.groupBy({
      by: ["startAccessHour"],
      where: {
        startAccessDate: {
          gte: firstWeekDay,
          lte: lastWeekDay,
        },
      },
      _count: {
        startAccessHour: true,
      },
    });
    return result;
  }

  async getNumberOfAccessByDay(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]> {
    const result = await this.database.groupBy({
      by: ["startAccessDate", "startAccessDay"],
      where: {
        startAccessDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        startAccessDate: "desc",
      },
      _count: {
        startAccessDate: true,
      },
    });
    return result;
  }

  async getNumberOfAccessByTime(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]> {
    const result = await this.database.groupBy({
      by: ["startAccessDate", "startAccessDay", "startAccessHour"],
      where: {
        startAccessDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        startAccessDate: "desc",
      },
      _count: {
        startAccessDate: true,
      },
    });
    return result;
  }

  async updateUserFkInLoginSession(
    updatedId: string,
    userIds: string[],
    client?: ILoginSessionClient,
  ): Promise<number> {
    const database = this.getDatabaseClient<ILoginSessionClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
