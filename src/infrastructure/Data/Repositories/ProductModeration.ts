import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import { IProductModeration } from "src/business/Interfaces/Prisma/IProductModeration";
import {
  IProductModerationClient,
  IProductModerationRepository,
} from "src/business/Interfaces/Repository/IProductModerationRepository";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ProductModerationRepository
  extends BaseRepository<
    IProductModeration,
    Prisma.ProductModerationFindManyArgs,
    Prisma.ProductModerationFindUniqueOrThrowArgs,
    Prisma.ProductModerationUpdateManyArgs,
    Prisma.ProductModerationDeleteManyArgs,
    Prisma.ProductModerationInclude
  >
  implements IProductModerationRepository
{
  database: IProductModerationClient;

  constructor() {
    super("productModeration");
    this.database = this.databaseModel;
  }

  async create(
    productModeration: ICreateProductModerationDTO,
    client?: IProductModerationClient,
  ): Promise<IProductModeration | null> {
    const database = this.getDatabaseClient<IProductModerationClient>(client);

    const data = await database.$create({
      data: { productId: productModeration.productId, reason: productModeration.moderation?.reason || "" },
      userId: this.userContext.userId,
    });

    return data;
  }

  async getByProductId(productId: string): Promise<IProductModeration[]> {
    const data = await this.database.findMany({
      include: { product: true },
      where: { productId },
    });

    return data;
  }

  async getMostRecentProductModeration(productId: string): Promise<IProductModeration | null> {
    const data = await this.database.findFirst({
      where: { productId, NOT: { reason: null } },
      orderBy: { createdAt: "desc" },
    });
    return data;
  }
}
