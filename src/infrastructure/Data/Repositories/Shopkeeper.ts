import { EProfileStatus, Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IShopkeeper } from "src/business/Interfaces/Prisma/IShopkeeper";
import { IShopkeeperClient, IShopkeeperRepository } from "src/business/Interfaces/Repository/IShopkeeper";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ShopkeeperRepository
  extends BaseRepository<
    IShopkeeper,
    Prisma.ShopkeeperFindManyArgs,
    Prisma.ShopkeeperFindUniqueOrThrowArgs,
    Prisma.ShopkeeperUpdateManyArgs,
    Prisma.ShopkeeperDeleteManyArgs,
    Prisma.ShopkeeperInclude
  >
  implements IShopkeeperRepository
{
  database: IShopkeeperClient;

  constructor(
    @inject(TOKENS.IUserProfileRepository)
    private userProfileRepository: IUserProfileRepository,
  ) {
    super("shopkeeper");
    this.database = this.databaseModel;
  }

  async create(data: IShopkeeper, client?: IShopkeeperClient): Promise<IShopkeeper> {
    const database = this.getDatabaseClient<IShopkeeperClient>(client);

    const result = await database.$create({
      data: {
        ...data,
        user: undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getUserShopkeeperByStatus(
    currentPage: number,
    pageSize: number,
    status: EProfileStatus,
  ): Promise<PagedResult<IShopkeeper>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const result = await this.database.findMany({
      where: { status },
      include: { user: true },
      skip: skipIndex,
      take: pageSize,
      orderBy: { updatedAt: "desc" },
    });

    const totalCount = await this.database.count({ where: { status } });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };
  }

  async isShopkeeperApproved(userId: string): Promise<boolean> {
    const approved = await this.database.findUnique({
      where: { userId, status: EProfileStatus.approved },
    });
    return !!approved;
  }

  async updateShopkeeperProfile(userId: string, data: IShopkeeper, profileId: string): Promise<boolean> {
    const shopkeeperData = await this.findOne({
      where: { userId },
    });

    const result = await this.transaction<boolean>(async (client) => {
      if (shopkeeperData && shopkeeperData.id) {
        await this.update(shopkeeperData.id, data, client.shopkeeper);
      } else {
        await this.create(data, client.shopkeeper);
      }

      await this.userProfileRepository.updateUserProfile(userId, profileId, client.userProfile);

      return true;
    });

    return result;
  }

  async getByUserId(userId: string): Promise<IShopkeeper | null> {
    const result = await this.findOne({
      where: { userId },
      include: { user: true },
    });

    if (result) {
      const filesShopkeeper = await this.databaseClient.file.findMany({
        where: { AND: [{ entity: "shopkeeper" }, { entityId: result.id }] },
      });

      const newDeliveryman: IShopkeeper = {
        ...result,
        files: filesShopkeeper || [undefined],
      };

      return newDeliveryman;
    }

    return null;
  }

  async deleteUserShopkeeperRelation(userIds: string[], client?: IShopkeeperClient): Promise<number> {
    const database = this.getDatabaseClient<IShopkeeperClient>(client);

    const result = await database.$deleteMany({
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
