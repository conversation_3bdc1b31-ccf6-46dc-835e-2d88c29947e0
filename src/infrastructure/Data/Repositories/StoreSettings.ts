import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IStoreSettings } from "src/business/Interfaces/Prisma/IStoreSettings";
import { IStoreSettingsClient, IStoreSettingsRepository } from "src/business/Interfaces/Repository/IStoreSettings";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
class StoreSettingsRepository
  extends BaseRepository<
    IStoreSettings,
    Prisma.StoreSettingsFindManyArgs,
    Prisma.StoreSettingsFindUniqueOrThrowArgs,
    Prisma.StoreSettingsUpdateManyArgs,
    Prisma.StoreSettingsDeleteManyArgs,
    Prisma.StoreSettingsInclude
  >
  implements IStoreSettingsRepository
{
  database: IStoreSettingsClient;

  constructor() {
    super("storeSettings");
    this.database = this.databaseModel;
  }

  async create(storeSettings: IStoreSettings): Promise<IStoreSettings | null> {
    const result = await this.database.$create({
      data: { ...storeSettings, store: undefined },
      userId: this.userContext.userId,
    });
    return result;
  }

  async getSettingsByStoreId(storeId: string): Promise<IStoreSettings | null> {
    const storeSettings: IStoreSettings | null = await this.database.findFirst({
      where: { storeId },
    });
    return storeSettings;
  }

  async updateSetting(storeId: string, setting: IStoreSettings): Promise<IStoreSettings> {
    const storeSettings = await this.database.findFirst({
      where: { storeId },
    });

    if (storeSettings) {
      const updateStoreSettings = await this.database.$update({
        where: { id: storeSettings.id },
        data: {
          ...setting,
          store: undefined,
        },
        userId: this.userContext.userId,
      });

      return updateStoreSettings;
    }

    const createStoreSettings = await this.database.$create({
      data: {
        ...setting,
        store: undefined,
        storeId,
      },
      userId: this.userContext.userId,
    });
    return createStoreSettings;
  }
}

export { StoreSettingsRepository };
