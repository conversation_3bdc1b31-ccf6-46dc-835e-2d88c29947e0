/* eslint-disable no-dupe-class-members */
import { Prisma } from "@prisma/client";
import { inject, injectable, unmanaged } from "inversify";
import { IBaseRepository, TransactionCallback } from "src/business/Interfaces/Repository/IBase";
import container from "src/business/Configs/Inversify/Container";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IUserContext } from "src/business/Interfaces/Tools/IUserContext";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";

// TODO Implement findFirst

@injectable()
export class BaseRepository<T, FindManyOptions, FindUniqueOptions, UpdateOptions, DeleteOptions, Include = null>
  implements IBaseRepository<T, FindManyOptions, FindUniqueOptions, UpdateOptions, DeleteOptions, Include>
{
  protected databaseClient: IExtendedPrismaClient["client"];

  protected databaseModel;

  @inject(TOKENS.UserContext)
  protected userContext: IUserContext;

  @inject(TOKENS.LoggerService)
  private loggerService: ILoggerService;

  constructor(@unmanaged() type: string) {
    const databaseClient = container.get<IExtendedPrismaClient>(TOKENS.ExtendedPrismaClient).client;

    this.databaseModel = databaseClient[type];
    this.databaseClient = databaseClient;
  }

  async find(options: FindManyOptions): Promise<T[]> {
    const result = await this.databaseModel.findMany(options);

    return result;
  }

  async findOne(options: FindUniqueOptions): Promise<T | null> {
    const result = await this.databaseModel.findUnique(options);
    return result;
  }

  async findFirst(options: FindManyOptions): Promise<T | null> {
    const result = await this.databaseModel.findFirst(options);
    return result;
  }

  async update(id: string, item: Partial<T>, client?: any): Promise<number> {
    const database = this.getDatabaseClient(client);

    const result = await database.$updateMany({
      where: { id },
      data: item,
      userId: this.userContext.userId,
    });
    return result.count;
  }

  async updateMany(options: UpdateOptions, client?: any): Promise<number> {
    const database = this.getDatabaseClient(client);

    const result = await database.$updateMany({
      ...options,
      userId: this.userContext.userId,
    });
    return result.count;
  }

  async delete(id: string, client?: any): Promise<boolean> {
    const database = this.getDatabaseClient(client);

    const result = await database.$delete({
      where: { id },
      userId: this.userContext.userId,
    });

    return result !== null;
  }

  async deleteMany(options: DeleteOptions, client?: any): Promise<number> {
    const database = this.getDatabaseClient(client);

    const result = await database.$deleteMany({
      ...options,
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async getPaged(options: FindManyOptions): Promise<{
    result: T[];
    totalCount: number;
    totalPages: number;
  }> {
    const take = "take";
    const where = "where";

    const result: T[] = await this.databaseModel.findMany(options);

    const totalCount = await this.databaseModel.count({ where: options[where] });

    const totalPages = Math.ceil(totalCount / options[take]);
    return {
      result,
      totalCount,
      totalPages,
    };
  }

  getById(id: string): Promise<T | null>;

  getById(id: string, includes: Include): Promise<T | null>;

  async getById(id: string, includes?: Include): Promise<T | null> {
    const result = await this.databaseModel.findUnique({
      where: { id },
      include: includes,
    });

    return result;
  }

  async getAll(): Promise<T[]> {
    const result = await this.databaseModel.findMany();

    return result;
  }

  async transaction<T>(callback: TransactionCallback<T>): Promise<T | boolean> {
    try {
      const result = await this.databaseClient.$transaction<T>(callback, {
        isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
        timeout: 20000,
      });

      return result;
    } catch (error) {
      console.log(error);

      this.loggerService.logError(error);

      return false;
    }
  }

  protected getDatabaseClient<T>(client?: T): T {
    if (client) {
      return client;
    }

    return this.databaseModel as T;
  }

  protected getPageSizeAndCurrentPage(pageSize: string, currentPage: string) {
    let pageSizeInt = parseInt(pageSize, 10);
    let currentPageInt = parseInt(currentPage, 10) < 1 ? 1 : parseInt(currentPage, 10);

    if (Number.isNaN(pageSizeInt) || pageSizeInt < 1) {
      pageSizeInt = 10;
    }

    if (Number.isNaN(currentPageInt) || currentPageInt < 1) {
      currentPageInt = 1;
    }

    return { pageSizeInt, currentPageInt };
  }
}
