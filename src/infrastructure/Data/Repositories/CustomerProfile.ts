import { Prisma } from "@prisma/client";
import { ICustomerProfile } from "src/business/Interfaces/Prisma/ICustomerProfile";
import { ICustomerProfileRepository } from "src/business/Interfaces/Repository/ICustomerProfile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

export class CustomerProfileRepository
  extends BaseRepository<
    ICustomerProfile,
    Prisma.CustomerProfileFindManyArgs,
    Prisma.CustomerProfileFindUniqueOrThrowArgs,
    Prisma.CustomerProfileUpdateManyArgs,
    Prisma.CustomerProfileDeleteManyArgs,
    null
  >
  implements ICustomerProfileRepository
{
  constructor() {
    super("customerProfile");
  }
}
