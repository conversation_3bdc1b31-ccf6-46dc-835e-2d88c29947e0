import { inject, injectable } from "inversify";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";
import { IResultMailing } from "src/business/Interfaces/Prisma/IResultMailing";
import { Prisma } from "@prisma/client";
import { IResultMailingClient, IResultMailingRepository } from "src/business/Interfaces/Repository/IResultMailing";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IUserRepository } from "src/business/Interfaces/Repository/IUser";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { PagedResult } from "src/business/DTOs/PagedResult";

@injectable()
export class ResultMailingRepository
  extends BaseRepository<
    IResultMailing,
    Prisma.ResultMailingFindManyArgs,
    Prisma.ResultMailingFindUniqueArgs,
    Prisma.ResultMailingUpdateManyArgs,
    Prisma.ResultMailingDeleteArgs,
    Prisma.ResultMailingInclude
  >
  implements IResultMailingRepository
{
  database: IResultMailingClient;

  constructor(
    @inject(TOKENS.IUserRepository)
    private userRepository: IUserRepository,
  ) {
    super("resultMailing");
    this.database = this.databaseModel;
  }

  async getUsersIdByMailingId(mailingId: string): Promise<{ userId: string | null }[]> {
    const result = await this.database.findMany({
      where: {
        mailingId,
      },
      select: {
        userId: true,
      },
    });

    return result;
  }

  async getUsersIdsByMailingIdPaged(
    currentPage: number,
    pageSize: number,
    mailingId: string,
    filterName?: string | undefined,
  ): Promise<PagedResult<IUserMailingDTO>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const lasRuntTimeDate = await this.database.findFirst({
      where: {
        mailingId,
      },
      orderBy: {
        runTimeDate: "desc",
      },
    });

    const dataResultMailing = await this.database.findMany({
      where: {
        mailingId,
        runTimeDate: {
          equals: lasRuntTimeDate?.runTimeDate,
        },
      },
      select: {
        userId: true,
      },
      skip: skipIndex,
      take: pageSize,
    });

    const usersIds = dataResultMailing.map((item) => item.userId || "");

    const firstName = filterName || undefined;

    const result = await this.userRepository.find({
      where: {
        id: {
          in: usersIds,
        },
        permanentlyDeleted: false,
        firstName,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        cpf: true,
        phone: true,
      },
    });

    const totalCount = await this.database.count({
      where: {
        mailingId,
      },
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };
  }
}
