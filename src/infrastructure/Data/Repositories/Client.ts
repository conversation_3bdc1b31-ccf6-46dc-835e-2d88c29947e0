import { <PERSON>risma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IClient } from "src/business/Interfaces/Prisma/IClient";
import { IClientClient, IClientRepository } from "src/business/Interfaces/Repository/IClient";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ClientRepository
  extends BaseRepository<
    IClient,
    Prisma.ClientFindManyArgs,
    Prisma.ClientFindUniqueOrThrowArgs,
    Prisma.ClientUpdateManyArgs,
    Prisma.ClientDeleteManyArgs,
    Prisma.ClientInclude
  >
  implements IClientRepository
{
  database: IClientClient;

  constructor(
    @inject(TOKENS.IUserProfileRepository)
    private userProfileRepository: IUserProfileRepository,
  ) {
    super("client");
    this.database = this.databaseModel;
  }

  async create(data: IClient, client?: IClientClient): Promise<IClient> {
    const database = this.getDatabaseClient<IClientClient>(client);

    const result = await database.$create({
      data: {
        ...data,
        user: undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async updateClientProfile(userId: string, data: IClient, profileId: string): Promise<boolean> {
    const clientData = await this.findOne({
      where: { userId },
    });

    const result = this.transaction<boolean>(async (client) => {
      if (clientData && clientData.id) {
        await this.update(clientData.id, data, client.client);
      } else {
        await this.create(data, client.client);
      }

      await this.userProfileRepository.updateUserProfile(userId, profileId, client.userProfile);

      return true;
    });

    return result;
  }

  async deleteUserClientRelation(userIds: string[], client?: IClientClient): Promise<number> {
    const database = this.getDatabaseClient<IClientClient>(client);

    const result = await database.$deleteMany({
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
