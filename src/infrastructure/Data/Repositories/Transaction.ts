import { ETransactionStatus, ETransactionType, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IListTransactionByOrderDTO } from "src/business/DTOs/Transactions/ListTransactionByOrder";
import { ITransaction } from "src/business/Interfaces/Prisma/ITransaction";
import { ITransactionClient, ITransactionRepository } from "src/business/Interfaces/Repository/ITransaction";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class TransactionRepository
  extends BaseRepository<
    ITransaction,
    Prisma.TransactionFindManyArgs,
    Prisma.TransactionFindUniqueArgs,
    Prisma.TransactionUpdateManyArgs,
    Prisma.TransactionDeleteManyArgs,
    Prisma.TransactionInclude
  >
  implements ITransactionRepository
{
  database: ITransactionClient;

  constructor() {
    super("transaction");
    this.database = this.databaseModel;
  }

  async create(transaction: ITransaction): Promise<ITransaction | null> {
    const result = this.database.$create({
      data: {
        ...transaction,
        transactionCard: undefined,
        order: undefined,
      },
      userId: this.userContext.userId,
    });
    return result;
  }

  async getTransactionByUserId(userId: string): Promise<ITransaction[]> {
    /*     const data = this.DBLink.createQueryBuilder("transactions")
          .leftJoin("transactions.order", "order")
          .where("order.userId = :userId", { userId })
          .orderBy("transactions.createdAt", "DESC")
          .getMany(); */

    const data = await this.database.findMany({
      include: {
        order: {
          include: {
            address: true,
          },
        },
      },
      orderBy: [
        {
          createdAt: "desc",
        },
      ],
      where: {
        order: {
          userId,
        },
      },
    });
    return data;
  }

  async getTransactionByOrderId(orderId: string): Promise<IListTransactionByOrderDTO[]> {
    /*     const data = this.DBLink.createQueryBuilder("transactions")
          .leftJoin("transactions.order", "order")
          .leftJoinAndSelect("transactions.transactionCard", "transactionCard")
          .leftJoinAndSelect("transactionCard.card", "card")
          .where("order.id = :orderId", { orderId })
          .orderBy("transactions.createdAt", "DESC")
          .getMany(); */

    const data = await this.database.findMany({
      select: {
        id: true,
        paymentMethod: true,
        pixKey: true,
        transactionCard: {
          select: {
            card: true,
          },
        },
      },
      where: {
        orderId,
      },
      orderBy: [
        {
          createdAt: "desc",
        },
      ],
    });

    return data;
  }

  async getLastTransactionByUser(userId: string): Promise<ITransaction | null> {
    /*     const data = await this.DBLink.createQueryBuilder("transactions")
          .leftJoin("transactions.order", "order")
          .leftJoinAndSelect("transactions.transactionCard", "transactionCard")
          .leftJoinAndSelect(
            "transactionCard.card",
            "card",
            "transactionCard.cardId = card.id AND card.saved = true"
          )
          .where("transactions.type = :type AND order.userId = :userId", {
            type: ETransactionType.payment,
            userId,
          })
          .orderBy("transactions.createdAt", "DESC")
          .getOne();
     */
    // NOTE - This query is taking too long
    const data = await this.database.findFirst({
      include: {
        order: {
          include: {
            address: true,
          },
        },
        transactionCard: {
          include: {
            card: true,
          },
          where: {
            card: {
              saved: true,
            },
          },
        },
      },
      where: {
        type: ETransactionType.payment,
        status: ETransactionStatus.approved,
        order: {
          userId,
        },
      },
      orderBy: { createdAt: "desc" },
    });
    return data;
  }

  async getLastTransactionByOrder(orderId: string): Promise<ITransaction | null> {
    /*     const data = await this.DBLink.createQueryBuilder("transactions")
          .leftJoin("transactions.order", "order")
          .where("transactions.type = :type AND order.id = :orderId", {
            type: ETransactionType.payment,
            orderId,
          })
          .orderBy("transactions.createdAt", "DESC")
          .getOne(); */

    const data = await this.database.findFirst({
      include: {
        order: {
          include: {
            address: true,
          },
        },
      },
      where: {
        type: ETransactionType.payment,
        orderId,
      },
      orderBy: [
        {
          createdAt: "desc",
        },
      ],
    });

    return data;
  }

  async isCanceled(orderId: string): Promise<boolean> {
    /*     const data = await this.DBLink.createQueryBuilder("transactions")
          .leftJoin("transactions.order", "order")
          .where(
            "transactions.type = :type AND transactions.status = :status AND order.id = :orderId",
            {
              type: ETransactionType.cancellation,
              status: ETransactionStatus.approved,
              orderId,
            }
          )
          .getOne(); */

    const data = await this.database.findFirst({
      where: {
        type: ETransactionType.cancellation,
        status: ETransactionStatus.approved,
        orderId,
      },
      include: {
        order: true,
      },
    });

    return !!data;
  }

  async getTransactionStatusByOrderId(orderId: string): Promise<ETransactionStatus | null> {
    const data = await this.database.findFirst({
      where: {
        orderId,
      },
      select: {
        status: true,
      },
    });

    return data?.status || null;
  }
}
