import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IChatMessage } from "src/business/Interfaces/Prisma/IChatMessage";
import { IChatMessageClient, IChatMessagesRepository } from "src/business/Interfaces/Repository/IChatMessages";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ChatMessagesRepository
  extends BaseRepository<
    IChatMessage,
    Prisma.ChatMessageFindManyArgs,
    Prisma.ChatMessageFindUniqueOrThrowArgs,
    Prisma.ChatMessageUpdateManyArgs,
    Prisma.ChatMessageDeleteManyArgs,
    Prisma.ChatMessageInclude
  >
  implements IChatMessagesRepository
{
  database: IChatMessageClient;

  constructor() {
    super("chatMessage");
    this.database = this.databaseModel;
  }

  async create(chatMessages: IChatMessage): Promise<IChatMessage> {
    const result = await this.database.$create({
      data: {
        message: chatMessages.message,
        chatId: undefined,
        userId: undefined,
        profileId: undefined,
        chat: {
          connect: {
            id: chatMessages.chatId,
          },
        },
        user: {
          connect: {
            id: chatMessages.userId,
          },
        },
        profile: {
          connect: {
            id: chatMessages.profileId,
          },
        },
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async updateUserFkInChatMessage(updatedId: string, userIds: string[], client?: IChatMessageClient): Promise<number> {
    const database = this.getDatabaseClient<IChatMessageClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
