import { EMessageStatus, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IUserMessage } from "src/business/Interfaces/Prisma/IUserMessage";
import { IUserMessageClient, IUserMessageRepository } from "src/business/Interfaces/Repository/IUserMessage";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class UserMessageRepository
  extends BaseRepository<
    IUserMessage,
    Prisma.UserMessageFindManyArgs,
    Prisma.UserMessageFindUniqueArgs,
    Prisma.UserMessageUpdateManyArgs,
    Prisma.UserMessageDeleteManyArgs,
    Prisma.UserMessageInclude
  >
  implements IUserMessageRepository
{
  database: IUserMessageClient;

  constructor() {
    super("userMessage");
    this.database = this.databaseModel;
  }

  async updateStatus(messageId: string, profileId: string, status: EMessageStatus): Promise<number> {
    const result = await this.database.$updateMany({
      where: {
        messageId,
        profileId,
      },
      data: {
        status,
      },
      userId: this.userContext.userId,
    });

    return result.count;
    // const data = await this.DBLink.createQueryBuilder()
    //   .update("users_messages")
    //   .set({
    //     status,
    //   })
    //   .where("messageId = :id", { id: messageId })
    //   .execute();
    // return data;
  }

  async updateUserFkInUserMessage(updatedId: string, userIds: string[], client?: IUserMessageClient): Promise<number> {
    const database = this.getDatabaseClient<IUserMessageClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
