import { EContentManagementType, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IContentManagement } from "src/business/Interfaces/Prisma/IContentManagement";
import {
  IContentManagementClient,
  IContentManagementRepository,
} from "src/business/Interfaces/Repository/IContentManagement";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ContentManagementRepository
  extends BaseRepository<
    IContentManagement,
    Prisma.ContentManagementFindManyArgs,
    Prisma.ContentManagementFindUniqueOrThrowArgs,
    Prisma.ContentManagementUpdateManyArgs,
    Prisma.ContentManagementDeleteManyArgs,
    Prisma.ContentManagementCreateArgs
  >
  implements IContentManagementRepository
{
  database: IContentManagementClient;

  constructor() {
    super("contentManagement");
    this.database = this.databaseModel;
  }

  async getByType(type: EContentManagementType): Promise<IContentManagement | null> {
    const content = await this.database.findFirst({
      where: {
        type,
      },
    });
    return content;
  }

  async create(contentData: IContentManagement): Promise<IContentManagement | null> {
    const card = await this.database.$create({
      data: {
        ...contentData,
        user: undefined,
      },
      userId: this.userContext.userId,
    });
    return card;
  }
}
