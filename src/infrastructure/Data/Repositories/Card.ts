import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ICard } from "src/business/Interfaces/Prisma/ICard";
import { ICardClient, ICardRepository } from "src/business/Interfaces/Repository/ICard";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class CardRepository
  extends BaseRepository<
    ICard,
    Prisma.CardFindManyArgs,
    Prisma.CardFindUniqueOrThrowArgs,
    Prisma.CardUpdateManyArgs,
    Prisma.CardDeleteManyArgs,
    Prisma.CardInclude
  >
  implements ICardRepository
{
  database: ICardClient;

  constructor() {
    super("card");
    this.database = this.databaseModel;
  }

  async getCardsByUserId(userId: string): Promise<ICard[]> {
    const cards = await this.database.findMany({
      where: {
        AND: {
          userId,
          saved: true,
        },
      },
    });
    return cards;
  }

  async create(cardData: ICard): Promise<ICard | null> {
    const card = await this.database.$create({
      data: {
        ...cardData,
        user: undefined,
        transactionCard: undefined,
      },
      userId: this.userContext.userId,
    });
    return card;
  }

  async updateUserFkInCard(updatedId: string, userIds: string[], client?: ICardClient): Promise<number> {
    const database = this.getDatabaseClient<ICardClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
