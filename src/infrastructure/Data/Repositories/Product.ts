import { EFileType, Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ICreateProductByImportDTO } from "src/business/DTOs/Product/ICreateProduct";
import { IExportedProductData } from "src/business/DTOs/Product/IExportedProductData";
import { IProductByStoreDTO } from "src/business/DTOs/Product/IProductByStore";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { IProductClient, IProductRepository } from "src/business/Interfaces/Repository/IProduct";
import { IProductAttributeRepository } from "src/business/Interfaces/Repository/IProductAttribute";
import { IProductAttributeOptionRepository } from "src/business/Interfaces/Repository/IProductAttributeOption";
import { IProductCategoryRepository } from "src/business/Interfaces/Repository/IProductCategory";
import { IProductModerationRepository } from "src/business/Interfaces/Repository/IProductModerationRepository";
import { IProductSubcategoryRepository } from "src/business/Interfaces/Repository/IProductSubcategory";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ProductRepository
  extends BaseRepository<
    IProduct,
    Prisma.ProductFindManyArgs,
    Prisma.ProductFindUniqueOrThrowArgs,
    Prisma.ProductUpdateManyArgs,
    Prisma.ProductDeleteManyArgs,
    Prisma.ProductInclude
  >
  implements IProductRepository
{
  database: IProductClient;

  constructor(
    @inject(TOKENS.IProductCategoryRepository)
    private productCategoryRepository: IProductCategoryRepository,
    @inject(TOKENS.IProductSubcategoryRepository)
    private productSubcategoryRepository: IProductSubcategoryRepository,
    @inject(TOKENS.IProductAttributeRepository)
    private productAttributeRepository: IProductAttributeRepository,
    @inject(TOKENS.IProductAttributeOptionRepository)
    private productAttributeOptionRepository: IProductAttributeOptionRepository,
    @inject(TOKENS.IFileRepository)
    private fileRepository: IFileRepository,
    @inject(TOKENS.IProductModerationRepository)
    private productModerationRepository: IProductModerationRepository,
  ) {
    super("product");
    this.database = this.databaseModel;
  }

  async create(product: IProduct, attachments?: string[]): Promise<IProduct | null> {
    const result = await this.transaction<IProduct>(async (client) => {
      const productCreated = await client.product.$create({
        data: {
          ...product,
          store: undefined,
          productAttribute: undefined,
          productCategory: product.productCategory
            ? {
                createMany: {
                  data: product.productCategory.map((pc) => ({
                    categoryId: pc.categoryId,
                  })),
                },
              }
            : undefined,
          productSubcategory: product.productSubcategory
            ? {
                createMany: {
                  data: product.productSubcategory.map((ps) => ({
                    subcategoryId: ps.subcategoryId,
                  })),
                },
              }
            : undefined,
          productModeration: undefined,
          orderItem: undefined,
          userFavoriteProducts: undefined,
        },
        userId: this.userContext.userId,
      });

      if (product.productAttribute && product.productAttribute.length > 0) {
        await Promise.all(
          product.productAttribute.map(async (pa) => {
            await this.productAttributeRepository.create(
              productCreated.id,
              pa.attributeId,
              pa.productAttributeOption,
              client.productAttribute,
            );
          }),
        );
      }

      if (attachments && attachments.length > 0) {
        await this.fileRepository.relateEntityFiles(productCreated.id, attachments, client.file);
      }

      return productCreated;
    });

    if (typeof result === "boolean") {
      return null;
    }

    return result;
  }

  async createManyProducts(products: ICreateProductByImportDTO[]): Promise<boolean> {
    const result = products.map(async (product) => {
      const createdProducts = await this.database.$create({
        data: {
          ...product,
          productCategory: {
            createMany: {
              data: product.productCategory ? product.productCategory.map((pc) => ({ categoryId: pc.categoryId })) : [],
            },
          },
          productSubcategory: {
            createMany: {
              data: product.productSubcategory
                ? product.productSubcategory.map((ps) => ({ subcategoryId: ps.subcategoryId }))
                : [],
            },
          },
          productAttribute: {
            createMany: {
              data: product.productAttribute
                ? product.productAttribute.map((pa) => ({ attributeId: pa.attributeId }))
                : [],
            },
          },
          storeId: product.storeId,
        },
      });

      return createdProducts;
    });

    return !!result;
  }

  async getWithAllDetails(productId: string): Promise<IProduct | null> {
    const product = await this.database.findFirst({
      include: {
        productCategory: {
          include: {
            category: true,
          },
        },
        productSubcategory: {
          include: {
            subcategory: true,
          },
        },
        userFavoriteProducts: { where: { userId: this.userContext.userId } },
        productAttribute: {
          include: {
            attribute: {
              include: {
                attributeOption: {
                  where: {
                    productAttributeOption: {
                      some: {
                        productAttribute: {
                          productId,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      where: {
        id: productId,
      },
    });

    // const products = await this.DBLink.createQueryBuilder("product")
    // .where("product.id = :productId", { productId })
    // .leftJoinAndSelect("product.productCategory", "productCategory")
    // .leftJoinAndSelect("productCategory.category", "category")
    // .leftJoinAndSelect("product.productSubcategory", "productSubcategory")
    // .leftJoinAndSelect("productSubcategory.subcategory", "subcategory")
    // .leftJoinAndMapOne("product.icon", "files", "product_file", 'product_file."entityId"= product.id')
    // .leftJoinAndSelect("product.userFavoriteProducts", "favoriteProducts")
    // .leftJoinAndSelect("product.productAttribute", "productAttribute")
    // .leftJoin(
    //   "productAttribute.productAttributeOption",
    //   "productAttributeOption",
    //   '"productAttributeOption"."productAttributeId" = "productAttribute".id',
    // )
    // .leftJoin("productAttributeOption.attributeOption", "prodAttributeOption")
    // .leftJoinAndSelect("productAttribute.attribute", "attribute")
    // .leftJoinAndSelect(
    //   "attribute.attributeOption",
    //   "attributeOption",
    //   '"attributeOption".id = "prodAttributeOption".id',
    // )
    // .getOne();

    return product;
  }

  async getWithStore(productId: string): Promise<IProduct | null> {
    const product = await this.database.findFirst({
      include: {
        store: {
          include: {
            storeUsers: true,
          },
        },
      },
      where: {
        id: productId,
      },
    });
    return product;
  }

  async getWithCategory(id: string): Promise<IProduct | null> {
    const product = await this.database.findFirst({
      include: {
        productCategory: {
          include: {
            category: true,
          },
        },
        productSubcategory: {
          include: {
            subcategory: true,
          },
        },
      },
      where: {
        id,
      },
    });

    if (product) {
      const file = await this.databaseClient.file.findFirst({
        where: { AND: [{ entity: "product" }, { entityId: product.id }] },
      });
      if (file) {
        const newProduct = { ...product, files: [file] };
        return newProduct;
      }
    }

    // const products = await this.DBLink.createQueryBuilder("product")
    // .where("product.id = :id", { id })
    // .leftJoinAndSelect("product.productCategory", "productCategory")
    // .leftJoinAndSelect("productCategory.category", "category")
    // .leftJoinAndSelect("product.productSubcategory", "productSubcategory")
    // .leftJoinAndSelect("productSubcategory.subcategory", "subcategory")
    // .leftJoinAndMapOne("product.icon", "files", "product_file", 'product_file."entityId"= product.id')
    // .getOne();
    return product;
  }

  async getByStore(storeId: string, userId: string): Promise<IProductByStoreDTO[]> {
    const userFavoriteWhere = userId ? { where: { userId } } : true;

    const products = await this.database.findMany({
      where: {
        storeId,
      },
      select: {
        id: true,
        name: true,
        shortDescription: true,
        price: true,
        salePrice: true,
        preparationTime: true,
        storeId: true,
        sku: true,
        active: true,
        activeByAdmin: true,
        productCategory: {
          select: {
            category: {
              select: {
                name: true,
              },
            },
          },
        },
        store: {
          select: {
            name: true,
          },
        },
        userFavoriteProducts: {
          select: {
            userId: true,
            productId: true,
          },
        },
      },
    });

    const productsResult = await Promise.all(
      products.map(async (product) => {
        const files = await this.databaseClient.file.findMany({
          where: { AND: [{ entity: "product" }, { entityId: product.id }] },
          select: {
            url: true,
            type: true,
          },
        });
        const newProduct: IProductByStoreDTO = { ...product, files };
        return newProduct;
      }),
    );

    //   const products = this.DBLink.createQueryBuilder("product")
    //   .where("product.storeId = :id", { id })
    //   .leftJoinAndSelect("product.productCategory", "productCategory")
    //   .leftJoinAndSelect("productCategory.category", "category")
    //   .leftJoinAndSelect("product.moderation", "productModeration")
    //   .orderBy("productModeration.createdAt", "DESC")
    //   .leftJoin(
    //     "product.moderation",
    //     "recentModeration",
    //     '"productModeration"."createdAt" < "recentModeration"."createdAt"',
    //   )
    //   .andWhere("recentModeration.id IS NULL")
    //   .leftJoinAndMapOne("product.icon", "files", "product_file", 'product_file."entityId"= product.id')
    //   .innerJoinAndSelect("product.store", "store")
    //   .leftJoinAndSelect("product.userFavoriteProducts", "favoriteProducts", userId);

    // if (userType === EProfile.client.toString()) {
    //   products.andWhere("product.active='true'").andWhere("product.activeByAdmin='true'");
    // }

    // const result = await products.orderBy("product.name", "ASC").getMany();

    return productsResult;
  }

  async getUserFavoriteProducts(userId: string): Promise<IProduct[]> {
    const products = await this.database.findMany({
      include: {
        userFavoriteProducts: true,
        store: {
          include: {
            reviews: true,
          },
        },
      },
      where: {
        userFavoriteProducts: {
          some: { userId },
        },
      },
    });

    const productsResult = await Promise.all(
      products.map(async (product) => {
        const fileProduct = await this.databaseClient.file.findFirst({
          where: { AND: [{ entity: "product" }, { entityId: product.id }] },
        });
        const filesStore = await this.databaseClient.file.findMany({
          where: { AND: [{ entity: "store" }, { entityId: product.storeId }] },
        });
        const newProduct: IProduct = {
          ...product,
          files: fileProduct ? [fileProduct] : undefined,
          store: { ...product.store, files: filesStore },
        };

        return newProduct;
      }),
    );

    // const products = await this.DBLink.createQueryBuilder("product")
    // .leftJoinAndSelect("product.userFavoriteProducts", "userFavoriteProducts")
    // .leftJoinAndSelect("product.store", "store")
    // .leftJoinAndMapOne("product.icon", "files", "product_file", 'product_file."entityId"= product.id')
    // .leftJoinAndMapMany("store.files", "files", "store_file", 'store_file."entityId"= store.id')
    // .where("userFavoriteProducts.userId =:userId", { userId })
    // .getMany();

    return productsResult;
  }

  async getPagedProductList(
    productFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
  ): Promise<PagedResult<IProduct>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const where = {
      name: productFilter
        ? {
            contains: productFilter,
          }
        : undefined,
    };

    const products = await this.database.findMany({
      include: {
        productCategory: {
          include: {
            category: true,
          },
        },
        productSubcategory: {
          include: {
            subcategory: true,
          },
        },
        userFavoriteProducts: true,
      },
      where,
      orderBy: favoriteFilter ? [{ userFavoriteProducts: { _count: "desc" } }, { name: "asc" }] : { name: "asc" },
      skip: skipIndex,
      take: pageSize,
    });

    const productsResult = await Promise.all(
      products.map(async (product) => {
        const files = await this.databaseClient.file.findMany({
          where: { AND: [{ entity: "product" }, { entityId: product.id }] },
        });
        const newProduct: IProduct = { ...product, files };
        return newProduct;
      }),
    );

    // const data = this.DBLink.createQueryBuilder("products")
    //   .leftJoinAndSelect("products.productCategory", "productCategory")
    //   .leftJoinAndSelect("productCategory.category", "category")
    //   .leftJoinAndSelect("products.productSubcategory", "subcategory")
    //   .leftJoinAndMapOne("products.icon", "files", "products_file", 'products_file."entityId"= products.id')
    //   .leftJoinAndSelect("products.userFavoriteProducts", "favoriteProducts", userId)
    //   .where(productFilter)
    //   .groupBy("products.id")
    //   .addGroupBy("favoriteProducts.id")
    //   .addGroupBy("products.name")
    //   .addGroupBy("productCategory.id")
    //   .addGroupBy("category.id")
    //   .addGroupBy("subcategory.id")
    //   .addGroupBy("products_file.id")
    //   .orderBy("products.name", "ASC");

    // const dataFavoriteFiltered = await data
    //   .orderBy("favoriteProducts.id", "ASC")
    //   .addOrderBy("products.name", "ASC")
    //   .take(pageSize)
    //   .skip(skipIndex)
    //   .getMany();

    // const dataNameFiltered = await data.orderBy("products.name", "ASC").take(pageSize).skip(skipIndex).getMany();

    const totalCount = await this.database.count({
      where,
    });
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: productsResult,
      totalCount,
      totalPages,
    };
  }

  async getWithAllDetailsByStore(storeId: string): Promise<IExportedProductData[] | null> {
    // FIXME - Verificar substituir o Prisma.raw por Prisma.sql
    const products = await this.databaseClient.$queryRawUnsafe<IExportedProductData[]>(
      `
    SELECT DISTINCT
      p.name, p."shortDescription" short_description, p.description, p.price,
      p."salePrice" sale_price, p.sku, p."createdAt", p."updatedAt", p.active,
      p."preparationTime" preparation_time, c.name category_name,
      c.description category_description, sc.name category_subcategory_name,
      sc.description category_subcategory_description, a.name attribute_name,
      a."shortDescription" attribute_shortdescription, a.required attribute_required,
      a.type attribute_type, ao.value attribute_option_value
    FROM
	    products p
      LEFT JOIN products_categories pc ON	p.id = pc."productId"
      LEFT JOIN products_subcategories ps ON ps."productId" = p.id 
      LEFT JOIN categories c ON pc."categoryId" =c.id 
      LEFT JOIN (
        SELECT s.*, cs."categoryId"
        FROM categories_subcategories cs 
	        LEFT JOIN subcategories s ON (cs."subcategoryId" = s.id)
      ) sc ON (c.id = sc."categoryId" AND ps."subcategoryId"= sc.id)
      LEFT JOIN products_attributes pa ON p.id = pa."productId" 
      LEFT JOIN "attributes" a ON pa."attributeId" = a.id
      LEFT JOIN attributes_options ao ON a.id = ao."attributeId" 
      LEFT JOIN products_attributes_options pao ON (ao.id = pao."attributeOptionId" 
        AND pa.id = pao."productAttributeId")
      WHERE
      p."storeId" = CAST($1 AS UUID)
    ORDER BY p.name, p."shortDescription", p.description, p.price, p."salePrice", p.sku,
      c.name, sc.name, a.name, ao.value
    `,
      storeId,
    );

    console.log("products here", products);
    return products;
  }

  async deleteByStore(storeId: string, client?: IProductClient): Promise<number> {
    const database = this.getDatabaseClient<IProductClient>(client);

    const result = await database.$deleteMany({
      where: { storeId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async getProductSuggestions(userId: string): Promise<IProduct[]> {
    console.log("🚀 ~ file: Product.ts:385 ~ getProductSuggestions ~ userId:", userId);
    const sql = Prisma.raw(`
    with last_purchased_products as (
      select
        distinct cp."productId"
      from
        customers_profile cp
        join (
          select
            cp3."orderId"
          from
            customers_profile cp3
          where
            cp3."userId" = '${userId}'
          group by
            cp3."orderTime",
            cp3."orderId"
          order by
            cp3."orderTime" desc
          limit
            5
        ) t3 on t3."orderId" = cp."orderId"
    ),
    customer_profile_products_suggestion as (
      select
        t."productId",
        sum(t."productQuantity") as "salesCount",
        3 as "weight"
      from
        (
          select
            distinct cp."orderItemId",
            cp."productId",
            cp."productQuantity"
          from
            customers_profile cp
            join (
              select
                distinct cp2."categoryId"
              from
                customers_profile cp2
                join (
                  select
                    cp3."orderId"
                  from
                    customers_profile cp3
                  where
                    cp3."userId" = '${userId}'
                  group by
                    cp3."orderTime",
                    cp3."orderId"
                  order by
                    cp3."orderTime" desc
                  limit
                    5
                ) t3 on t3."orderId" = cp2."orderId"
            ) t2 on t2."categoryId" = cp."categoryId"
        ) t
      where
        t."productId" not in (
          select
            *
          from
            last_purchased_products
        )
      group by
        t."productId"
      order by
        "salesCount" desc
      limit
        5
    ), customer_favorites_products_suggestion as (
      select
        t."productId",
        sum(t."productQuantity") as "salesCount",
        2 as "weight"
      from
        (
          select
            distinct cp2."orderItemId",
            cp2."productId",
            cp2."productQuantity"
          from
            customers_profile cp2
            join (
              select
                distinct cp3."categoryId"
              from
                users_favorites_products ufp
                join customers_profile cp3 on cp3."productId" = ufp."productId"
              where
                ufp."userId" = '${userId}'
            ) t2 on t2."categoryId" = cp2."categoryId"
        ) t
      where
        t."productId" not in (
          select
            *
          from
            last_purchased_products
        )
      group by
        t."productId"
      order by
        "salesCount" desc
      limit
        5
    ), products_suggestion as (
      select
        t."productId",
        sum(t."productQuantity") as "salesCount",
        1 as "weight"
      from
        (
          select
            distinct cp2."orderItemId",
            cp2."productId",
            cp2."productQuantity"
          from
            customers_profile cp2
        ) t
      where
        t."productId" not in (
          select
            *
          from
            last_purchased_products
        )
      group by
        t."productId"
      order by
        "salesCount" desc
      limit
        5
    )
    select
      p.*,
      s."name" as "storeName",
      pd.weight,
      pd."salesCount",
      pd."iconUrl"
    from
      products p
      join (
        select
          distinct pid."productId",
          max(pid."salesCount") as "salesCount",
          max(pid.weight) as "weight",
          max(f.url) as "iconUrl"
        from
          (
            (
              select
                *
              from
                customer_profile_products_suggestion
            )
            union
            all (
              select
                *
              from
                customer_favorites_products_suggestion
            )
            union
            all (
              select
                *
              from
                products_suggestion
            )
          ) pid
          left join files f on f."entityId" = pid."productId"
          and f."type" = '${EFileType.photo}'
        group by
          pid."productId",
          pid."salesCount"
      ) pd on pd."productId" = p.id
      left join stores s on s.id = p."storeId"
    order by
      pd.weight desc,
      pd."salesCount" desc,
      pd."iconUrl" desc
    `);

    const products: any[] = await this.databaseClient.$queryRaw(sql);

    const result = products.map((product) => {
      const { iconUrl, storeName } = product;
      delete product.iconUrl;
      delete product.weight;
      delete product.salesCount;
      delete product.storeName;

      return {
        ...product,
        price: parseFloat(product.price),
        sale_price: parseFloat(product.sale_price),
        store: {
          name: storeName,
        },
        icon: {
          url: iconUrl,
        },
      } as IProduct;
    });

    return result;
  }

  async getPagedListFront(
    currentPage: number,
    pageSize: number,
    storeId?: string,
  ): Promise<{ result: IProduct[]; totalCount: number; totalPages: number }> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    // const data = this.DBLink.createQueryBuilder("products");
    // if (storeId) {
    //   data.where("products.storeId = :id", { id: storeId });
    // }
    // data.groupBy("products.id").addGroupBy("products.name");

    // const result = await data.take(pageSize).skip(skipIndex).getMany();
    const where = storeId
      ? {
          storeId,
        }
      : undefined;

    const result: IProduct[] = await this.database.findMany({
      where,
      orderBy: [{ storeId: "asc" }, { name: "asc" }],
      skip: skipIndex,
      take: pageSize,
    });

    const totalCount = await this.database.count({ where });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };
  }

  async getWithAllDetailsFront(productId: string): Promise<IProduct | null> {
    const product = await this.database.findUnique({
      include: {
        store: true,
        productCategory: { include: { category: true } },
        productSubcategory: { include: { subcategory: true } },
        productModeration: { orderBy: { createdAt: "desc" } },
        productAttribute: {
          include: {
            attribute: {
              include: {
                attributeOption: {
                  where: {
                    productAttributeOption: {
                      some: {
                        productAttribute: {
                          productId,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      where: { id: productId },
    });

    if (product) {
      const file = await this.databaseClient.file.findMany({
        where: { AND: [{ entity: "product" }, { entityId: product.id }] },
      });
      if (file) {
        const newProduct = { ...product, files: file };
        return newProduct;
      }
    }
    // const products = await this.DBLink.createQueryBuilder("product")
    //   .where("product.id = :id", { id: productId })
    //   .innerJoinAndSelect("product.store", "store")
    //   .leftJoinAndSelect("product.productCategory", "productCategory")
    //   .leftJoinAndSelect("productCategory.category", "category")
    //   .leftJoinAndSelect("product.productSubcategory", "productSubcategory")
    //   .leftJoinAndSelect("productSubcategory.subcategory", "subcategory")
    //   .leftJoinAndSelect("product.moderation", "productModeration")
    //   .orderBy("productModeration.createdAt", "DESC")
    //   .leftJoin(
    //     "product.moderation",
    //     "recentModeration",
    //     '"productModeration"."createdAt" < "recentModeration"."createdAt"',
    //   )
    //   .andWhere("recentModeration.id IS NULL")
    //   .leftJoinAndMapMany(
    //     "product.files",
    //     "files",
    //     "product_file",
    //     'product_file."entityId"= product.id AND product_file."entity"= :entity',
    //     { entity: EFile.product },
    //   )
    //   .leftJoinAndSelect("product.productAttribute", "productAttribute")
    //   .leftJoin(
    //     "productAttribute.productAttributeOption",
    //     "productAttributeOption",
    //     '"productAttributeOption"."productAttributeId" = "productAttribute".id',
    //   )
    //   .leftJoin("productAttributeOption.attributeOption", "prodAttributeOption")
    //   .leftJoinAndSelect("productAttribute.attribute", "attribute")
    //   .leftJoinAndSelect(
    //     "attribute.attributeOption",
    //     "attributeOption",
    //     '"attributeOption".id = "prodAttributeOption".id',
    //   )

    //   .groupBy("product.id")
    //   .addGroupBy("product.name")
    //   .addGroupBy("productCategory.id")
    //   .addGroupBy("category.id")
    //   .addGroupBy("productSubcategory.id")
    //   .addGroupBy("subcategory.id")
    //   .addGroupBy("productModeration.id")
    //   .addGroupBy("product_file.id")
    //   .addGroupBy("productAttribute.id")
    //   .addGroupBy("attribute.id")
    //   .addGroupBy("attributeOption.id")
    //   .addGroupBy("store.id")

    //   .getOne();

    return product;
  }

  async deleteWithRelations(id: string): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.productSubcategoryRepository.deleteByProductId(id, client.productSubcategory);
      await this.productCategoryRepository.deleteByProductId(id, client.productCategory);

      const productAttribute = await this.productAttributeRepository.getByProductId(id);

      Promise.all(
        productAttribute?.map(async (item) => {
          await this.productAttributeOptionRepository.deleteByProductAttributeId(
            item.id,
            client.productAttributeOption,
          );
        }),
      );

      await this.productAttributeRepository.deleteByProductId(id, client.productAttribute);

      await this.delete(id, client.product);

      return true;
    });

    return result;
  }

  async updateWithRelations(data: IProduct): Promise<boolean> {
    const productId = data.id;

    let productAttributes: IProductAttribute[];
    if (data.productAttribute && data.productAttribute.length > 0) {
      productAttributes = await this.productAttributeRepository.getByProductId(productId);
    }

    const result = await this.transaction<boolean>(async (client) => {
      if (data.productCategory && data.productCategory.length > 0) {
        await this.productCategoryRepository.deleteByProductId(productId, client.productCategory);

        const productCategories = data.productCategory.map(
          (item) =>
            ({
              productId,
              categoryId: item.categoryId,
            } as IProductCategory),
        );

        await this.productCategoryRepository.createMany(productCategories, client.productCategory);
      }

      if (data.productSubcategory && data.productSubcategory.length > 0) {
        const productSubcategories = data.productSubcategory.map(
          (item) =>
            ({
              productId,
              subcategoryId: item.subcategoryId,
            } as IProductSubcategory),
        );
        await this.productSubcategoryRepository.deleteByProductId(productId, client.productSubcategory);
        await this.productSubcategoryRepository.createMany(productSubcategories, client.productSubcategory);
      }

      data.productCategory = undefined;
      data.productSubcategory = undefined;

      if (productAttributes && data.productAttribute) {
        Promise.all(
          productAttributes?.map(async (item) => {
            await this.productAttributeOptionRepository.deleteByProductAttributeId(
              item.id,
              client.productAttributeOption,
            );
          }),
        );

        await this.productAttributeRepository.deleteByProductId(productId, client.productAttribute);
        await this.productAttributeRepository.createMany(productId, data.productAttribute);
      }

      data.productAttribute = undefined;

      await this.update(data.id, data, client.product);

      return true;
    });

    return result;
  }

  async updateCategoryAndSubcategory(
    id: string,
    productCategories: IProductCategory[],
    productSubcategories: IProductSubcategory[],
  ): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.productSubcategoryRepository.deleteByProductId(id, client.productSubcategory);
      await this.productCategoryRepository.deleteByProductId(id, client.productCategory);
      await this.productSubcategoryRepository.createMany(productSubcategories, client.productSubcategory);
      await this.productCategoryRepository.createMany(productCategories, client.productCategory);

      return true;
    });

    return result;
  }

  async updateProductAttributes(
    id: string,
    productAttributes: IProductAttribute[],
    currentProductAttributes: IProductAttribute[],
    newAttributeOption: IProductAttributeOption[],
  ): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      Promise.all(
        currentProductAttributes?.map(async (item) => {
          if (item.productAttributeOption) {
            await this.productAttributeOptionRepository.inactiveProductAttributeOptById(
              item.productAttributeOption?.filter((f) => f.active === false)?.map((opt) => opt.id),
              client.productAttributeOption,
            );
            await this.productAttributeOptionRepository.activeProductAttributeOptById(
              item.productAttributeOption?.filter((f) => f.active === true)?.map((opt) => opt.id),
              client.productAttributeOption,
            );
          }
        }),
      );

      await this.productAttributeRepository.inactiveProductAttributeById(
        currentProductAttributes.filter((f) => f.active === false).map((prodAtt) => prodAtt.id),
        client.productAttribute,
      );

      await this.productAttributeRepository.activeProductAttributeById(
        currentProductAttributes.filter((f) => f.active === true).map((prodAtt) => prodAtt.id),
        client.productAttribute,
      );

      await Promise.all(
        productAttributes?.map(async (item) => {
          const result = await this.productAttributeRepository.create(
            id,
            item.attributeId,
            item.productAttributeOption,
            client.productAttribute,
          );
          return result;
        }),
      );

      await Promise.all(
        newAttributeOption?.map(async (item) => {
          const result = await this.productAttributeOptionRepository.create(
            item.productAttributeId,
            item.attributeOptionId,
          );
          return result;
        }),
      );
      return true;
    });

    return result;
  }

  async saveProductModeration(
    id: string,
    product: IProduct,
    productModeration: ICreateProductModerationDTO,
  ): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.update(id, product, client.product);
      await this.productModerationRepository.create(productModeration, client.productModeration);

      return true;
    });

    return result;
  }

  async updateProductCategories(id: string, productCategories: IProductCategory[]): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.productCategoryRepository.deleteByProductId(id, client.productCategory);
      await this.productCategoryRepository.createMany(productCategories, client.productCategory);

      return true;
    });

    return result;
  }

  async updateProductSubcategories(id: string, productSubcategories: IProductSubcategory[]): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.productSubcategoryRepository.deleteByProductId(id, client.productSubcategory);
      await this.productSubcategoryRepository.createMany(productSubcategories, client.productSubcategory);

      return true;
    });

    return result;
  }

  async deleteByStoreId(id: string): Promise<boolean> {
    // NOTE Files should be deleted too
    const result = await this.transaction<boolean>(async (client) => {
      await this.productCategoryRepository.deleteByStore(id, client.productCategory);
      await this.productSubcategoryRepository.deleteByStore(id, client.productSubcategory);
      await this.productAttributeOptionRepository.deleteByStore(id, client.productAttributeOption);
      await this.productAttributeRepository.deleteByStore(id, client.productAttribute);
      await this.deleteByStore(id, client.product);

      return true;
    });

    return result;
  }
}
