import { EDayOfWeek, EFile, EFileType, EStoreModeratorStatus, Prisma } from "@prisma/client";
import { addHours } from "date-fns";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ExportStoreDTO } from "src/business/DTOs/Store/ExportStore";
import { ICreateStoreByExportDTO } from "src/business/DTOs/Store/ICreateStore";
import { IListStoresDTO } from "src/business/DTOs/Store/IListStores";
import { IListUserStoreDTO } from "src/business/DTOs/Store/IListUserStore";
import { IStoreNameDTO } from "src/business/DTOs/Store/IStoreName";
import { IStoreShowcaseDTO } from "src/business/DTOs/Store/IStoreShowcase";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IStoreClient, IStoreRepository } from "src/business/Interfaces/Repository/IStore";
import { IStoreCategoryRepository } from "src/business/Interfaces/Repository/IStoreCategory";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class StoreRepository
  extends BaseRepository<
    IStore,
    Prisma.StoreFindManyArgs,
    Prisma.StoreFindUniqueOrThrowArgs,
    Prisma.StoreUpdateManyArgs,
    Prisma.StoreDeleteManyArgs,
    Prisma.StoreInclude
  >
  implements IStoreRepository
{
  database: IStoreClient;

  constructor(
    @inject(TOKENS.IStoreCategoryRepository)
    private storeCategoryRepository: IStoreCategoryRepository,
  ) {
    super("store");
    this.database = this.databaseModel;
  }

  async relateStoreUser(storeId: string, userId: string): Promise<boolean> {
    const result = await this.databaseClient.storeUser.$create({
      data: {
        storeId,
        userId,
      },
      userId: this.userContext.userId,
    });

    if (result) return true;

    return false;
  }

  async create(store: IStore): Promise<IStore | null> {
    const address =
      store.address && Object.keys(store.address).length > 0
        ? {
            create: {
              ...store.address,
              userAddress: undefined,
            },
          }
        : undefined;

    const storeUsers = store.storeUsers
      ? {
          createMany: {
            data: store.storeUsers.map((su) => ({
              userId: su.userId,
              owner: su.owner,
              status: su.status,
              user: undefined,
            })),
          },
        }
      : undefined;

    // store.storeHours?.map((sh) => {
    //   sh.open = parse(sh.open.toString(), "HH:mm", new Date());
    //   sh.close = parse(sh.close.toString(), "HH:mm", new Date());
    // });

    const storeHours = store.storeHours
      ? {
          createMany: {
            data: store.storeHours,
          },
        }
      : undefined;

    const storeSettings = store.storeSettings
      ? {
          create: { ...store.storeSettings },
        }
      : undefined;

    const storeCategory =
      store.storeCategory && store.storeCategory.length > 0
        ? {
            createMany: {
              data: store.storeCategory.map((sc) => ({
                categoryId: sc.categoryId,
              })),
            },
          }
        : undefined;

    const result = await this.databaseClient.store.$create({
      data: {
        ...store,
        addressId: undefined,
        order: undefined,
        products: undefined,
        reviews: undefined,
        storeModeration: undefined,
        userFavoriteStores: undefined,
        storeSettings,
        address,
        storeUsers,
        storeHours,
        storeCategory,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getPagedListWithFavoriteStores(
    currentDay: EDayOfWeek,
    currentTime: Date,
    categoryIdFilter: string,
    storeFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
    latitude: number,
    longitude: number,
  ): Promise<PagedResult<IListStoresDTO>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    let whereCondition: string = "where s.active = true";
    let orderBy: string = 'opened desc, s."name" asc';

    if (latitude && longitude) {
      whereCondition = whereCondition.concat(
        ` and (6371 * acos(cos(radians(${latitude})) * cos(radians(a.latitude))
          * cos(radians(${longitude}) - radians(a.longitude))
          + sin(radians(${latitude})) * sin(radians(a.latitude)))) <= sst."deliveryRange"
         `,
      );
    }

    if (storeFilter) {
      whereCondition = whereCondition.concat(
        `
         and (
          lower(s.name) like lower('%${storeFilter}%')
          or lower(p.name) like lower('%${storeFilter}%')
        ) 
        `,
      );
    }

    if (categoryIdFilter) {
      whereCondition = whereCondition.concat(`
       and (
        sc."categoryId" = '${categoryIdFilter}'
        )
      `);
    }

    if (favoriteFilter) {
      orderBy = 'opened desc, ufs.id asc, s."name" asc';
    }

    const stores = await this.databaseClient.$queryRaw<IListStoresDTO[]>(
      Prisma.raw(`
        select
          distinct 
          s.id, 
          s.name,
          ufs.id as "userFavoriteStoreId",
          f.url as "fileUrl",
          a.latitude as latitude,
          a.longitude as longitude,
          round(avg(r.rate), 1) as review,
          case
            when sh.id is null then false
            else true
          end as "opened"
        from
          stores s
          left join users_favorites_stores ufs on ufs."storeId" = s.id
          and ufs."userId" = '${this.userContext.userId}'
          left join addresses a on s."addressId" = a.id
          left join reviews r on r."storeId" = s.id
          left join store_settings sst on sst."storeId" = s.id
          left join (
            select distinct on (fsb."entityId") fsb.* from files fsb
              where fsb."entity" = 'store' and fsb."type" = '${EFileType.icon}'
              order by fsb."entityId", fsb."createdAt" desc
          ) f on f."entityId" = s.id
          left join products p on p."storeId" = s.id
          left join stores_categories sc on sc."storeId" = s.id
          left join store_hours sh on (
            s.id = sh."storeId"
            and sh."dayOfWeek" = '${currentDay}'
            and '${currentTime}' between sh.open and sh.close
            and s.open = true
          )
        ${whereCondition}
        group by
          s.id, 
          sst.*,
          ufs.id,
          f.url,
          sh.id,
          a.latitude,
          a.longitude
        order by
          ${orderBy}
        limit
          ${pageSize} offset ${skipIndex}
      `),
    );

    const count = await this.databaseClient.$queryRaw<{ value: number }[]>(
      Prisma.raw(`
        select count(distinct s.id)::int as value from stores s
        left join addresses a on
          s."addressId" = a.id
        left join store_settings sst on
          sst."storeId" = s.id
        left join stores_categories sc on
          sc."storeId" = s.id
        left join categories c on
          sc."categoryId" = c.id
        left join products p on
          p."storeId" = s.id
        ${whereCondition}
      `),
    );

    const totalCount = count[0].value;
    const totalPages = Math.ceil(totalCount / pageSize);

    return { result: stores, totalCount, totalPages };

    //   const calcRangeSB = this.DBLink.createQueryBuilder("stores")
    //   .select("stores.id", "store_id")
    //   .leftJoin("stores.address", "address")
    //   .addSelect(
    //     `(6371 * acos(cos(radians(${latitude})) * cos(radians(address.latitude)) * cos(radians(${longitude}) - radians(address.longitude)) + sin(radians(${latitude})) * sin(radians(address.latitude))))`,
    //     "distance"
    //   );

    // const storesByRange = this.DBLink.createQueryBuilder("stores")
    //   .select("stores.id", "store_id")
    //   .leftJoin(
    //     `(${calcRangeSB.getQuery()})`,
    //     "range",
    //     "range.store_id = stores.id"
    //   )
    //   .leftJoin("stores.storeSettings", "settings")
    //   .where("range.distance <= settings.deliveryRange");

    // const productSB = dataSource
    //   .getRepository(Product)
    //   .createQueryBuilder("product")
    //   .select("product.storeId", "prod_store_id")
    //   .leftJoinAndSelect("product.store", "store")
    //   .where(
    //     storeFilter
    //       ? `LOWER(product.name) LIKE LOWER('%${storeFilter}%')`
    //       : "product.name IS NOT NULL"
    //   );

    // const data = this.DBLink.createQueryBuilder("stores")
    //   .leftJoinAndSelect("stores.storeSettings", "settings")
    //   .leftJoinAndSelect("stores.storeCategory", "SC")
    //   .leftJoinAndSelect("SC.category", "category")
    //   .leftJoinAndSelect("stores.address", "address")
    //   .leftJoinAndSelect("stores.userFavoriteStores", "favoriteStores", userId)

    //   .leftJoinAndSelect("stores.moderation", "storeModeration")
    //   .orderBy("storeModeration.createdAt", "DESC")
    //   .leftJoin(
    //     "stores.moderation",
    //     "recentModeration",
    //     '"storeModeration"."createdAt" < "recentModeration"."createdAt"'
    //   )
    //   .where("recentModeration.id IS NULL")

    //   .leftJoinAndMapMany(
    //     "stores.files",
    //     "files",
    //     "store_file",
    //     'store_file."entityId"= stores.id'
    //   )
    //   .leftJoin(
    //     `(${productSB.getQuery()})`,
    //     "product_query",
    //     "product_query.prod_store_id = stores.id"
    //   );
    // if (latitude) {
    //   data.innerJoin(
    //     `(${storesByRange.getQuery()})`,
    //     "stores_in_range",
    //     "stores_in_range.store_id = stores.id"
    //   );
    // }

    // data
    //   .where(
    //     storeFilter
    //       ? `LOWER(stores.name) LIKE LOWER('%${storeFilter}%') OR product_query.prod_store_id = stores.id`
    //       : "stores.name IS NOT NULL"
    //   )
    //   .andWhere(categoryFilter)
    //   .andWhere("stores.active='0'")
    //   .andWhere("stores.activeByAdmin='0'")
    //   .groupBy("stores.id")
    //   .addGroupBy("favoriteStores.id")
    //   .addGroupBy("stores.name")
    //   .addGroupBy("SC.id")
    //   .addGroupBy("category.id")
    //   .addGroupBy("address.id")
    //   .addGroupBy("settings.id")
    //   .addGroupBy("storeModeration.id")
    //   .addGroupBy("store_file.id");

    // if (favoriteFilter) {
    //   data.addOrderBy("favoriteStores.id", "ASC");
    // } else {
    //   data.addOrderBy("stores.name", "ASC");
    // }

    // const totalCount = await data.getCount();

    // const result = await data
    //   .addOrderBy("stores.name", "ASC")
    //   .take(pageSize)
    //   .skip(skipIndex)
    //   .getMany();
  }

  async getStoreInfoById(storeId: string): Promise<IStore | null> {
    const result: IStore | null = await this.database.findUnique({
      include: {
        storeCategory: { include: { category: true } },
        address: true,
        storeHours: true,
        storeSettings: true,
        storeUsers: true,
      },
      where: { id: storeId },
    });

    // const result = await this.DBLink.createQueryBuilder("store")
    //   .leftJoinAndSelect("store.storeCategory", "SC")
    //   .leftJoinAndSelect("SC.category", "category")
    //   .leftJoinAndSelect("store.address", "address")
    //   .leftJoinAndSelect("store.storeHours", "storeHours")
    //   .leftJoinAndSelect("store.storeSettings", "storeSettings")
    //   .leftJoinAndMapMany(
    //     "store.files",
    //     "files",
    //     "store_file",
    //     'store_file."entityId"= store.id'
    //   )
    //   .where("store.id = :storeId", { storeId })
    //   .getOne();

    return result;
  }

  async getByCnpj(cnpj: string): Promise<IStore | null> {
    const result = await this.database.findUnique({ where: { cnpj } });

    return result;
  }

  async getBySlug(slug: string): Promise<IStore[]> {
    const result = await this.database.findMany({ where: { slug } });

    return result;
  }

  async getWithUser(id: string): Promise<IStore | null> {
    const result = await this.database.findUnique({
      where: { id },
      include: {
        storeUsers: {
          include: {
            user: true,
          },
        },
      },
    });

    return result;
  }

  async getWithCategory(id: string): Promise<IStore | null> {
    const result = await this.database.findUnique({
      where: { id },
      include: {
        storeCategory: {
          include: {
            category: true,
          },
        },
      },
    });

    return result;
  }

  async getWithAddress(id: string): Promise<IStore | null> {
    const result = await this.database.findUnique({
      where: { id },
      include: {
        address: true,
      },
    });

    return result;
    // [?] Does storeAddress exists?
    // const store = this.DBLink.createQueryBuilder("store")
    //   .where("store.id = :idStore", { idStore })
    //   .innerJoinAndSelect("store.storeAddresses", "storeAddresses")
    //   .innerJoinAndSelect("storeAddresses.address", "address")
    //   .getOne();

    // return store;
  }

  async getStoresByUserId(userId: string): Promise<IListUserStoreDTO[]> {
    const stores = await this.database.findMany({
      select: {
        id: true,
        name: true,
        phone: true,
        pixKey: true,
        cnpj: true,
        email: true,
        active: true,
        activeByAdmin: true,
        open: true,
        storeHours: true,
        storeUsers: {
          where: { userId },
          select: {
            id: true,
            owner: true,
            status: true,
          },
        },
        address: {
          select: {
            id: true,
          },
        },
      },

      where: { storeUsers: { some: { userId } } },
    });

    const storesResult = await Promise.all(
      stores.map(async (store) => {
        const file = await this.databaseClient.file.findFirst({
          select: {
            url: true,
          },
          where: { AND: [{ entity: "store", type: EFileType.icon }, { entityId: store.id }] },
        });

        const newStore: IListUserStoreDTO = { ...store, iconUrl: file?.url };

        return newStore;
      }),
    );

    return storesResult;
    // const stores = await this.DBLink.createQueryBuilder("store")
    //   .leftJoinAndSelect("store.storeUsers", "storeUsers")
    //   .where("storeUsers.userId = :id", { id: userId })
    //   .leftJoinAndSelect("store.address", "storeAddresses")
    //   .leftJoinAndSelect("store.storeCategory", "storeCategory")
    //   .leftJoinAndSelect("storeCategory.category", "category")

    //   .leftJoinAndSelect("store.moderation", "storeModeration")
    //   .orderBy("storeModeration.createdAt", "DESC")
    //   .leftJoin(
    //     "store.moderation",
    //     "recentModeration",
    //     '"storeModeration"."createdAt" < "recentModeration"."createdAt"'
    //   )
    //   .where("recentModeration.id IS NULL")

    //   .leftJoinAndMapMany(
    //     "store.files",
    //     "files",
    //     "store_file",
    //     'store_file."entityId"= store.id'
    //   )
    //   .getMany();
  }

  async getStoresNameByUserId(userId: string): Promise<IStoreNameDTO[]> {
    const stores: IStoreNameDTO[] = await this.database.findMany({
      select: {
        id: true,
        name: true,
      },
      where: {
        storeUsers: {
          some: {
            userId,
            status: {
              equals: EStoreModeratorStatus.active,
            },
          },
        },
      },
    });

    return stores;
  }

  async getPagedListFront(currentPage: number, pageSize: number): Promise<PagedResult<IStore>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const result = await this.database.findMany({
      take: pageSize,
      skip: skipIndex,
    });

    const totalCount = await this.database.count();

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };

    // const data = this.DBLink.createQueryBuilder("allStores")

    //   .groupBy("allStores.id")
    //   .addGroupBy("allStores.name");

    // const totalCount = await data.getCount();

    // const result = await data.take(pageSize).skip(skipIndex).getMany();

    // const totalPages = Math.ceil(totalCount / pageSize);

    // return {
    //   result,
    //   totalCount,
    //   totalPages,
    // };
  }

  async getWithAllDetailsFront(storeId: string): Promise<IStore | null> {
    const store = await this.database.findUnique({
      where: { id: storeId },
      include: {
        storeUsers: true,
        address: true,
        storeCategory: { include: { category: true } },
        storeModeration: {
          where: { NOT: { reason: null } },
          orderBy: { createdAt: "desc" },
          take: 1,
        },
        storeSettings: true,
      },
    });

    if (store) {
      const files = await this.databaseClient.file.findMany({
        where: { AND: [{ entity: EFile.store }, { entityId: store.id }] },
      });
      const newStore = { ...store, files };
      return newStore;
    }

    return store;

    // const store = await this.DBLink.createQueryBuilder("store")
    //   .where("store.id = :id", { id: storeId })
    //   .leftJoinAndSelect("store.address", "storeAddress")
    //   .leftJoinAndSelect("store.storeCategory", "storeCategory")
    //   .leftJoinAndSelect("storeCategory.category", "category")
    //   .leftJoinAndSelect("store.storeSettings", "storeSettings")
    //   .leftJoinAndSelect("store.moderation", "storeModeration")
    //   .orderBy("storeModeration.createdAt", "DESC")
    //   .leftJoin(
    //     "store.moderation",
    //     "recentModeration",
    //     '"storeModeration"."createdAt" < "recentModeration"."createdAt"',
    //   )
    //   .andWhere("recentModeration.id IS NULL")
    //   .leftJoinAndMapMany(
    //     "store.files",
    //     "files",
    //     "store_file",
    //     'store_file."entityId"= store.id AND store_file."entity"= :entity',
    //     { entity: EFile.store },
    //   )

    //   .groupBy("store.id")
    //   .addGroupBy("store.name")
    //   .addGroupBy("storeCategory.id")
    //   .addGroupBy("category.id")
    //   .addGroupBy("storeAddress.id")
    //   .addGroupBy("storeSettings.id")
    //   .addGroupBy("storeModeration.id")
    //   .addGroupBy("store_file.id")

    //   .getOne();

    // return store;
  }

  // async getWithFavoriteStore(): Promise<IStore[]> {
  //   const stores = await this.DBLink.find({
  //     relations: { userFavoriteStores: true },
  //   });

  //   return stores;
  // }

  async checkAvailabilityByStoreId(storeId: string): Promise<IStore | null> {
    const currentDate = addHours(new Date(), -3);
    const currentDay = currentDate.toLocaleString("en", { weekday: "long" }).toLowerCase() as EDayOfWeek;

    const result = await this.database.findFirst({
      where: {
        id: storeId,
        storeHours: {
          some: {
            dayOfWeek: currentDay,
            open: { lte: currentDate },
            close: { gte: currentDate },
          },
        },
      },
      include: {
        storeSettings: true,
        storeHours: {
          where: {
            dayOfWeek: currentDay,
            open: { lte: currentDate },
            close: { gte: currentDate },
          },
        },
      },
    });

    return result;
    // const result = await this.DBLink.createQueryBuilder("store")
    //   .leftJoinAndSelect("store.storeSettings", "storeSettings")
    //   .leftJoinAndSelect("store.storeHours", "storeHours")
    //   .where("store.id = :storeId", { storeId })
    //   .andWhere("storeHours.dayOfWeek = :currentDay", { currentDay })
    //   .andWhere("storeHours.open <= :currentTime", { currentTime })
    //   .andWhere("storeHours.close >= :currentTime", {
    //     currentTime,
    //   })
    //   .getOne();

    // return result;

    // return {} as any;
  }

  async getAllStoresToExport(): Promise<ExportStoreDTO[] | null> {
    const result: ExportStoreDTO[] = await this.database.findMany({
      select: {
        description: true,
        name: true,
        cnpj: true,
        slug: true,
        email: true,
        phone: true,
        address: {
          select: {
            street: true,
            city: true,
            country: true,
            district: true,
            nickname: true,
            number: true,
            state: true,
            postcode: true,
            complement: true,
          },
        },
        storeCategory: {
          select: {
            category: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: "desc",
      },
    });

    return result;
  }

  async createManyStores(stores: ICreateStoreByExportDTO[]): Promise<boolean> {
    const result = stores.map(async (store) => {
      const result = await this.database.$create({
        data: {
          ...store,
          address: {
            create: {
              street: store.address?.street,
              city: store.address?.city,
              country: store.address?.country,
              district: store.address?.district,
              postcode: store.address?.postcode,
              state: store.address?.state,
              complement: store.address?.complement,
              nickname: store.address.nickname,
              number: store.address.number,
            },
          },
          storeCategory: {
            createMany: {
              data: store.storeCategory
                ? store.storeCategory.map((item) => ({
                    categoryId: item.categoryId,
                  }))
                : [],
            },
          },
        },
        userId: this.userContext.userId,
      });

      return result;
    });

    return !!result;
  }

  async deleteWithRelations(id: string): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await client.storeUser.$deleteMany({
        where: { storeId: id },
        userId: this.userContext.userId,
      });

      await this.storeCategoryRepository.deleteByStoreId(id, client.storeCategory);

      return true;
    });

    // [?] Should not we delete the store?

    return result;
  }

  async updateStoreCategories(id: string, storeCategories: IStoreCategory[]): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.storeCategoryRepository.deleteByStoreId(id, client.storeCategory);
      await this.storeCategoryRepository.createMany(id, storeCategories, client.storeCategory);

      return true;
    });

    return result;
  }

  async getStoreShowcase(id: string): Promise<IStoreShowcaseDTO | null> {
    const data = await this.database.findUnique({
      where: { id },
      select: {
        id: true,
        open: true,
        name: true,
        reviews: {
          where: {
            storeId: id,
          },
        },
      },
    });

    let response: IStoreShowcaseDTO | null = null;

    if (data) {
      const files = await this.databaseClient.file.findMany({
        where: {
          AND: [{ entity: "store" }, { entityId: id }],
          OR: [{ type: EFileType.icon }, { type: EFileType.bannerMobile }],
        },
        select: {
          url: true,
          type: true,
        },
      });

      response = { ...data, files };
    }

    return response;
  }

  async getSelect(storeName: string, currentPage: number, pageSize: number): Promise<PagedResult<ISelectDTO>> {
    const where = storeName
      ? {
          name: {
            contains: storeName,
            mode: Prisma.QueryMode.insensitive,
          },
        }
      : undefined;

    const data = await this.database.findMany({
      select: {
        id: true,
        name: true,
      },
      where,
      orderBy: {
        name: "asc",
      },
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    });

    const totalCount = await this.databaseModel.count({ where });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: data,
      totalCount,
      totalPages,
    };
  }

  async checkIfUserIsStoreOwnerByOrderId(orderId: string, userId: string): Promise<boolean> {
    const result = await this.databaseClient.storeUser.findFirst({
      where: {
        userId,
        status: EStoreModeratorStatus.active,
        store: {
          order: {
            some: {
              id: orderId,
            },
          },
        },
      },
      select: {
        id: true,
      },
    });

    return !!result;
  }

  async filterStoresByUserDistance(latitude: number, longitude: number, distance: number): Promise<string[]> {
    const stores = await this.databaseClient.$queryRaw<IListStoresDTO[]>(
      Prisma.raw(`
        select
          distinct s.* as storeId,
          a.latitude as latitude,
          a.longitude as longitude
        from
          stores s
          left join addresses a on s."addressId" = a.id
        where
        (6371 * acos(cos(radians(${latitude})) * cos(radians(a.latitude))
          * cos(radians(${longitude}) - radians(a.longitude))
          + sin(radians(${latitude})) * sin(radians(a.latitude)))) <= ${distance} 
      `),
    );
    return stores.map((store) => store.id);
  }

  async getHasStoreMissingPixKey(userId: string): Promise<boolean> {
    const hasStoreMissingPixKey = await this.databaseClient.storeUser.findFirst({
      where: {
        userId,
        store: { pixKey: null },
      },
    });
    return !!hasStoreMissingPixKey;
  }
}
