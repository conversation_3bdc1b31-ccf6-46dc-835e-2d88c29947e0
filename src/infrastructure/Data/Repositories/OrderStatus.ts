import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { IOrderStatus } from "src/business/Interfaces/Prisma/IOrderStatus";
import { IOrderStatusClient, IOrderStatusRepository } from "src/business/Interfaces/Repository/IOrderStatus";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class OrderStatusRepository
  extends BaseRepository<
    IOrderStatus,
    Prisma.OrderStatusFindManyArgs,
    Prisma.OrderStatusFindUniqueOrThrowArgs,
    Prisma.OrderStatusUpdateManyArgs,
    Prisma.OrderStatusDeleteManyArgs,
    Prisma.OrderStatusInclude
  >
  implements IOrderStatusRepository
{
  database: IOrderStatusClient;

  constructor() {
    super("orderStatus");
    this.database = this.databaseModel;
  }

  async getAllStatusByOrderId(orderId: string): Promise<IOrderStatus[]> {
    const result = await this.database.findMany({
      where: {
        orderId,
      },
      include: {
        orderStatusType: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return result;
  }

  async changeOrderStatus(orderId: string, statusTypeId: string, observation?: string): Promise<IOrderStatus> {
    const result = await this.database.$create({
      data: {
        orderId,
        orderStatusTypeId: statusTypeId,
        observation,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async changeOrderStatusTransaction(orderId: string, statusTypeId: string, observation?: string): Promise<boolean> {
    const result = this.transaction<boolean>(async (client) => {
      const lastStatus = await this.getLastByOrderIdWithRelations(orderId);
      if (!lastStatus) return true;

      if (lastStatus.orderStatusType?.value === EOrderStatusValue.on_route_to_store) {
        return false;
      }

      const result = await this.database.$create({
        data: {
          orderId,
          orderStatusTypeId: statusTypeId,
          observation,
        },
        userId: this.userContext.userId,
      });

      return true;
    });

    return result;
  }

  async createOrUpdateOrderStatus(orderId: string, statusTypeId: string): Promise<IOrderStatus> {
    const lastStatus = await this.getLastByOrderId(orderId);

    const result = await this.database.upsert({
      where: {
        id: lastStatus?.id,
        orderId,
      },
      update: {
        orderStatusTypeId: statusTypeId,
        current: true,
      },
      create: {
        orderId,
        orderStatusTypeId: statusTypeId,
        current: true,
      },
    });

    return result;
  }

  async getLastByOrderId(orderId: string): Promise<IOrderStatus | null> {
    const result = await this.find({
      where: {
        orderId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 1,
    });

    return result[0] || null;
  }

  async getLastByOrderIdWithRelations(orderId: string): Promise<IOrderStatus | null> {
    const result = await this.find({
      where: {
        orderId,
        current: true,
      },
      select: {
        id: true,
        createdAt: true,
        observation: true,
        orderStatusType: {
          select: {
            value: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 1,
    });

    return result[0] || null;
  }

  async getLastOrderStatusTypeByOrderId(orderId: string): Promise<EOrderStatusValue | null> {
    const result = await this.database.findFirst({
      where: {
        orderId,
        current: true,
      },
      select: {
        orderStatusType: {
          select: {
            value: true,
          },
        },
      },
    });

    return (result?.orderStatusType.value as EOrderStatusValue) || null;
  }
}
