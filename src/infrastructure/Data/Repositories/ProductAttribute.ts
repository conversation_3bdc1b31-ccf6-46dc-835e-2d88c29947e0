import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";
import {
  IProductAttributeClient,
  IProductAttributeRepository,
} from "src/business/Interfaces/Repository/IProductAttribute";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ProductAttributeRepository
  extends BaseRepository<
    IProductAttribute,
    Prisma.ProductAttributeFindManyArgs,
    Prisma.ProductAttributeFindUniqueOrThrowArgs,
    Prisma.ProductAttributeUpdateManyArgs,
    Prisma.ProductAttributeDeleteManyArgs,
    Prisma.ProductAttributeInclude
  >
  implements IProductAttributeRepository
{
  database: IProductAttributeClient;

  constructor() {
    super("productAttribute");
    this.database = this.databaseModel;
  }

  async create(
    productId: string,
    attributeId: string,
    attributeoptionIds?: IProductAttributeOption[],
    client?: IProductAttributeClient,
  ): Promise<IProductAttribute | null> {
    const database = this.getDatabaseClient<IProductAttributeClient>(client);

    const productAttribute = await database.$create({
      data: {
        productId,
        attributeId,
        productAttributeOption:
          attributeoptionIds && attributeoptionIds?.length > 0
            ? {
                createMany: {
                  data: attributeoptionIds,
                },
              }
            : undefined,
      },
      userId: this.userContext.userId,
    });

    return productAttribute;
  }

  async createMany(
    productId: string,
    attributeIds: IProductAttribute[],
    client?: IProductAttributeClient,
  ): Promise<number> {
    const database = this.getDatabaseClient<IProductAttributeClient>(client);

    const productAttibutes = await database.createMany({
      data: attributeIds.map((item) => ({ attributeId: item.attributeId, productId })),
    });
    return productAttibutes.count;
  }

  async getByProductAndAttribute(productId: string, attributeId: string): Promise<IProductAttribute | null> {
    const productAttribute = this.database.findFirst({
      where: { productId, attributeId },
    });
    return productAttribute;
  }

  async getByProductId(productId: string): Promise<IProductAttribute[]> {
    const productAttributes = this.database.findMany({
      where: { productId },
      include: { productAttributeOption: true },
    });
    return productAttributes;
  }

  async deleteByProductId(productId: string, client?: IProductAttributeClient): Promise<number> {
    const database = this.getDatabaseClient<IProductAttributeClient>(client);

    const result = await database.$deleteMany({
      where: { productId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  // FIXME Validar
  async deleteByStore(storeId: string, client?: IProductAttributeClient): Promise<number> {
    const database = this.getDatabaseClient<IProductAttributeClient>(client);

    const result = await database.$deleteMany({
      where: { product: { storeId } },
      userId: this.userContext.userId,
    });

    // const products = await Product.createQueryBuilder("products")
    //   .where("products.storeId = :storeId", { storeId })
    //   .getMany();

    // if (products.length < 1) return new DeleteResult();

    // const result = await this.DBLink.createQueryBuilder()
    //   .delete()
    //   .from(ProductAttribute)
    //   .where("productId IN (:...ids)", { ids: products.map((product) => product.id) })
    //   .execute();
    return result.count;
  }

  async updateActiveStatusById(status: boolean, ids: string[], client?: IProductAttributeClient): Promise<number> {
    const database = this.getDatabaseClient<IProductAttributeClient>(client);

    const result = await database.$updateMany({
      data: { active: status },
      where: { id: { in: ids } },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async activeProductAttributeById(ids: string[], client?: IProductAttributeClient): Promise<number> {
    const result = await this.updateActiveStatusById(true, ids, client);
    return result;
  }

  async inactiveProductAttributeById(ids: string[], client?: IProductAttributeClient): Promise<number> {
    const result = await this.updateActiveStatusById(false, ids, client);
    return result;
  }
}
