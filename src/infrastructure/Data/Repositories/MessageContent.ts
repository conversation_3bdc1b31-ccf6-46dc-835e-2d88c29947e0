import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IMessageContent } from "src/business/Interfaces/Prisma/IMessageContent";
import { IMessageContentRepository } from "src/business/Interfaces/Repository/IMessageContent";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class MessageContentRepository
  extends BaseRepository<
    IMessageContent,
    Prisma.MessageContentFindManyArgs,
    Prisma.MessageContentFindUniqueOrThrowArgs,
    Prisma.MessageContentUpdateManyArgs,
    Prisma.MessageContentDeleteManyArgs,
    Prisma.MessageContentInclude
  >
  implements IMessageContentRepository
{
  constructor() {
    super("messageContent");
  }
}
