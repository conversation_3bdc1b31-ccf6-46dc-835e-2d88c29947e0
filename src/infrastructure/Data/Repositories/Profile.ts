import { <PERSON>risma } from "@prisma/client";
import { injectable } from "inversify";
import { IProfile } from "src/business/Interfaces/Prisma/IProfile";
import { IProfileClient, IProfileRepository } from "src/business/Interfaces/Repository/IProfile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ProfileRepository
  extends BaseRepository<
    IProfile,
    Prisma.ProfileFindManyArgs,
    Prisma.ProfileFindUniqueOrThrowArgs,
    Prisma.ProfileUpdateManyArgs,
    Prisma.ProfileDeleteManyArgs,
    Prisma.ProfileInclude
  >
  implements IProfileRepository
{
  database: IProfileClient;

  constructor() {
    super("profile");
    this.database = this.databaseModel;
  }

  async create(profile: IProfile): Promise<IProfile> {
    const result = await this.database.$create({ data: profile, userId: this.userContext.userId });

    return result;
  }

  async findByIdWithPermission(id: string): Promise<IProfile | null> {
    const profile = await this.database.findUnique({
      where: { id },
      include: { profilePermissions: { include: { permission: true } } },
    });

    return profile;
  }

  async relateProfilePermission(profileId: string, permissionId: string): Promise<void> {
    await this.databaseClient.profilePermission.$create({
      data: {
        profileId,
        permissionId,
      },
      userId: this.userContext.userId,
    });
  }
}
