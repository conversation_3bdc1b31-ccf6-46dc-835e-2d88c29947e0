import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IFinancialConsolidation } from "src/business/Interfaces/Prisma/IFinancialConsolidation";
import {
  IFinancialConsolidationClient,
  IFinancialConsolidationRepository,
} from "src/business/Interfaces/Repository/IFinancialConsolidation";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class FinancialConsolidationRepository
  extends BaseRepository<
    IFinancialConsolidation,
    Prisma.FinancialConsolidationFindManyArgs,
    Prisma.FinancialConsolidationFindUniqueOrThrowArgs,
    Prisma.FinancialConsolidationUpdateManyArgs,
    Prisma.FinancialConsolidationDeleteManyArgs,
    Prisma.FinancialConsolidationCreateArgs
  >
  implements IFinancialConsolidationRepository
{
  database: IFinancialConsolidationClient;

  constructor() {
    super("FinancialConsolidation");
    this.database = this.databaseModel;
  }

  async create(data: IFinancialConsolidation): Promise<IFinancialConsolidation> {
    const result = await this.database.$create({
      data: {
        ...data,
        transactions: {
          createMany: {
            data: data.transactions || [],
          },
        },
      },
      userId: this.userContext.userId,
    });

    return result;
  }
}
