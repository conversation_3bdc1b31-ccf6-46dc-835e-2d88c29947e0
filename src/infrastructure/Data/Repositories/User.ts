import { EFileType, EMessageStatus, EProfileStatus, Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import { IDeleteUser } from "src/api/ViewModels/User/IDeleteUser";
import { IFilterMailingDTO } from "src/business/DTOs/FilterMailing/IFilterMailing";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IReactivateAccountViewModel } from "src/api/ViewModels/User/IReactivateAccount";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import IPermanentDeleteUserDTO from "src/business/DTOs/User/IPermanentDelete";
import { IUserWithProfilePictureDTO } from "src/business/DTOs/User/IUserWithProfilePicture";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { ICardRepository } from "src/business/Interfaces/Repository/ICard";
import { IChatMessagesRepository } from "src/business/Interfaces/Repository/IChatMessages";
import { IClientRepository } from "src/business/Interfaces/Repository/IClient";
import { IDeliverymanRepository } from "src/business/Interfaces/Repository/IDeliveryman";
import { IDeviceRepository } from "src/business/Interfaces/Repository/IDevice";
import { ILoginSessionRepository } from "src/business/Interfaces/Repository/ILoginSession";
import { IMessageRepository } from "src/business/Interfaces/Repository/IMessage";
import { IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IReviewRepository } from "src/business/Interfaces/Repository/IReview";
import { IShopkeeperRepository } from "src/business/Interfaces/Repository/IShopkeeper";
import { IStoreUserRepository } from "src/business/Interfaces/Repository/IStoreUser";
import { IUserClient, IUserRepository } from "src/business/Interfaces/Repository/IUser";
import { handleENumberComparison } from "src/business/Utils/HandleENumberComparison";
import { IUserAddressRepository } from "src/business/Interfaces/Repository/IUserAddress";
import { IUserFavoriteProductsRepository } from "src/business/Interfaces/Repository/IUserFavoriteProducts";
import { IUserFavoriteStoresRepository } from "src/business/Interfaces/Repository/IUserFavoriteStores";
import { IUserMessageRepository } from "src/business/Interfaces/Repository/IUserMessage";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";
import { IOrder } from "src/business/DTOs/Order";

@injectable()
export class UsersRepository
  extends BaseRepository<
    IUser,
    Prisma.UserFindManyArgs,
    Prisma.UserFindUniqueArgs,
    Prisma.UserUpdateManyArgs,
    Prisma.UserDeleteManyArgs,
    Prisma.UserInclude
  >
  implements IUserRepository
{
  database: IUserClient;

  constructor(
    @inject(TOKENS.IClientRepository)
    private clientRepository: IClientRepository,
    @inject(TOKENS.IShopkeeperRepository)
    private shopkeeperRepository: IShopkeeperRepository,
    @inject(TOKENS.IDeliverymanRepository)
    private deliverymanRepository: IDeliverymanRepository,
    @inject(TOKENS.IOrderRepository)
    private orderRepository: IOrderRepository,
    @inject(TOKENS.IReviewRepository)
    private reviewRepository: IReviewRepository,
    @inject(TOKENS.IDeviceRepository)
    private deviceRepository: IDeviceRepository,
    @inject(TOKENS.IMessageRepository)
    private messageRepository: IMessageRepository,
    @inject(TOKENS.ICardRepository)
    private cardRepository: ICardRepository,
    @inject(TOKENS.IChatMessagesRepository)
    private chatMessagesRepository: IChatMessagesRepository,
    @inject(TOKENS.ILoginSessionRepository)
    private loginSessionRepository: ILoginSessionRepository,
    @inject(TOKENS.IUserMessageRepository)
    private userMessageRepository: IUserMessageRepository,
    @inject(TOKENS.IUserProfileRepository)
    private userProfileRepository: IUserProfileRepository,
    @inject(TOKENS.IUserAddressRepository)
    private userAddressRepository: IUserAddressRepository,
    @inject(TOKENS.IUserFavoriteProductsRepository)
    private favProductsRepository: IUserFavoriteProductsRepository,
    @inject(TOKENS.IUserFavoriteStoresRepository)
    private favStoresRepository: IUserFavoriteStoresRepository,
    @inject(TOKENS.IStoreUserRepository)
    private storeUserRepository: IStoreUserRepository,
  ) {
    super("user");
    this.database = this.databaseModel;
  }

  async create(user: IUser): Promise<IUser> {
    const userProfilesInput = {
      create: user.userProfiles
        ? user.userProfiles.map((profile) => ({
            profileId: profile.profileId,
          }))
        : undefined,
    };

    const userAddressInput = {
      create: user.userAddress
        ? user.userAddress.map((userAddress) => ({
            address: { create: userAddress.address },
          }))
        : undefined,
    };

    const clientInput = user.client ? { create: user.client } : undefined;
    const deliverymanInput = user.deliveryman ? { create: user.deliveryman } : undefined;
    const shopkeeperInput = user.shopkeeper ? { create: user.shopkeeper } : undefined;

    const result = await this.database.$create({
      data: {
        ...user,
        userProfiles: userProfilesInput,
        userAddress: userAddressInput,
        client: clientInput,
        deliveryman: deliverymanInput,
        shopkeeper: shopkeeperInput,
      },
      include: {
        userProfiles: true,
        userAddress: true,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getWithProfilePictureById(id: string): Promise<IUserWithProfilePictureDTO | null> {
    const fileDatabaseClient = this.databaseClient.file;

    const userData = await this.database.findUnique({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        cpf: true,
        email: true,
        phone: true,
        dateOfBirth: true,
        contactByEmail: true,
        contactBySms: true,
        contactByWhatsapp: true,
        cognitoId: true,
        deliveryman: {
          select: { id: true, pixKey: true },
        },
        shopkeeper: {
          select: { id: true },
        },
      },
      where: { id, permanentlyDeleted: false },
    });

    const files = await fileDatabaseClient.findMany({
      where: {
        entityId: id,
        type: EFileType.profilePhoto,
      },
    });

    const profilePicture = files[0];

    if (userData && profilePicture) {
      const result: IUserWithProfilePictureDTO = {
        ...userData,
        profilePictureUrl: profilePicture.url,
        profilePictureId: profilePicture.id,
      };

      return result;
    }

    if (userData && !profilePicture) {
      const result: IUserWithProfilePictureDTO = {
        ...userData,
        profilePictureUrl: "",
        profilePictureId: "",
      };

      return result;
    }

    // const user: UserProfilePictureViewModel | undefined = await this.DBLink.createQueryBuilder("users")
    // .select("users.id", "id")
    // .addSelect("users.cognitoId", "cognitoId")
    // .addSelect("users.cognitoIdGoogle", "cognitoIdGoogle")
    // .addSelect("users.firstName", "firstName")
    // .addSelect("users.lastName", "lastName")
    // .addSelect("users.email", "email")
    // .addSelect("users.phone", "phone")
    // .addSelect("users.contactByEmail", "contactByEmail")
    // .addSelect("users.contactBySms", "contactBySms")
    // .addSelect("users.contactByWhatsapp", "contactByWhatsapp")
    // .addSelect("files.id", "profilePictureId")
    // .addSelect("files.url", "profilePictureUrl")
    // .where("users.id = :id", { id })
    // .leftJoin("files", "files", "files.entityId = users.id AND files.usage = 8")
    // .getRawOne();
    return null;
  }

  async getWithProfile(id: string): Promise<IUser | null> {
    const user = await this.findOne({
      where: { id, permanentlyDeleted: false },
      include: {
        userProfiles: {
          include: {
            profile: true,
          },
        },
      },
    });

    return user;
  }

  async getWithProfileData(id: string): Promise<IUser | null> {
    const user = await this.findOne({
      where: { id, permanentlyDeleted: false },
      include: {
        userProfiles: {
          include: {
            profile: true,
          },
        },
        client: true,
        deliveryman: true,
        shopkeeper: true,
      },
    });

    if (user) {
      const files = await this.databaseClient.file.findMany({
        where: { entityId: user.id },
      });
      const userWithFiles: IUser = { ...user, files };
      return userWithFiles;
    }

    return user;
  }

  async getWithAddress(id: string): Promise<IUser | null> {
    const user = await this.findOne({
      where: { id, permanentlyDeleted: false },
      include: {
        userAddress: {
          include: {
            address: true,
          },
        },
      },
    });

    return user;
  }

  async deleteUserAddress(id: string): Promise<number> {
    const result = await this.deleteMany({
      where: {
        userAddress: {
          some: {
            addressId: id,
          },
        },
      },
    });

    //   const resultDb = await UserAddress.createQueryBuilder("userAddress")
    //   .where("userAddress.userId = :id", { id })
    //   .getMany();

    // const result: DeleteResult[] = [];

    // if (resultDb) {
    //   resultDb.map(async (item) => {
    //     result.push(await UserAddress.delete(item.id));
    //   });
    // }

    return result;
  }

  async deleteFavoriteStore(userId: string, storeId: string): Promise<number> {
    const result = await this.databaseClient.userFavoriteStores.deleteMany({
      where: {
        userId,
        storeId,
      },
    });

    //   const resultDb = await UserFavoriteStores.createQueryBuilder("favoriteStore")
    //   .where("favoriteStore.userId = :userId", { userId })
    //   .andWhere("favoriteStore.storeId = :storeId", { storeId })
    //   .getMany();

    // const result: DeleteResult[] = [];

    // if (resultDb) {
    //   resultDb.map(async (item) => {
    //     result.push(await UserFavoriteStores.delete(item.id));
    //   });
    // }

    return result.count;
  }

  async getByCognitoId(cognitoId: string): Promise<IUser | null> {
    const user = await this.findOne({ where: { cognitoId, permanentlyDeleted: false } });

    return user;
  }

  async deleteFavoriteProduct(userId: string, productId: string): Promise<number> {
    const result = await this.databaseClient.userFavoriteProducts.deleteMany({
      where: {
        userId,
        productId,
      },
    });

    return result.count;
  }

  async getByPhone(phone: string): Promise<IUser | null> {
    const user = await this.database.findFirst({ where: { phone, permanentlyDeleted: false } });

    return user;
  }

  async getByEmail(email: string): Promise<IUser | null> {
    const user = await this.findOne({ where: { email, permanentlyDeleted: false } });

    return user;
  }

  async relateUserAddress(userId: string, addressId: string): Promise<void> {
    await this.databaseClient.userAddress.$create({
      data: { userId, addressId },
      userId: this.userContext.userId,
    });
  }

  async relateUserFavoriteStore(userId: string, storeId: string): Promise<void> {
    await this.databaseClient.userFavoriteStores.$create({
      data: { userId, storeId },
      userId: this.userContext.userId,
    });
  }

  async relateUserFavoriteProduct(userId: string, productId: string): Promise<void> {
    await this.databaseClient.userFavoriteProducts.$create({
      data: { userId, productId },
      userId: this.userContext.userId,
    });
  }
  // async deleteFavoriteProduct(
  //   userId: string,
  //   productId: string
  // ): Promise<DeleteResult[]> {
  //   const resultDb = await UserFavoriteProducts.createQueryBuilder(
  //     "favoriteProducts"
  //   )
  //     .where("favoriteProducts.userId = :userId", { userId })
  //     .andWhere("favoriteProducts.productId = :productId", { productId })
  //     .getMany();

  //   const result: DeleteResult[] = [];

  //   if (resultDb) {
  //     resultDb.map(async (item) => {
  //       result.push(await UserFavoriteProducts.delete(item.id));
  //     });
  //   }

  //   return result;
  // }

  async getNotificationQuantity(id: string, profileId: string): Promise<number> {
    const user = await this.database.findFirst({
      where: { id, permanentlyDeleted: false },
      include: {
        userMessage: {
          where: {
            status: {
              not: EMessageStatus.read,
            },
            profileId,
          },
        },
      },
    });

    return user?.userMessage?.length || 0;
    // const user = await this.DBLink.createQueryBuilder("user")
    //   .where("user.id = :id", { id })
    //   .leftJoinAndSelect("user.userProfiles", "userProfiles")
    //   .leftJoinAndSelect("userProfiles.profile", "profile")
    //   .leftJoinAndSelect(
    //     "user.userMessage",
    //     "userMessage",
    //     "userMessage.status != :status AND userMessage.profileId = :profileId",
    //     { status: EMessageStatus.read, profileId },
    //   )
    //   .leftJoinAndSelect("userMessage.message", "message", "'notification' = ANY(message.sendingType)")
    //   .leftJoinAndSelect("user.userMessage", "user_message", "user_message.messageId = message.id")
    //   .getOne();

    // return user?.userMessage?.length || 0;
  }

  async saveCognitoIdGoogle(id: string, cognitoIdGoogle: string): Promise<void> {
    await this.update(id, { cognitoIdGoogle });

    // await this.DBLink.createQueryBuilder("users")
    //   .update("users")
    //   .set({
    //     cognitoIdGoogle,
    //   })
    //   .where("id = :id", { id: userId })
    //   .execute();
  }

  async getByCognitoIdGoogle(cognitoIdGoogle: string): Promise<IUser | null> {
    const result = await this.findOne({ where: { cognitoIdGoogle, permanentlyDeleted: false } });

    return result;
  }

  async getAllPaged(
    currentPage: number,
    pageSize: number,
    filterName?: string,
    filterProfile?: string,
    filterStatus?: string,
    sortDirection?: IOrder,
  ): Promise<PagedResult<IUser>> {
    filterName = filterName?.toLowerCase();
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    let whereCondition: Prisma.UserWhereInput | undefined = { permanentlyDeleted: false };

    if (filterName && filterName !== "") {
      whereCondition = {
        firstName: filterName,
      };
    }

    if (filterProfile && filterProfile !== "") {
      whereCondition = {
        ...whereCondition,
        userProfiles: {
          some: {
            profile: {
              name: filterProfile,
            },
          },
        },
      };
    }

    if (filterStatus) {
      const deliverymanCondition =
        filterProfile === EProfile.deliveryman
          ? {
              deliveryman: { status: filterStatus as EProfileStatus },
            }
          : {};

      const shopkeeperCondition =
        filterProfile === EProfile.shopkeeper
          ? {
              shopkeeper: { status: filterStatus as EProfileStatus },
            }
          : {};

      const bothProfilesCondition =
        filterProfile === undefined || filterProfile === ""
          ? [
              {
                deliveryman: { status: filterStatus as EProfileStatus },
              },
              {
                shopkeeper: { status: filterStatus as EProfileStatus },
              },
            ]
          : [];

      whereCondition = {
        ...whereCondition,
        OR: bothProfilesCondition.length > 0 ? bothProfilesCondition : [deliverymanCondition, shopkeeperCondition],
      };
    }

    const result = await this.database.findMany<IUser>({
      where: whereCondition,
      select: {
        id: true,
        cpf: true,
        createdAt: true,
        updatedAt: true,
        deleted: true,
        disabled: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        userProfiles: {
          select: {
            id: true,
            profile: true,
          },
        },
        shopkeeper: {
          select: {
            id: true,
            status: true,
          },
        },
        deliveryman: {
          select: {
            id: true,
            status: true,
          },
        },
      },
      skip: skipIndex,
      take: pageSize,
      orderBy: { updatedAt: sortDirection || "desc" },
    });

    const totalCount = await this.database.count({
      where: whereCondition,
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };
  }

  async updateByUserId(userId: string, data: IUser): Promise<boolean | null> {
    console.log(data, userId);
    const updateUser = await this.database.update({
      where: {
        id: userId,
      },
      data: {
        firstName: data.firstName,
        lastName: data.lastName,
        cpf: data.cpf,
        phone: data.phone,
        email: data.email,
      },
    });

    if (updateUser) {
      return true;
    }
    return null;
  }

  async deleteByUserId(userId: string): Promise<boolean | null> {
    const result = await this.database.delete({
      where: {
        id: userId,
      },
    });

    if (result) {
      return true;
    }
    return null;
  }

  async disableUser(data: IDeleteUser): Promise<boolean | null> {
    const result = await this.database.update({
      where: {
        id: data.id,
      },
      data: {
        disabled: data.disabled,
      },
    });

    if (result) {
      return true;
    }
    return null;
  }

  async getByMailingFilter(filter: IFilterMailingDTO): Promise<IUserMailingDTO[] | null> {
    try {
      const usersFilteredByDateOfBirth =
        filter.birthDayStartDate && filter.birthDayEndDate
          ? await this.databaseClient.$queryRaw<{ id: string }[]>(
              Prisma.sql`
          SELECT users.id
          FROM users
          WHERE
          EXTRACT(MONTH FROM users."dateOfBirth") >= ${filter.birthDayStartDate.getMonth() + 1}
          AND EXTRACT(DAY FROM users."dateOfBirth") >= ${filter.birthDayStartDate.getDate()}
          AND EXTRACT(MONTH FROM users."dateOfBirth") <= ${filter.birthDayEndDate.getMonth() + 1}
          AND EXTRACT(DAY FROM users."dateOfBirth") <= ${filter.birthDayEndDate.getDate()};
        `,
            )
          : [];

      const usersIdsFiltered = usersFilteredByDateOfBirth.map((user) => user?.id);

      const dateOfBirthCondition =
        usersFilteredByDateOfBirth.length > 0
          ? {
              id: {
                in: usersIdsFiltered,
              },
            }
          : undefined;

      const district = filter.district ? { equals: filter.district } : undefined;
      const city = filter.city ? { equals: filter.city } : undefined;
      const state = filter.state ? { equals: filter.state } : undefined;

      const price =
        filter.orderPrice && filter.orderPriceComparison
          ? handleENumberComparison(filter.orderPrice, filter.orderPriceComparison)
          : undefined;

      const paymentMethod = filter.paymentMethod ? { equals: filter.paymentMethod } : undefined;

      const category =
        filter.category && filter.category?.length < 0
          ? {
              in: filter.category.map((cat) => cat),
            }
          : undefined;

      const result = await this.database.findMany({
        where: {
          ...dateOfBirthCondition,
          userAddress: {
            some: {
              address: {
                district,
                city,
                state,
              },
            },
          },
          order: {
            some: {
              price,
              transaction: {
                some: {
                  paymentMethod,
                },
              },
              orderItem: {
                some: {
                  product: {
                    productCategory: {
                      some: {
                        category: {
                          name: category,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          cpf: true,
          phone: true,
        },
      });

      return result;
    } catch (error) {
      console.log(error);
    }

    return null;
  }

  async getPermanentlyDeletedUser(): Promise<boolean | null> {
    const result = await this.database.findFirst({ where: { permanentlyDeleted: true } });
    return result ? true : null;
  }

  async getUsersToDeletePermanently(dateLimit: Date): Promise<IPermanentDeleteUserDTO[] | null> {
    const result = await this.database.findMany({
      select: {
        id: true,
        cognitoId: true,
        cognitoIdGoogle: true,
      },
      where: {
        deletedAt: {
          lte: dateLimit,
        },
        cognitoId: {
          not: null,
        },
      },
    });

    return result;
  }

  async deleteUsersPastRecoverLimit(ids: string[]): Promise<number | null> {
    const deletedUser = await this.database.findFirst({ select: { id: true }, where: { permanentlyDeleted: true } });

    const result = await this.transaction<boolean>(async (client) => {
      if (deletedUser) {
        await this.clientRepository.deleteUserClientRelation(ids, client.client);
        await this.shopkeeperRepository.deleteUserShopkeeperRelation(ids, client.shopkeeper);
        await this.deliverymanRepository.deleteUserDeliverymanRelation(ids, client.deliveryman);
        await this.deviceRepository.deleteUserDeviceRelation(ids, client.device);

        await this.orderRepository.updateUserFkInOrder(deletedUser.id, ids, client.order);
        await this.reviewRepository.updateUserFkInReview(deletedUser.id, ids, client.review);
        await this.messageRepository.updateUserFkInMessage(deletedUser.id, ids, client.message);
        await this.cardRepository.updateUserFkInCard(deletedUser.id, ids, client.card);
        await this.chatMessagesRepository.updateUserFkInChatMessage(deletedUser.id, ids, client.chatMessage);
        await this.loginSessionRepository.updateUserFkInLoginSession(deletedUser.id, ids, client.loginSession);
        await this.userMessageRepository.updateUserFkInUserMessage(deletedUser.id, ids, client.userMessage);
        await this.userProfileRepository.updateUserFkInUserProfile(deletedUser.id, ids, client.userProfile);
        await this.userAddressRepository.updateUserFkInUserAddress(deletedUser.id, ids, client.userAddress);
        await this.favProductsRepository.updateUserFkInFavProducts(deletedUser.id, ids, client.userFavoriteProducts);
        await this.favStoresRepository.updateUserFkInFavStores(deletedUser.id, ids, client.userFavoriteStores);
        await this.storeUserRepository.updateUserFkInStoreUser(deletedUser.id, ids, client.storeUser);
      }

      return true;
    });

    if (result) {
      const deleted = await this.database.$deleteMany({ where: { id: { in: ids } }, userId: deletedUser?.id });
      return deleted.count;
    }

    return 0;
  }

  async getByTemporallyDeletedStatus(id: string): Promise<IUser | null> {
    const result = await this.database.findFirst({ where: { id, deleted: true, permanentlyDeleted: false } });
    return result;
  }

  async updateDeletedAndReactivateAccount(id: string): Promise<IUser | null> {
    const result = await this.database.update({
      where: { id },
      data: { deleted: false, deletedAt: null, reactivationCode: null },
    });
    return result;
  }

  async getByEmailAndCpf({ email, cpf }: IReactivateAccountViewModel): Promise<IUser | null> {
    const result = await this.database.findFirst({ where: { email, cpf, permanentlyDeleted: false } });
    return result;
  }

  async getEmailsByUsersIds(usersIds: string[]): Promise<{ email: string }[]> {
    const result = await this.database.findMany({
      where: {
        id: {
          in: usersIds,
        },
      },
      select: {
        email: true,
      },
    });

    return result;
  }

  async getUserDetailsBackOffice(id: string): Promise<IUser | null> {
    const result = await this.database.findFirst<IUser>({
      where: {
        id,
      },
      select: {
        id: true,
        cpf: true,
        createdAt: true,
        updatedAt: true,
        deleted: true,
        disabled: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        userProfiles: {
          select: {
            id: true,
            profile: true,
          },
        },
        shopkeeper: {
          select: {
            id: true,
            status: true,
          },
        },
        deliveryman: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    });

    return result;
  }
}
