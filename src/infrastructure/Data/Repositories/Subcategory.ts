import { Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ISubcategory } from "src/business/Interfaces/Prisma/ISubcategory";
import { ICategorySubcategoryRepository } from "src/business/Interfaces/Repository/ICategorySubcategory";
import { ISubcategoryClient, ISubcategoryRepository } from "src/business/Interfaces/Repository/ISubcategory";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class SubcategoryRepository
  extends BaseRepository<
    ISubcategory,
    Prisma.SubcategoryFindManyArgs,
    Prisma.SubcategoryFindUniqueOrThrowArgs,
    Prisma.SubcategoryUpdateManyArgs,
    Prisma.SubcategoryDeleteManyArgs,
    Prisma.SubcategoryInclude
  >
  implements ISubcategoryRepository
{
  database: ISubcategoryClient;

  constructor(
    @inject(TOKENS.ICategorySubcategoryRepository)
    private categorySubcategoryRepository: ICategorySubcategoryRepository,
  ) {
    super("subcategory");
    this.database = this.databaseModel;
  }

  async create(data: ISubcategory): Promise<ISubcategory> {
    const result = await this.database.$create({
      data: {
        name: data.name,
        description: data.description,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  // async getWithCategory(storeId: string): Promise<ISubcategory[]> {
  //   const response = await this.database.findMany({
  //     where: {
  //       categorySubcategory: {
  //         some: {
  //           category: {
  //             storeCategory: {
  //               some: {
  //                 storeId,
  //               },
  //             },
  //           },
  //         },
  //       },
  //     },
  //     include: {
  //       categorySubcategory: {
  //         include: {
  //           category: true,
  //         },
  //       },
  //     },
  //   });
  // const categories = await this.database
  //   .createQueryBuilder("category")
  //   .leftJoinAndSelect("category.storeCategory", "storeCategory")
  //   .where("storeCategory.storeId = :id", { id: storeId })
  //   .leftJoinAndSelect("category.categorySubcategory", "categorySubcategory")
  //   .leftJoinAndSelect("categorySubcategory.subcategory", "subcategory")
  //   .getMany();
  // return categories;
  // }

  async deleteWithRelations(id: string): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.categorySubcategoryRepository.deleteBySubcategoryId(id, client.categorySubcategory);
      await this.delete(id, client.subcategory);

      return true;
    });

    return result;
  }
}
