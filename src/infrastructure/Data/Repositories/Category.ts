import { EFile, EFileType, Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICategoryDTO } from "src/business/DTOs/Category/ICategory";
import { ICategorySubcategoryDTO } from "src/business/DTOs/Category/ICategorySubcategory";
import { ICreateCategory } from "src/business/DTOs/Category/ICreateCategory";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ISubcategoryDTO } from "src/business/DTOs/Subcategory/ISubcategory";
import { ICategory } from "src/business/Interfaces/Prisma/ICategory";
import { ICategoryClient, ICategoryRepository } from "src/business/Interfaces/Repository/ICategory";
import { ICategorySubcategoryRepository } from "src/business/Interfaces/Repository/ICategorySubcategory";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { IProductCategoryRepository } from "src/business/Interfaces/Repository/IProductCategory";
import { IStoreCategoryRepository } from "src/business/Interfaces/Repository/IStoreCategory";
import { categoriesData } from "src/infrastructure/Data/Categories";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class CategoryRepository
  extends BaseRepository<
    ICategory,
    Prisma.CategoryFindManyArgs,
    Prisma.CategoryFindUniqueOrThrowArgs,
    Prisma.CategoryUpdateManyArgs,
    Prisma.CategoryDeleteManyArgs,
    Prisma.CategoryInclude
  >
  implements ICategoryRepository
{
  database: ICategoryClient;

  constructor(
    @inject(TOKENS.ICategorySubcategoryRepository)
    private categorySubcategoryRepository: ICategorySubcategoryRepository,
    @inject(TOKENS.IStoreCategoryRepository)
    private storeCategoryRepository: IStoreCategoryRepository,
    @inject(TOKENS.IProductCategoryRepository)
    private productCategoryRepository: IProductCategoryRepository,
    @inject(TOKENS.IFileRepository)
    private fileRepository: IFileRepository,
  ) {
    super("category");
    this.database = this.databaseModel;
  }

  async create(data: ICreateCategory, attachments?: string[]): Promise<ICategory> {
    // const result = await this.database.create({
    //   data: {
    //     ...data,
    //     categorySubcategory: {
    //       createMany: {
    //         data: data.categorySubcategory
    //           ? data.categorySubcategory?.map((item) => ({
    //               subcategoryId: item.subcategoryId,
    //             }))
    //           : [],
    //       },
    //     },
    //   },
    // });

    const result = await this.database.$create({
      data: {
        name: data.name,
        description: data.description,
        categorySubcategory: {
          createMany: {
            data: data.categorySubcategory
              ? data.categorySubcategory?.map((item) => ({
                  subcategoryId: item.subcategoryId,
                }))
              : [],
          },
        },
      },
      userId: this.userContext.userId,
    });

    if (result && attachments && attachments.length > 0)
      await this.fileRepository.relateEntityFiles(result.id, attachments);

    return result;
  }

  async getAllCategoriesWithSubCategory(): Promise<ICategory[]> {
    const categories = await this.database.findMany({
      include: {
        categorySubcategory: {
          include: {
            subcategory: true,
          },
        },
      },
    });

    const result = await Promise.all(
      categories.map(async (item) => {
        const icon = await this.databaseClient.file.findFirst({
          where: { AND: [{ entity: "category" }, { entityId: item.id }] },
        });
        const newCategory: ICategory = { ...item, icon };
        return newCategory;
      }),
    );

    return result;
  }

  async getCategoriesWithIcon(currentPage: string = "", pageSize: string = ""): Promise<PagedResult<ICategory>> {
    const { currentPageInt, pageSizeInt } = this.getPageSizeAndCurrentPage(pageSize, currentPage);

    const limit = pageSizeInt;
    const offset = (currentPageInt - 1) * pageSizeInt;

    const categories = await this.databaseClient.$queryRaw<ICategory[]>(
      Prisma.raw(`
        select distinct on (c.id)
          c.*,
            to_jsonb(f.*) as icon
        from categories c 
        left join files f on (
          f."entityId" = c.id
          and f."type" = '${EFileType.photo}'
          and f.entity = 'category'
        )
        order by c.id
        limit ${limit}
        offset ${offset}
      `),
    );

    const totalCount = await this.database.count();
    const totalPages = Math.ceil(totalCount / pageSizeInt);

    return {
      result: categories,
      totalCount,
      totalPages,
    };
  }

  async getPagedWithStoreOptions(
    storeId: string,
    currentPage: number,
    pageSize: number,
    filter: string,
  ): Promise<PagedResult<ICategoryDTO>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const whereRaw = filter ? `WHERE lower(c.name) LIKE lower('%${filter}%')` : "";

    const categories = await this.databaseClient.$queryRaw<ICategoryDTO[]>(
      Prisma.raw(`
        SELECT 
          c.id,
          c.name,
          CASE WHEN sc."storeId" IS NOT NULL THEN true ELSE false END AS checked
        FROM
        categories c
        LEFT JOIN
          stores_categories sc ON c.id = sc."categoryId" AND sc."storeId" = '${storeId}'
        ${whereRaw}
        ORDER BY
          checked DESC
        LIMIT
          ${pageSize}
        OFFSET
          ${skipIndex}
      `),
    );

    const count = await this.databaseClient.$queryRaw<{ value: number }[]>(
      Prisma.raw(
        `
          SELECT COUNT(c.id)::int AS value FROM categories c
          LEFT JOIN
          stores_categories sc ON c.id = sc."categoryId" AND sc."storeId" = '${storeId}'
          ${whereRaw}
        `,
      ),
    );

    const totalCount = count[0].value;

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: categories,
      totalCount,
      totalPages,
    };
  }

  async getWithSubcategoryByStore(storeId: string): Promise<ICategory[]> {
    const result = await this.database.findMany({
      where: {
        storeCategory: {
          some: {
            storeId,
          },
        },
      },
      include: {
        categorySubcategory: {
          include: {
            subcategory: true,
          },
        },
      },
    });

    const categoriesWithIcon = await Promise.all(
      result.map(async (item) => {
        const icon = await this.databaseClient.file.findFirst({
          where: { AND: [{ entity: EFile.category }, { entityId: item.id }] },
        });
        const newCategory: ICategory = { ...item, icon };
        return newCategory;
      }),
    );

    return categoriesWithIcon;

    // const categories = await this.database
    //   .createQueryBuilder("category")
    //   .leftJoinAndSelect("category.storeCategory", "storeCategory")
    //   .where("storeCategory.storeId = :id", { id: storeId })
    //   .leftJoinAndSelect("category.categorySubcategory", "categorySubcategory")
    //   .leftJoinAndSelect("categorySubcategory.subcategory", "subcategory")
    //   .leftJoinAndMapOne(
    //     "category.icon",
    //     "files",
    //     "category_file",
    //     'category_file."entityId"= category.id'
    //   )
    //   .getMany();
    // return categories;
  }

  async getCategoryByProductWithSubcategory(storeId: string, productId?: string): Promise<ICategorySubcategoryDTO[]> {
    const allStoreCategories = await this.database.findMany({
      where: {
        storeCategory: {
          some: {
            storeId,
          },
        },
      },
      select: {
        id: true,
        name: true,
        categorySubcategory: {
          select: {
            subcategory: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    const result = await Promise.all(
      allStoreCategories.map(async (storeCategory) => {
        const data: ICategorySubcategoryDTO = {
          id: storeCategory.id,
          name: storeCategory.name,
          checked: false,
          subcategory: [],
        };

        const productHasCategory = await this.database.count({
          where: {
            productCategories: {
              some: {
                AND: [
                  {
                    productId,
                  },
                  {
                    categoryId: storeCategory.id,
                  },
                  {
                    product: {
                      storeId,
                    },
                  },
                ],
              },
            },
          },
        });

        if (productHasCategory) {
          data.checked = true;
        }

        const result = await Promise.all(
          storeCategory.categorySubcategory.map(async (categorySubcategory) => {
            const data: ISubcategoryDTO = {
              id: categorySubcategory.subcategory.id,
              name: categorySubcategory.subcategory.name,
              checked: false,
            };

            const productHasSubcategory = await this.databaseClient.productSubcategory.count({
              where: {
                productId,
                product: {
                  storeId,
                  productCategory: {
                    some: {
                      categoryId: storeCategory.id,
                    },
                  },
                },
                subcategoryId: categorySubcategory.subcategory.id,
              },
            });

            if (productHasSubcategory) {
              data.checked = true;
            }

            return data;
          }),
        );

        data.subcategory = result;

        return data;
      }),
    );

    return result;

    // const result = await Promise.all(
    //   categories.map(async (item) => {
    //     const icon = await this.databaseClient.file.findFirst({
    //       where: { AND: [{ entity: EFile.category }, { entityId: item.id }] },
    //     });
    //     const newCategory: ICategory = { ...item, icon };
    //     return newCategory;
    //   })
    // )

    // const productCategorySB = dataSource
    //   .getRepository(ProductCategory)
    //   .createQueryBuilder("product_category")
    //   .select("product_category.category", "categoryId")
    //   .addSelect("product_category.productId", "productId")
    //   .leftJoinAndSelect("product_category.category", "category");

    // const productSubcategorySB = dataSource
    //   .getRepository(ProductSubcategory)
    //   .createQueryBuilder("product_subcategory")
    //   .select("product_subcategory.subcategory", "subcategoryId")
    //   .addSelect("product_subcategory.productId", "productId")
    //   .leftJoinAndSelect("product_subcategory.subcategory", "subcategory");

    // const categories = await this.database
    //   .createQueryBuilder("category")
    //   .leftJoinAndSelect("category.storeCategory", "storeCategory")
    //   .where("storeCategory.storeId = :storeId", { storeId })
    //   .leftJoinAndSelect("category.categorySubcategory", "categorySubcategory")
    //   .addSelect(
    //     'CASE WHEN "productCategorySB"."categoryId" IS NULL THEN false ELSE true END',
    //     "category_checked"
    //   )
    //   .leftJoin(
    //     `(${productCategorySB.getQuery()})`,
    //     "productCategorySB",
    //     '"productCategorySB"."categoryId" = category.id AND "productCategorySB"."productId" = :id',
    //     { id }
    //   )
    //   .leftJoinAndSelect("categorySubcategory.subcategory", "subcategory")
    //   .addSelect(
    //     'CASE WHEN "productSubcategorySB"."subcategoryId" IS NULL THEN false ELSE true END',
    //     "subcategory_checked"
    //   )
    //   .leftJoin(
    //     `(${productSubcategorySB.getQuery()})`,
    //     "productSubcategorySB",
    //     '"productSubcategorySB"."subcategoryId" = subcategory.id  AND "productSubcategorySB"."productId" = :id',
    //     { id }
    //   )
    //   .leftJoinAndMapOne(
    //     "category.icon",
    //     "files",
    //     "category_file",
    //     'category_file."entityId"= category.id'
    //   )
    //   .getRawMany();
    // return categories;
  }

  async createCategoriesWithSubcategories() {
    const data = categoriesData;

    data.forEach(async (item) => {
      const createSubcategoryInput = item.categorySubcategory.map((categorySubcategory) => ({
        subcategory: {
          create: {
            name: categorySubcategory.subcategory.name,
            description: categorySubcategory.subcategory.description,
          },
        },
      }));

      await this.database.$create({
        data: {
          name: item.name,
          description: item.description,
          categorySubcategory: {
            create: createSubcategoryInput,
          },
        },
        userId: this.userContext.userId,
      });
    });
  }

  async deleteWithRelations(id: string): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.categorySubcategoryRepository.deleteByCategoryId(id, client.categorySubcategory);
      await this.productCategoryRepository.deleteByCategoryId(id, client.productCategory);
      await this.storeCategoryRepository.deleteByCategoryId(id, client.storeCategory);
      await this.delete(id, client.category);

      return true;
    });

    return result;
  }

  async updateCategoryAndSubcategoryRelation(data: ICategory, subcategoryIds: { id: string }[]): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.update(data.id, data, client.category);

      if (subcategoryIds && subcategoryIds.length > 0) {
        await this.categorySubcategoryRepository.deleteByCategoryId(data.id, client.categorySubcategory);
        await this.categorySubcategoryRepository.createMany(
          subcategoryIds.map((item) => ({
            categoryId: data.id!,
            subcategoryId: item.id,
          })),
          client.categorySubcategory,
        );
      }

      return true;
    });

    return result;
  }

  async getSelect(categoryName: string, currentPage: number, pageSize: number): Promise<PagedResult<ISelectDTO>> {
    const where = categoryName
      ? {
          name: {
            contains: categoryName,
            mode: Prisma.QueryMode.insensitive,
          },
        }
      : undefined;

    const data = await this.database.findMany({
      select: {
        id: true,
        name: true,
      },
      where,
      orderBy: {
        name: "asc",
      },
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    });

    const totalCount = await this.databaseModel.count({ where });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: data,
      totalCount,
      totalPages,
    };
  }

  async getCategoryDetails(id: string): Promise<ICategory | null> {
    const result = await this.database.findFirst<ICategory>({
      where: {
        id,
      },
      select: {
        id: true,
        name: true,
        createdAt: true,
        description: true,
        categorySubcategory: {
          select: {
            subcategory: {
              select: {
                id: true,
                description: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (result) {
      const icon = await this.databaseClient.file.findFirst({
        where: { AND: [{ entity: EFile.category }, { entityId: result.id }] },
      });
      return { ...result, icon };
    }

    return result;
  }
}
