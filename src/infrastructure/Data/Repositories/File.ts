import { EFile, EFileType, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IFileClient, IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class FileRepository
  extends BaseRepository<
    IFile,
    Prisma.FileFindManyArgs,
    Prisma.FileFindUniqueOrThrowArgs,
    Prisma.FileUpdateManyArgs,
    Prisma.FileDeleteManyArgs,
    Prisma.FileCreateArgs
  >
  implements IFileRepository
{
  database: IFileClient;

  constructor() {
    super("file");
    this.database = this.databaseModel;
  }

  async create(data: IFile): Promise<IFile> {
    const file = this.database.$create({ data, userId: this.userContext.userId });

    return file;
  }

  async getPhotosByEntityId(entityId: string, entity: EFile): Promise<IFile[]> {
    const files = this.database.findMany({
      where: { entityId, entity, type: EFileType.photo },
    });

    return files;
  }

  async getFilesByEntityId(entityId: string, entity: EFile): Promise<IFile[]> {
    const files = this.database.findMany({ where: { entityId, entity } });

    return files;
  }

  async getFilesByEntityAndType(entityId: string, entity: EFile, type: EFileType): Promise<IFile[]> {
    const files = this.database.findMany({
      where: { entityId, entity, type },
    });

    return files;
  }

  async selectFilesUntied(dateLimit: Date): Promise<IFile[]> {
    const filesUntied = this.database.findMany({
      where: {
        entityId: null,
        createdAt: {
          lte: dateLimit,
        },
      },
    });

    return filesUntied;
  }

  async relateEntityFiles(entityId: string, filesId: string[], client?: IFileClient): Promise<number> {
    const database = this.getDatabaseClient<IFileClient>(client);

    const result = await database.$updateMany({
      where: {
        id: {
          in: filesId,
        },
        entityId: null,
      },
      data: {
        entityId,
      },
      userId: this.userContext.userId,
    });

    return result.count;
  }
}
