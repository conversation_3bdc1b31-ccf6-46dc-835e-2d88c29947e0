import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ISettings } from "src/business/Interfaces/Prisma/ISettings";
import { ISettingClient, ISettingsRepository } from "src/business/Interfaces/Repository/ISettings";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class SettingsRepository
  extends BaseRepository<
    ISettings,
    Prisma.SettingsFindManyArgs,
    Prisma.SettingsFindUniqueOrThrowArgs,
    Prisma.SettingsUpdateManyArgs,
    Prisma.SettingsDeleteManyArgs,
    Prisma.SettingsInclude
  >
  implements ISettingsRepository
{
  database: ISettingClient;

  constructor() {
    super("settings");
    this.database = this.databaseModel;
  }

  async createMany(settings: ISettings[]): Promise<boolean> {
    const result = await this.database.$createMany({
      data: settings,
      userId: this.userContext.userId,
    });

    return !!result.count;
  }

  async getByName(name: string): Promise<ISettings | null> {
    const settings = await this.findFirst({ where: { name } });
    return settings;
  }
}
