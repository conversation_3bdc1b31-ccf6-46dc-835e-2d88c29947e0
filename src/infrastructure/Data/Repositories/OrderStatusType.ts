import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IOrderStatusType } from "src/business/Interfaces/Prisma/IOrderStatusType";
import {
  IOrderStatusTypeClient,
  IOrderStatusTypeRepository,
} from "src/business/Interfaces/Repository/IOrderStatusType";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class OrderStatusTypeRepository
  extends BaseRepository<
    IOrderStatusType,
    Prisma.OrderStatusTypeFindManyArgs,
    Prisma.OrderStatusTypeFindUniqueOrThrowArgs,
    Prisma.OrderStatusTypeUpdateManyArgs,
    Prisma.OrderStatusTypeDeleteManyArgs,
    Prisma.OrderStatusTypeInclude
  >
  implements IOrderStatusTypeRepository
{
  database: IOrderStatusTypeClient;

  constructor() {
    super("orderStatusType");
    this.database = this.databaseModel;
  }

  async create(data: IOrderStatusType): Promise<IOrderStatusType> {
    const result = await this.database.$create({
      data,
      userId: this.userContext.userId,
    });

    return result;
  }

  async findByStatus(status: string): Promise<IOrderStatusType | null> {
    const result = await this.database.findFirst({
      where: { value: status },
    });

    return result;
  }
}
