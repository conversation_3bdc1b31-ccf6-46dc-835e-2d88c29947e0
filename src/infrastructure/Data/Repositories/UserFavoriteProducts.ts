import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IUserFavoriteProducts } from "src/business/Interfaces/Prisma/IUserFavoriteProducts";
import {
  IUserFavoriteProductsClient,
  IUserFavoriteProductsRepository,
} from "src/business/Interfaces/Repository/IUserFavoriteProducts";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class UserFavoriteProductsRepository
  extends BaseRepository<
    IUserFavoriteProducts,
    Prisma.UserFavoriteProductsFindManyArgs,
    Prisma.UserFavoriteProductsFindUniqueOrThrowArgs,
    Prisma.UserFavoriteProductsUpdateManyArgs,
    Prisma.UserFavoriteProductsDeleteManyArgs,
    Prisma.UserFavoriteProductsInclude
  >
  implements IUserFavoriteProductsRepository
{
  database: IUserFavoriteProductsClient;

  constructor() {
    super("userFavoriteProducts");
    this.database = this.databaseModel;
  }

  async updateUserFkInFavProducts(
    updatedId: string,
    userIds: string[],
    client?: IUserFavoriteProductsClient,
  ): Promise<number> {
    const database = this.getDatabaseClient<IUserFavoriteProductsClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
