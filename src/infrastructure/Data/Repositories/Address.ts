import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IAddressOptionDTO } from "src/business/DTOs/Address/IAddressOption";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IAddressClient, IAddressRepository } from "src/business/Interfaces/Repository/IAddress";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class AddressRepository
  extends BaseRepository<
    IAddress,
    Prisma.AddressFindManyArgs,
    Prisma.AddressFindUniqueOrThrowArgs,
    Prisma.AddressUpdateManyArgs,
    Prisma.AddressDeleteManyArgs,
    Prisma.AddressInclude
  >
  implements IAddressRepository
{
  database: IAddressClient;

  constructor() {
    super("address");
    this.database = this.databaseModel;
  }

  // async deleteUserAddress(id: string): Promise<DeleteResult[]> {
  //   const resultDb = await UserAddress.createQueryBuilder("userAddress")
  //     .where("userAddress.addressId = :id", { id })
  //     .getMany();

  //   const result: DeleteResult[] = [];

  //   if (resultDb) {
  //     resultDb.map(async (item) => {
  //       result.push(await UserAddress.delete(item.id));
  //     });
  //   }

  //   return result;
  // }

  async getDefaultAddress(userId: string): Promise<IAddress | null> {
    const result = await this.database.findFirst({
      where: {
        AND: [{ isDefault: true }, { userAddress: { some: { userId } } }],
      },
    });

    // const subQuery = await UserAddress.createQueryBuilder("userAddress")
    //   .where("userAddress.userId = :userId", { userId })
    //   .leftJoinAndSelect("userAddress.address", "address")
    //   .andWhere("address.isDefault = :x", { x: true })
    //   .getOne();

    return result;
  }

  async create(dataInput: IAddress, userId?: string): Promise<IAddress> {
    const result = await this.database.$create({
      data: {
        ...dataInput,
        userAddress: userId
          ? {
              create: {
                userId,
              },
            }
          : undefined,
      },
      include: {
        userAddress: true,
      },
      userId: this.userContext.userId,
    });

    return result;
  }
  // FIXME Método aparentemente não utilizado
  // async getPaged(
  //   currentPage: number,
  //   pageSize: number,
  //   userId: string,
  //   filter?: string,
  // ): Promise<PagedResult<IAddress>> {
  //   const page = currentPage > 0 ? currentPage : 1;
  //   const skipIndex = (page - 1) * pageSize;

  //   const result = await this.database.findMany({
  //     skip: skipIndex,
  //     take: pageSize,
  //     where: {
  //       userAddress: {
  //         some: {
  //           userId,
  //         },
  //       },
  //       AND: [
  //         {
  //           OR: [
  //             {
  //               nickname: {
  //                 contains: filter,
  //                 mode: "insensitive",
  //               },
  //             },
  //           ],
  //         },
  //       ],
  //     },
  //   });

  //   const totalCount = await databaseClient.userAddress.count({
  //     where: {
  //       userId,
  //     },
  //   });

  //   const totalPages = Math.ceil(totalCount / pageSize);

  //   return {
  //     result,
  //     totalCount,
  //     totalPages,
  //   };
  // }

  async getAddressesByUserId(userId: string): Promise<IAddress[]> {
    const result: IAddress[] = await this.database.findMany({
      where: { userAddress: { some: { userId } } },
      orderBy: {
        isDefault: "desc",
      },
    });

    return result;
  }

  async getAddresseOptionsByUserId(userId: string): Promise<IAddressOptionDTO[]> {
    const result = await this.database.findMany({
      where: { userAddress: { some: { userId } } },
      select: {
        id: true,
        nickname: true,
        street: true,
        latitude: true,
        longitude: true,
      },
      orderBy: {
        isDefault: "desc",
      },
    });

    return result;
  }
}
