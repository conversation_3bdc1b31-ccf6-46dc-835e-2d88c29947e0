import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IStoreCategoryClient, IStoreCategoryRepository } from "src/business/Interfaces/Repository/IStoreCategory";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class StoreCategoryRepository
  extends BaseRepository<
    IStoreCategory,
    Prisma.StoreCategoryFindManyArgs,
    Prisma.StoreCategoryFindUniqueOrThrowArgs,
    Prisma.StoreCategoryUpdateManyArgs,
    Prisma.StoreCategoryDeleteManyArgs,
    Prisma.StoreCategoryInclude
  >
  implements IStoreCategoryRepository
{
  database: IStoreCategoryClient;

  constructor() {
    super("storeCategory");
    this.database = this.databaseModel;
  }

  async createMany(storeId: string, storeCategories: IStoreCategory[], client?: IStoreCategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IStoreCategoryClient>(client);

    const result = await database.$createMany({
      data: storeCategories.map((item) => ({ ...item, storeId })),
      userId: this.userContext.userId,
    });
    return result.count;
  }

  async deleteByStoreId(storeId: string, client?: IStoreCategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IStoreCategoryClient>(client);

    const result = await database.$deleteMany({
      where: { storeId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async deleteByCategoryId(categoryId: string, client?: IStoreCategoryClient): Promise<number> {
    const database = this.getDatabaseClient<IStoreCategoryClient>(client);

    const result = await database.$deleteMany({
      where: { categoryId },
      userId: this.userContext.userId,
    });

    return result.count;
  }
}
