import { EStoreModeratorStatus, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IStoreModeratorDTO } from "src/business/DTOs/StoreUser/IStoreModerator";
import { IStoreUser } from "src/business/Interfaces/Prisma/IStoreUser";
import { IStoreUserClient, IStoreUserRepository } from "src/business/Interfaces/Repository/IStoreUser";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class StoreUserRepository
  extends BaseRepository<
    IStoreUser,
    Prisma.StoreUserFindManyArgs,
    Prisma.StoreUserFindUniqueOrThrowArgs,
    Prisma.StoreUserUpdateManyArgs,
    Prisma.StoreUserDeleteManyArgs,
    Prisma.StoreUserInclude
  >
  implements IStoreUserRepository
{
  database: IStoreUserClient;

  constructor() {
    super("StoreUser");
    this.database = this.databaseModel;
  }

  async create(storeUser: IStoreUser): Promise<IStoreUser | null> {
    const result = await this.database.$create({
      data: { ...storeUser, store: undefined, user: undefined },
    });
    return result;
  }

  async getStoreUsersByStoreId(storeId: string): Promise<IStoreUser[]> {
    const storeUsers: IStoreUser[] = await this.database.findMany({
      where: { storeId },
    });
    return storeUsers;
  }

  async getModeratorByStoreId(storeId: string): Promise<IStoreModeratorDTO[]> {
    const data = await this.database.findMany({
      select: { user: { select: { email: true } }, id: true, status: true },
      where: { AND: [{ storeId }, { owner: false }] },
    });

    const storeModerators: IStoreModeratorDTO[] = data.map((item) => ({
      storeUserId: item.id,
      email: item.user.email,
      status: item.status,
    }));

    return storeModerators;
  }

  async setStatus(storeUserId: string, status: EStoreModeratorStatus): Promise<boolean> {
    const result = await this.database.update({
      where: { id: storeUserId },
      data: {
        status,
        // store: undefined,
        // user: undefined,
      },
    });
    if (result) return true;

    return false;
  }

  async updateUserFkInStoreUser(updatedId: string, userIds: string[], client?: IStoreUserClient): Promise<number> {
    const database = this.getDatabaseClient<IStoreUserClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
