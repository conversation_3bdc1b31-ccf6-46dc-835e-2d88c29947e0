import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";
import { IStoreHoursClient, IStoreHoursRepository } from "src/business/Interfaces/Repository/IStoreHours";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class StoreHoursRepository
  extends BaseRepository<
    IStoreHours,
    Prisma.StoreHoursFindManyArgs,
    Prisma.StoreHoursFindUniqueOrThrowArgs,
    Prisma.StoreHoursUpdateManyArgs,
    Prisma.StoreHoursDeleteManyArgs,
    Prisma.StoreHoursInclude
  >
  implements IStoreHoursRepository
{
  database: IStoreHoursClient;

  constructor() {
    super("storeHours");
    this.database = this.databaseModel;
  }

  async createMany(id: string, storeHours: IStoreHours[], client?: IStoreHoursClient): Promise<number> {
    const database = this.getDatabaseClient<IStoreHoursClient>(client);

    const result = await database.$createMany({
      data: storeHours.map((item) => ({ ...item, storeId: id })),
      userId: this.userContext.userId,
    });
    return result.count;
  }

  async deleteByStoreId(id: string, client?: IStoreHoursClient): Promise<number> {
    const database = this.getDatabaseClient<IStoreHoursClient>(client);

    const result = await database.$deleteMany({
      where: { storeId: id },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async getStoreHoursByStoreId(id: string): Promise<IStoreHours[]> {
    const stores = await this.database.findMany({
      where: { store: { id } },
    });

    return stores;
  }

  async updateByStore(storeId: string, storeHours: IStoreHours[]): Promise<boolean> {
    const result = await this.transaction<boolean>(async (client) => {
      await this.deleteByStoreId(storeId, client.storeHours);
      await this.createMany(storeId, storeHours, client.storeHours);

      return true;
    });

    return result;
  }
}
