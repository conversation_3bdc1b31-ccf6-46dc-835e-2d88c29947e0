/* eslint-disable no-nested-ternary */
import { EPayoutOwner, EPayoutStatus, Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IAggregatePayoutsDTO } from "src/business/DTOs/Bordero/IAggregatePayouts";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult, Totalizer } from "src/business/DTOs/PagedResult";
import { IPayoutDetailsDTO } from "src/business/DTOs/Payout/IPayoutDetails";
import { IPayoutListDTO } from "src/business/DTOs/Payout/IPayoutList";
import { IPayoutReportDTO } from "src/business/DTOs/Payout/IPayoutReport";
import { IPayoutStatusDTO } from "src/business/DTOs/Payout/IPayoutStatus";
import { IPayout } from "src/business/Interfaces/Prisma/IPayout";
import { IPayoutClient, IPayoutRepository } from "src/business/Interfaces/Repository/IPayout";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class PayoutRepository
  extends BaseRepository<
    IPayout,
    Prisma.PayoutFindManyArgs,
    Prisma.PayoutFindUniqueOrThrowArgs,
    Prisma.PayoutUpdateManyArgs,
    Prisma.PayoutDeleteManyArgs,
    Prisma.PayoutInclude
  >
  implements IPayoutRepository
{
  database: IPayoutClient;

  constructor() {
    super("payout");
    this.database = this.databaseModel;
  }

  async create(data: IPayout): Promise<IPayout> {
    const result = await this.database.$create({
      data: {
        orderId: data.orderId,
        cooperativeId: data.cooperativeId,
        transferValue: data.transferValue,
        administrativeFeePercent: data.administrativeFeePercent,
        administrativeFeeValue: data.administrativeFeeValue,
        status: data.status,
        payoutOwner: data.payoutOwner,
        statusDate: data.statusDate,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getTotalPayoutAmount(whereCondition: object | undefined): Promise<Totalizer> {
    const totalizer = await this.database.aggregate({
      where: whereCondition,
      _sum: {
        administrativeFeeValue: true,
        transferValue: true,
      },
    });

    return totalizer._sum;
  }

  async getPayoutDetailsById(id: string): Promise<IPayoutDetailsDTO | null> {
    const result = await this.database.findFirst({
      where: {
        id,
      },
      select: {
        id: true,
        orderId: true,
        administrativeFeePercent: true,
        administrativeFeeValue: true,
        createdAt: true,
        status: true,
        statusDate: true,
        transferValue: true,
        payoutOwner: true,
        order: {
          select: {
            createdAt: true,
            shippingPrice: true,
            price: true,
            totalPrice: true,
            deliveryman: {
              select: {
                user: {
                  select: {
                    cpf: true,
                    email: true,
                    phone: true,
                    firstName: true,
                  },
                },
              },
            },
            store: {
              select: {
                name: true,
                description: true,
                cnpj: true,
                email: true,
                phone: true,
                slug: true,
              },
            },
            transaction: {
              select: {
                paymentMethod: true,
              },
            },
          },
        },
      },
    });

    return result as unknown as IPayoutDetailsDTO;
  }

  async getPayoutsValueTotalizer(
    whereCondition: object | undefined,
    owner: EPayoutOwner,
    status: EPayoutStatus,
  ): Promise<number> {
    const totalizer = await this.database.aggregate({
      where: {
        order: whereCondition,
        payoutOwner: owner,
        status,
      },
      _sum: {
        transferValue: true,
      },
    });

    return totalizer._sum.transferValue || 0;
  }

  async getCountPayoutWithoutBordero(payouts: string[]): Promise<number> {
    const count = await this.database.count({
      where: {
        id: {
          in: payouts,
        },
      },
    });

    return count;
  }

  async getPayoutsTotalizerByIds(payoutIds: string[]): Promise<IAggregatePayoutsDTO> {
    const totalizer = await this.databaseClient.$queryRawUnsafe<IAggregatePayoutsDTO[]>(`
      SELECT 
        COUNT(o.id)::integer as "countOrders", 
        SUM(o.price) as "sumPrice", 
        SUM(o."shippingPrice") as "sumShipping", 
	      SUM(p."administrativeFeeValue") as "sumAdm", 
        SUM(p."transferValue") as "sumTransfer"
      FROM payouts p JOIN orders o ON (p."orderId"=o.id)
      WHERE p.id IN (${payoutIds.map((payout) => `'${payout}'`).join(",")})
    `);

    return totalizer[0];
  }

  async getPayoutsWithoutBorderoByDeliveryman(deliverymanId: string): Promise<number> {
    const payouts = await this.database.count({
      where: { AND: [{ order: { deliverymanId } }, { borderoId: null }] },
    });
    return payouts;
  }

  async getPayoutsWithoutBorderoByStore(storeId: string): Promise<number> {
    const payouts = await this.database.count({
      where: { AND: [{ order: { storeId } }, { borderoId: null }] },
    });
    return payouts;
  }

  async getPayoutsWithoutBorderoByCooperative(cooperativeId: string): Promise<number> {
    const payouts = await this.database.count({
      where: { AND: [{ cooperativeId }, { borderoId: null }] },
    });
    return payouts;
  }

  async getPayoutsPaginatedToCreateBordero(
    currentPage: number,
    pageSize: number,
    borderoId: string,
    ownerId: string,
    ownerFilter: EPayoutOwner,
    startDateFilter?: Date | undefined,
    endDateFilter?: Date | undefined,
    orderBy?: string | undefined,
    sortDirection?: IOrder | undefined,
    filterValue?: string | undefined,
  ): Promise<PagedResult<IPayoutListDTO>> {
    const skip = (currentPage - 1) * pageSize;

    let filterCondition = "";

    if (filterValue) {
      filterCondition = `
        AND (p."administrativeFeePercent" = ${filterValue} 
            OR p."administrativeFeeValue" = ${filterValue}
            OR o.price = ${filterValue}
            OR o."shippingPrice" = ${filterValue}
            OR o."totalPrice" = ${filterValue})
        `;
    }

    let orderByCondition = `ORDER BY p."createdAt" ${sortDirection}`;

    if (orderBy) {
      switch (orderBy) {
        case "price":
          orderByCondition = ` ORDER BY o.price ${sortDirection}`;
          break;
        case "shippingPrice":
          orderByCondition = ` ORDER BY o."shippingPrice" ${sortDirection}`;
          break;
        case "totalPrice":
          orderByCondition = ` ORDER BY o."totalPrice" ${sortDirection}`;
          break;
        default:
          break;
      }
    }

    const dataFilter =
      endDateFilter && startDateFilter
        ? ` AND p."createdAt" <= '${endDateFilter}' AND p."createdAt" >= '${startDateFilter}'`
        : "";

    const ownerIdFilter =
      ownerFilter === EPayoutOwner.deliveryman
        ? ` AND u.id = '${ownerId}'`
        : ownerFilter === EPayoutOwner.store
        ? ` AND o."storeId" = '${ownerId}'`
        : `AND p."cooperativeId" = '${ownerId}'`;

    const whereCondition = `
      WHERE	p.status = '${EPayoutStatus.available}'
        AND p."payoutOwner" = '${ownerFilter}'
	      AND (p."borderoId" is null OR p."borderoId" = '${borderoId}')
        ${ownerIdFilter}
        ${dataFilter}
        ${filterCondition}
    `;
    const data = await this.databaseClient.$queryRawUnsafe<any>(`
      SELECT
        p.id,
        p."orderId",
        p."borderoId",
        p."payoutOwner",
        p."administrativeFeePercent",
        p."administrativeFeeValue",
        p."createdAt",
        p."transferValue",
        p."statusDate",
        p.status,
        o.price,
        o."shippingPrice",
        o."totalPrice"
      FROM payouts as p JOIN orders as o ON (p."orderId"=o.id)
        JOIN deliverymen as d ON (o."deliverymanId"=d.id)
        JOIN users as u ON (d."userId"=u.id)
        JOIN cooperative as c ON (p."cooperativeId"=c.id)
      ${whereCondition}
      ${orderByCondition}
      limit ${pageSize}
      offset ${skip}
    `);

    const count = await this.databaseClient.$queryRawUnsafe<{ value: number }[]>(`
      SELECT COUNT(p.id)::int as value
      FROM payouts as p JOIN orders as o ON (p."orderId"=o.id)
        JOIN deliverymen as d ON (o."deliverymanId"=d.id)
        JOIN users as u ON (d."userId"=u.id)
      ${whereCondition}
    `);

    const totalCount = count[0].value;
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: data,
      totalCount,
      totalPages,
    };
  }

  async getCountPayoutsByBordero(borderoId: string): Promise<number> {
    const count = await this.database.aggregate({ _count: true, where: { borderoId } });
    return count._count;
  }

  async getAllPayoutsByBordero(borderoId: string): Promise<IPayoutListDTO[]> {
    const data = await this.databaseClient.$queryRawUnsafe<any>(`
      SELECT
        p.id,
        p."orderId",
        p."borderoId",
        p."payoutOwner",
        p."administrativeFeePercent",
        p."administrativeFeeValue",
        p."createdAt",
        p."transferValue",
        p."statusDate",
        p.status,
        o.price,
        o."shippingPrice",
        o."totalPrice"
      FROM payouts as p JOIN orders as o ON (p."orderId"=o.id)
        JOIN deliverymen as d ON (o."deliverymanId"=d.id)
        JOIN users as u ON (d."userId"=u.id)
        JOIN cooperative as c ON (p."cooperativeId"=c.id)
      WHERE p."borderoId" = '${borderoId}'
    `);
    return data;
  }

  async updateStatus(id: string, status: EPayoutStatus): Promise<boolean> {
    const result = await this.database.$update({
      where: {
        id,
      },
      data: {
        status,
        statusDate: new Date(),
      },
      userId: this.userContext.userId,
    });

    return !!result;
  }

  async getPayoutStatusByOrderId(orderId: string): Promise<IPayoutStatusDTO | null> {
    const result = await this.database.findFirst({
      where: {
        orderId,
      },
      select: {
        id: true,
        status: true,
      },
    });

    return result;
  }

  async getPayoutReportData(
    startDateFilter,
    endDateFilter,
    filterValue,
    orderBy,
    sortDirection,
    statusFilter,
    ownerFilter,
  ): Promise<IPayoutReportDTO[]> {
    const data = await this.database.findMany({
      select: {
        administrativeFeePercent: true,
        administrativeFeeValue: true,
        transferValue: true,
        createdAt: true,
        status: true,
        statusDate: true,
        payoutOwner: true,
        cooperative: {
          select: {
            name: true,
            cnpj: true,
          },
        },
        order: {
          select: {
            totalPrice: true,
            price: true,
            shippingPrice: true,
            store: {
              select: {
                name: true,
                cnpj: true,
              },
            },
            deliveryman: {
              select: {
                user: {
                  select: {
                    firstName: true,
                    cpf: true,
                  },
                },
              },
            },
          },
        },
      },
      where: {
        createdAt: {
          gte: startDateFilter,
          lte: endDateFilter,
        },
        status: statusFilter,
        payoutOwner: ownerFilter,
        OR: [
          {
            order: {
              store: {
                OR: [
                  {
                    name: {
                      contains: filterValue,
                      mode: "insensitive",
                    },
                  },
                  {
                    cnpj: {
                      contains: filterValue,
                      mode: "insensitive",
                    },
                  },
                ],
              },
            },
          },

          {
            cooperative: {
              OR: [
                {
                  name: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
                {
                  cnpj: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
              ],
            },
          },

          {
            order: {
              deliveryman: {
                user: {
                  OR: [
                    {
                      firstName: {
                        equals: filterValue,
                      },
                    },
                    {
                      cpf: {
                        equals: filterValue,
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
      },
    });

    return data;
  }
}
