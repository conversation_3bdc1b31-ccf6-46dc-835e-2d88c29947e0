import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { ITrackingOrder } from "src/business/Interfaces/Prisma/ITrackingOrder";
import { ITrackingOrderClient, ITrackingOrderRepository } from "src/business/Interfaces/Repository/ITrackingOrder";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class TrackingOrderRepository
  extends BaseRepository<
    ITrackingOrder,
    Prisma.TrackingOrderFindManyArgs,
    Prisma.TrackingOrderFindUniqueOrThrowArgs,
    Prisma.TrackingOrderUpdateManyArgs,
    Prisma.TrackingOrderDeleteManyArgs,
    Prisma.TrackingOrderInclude
  >
  implements ITrackingOrderRepository
{
  database: ITrackingOrderClient;

  constructor() {
    super("trackingOrder");
    this.database = this.databaseModel;
  }

  async create(trackingOrder: ITrackingOrder): Promise<ITrackingOrder | null> {
    const result = await this.database.$create({
      data: {
        ...trackingOrder,
        orderId: undefined,
        order: {
          connect: {
            id: trackingOrder.orderId,
          },
        },
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async trackOrderDeliveryById(orderId: string): Promise<ITrackingOrder[]> {
    const data = await this.database.findMany({
      include: {
        order: {
          include: {
            address: true,
            store: {
              include: {
                address: true,
              },
            },
          },
        },
      },
      where: {
        orderId,
      },
    });
    // const result = await this.database.find({
    //   where: { order: { id: orderId } },
    //   relations: ["order.address", "order.store", "order.store.address"],
    //   order: { createdAt: "ASC" },
    // });
    return data;
  }

  async deleteByOrder(orderId: string): Promise<number> {
    const result = await this.database.$deleteMany({
      where: { orderId },
      userId: this.userContext.userId,
    });

    return result.count;
  }
}
