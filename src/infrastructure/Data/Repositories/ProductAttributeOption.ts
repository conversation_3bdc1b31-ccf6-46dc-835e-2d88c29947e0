import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";
import {
  IProductAttributeOptionClient,
  IProductAttributeOptionRepository,
} from "src/business/Interfaces/Repository/IProductAttributeOption";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class ProductAttributeOptionRepository
  extends BaseRepository<
    IProductAttributeOption,
    Prisma.ProductAttributeOptionFindManyArgs,
    Prisma.ProductAttributeOptionFindUniqueOrThrowArgs,
    Prisma.ProductAttributeOptionUpdateManyArgs,
    Prisma.ProductAttributeOptionDeleteManyArgs,
    Prisma.ProductAttributeOptionInclude
  >
  implements IProductAttributeOptionRepository
{
  database: IProductAttributeOptionClient;

  constructor() {
    super("productAttributeOption");
    this.database = this.databaseModel;
  }

  async create(productAttributeId: string, attributeOptionId: string): Promise<IProductAttributeOption | null> {
    const productAttributeOption = await this.database.$create({
      data: {
        productAttributeId,
        attributeOptionId,
      },
      userId: this.userContext.userId,
    });
    return productAttributeOption;
  }

  async getByProductAttributeAndAttributeOption(
    productAttributeId: string,
    attributeOptionId: string,
  ): Promise<IProductAttributeOption | null> {
    const productAttributeOption = this.database.findFirst({
      where: { productAttributeId, attributeOptionId },
    });
    return productAttributeOption;
  }

  async deleteByProductAttributeId(
    productAttributeId: string,
    client?: IProductAttributeOptionClient,
  ): Promise<number> {
    const database = this.getDatabaseClient<IProductAttributeOptionClient>(client);

    const result = await database.$deleteMany({
      where: { productAttributeId },
      userId: this.userContext.userId,
    });
    return result.count;
  }

  async deleteByStore(storeId: string, client?: IProductAttributeOptionClient): Promise<number> {
    const database = this.getDatabaseClient<IProductAttributeOptionClient>(client);

    const result = await database.$deleteMany({
      where: { productAttribute: { product: { storeId } } },
      userId: this.userContext.userId,
    });

    // const products = await Product.createQueryBuilder("products")
    //   .where("products.storeId = :storeId", { storeId })
    //   .getMany();

    // if (products.length < 1) return new DeleteResult();

    // const productsAttributes = await ProductAttribute.createQueryBuilder("productsAttributes")
    //   .where("productsAttributes.productId IN (:...ids)", { ids: products.map((product) => product.id) })
    //   .getMany();

    // if (productsAttributes.length < 1) return new DeleteResult();

    // const result = await this.DBLink.createQueryBuilder()
    //   .delete()
    //   .from(ProductAttributeOption)
    //   .where("productAttributeId IN (:...ids)", { ids: productsAttributes.map((pa) => pa.id) })
    //   .execute();
    return result.count;
  }

  async updateActiveStatusById(
    status: boolean,
    ids: string[],
    client?: IProductAttributeOptionClient,
  ): Promise<number> {
    const database = this.getDatabaseClient<IProductAttributeOptionClient>(client);

    const result = await database.$updateMany({
      data: { active: status },
      where: { id: { in: ids } },
      userId: this.userContext.userId,
    });
    return result.count;
  }

  async activeProductAttributeOptById(ids: string[], client?: IProductAttributeOptionClient): Promise<number> {
    const result = await this.updateActiveStatusById(true, ids, client);
    return result;
  }

  async inactiveProductAttributeOptById(ids: string[], client?: IProductAttributeOptionClient): Promise<number> {
    const result = await this.updateActiveStatusById(false, ids, client);
    return result;
  }
}
