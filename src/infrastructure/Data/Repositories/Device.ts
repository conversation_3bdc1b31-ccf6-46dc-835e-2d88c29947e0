import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IDevice } from "src/business/Interfaces/Prisma/IDevice";
import { IDeviceClient, IDeviceRepository } from "src/business/Interfaces/Repository/IDevice";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class DeviceRepository
  extends BaseRepository<
    IDevice,
    Prisma.DeviceFindManyArgs,
    Prisma.DeviceFindUniqueOrThrowArgs,
    Prisma.DeviceUpdateManyArgs,
    Prisma.DeviceDeleteManyArgs,
    Prisma.DeviceInclude
  >
  implements IDeviceRepository
{
  database: IDeviceClient;

  constructor() {
    super("device");
    this.database = this.databaseModel;
  }

  async create(device: IDevice) {
    const createdDevice = await this.database.$create({
      data: {
        ...device,
        profileId: undefined,
        userId: undefined,
        profile: {
          connect: device.profileId ? { id: device.profileId } : undefined,
        },
        user: {
          connect: { id: device.userId },
        },
      },
      userId: this.userContext.userId,
    });

    return createdDevice;
  }

  async createMany(devices: IDevice[]) {
    const createdDevices = await this.database.$createMany({
      data: devices,
      userId: this.userContext.userId,
    });

    return createdDevices.count;
  }

  async getDeliverymanDevices(deliveryManProfileId: string, topicArn?: string): Promise<IDevice[]> {
    const result = await this.database.findMany({
      where: {
        deliverymanSubscriptionArn: {
          equals: null,
        },
        profileId: deliveryManProfileId,
        active: true,
      },
    });

    return result;

    // const profileSubQuery = dataSource
    //   .getRepository(UserProfile)
    //   .createQueryBuilder("user_profile")
    //   .leftJoin("user_profile.user", "user")
    //   .addSelect("user.id", "user_id")
    //   .innerJoin("user_profile.profile", "profile_db")
    //   .where(`LOWER(profile_db.name) LIKE LOWER('%${profile}%')`);

    // const devices = await this.database
    //   .createQueryBuilder("devices")
    //   .innerJoin(
    //     `(${profileSubQuery.getQuery()})`,
    //     "profile_sub_query",
    //     "profile_sub_query.user_id = devices.userId"
    //   )
    //   .where(
    //     "devices.subscriptionArn IS NULL OR devices.subscriptionArn NOT LIKE :topic",
    //     { topic: `${topicArn}:%` }
    //   )
    //   .getMany();
  }

  async updateDevice(device: IDevice): Promise<number> {
    const result = await this.database.$updateMany({
      where: { id: device.id },
      data: { ...device, userId: undefined },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async getByDeviceId(deviceId: string): Promise<IDevice[]> {
    const device = await this.database.findMany({
      where: { deviceId },
    });

    return device;
  }

  async updateDeviceLanguage(device: IDevice): Promise<number> {
    const result = await this.database.$update({
      data: {
        language: device.language,
      },
      where: { id: device.id },
      userId: this.userContext.userId,
    });

    return result ? 1 : 0;
  }

  async deleteUserDeviceRelation(userIds: string[], client?: IDeviceClient): Promise<number> {
    const database = this.getDatabaseClient<IDeviceClient>(client);

    const result = await database.$deleteMany({
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }

  async getUsersDevicesByUserId(usersId: string[]): Promise<IDevice[] | null> {
    const result = await this.database.findMany({
      where: {
        userId: {
          in: usersId,
        },
        active: true,
      },
    });

    if (result) return result;

    return null;
  }

  async getUserDevicesByUserId(userId: string): Promise<IDevice[] | null> {
    const result = await this.database.findMany({
      where: {
        userId,
        active: true,
      },
    });

    if (result) return result;

    return null;
  }

  async disableOldDevices(userId: string, deviceId: string, profileId: string): Promise<void> {
    await this.database.updateMany({
      data: { active: false },
      where: { AND: [{ userId }, { deviceId: { not: deviceId } }, { profileId }] },
    });
  }
}
