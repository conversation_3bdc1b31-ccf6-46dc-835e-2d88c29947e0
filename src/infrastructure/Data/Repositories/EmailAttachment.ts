import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IEmailAttachment } from "src/business/Interfaces/Prisma/IEmailAttachment";
import {
  IEmailAttachmentClient,
  IEmailAttachmentRepository,
} from "src/business/Interfaces/Repository/IEmailAttachment";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class EmailAttachmentRepository
  extends BaseRepository<
    IEmailAttachment,
    Prisma.EmailAttachmentFindManyArgs,
    Prisma.EmailAttachmentFindUniqueOrThrowArgs,
    Prisma.EmailAttachmentUpdateManyArgs,
    Prisma.EmailAttachmentDeleteManyArgs,
    Prisma.EmailAttachmentCreateArgs
  >
  implements IEmailAttachmentRepository
{
  private database: IEmailAttachmentClient;

  constructor() {
    super("emailAttachment");
    this.database = this.databaseModel;
  }

  async create(data: IEmailAttachment): Promise<IEmailAttachment | null> {
    const messageContentId = data.messageContentId || data.messageContent?.id;

    const result = await this.database.$create({
      data: {
        ...data,
        messageContentId: undefined,
        messageContent: {
          connect: messageContentId
            ? {
                id: messageContentId,
              }
            : undefined,
        },
      },
      userId: this.userContext.userId,
    });

    return result;
  }
}
