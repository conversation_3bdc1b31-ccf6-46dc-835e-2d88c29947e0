import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import {
  IAttributeOptionClient,
  IAttributeOptionRepository,
} from "src/business/Interfaces/Repository/IAttributeOption";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class AttributeOptionRepository
  extends BaseRepository<
    IAttributeOption,
    Prisma.AttributeOptionFindManyArgs,
    Prisma.AttributeOptionFindUniqueOrThrowArgs,
    Prisma.AttributeOptionUpdateManyArgs,
    Prisma.AttributeOptionDeleteManyArgs,
    Prisma.AttributeOptionInclude
  >
  implements IAttributeOptionRepository
{
  database: IAttributeOptionClient;

  constructor() {
    super("attributeOption");
    this.database = this.databaseModel;
  }

  async create(attributeOption: IAttributeOption): Promise<IAttributeOption | null> {
    const result = await this.database.$create({
      data: { ...attributeOption, attribute: undefined },
      userId: this.userContext.userId,
    });
    return result;
  }
}
