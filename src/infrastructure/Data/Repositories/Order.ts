/* eslint-disable object-shorthand */
/* eslint-disable no-underscore-dangle */
/* eslint-disable camelcase */
import { EPayoutOwner, EPayoutStatus, Prisma } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IExportOrdersReportDTO } from "src/business/DTOs/Order/IExportOrdersReport";
import { IOrderByStatusDTO } from "src/business/DTOs/Order/IOrderByStatus";
import { IOrderDeliverymanDetailsDTO, IOrderDetailsDTO } from "src/business/DTOs/Order/IOrderDetails";
import { IOrderDetailsBackOfficeDTO } from "src/business/DTOs/Order/IOrderDetailsBackOffice";
import { IOrderUsersDTO } from "src/business/DTOs/Order/IOrderUsers";
import { IUserOrdersDTO } from "src/business/DTOs/Order/IUserOrders";
import { PagedResult, Totalizer } from "src/business/DTOs/PagedResult";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IOrderClient, IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IPayoutRepository } from "src/business/Interfaces/Repository/IPayout";
import OrderStatusTypeSingleton from "src/business/Singletons/OrderStatusType";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class OrderRepository
  extends BaseRepository<
    IOrder,
    Prisma.OrderFindManyArgs,
    Prisma.OrderFindUniqueOrThrowArgs,
    Prisma.OrderUpdateManyArgs,
    Prisma.OrderDeleteManyArgs,
    Prisma.OrderInclude
  >
  implements IOrderRepository
{
  database: IOrderClient;

  constructor(
    @inject(TOKENS.OrderStatusTypeSingleton)
    private orderStatusTypeSingleton: OrderStatusTypeSingleton,
    @inject(TOKENS.IPayoutRepository)
    private payoutRepository: IPayoutRepository,
  ) {
    super("order");
    this.database = this.databaseModel;
  }

  async create(data: IOrder): Promise<IOrder> {
    const result = await this.transaction<IOrder>(async (client) => {
      const orderCreated = await client.order.$create({
        data: {
          ...data,
          userId: undefined,
          addressId: undefined,
          storeId: undefined,
          deliverymanId: undefined,
          orderItem: undefined,
          deliveryman: undefined,
          transaction: undefined,
          review: undefined,
          financialConsolidation: undefined,
          store: {
            connect: {
              id: data.storeId,
            },
          },
          address: {
            connect: {
              id: data.addressId,
            },
          },
          user: {
            connect: {
              id: data.userId,
            },
          },
          orderStatus: {
            createMany: {
              data: data.orderStatus || [],
            },
          },
        },
        userId: this.userContext.userId,
      });

      if (orderCreated && data.orderItem) {
        // TODO Maybe add validation to check if attribute is simple or multiple selection

        await Promise.all(
          data.orderItem.map(async (orderItem) => {
            await client.orderItem.$create({
              data: {
                ...orderItem,
                productId: undefined,
                orderId: undefined,
                product: {
                  connect: {
                    id: orderItem.productId,
                  },
                },
                order: {
                  connect: {
                    id: orderCreated.id,
                  },
                },
                orderItemProductAttributeOption: {
                  createMany: {
                    data: orderItem.orderItemProductAttributeOption
                      ? orderItem.orderItemProductAttributeOption.map((orderItemProductAttributeOption) => ({
                          productAttributeOptionId: orderItemProductAttributeOption.productAttributeOptionId,
                        }))
                      : [],
                  },
                },
              },
              userId: this.userContext.userId,
            });
          }),
        );
      }

      return orderCreated;
    });

    if (typeof result === "boolean") {
      throw new Error("Error on order creation");
    }

    return result;
  }

  async getProductAttributeOptionIds(
    productId: string,
    attributeId: string[],
    attributeOptionId: string[],
  ): Promise<{ id: string }[]> {
    const data = await this.databaseClient.productAttributeOption.findMany({
      where: {
        attributeOptionId: {
          in: attributeOptionId,
        },
        productAttribute: {
          productId,
          attributeId: {
            in: attributeId,
          },
        },
      },
      select: {
        id: true,
      },
    });

    return data;

    // .getRepository(Product)
    // .createQueryBuilder("product")
    // .leftJoin("product.productAttribute", "productAttribute")
    // .where("productAttribute.productId = :productId", { productId })
    // .andWhere("productAttribute.attributeId IN (:...attributeId)", {
    //   attributeId,
    // })
    // .leftJoin("productAttribute.productAttributeOption", "productAttributeOption")
    // .andWhere("productAttributeOption.attributeOptionId IN (:...attributeOptionId)", { attributeOptionId })
    // .select("productAttributeOption.id", "productAttributeOptionId")
    // .getRawMany();
  }

  async getOrdersByStatus(
    storeId: string,
    currentPage: number,
    pageSize: number,
    status?: string,
  ): Promise<PagedResult<IOrderByStatusDTO>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const whereCondition: Prisma.OrderWhereInput = {
      storeId,
      orderStatus: {
        some: {
          current: true,
          orderStatusType: {
            value: status,
          },
        },
      },
    };

    const data = await this.database.findMany({
      where: whereCondition,
      select: {
        id: true,
        code: true,
        shippingPrice: true,
        totalPrice: true,
        createdAt: true,
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          select: {
            createdAt: true,
            orderStatusType: {
              select: {
                value: true,
              },
            },
          },
          take: 1,
        },
        orderItem: {
          select: {
            quantity: true,
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      /*  include: {
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          include: {
            orderStatusType: true,
          },
        },
        orderItem: {
          include: {
            product: true,
            orderItemProductAttributeOption: {
              include: {
                productAttributeOption: {
                  include: {
                    attributeOption: true,
                    productAttribute: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      }, */
      orderBy: {
        code: "desc",
      },
      skip: skipIndex,
      take: pageSize,
    });

    const totalCount = await this.database.count({
      where: whereCondition,
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: data,
      totalCount,
      totalPages,
    };
  }

  async getAllPaymentMadeStatus(): Promise<IOrder[]> {
    const data = await this.database.findMany({
      where: {
        orderStatus: {
          some: {
            orderStatusType: {
              value: "Pagamento efetuado",
            },
          },
        },
      },
      include: {
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
          include: {
            orderStatusType: true,
          },
        },
        address: true,
        orderItem: {
          include: {
            product: true,
            orderItemProductAttributeOption: {
              include: {
                productAttributeOption: {
                  include: {
                    attributeOption: true,
                    productAttribute: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return data;
  }

  async getOrdersByStatusAndDeliverymanId(
    userId: string,
    currentPage: number,
    pageSize: number,
    status?: string,
  ): Promise<PagedResult<IOrderByStatusDTO>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    let deliveryman;

    if (status && status === this.orderStatusTypeSingleton.waiting_for_the_delivery_person.value) {
      deliveryman = undefined;
    } else {
      deliveryman = { userId: userId };
    }

    const where = {
      orderStatus: {
        some: {
          current: true,
          orderStatusType: {
            value: status,
          },
        },
      },
      deliveryman,
    };

    const data = await this.databaseClient.order.findMany({
      where,
      select: {
        id: true,
        code: true,
        totalPrice: true,
        shippingPrice: true,
        createdAt: true,
        orderItem: {
          select: {
            quantity: true,
            product: {
              select: {
                name: true,
              },
            },
          },
        },
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          select: {
            createdAt: true,
            orderStatusType: {
              select: {
                value: true,
              },
            },
          },
          take: 1,
        },
      },

      /* include: {
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          include: {
            orderStatusType: true,
          },
        },
        address: true,
        orderItem: {
          include: {
            product: true,
            orderItemProductAttributeOption: true,
          },
        },
      }, */
      skip: skipIndex,
      take: pageSize,
    });

    const totalCount = await this.database.count({
      where,
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result: data,
      totalCount,
      totalPages,
    };

    // const orders = await this.DBLink.createQueryBuilder("orders")
    //   .where("orders.userDeliveryman = :deliveryId", { deliveryId })
    //   .leftJoinAndSelect("orders.orderStatus", "orderStatus")
    //   .leftJoin("orders.orderStatus", "next_status", '"orderStatus"."createdAt" < next_status."createdAt"')
    //   .andWhere("next_status.id IS NULL")
    //   .leftJoinAndSelect("orderStatus.orderStatusType", "orderStatusType", '"orderStatusType".value LIKE :status', {
    //     status,
    //   })
    //   .andWhere('"orderStatusType".id IS NOT NULL')
    //   .leftJoinAndSelect("orders.address", "address")
    //   .leftJoinAndSelect("orders.orderItem", "orderItem")
    //   .leftJoinAndSelect("orderItem.product", "product")
    //   .leftJoinAndSelect("orderItem.orderItemProductAttributeOption", "orderItemProductAttributeOption")
    //   .leftJoinAndSelect("orderItemProductAttributeOption.productAttributeOption", "productAttributeOption")
    //   .leftJoinAndSelect("productAttributeOption.productAttribute", "productAttribute")
    //   .leftJoinAndSelect("productAttributeOption.attributeOption", "attributeOption")
    //   .leftJoinAndSelect("productAttribute.attribute", "attribute")
    //   .leftJoinAndSelect("orders.store", "storeOrders")
    //   .orderBy("orders.createdAt", "DESC")
    //   .andWhere('DATE_TRUNC(\'day\', "orders"."createdAt") = :currentDate', {
    //     currentDate,
    //   })
    //   .getMany();

    //   console.log(orders);
    // return orders;
  }

  async getOrdersByUserId(userId: string, currentPage: number, pageSize: number): Promise<PagedResult<IUserOrdersDTO>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;

    const result = await this.databaseClient.order.findMany({
      where: {
        userId,
      },
      select: {
        id: true,
        code: true,
        totalPrice: true,
        createdAt: true,
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          select: {
            orderStatusType: {
              select: {
                value: true,
              },
            },
          },
          take: 1,
        },
        review: {
          select: {
            rate: true,
            storeId: true,
            deliverymanId: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        deliveryman: {
          select: {
            id: true,
          },
        },
        orderItem: {
          select: {
            id: true,
            quantity: true,
            observation: true,
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        code: "desc",
      },
      skip: skipIndex,
      take: pageSize,
    });

    const totalCount = await this.databaseClient.order.count({
      where: {
        userId,
      },
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
    };

    // const data = this.DBLink.createQueryBuilder("orders")
    // .where("orders.userId = :userId", { userId })
    // .leftJoinAndSelect("orders.orderItem", "orderItem")
    // .leftJoinAndSelect("orders.store", "store")
    // .leftJoinAndMapMany("store.files", "files", "store_file", 'store_file."entityId"= store.id')
    // .leftJoinAndSelect("orders.address", "address")
    // .leftJoinAndSelect("orders.deliveryman", "deliveryman")
    // .leftJoinAndSelect("deliveryman.deliveryman", "userDeliveryman")
    // .leftJoinAndSelect("userDeliveryman.reviewDeliveryman", "reviewDeliveryman")
    // .leftJoinAndSelect("orders.reviewOrder", "review")
    // .leftJoinAndSelect("review.deliveryman", "review_Deliveryman")
    // .leftJoinAndSelect("orders.orderStatus", "orderStatus")
    // .leftJoinAndSelect("orderStatus.orderStatusType", "orderStatusType")
    // .leftJoinAndSelect("orderItem.product", "product")
    // .leftJoinAndSelect("orderItem.orderItemProductAttributeOption", "orderItemPAO")
    // .leftJoinAndSelect("orderItemPAO.productAttributeOption", "productAttributeOption")
    // .leftJoinAndSelect("productAttributeOption.productAttribute", "productAttribute")
    // .leftJoinAndSelect("productAttributeOption.attributeOption", "attributeOption")
    // .leftJoinAndSelect("productAttribute.attribute", "attribute")
    // .leftJoin("orders.orderStatus", "OS", "orderStatus.createdAt < OS.createdAt")
    // .orderBy("orders.createdAt", "DESC");
  }

  async getOrdersToExport(deliveredStatusId: string): Promise<IExportOrdersReportDTO[]> {
    const data = await this.databaseClient.$queryRawUnsafe<IExportOrdersReportDTO[]>(
      `
      SELECT "order"."code" AS "orderCode",
      "order"."createdAt" AS "orderCreatedAt",
      "order"."price" AS "orderPrice",
      "order"."shippingPrice" AS "orderShippingPrice",
      "order"."totalPrice" AS "orderTotalPrice",
      "order"."quantityItems" AS "orderQuantityItems",
      "order"."quantityProducts" AS "orderQuantityProducts",
      "transactions"."paymentMethod" AS "paymentMethod",
      "orderItem"."quantity" AS "orderItemQuantity",
      "orderItem"."unitPrice" AS "orderItemUnityPrice",
      "orderItem"."totalPrice" AS "orderItemTotalPrice",
      "orderItem"."observation" AS "orderItemObservation",
      "attributeOption"."value" AS "attributeOptionValue",
      "attribute"."name" AS "attributeName",
      "product"."name" AS "productName",
      "product"."salePrice" AS "productSalePrice",
      "category"."name" AS "categoryName",
      "store"."name" AS "storeName",
      "store"."cnpj" AS "storeCnpj"
      FROM "orders" "order"
      LEFT JOIN "order_items" "orderItem" ON "orderItem"."orderId"="order"."id"
      LEFT JOIN "transactions" "transactions" ON "transactions"."orderId"="order"."id"
      LEFT JOIN "orders_items_products_attributes_options" "orderItemPAO" ON "orderItemPAO"."orderItemId"="orderItem"."id"
      LEFT JOIN "products_attributes_options" "pao" ON "pao"."id"="orderItemPAO"."productAttributeOptionId"
      LEFT JOIN "attributes_options" "attributeOption" ON "attributeOption"."id"="pao"."attributeOptionId"
      LEFT JOIN "attributes" "attribute" ON "attribute"."id"="attributeOption"."attributeId"
      LEFT JOIN "products" "product" ON "product"."id"="orderItem"."productId"
      LEFT JOIN "products_categories" "pCategory" ON "pCategory"."productId"="product"."id"
      LEFT JOIN "categories" "category" ON "category"."id"="pCategory"."categoryId"
      LEFT JOIN "orders_status" "orderStatus" ON "orderStatus"."orderId"="order"."id"
      LEFT JOIN "stores" "store" ON "store"."id"="order"."storeId"
      LEFT JOIN "addresses" "address" ON "address"."id"="order"."addressId"
      LEFT JOIN "order_status_types" "orderStatusType" ON "orderStatusType"."id"="orderStatus"."orderStatusTypeId"
      INNER JOIN
        (SELECT "orders_status"."id" AS "os_id",
                "orders_status"."orderId" AS "os_order_id"
        FROM "orders_status" "orders_status"
        WHERE "orders_status"."orderStatusTypeId" = CAST($1 AS UUID)) "sub_status" ON sub_status.os_order_id = "order"."id"
        ORDER BY "orderCode" ASC
    `,
      deliveredStatusId,
    );

    return data;

    // const data: ExportOrdersReport[] = await dataSource.query(
    //   `
    //  SELECT "order"."code" AS "orderCode",
    //  "order"."createdAt" AS "orderCreatedAt",
    //  "order"."price" AS "orderPrice",
    //  "order"."shippingPrice" AS "orderShippingPrice",
    //  "order"."totalPrice" AS "orderTotalPrice",
    //  "order"."quantityItems" AS "orderQuantityItems",
    //  "order"."quantityProducts" AS "orderQuantityProducts",
    //  "transactions"."paymentMethod" AS "paymentMethod",
    //  "orderItem"."quantity" AS "orderItemQuantity",
    //  "orderItem"."unityPrice" AS "orderItemUnityPrice",
    //  "orderItem"."totalPrice" AS "orderItemTotalPrice",
    //  "orderItem"."observation" AS "orderItemObservation",
    //  "attributeOption"."value" AS "attributeOptionValue",
    //  "attribute"."name" AS "attributeName",
    //  "product"."name" AS "productName",
    //  "product"."sale_price" AS "productSalePrice",
    //  "category"."name" AS "categoryName",
    //  "store"."name" AS "storeName",
    //  "store"."cnpj" AS "storeCnpj",
    //  "address"."district" AS "addressDistrict"
    //   FROM "orders" "order"
    //   LEFT JOIN "order_items" "orderItem" ON "orderItem"."orderId"="order"."id"
    //   LEFT JOIN "transactions" "transactions" ON "transactions"."orderId"="order"."id"
    //   LEFT JOIN "orders_items_products_attributes_options" "orderItemPAO" ON "orderItemPAO"."orderItemId"="orderItem"."id"
    //   LEFT JOIN "products_attributes_options" "pao" ON "pao"."id"="orderItemPAO"."productAttributeOptionId"
    //   LEFT JOIN "attributes_options" "attributeOption" ON "attributeOption"."id"="pao"."attributeOptionId"
    //   LEFT JOIN "attributes" "attribute" ON "attribute"."id"="attributeOption"."attributeId"
    //   LEFT JOIN "products" "product" ON "product"."id"="orderItem"."productId"
    //   LEFT JOIN "products_categories" "pCategory" ON "pCategory"."productId"="product"."id"
    //   LEFT JOIN "categories" "category" ON "category"."id"="pCategory"."categoryId"
    //   LEFT JOIN "orders_status" "orderStatus" ON "orderStatus"."orderId"="order"."id"
    //   LEFT JOIN "stores" "store" ON "store"."id"="order"."storeId"
    //   LEFT JOIN "addresses" "address" ON "address"."id"="order"."addressId"
    //   LEFT JOIN "order_status_types" "orderStatusType" ON "orderStatusType"."id"="orderStatus"."orderStatusTypeId"
    //   INNER JOIN
    //     (SELECT "orders_status"."id" AS "os_id",
    //             "orders_status"."orderId" AS "os_order_id"
    //     FROM "orders_status" "orders_status"
    //     WHERE "orders_status"."orderStatusTypeId" = '${deliveredId}') "sub_status" ON sub_status.os_order_id = "order"."id"
    //     ORDER BY "orderCode" ASC
    //       `,
    // );
  }

  async getOrdersPaged(
    currentPage: string,
    pageSize: string,
    storeFilter: string,
    deliveredStatusId?: string,
  ): Promise<PagedResult<IOrder>> {
    const { currentPageInt, pageSizeInt } = this.getPageSizeAndCurrentPage(pageSize, currentPage);

    const skip = (currentPageInt - 1) * pageSizeInt;
    const take = pageSizeInt;

    const result = await this.databaseClient.order.findMany({
      where: {
        AND: [
          {
            OR: [
              {
                store: {
                  name: {
                    contains: storeFilter,
                    mode: "insensitive",
                  },
                },
              },
              {
                deliveryman: {
                  user: {
                    firstName: { equals: storeFilter },
                  },
                },
              },
              {
                user: {
                  firstName: { equals: storeFilter },
                },
              },
            ],
          },
          {
            orderStatus: {
              some: deliveredStatusId
                ? {
                    orderStatusTypeId: deliveredStatusId,
                  }
                : undefined,
            },
          },
        ],
      },
      // TODO Remove include (request data when needed)
      include: {
        store: true,
        address: true,
        user: true,
        deliveryman: {
          include: {
            user: true,
          },
        },
        orderStatus: {
          include: {
            orderStatusType: true,
          },
        },
        orderItem: {
          include: {
            orderItemProductAttributeOption: true,
            product: true,
          },
        },
      },
      skip,
      take,
    });

    const totalCount = await this.databaseClient.order.count({
      where: {
        store: {
          name: {
            contains: storeFilter,
            mode: "insensitive",
          },
        },
      },
    });

    const totalPages = Math.ceil(totalCount / pageSizeInt);

    return {
      result,
      totalCount,
      totalPages,
    };

    // const subQueryStatus = dataSource
    //   .getRepository(OrderStatus)
    //   .createQueryBuilder("orders_status")
    //   .select("orders_status.id", "os_id")
    //   .addSelect("orders_status.orderId", "os_order_id")
    //   .where(`orders_status.orderStatusTypeId = '${deliveredId}'`);

    // const data = this.DBLink.createQueryBuilder("order")
    //   .leftJoinAndSelect("order.store", "store")
    //   .where(storeFilter ? `LOWER(store.name) LIKE LOWER('%${storeFilter}%')` : "store.name IS NOT NULL")
    //   .leftJoinAndSelect("order.orderItem", "orderItem")
    //   .leftJoinAndSelect("orderItem.product", "product")
    //   .leftJoinAndSelect("order.address", "address")
    //   .leftJoinAndSelect("order.user", "user")
    //   .leftJoinAndSelect("order.deliveryman", "deliveryman")
    //   .leftJoinAndSelect("deliveryman.deliveryman", "userDeliveryman")
    //   .leftJoinAndSelect("order.orderStatus", "orderStatus")
    //   .leftJoinAndSelect("orderStatus.orderStatusType", "orderStatusType");

    // if (deliveredId) {
    //   data.innerJoin(`(${subQueryStatus.getQuery()})`, "sub_status", "sub_status.os_order_id = order.id");
    // }

    // const totalCount = await data.getCount();

    // const result = await data.take(pageSize).skip(skipIndex).getMany();

    // const totalPages = Math.ceil(totalCount / pageSize);
    // return {
    //   result,
    //   totalCount,
    //   totalPages,
    // };
  }

  async getOrderWithItems(id: string): Promise<IOrder | null> {
    const result = await this.databaseClient.order.findUnique({
      where: {
        id,
      },
      include: {
        orderItem: {
          include: {
            product: true,
            orderItemProductAttributeOption: true,
          },
        },
        address: true,
        store: true,
      },
    });

    return result;
    // const order = await this.DBLink.createQueryBuilder("orders")
    //   .leftJoinAndSelect("orders.store", "stores")
    //   .leftJoinAndSelect("orders.address", "addresses")
    //   .leftJoinAndSelect("orders.orderItem", "order_items")
    //   .leftJoinAndSelect("order_items.product", "products")
    //   .where("orders.id = :orderId", { orderId })
    //   .getOne();
  }

  async getOrderWithStore(id: string): Promise<IOrder | null> {
    const result = await this.databaseClient.order.findUnique({
      where: {
        id,
      },
      include: {
        store: {
          include: {
            storeUsers: true,
          },
        },
      },
    });

    // FIXME Verificar retorno do prisma e construir objeto corretamente
    return result;
    // const order = await this.DBLink.createQueryBuilder("orders")
    //   .leftJoinAndSelect("orders.store", "stores")
    //   .leftJoinAndSelect("orders.address", "addresses")
    //   .leftJoinAndSelect("orders.orderItem", "order_items")
    //   .leftJoinAndSelect("order_items.product", "products")
    //   .where("orders.id = :orderId", { orderId })
    //   .getOne();
  }

  async getUsers(id: string): Promise<IOrderUsersDTO | null> {
    const result = this.findOne({
      where: {
        id,
      },
      select: {
        userId: true,
        deliverymanId: true,
        store: {
          select: {
            storeUsers: {
              select: {
                userId: true,
              },
            },
          },
        },
      },
    });

    // FIXME Verificar retorno do prisma e construir objeto corretamente
    return result as any;
  }

  async getCode(id: string): Promise<number | null> {
    const result = await this.database.findUnique({
      where: {
        id,
      },
      select: {
        code: true,
      },
    });

    if (result?.code) {
      return result.code;
    }

    return null;
  }

  async getFinancialConsolidationPaged(
    page: number,
    pageSize: number,
    startDateFilter?: Date,
    endDateFilter?: Date,
    filterValue?: string,
  ): Promise<PagedResult<IOrder>> {
    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const numberFilter = filterValue?.includes(",") ? Number(filterValue.replace(",", ".")) : Number(filterValue);

    let whereCondition: object | undefined = {
      createdAt: {
        lte: endDateFilter,
        gte: startDateFilter,
      },
    };

    if (filterValue) {
      whereCondition = {
        ...whereCondition,
        OR: [
          {
            deliveryman: {
              user: {
                firstName: filterValue,
              },
            },
          },
          {
            store: {
              name: { contains: filterValue },
            },
          },
          !Number.isNaN(numberFilter) ? { price: { equals: numberFilter } } : {},
          !Number.isNaN(numberFilter) ? { totalPrice: { equals: numberFilter } } : {},
          !Number.isNaN(numberFilter) ? { shippingPrice: { equals: numberFilter } } : {},
        ],
      };
    }

    const result = await this.databaseClient.order.findMany<IOrder>({
      take,
      skip,
      orderBy: { createdAt: "desc" },
      include: {
        store: true,
        orderItem: {
          include: {
            orderItemProductAttributeOption: true,
            product: true,
          },
        },
        deliveryman: {
          include: {
            user: { select: { firstName: true, lastName: true } },
          },
        },
        transaction: true,
      },
      where: whereCondition,
    });

    const totalCount = await this.database.count({
      where: whereCondition,
    });

    const totalizer = await this.getTotalOrderAmount(whereCondition);

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      result,
      totalCount,
      totalPages,
      totalizer: totalizer,
    };
  }

  async getTotalOrderAmount(whereCondition: object | undefined): Promise<Totalizer> {
    const totalizer = await this.database.aggregate({
      where: whereCondition,
      _sum: {
        totalPrice: true,
        price: true,
        shippingPrice: true,
      },
    });

    return totalizer._sum;
  }

  async getTotalPayoutOrderAmount(whereCondition: object | undefined): Promise<Totalizer> {
    const totalizer = await this.database.aggregate({
      where: whereCondition,
      _sum: {
        shippingPrice: true,
        price: true,
      },
      _count: {
        id: true,
      },
    });

    return {
      shippingPriceSum: totalizer._sum.shippingPrice,
      priceSum: totalizer._sum.price,
      orderCount: totalizer._count.id,
    };
  }

  async getOneFinancialConsolidationOrder(orderId: string): Promise<IOrder | null> {
    const response = await this.databaseClient.order.findFirst({
      include: {
        store: true,
        orderItem: {
          include: {
            orderItemProductAttributeOption: true,
            product: true,
          },
        },
        deliveryman: {
          include: {
            user: true,
          },
        },
        transaction: true,
      },
      where: {
        id: orderId,
      },
    });

    return response;
  }

  async updateUserFkInOrder(updatedId: string, userIds: string[], client?: IOrderClient): Promise<number> {
    const database = this.getDatabaseClient<IOrderClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }

  async getOrderDetailsById(orderId: string): Promise<IOrderDetailsDTO | null> {
    const result = await this.database.findFirst({
      where: {
        id: orderId,
      },
      select: {
        id: true,
        code: true,
        totalPrice: true,
        shippingPrice: true,
        customerCode: true,
        estimatedDeliveryTime: true,
        user: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          select: {
            id: true,
            createdAt: true,
            orderStatusType: true,
            current: true,
            observation: true,
          },
        },
        deliveryman: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
            review: {
              select: {
                rate: true,
              },
            },
          },
        },
        address: {
          select: {
            id: true,
            nickname: true,
            street: true,
            number: true,
            district: true,
            city: true,
            state: true,
            complement: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        orderItem: {
          include: {
            product: true,
            orderItemProductAttributeOption: {
              include: {
                productAttributeOption: {
                  include: {
                    attributeOption: true,
                    productAttribute: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return result;
  }

  async getSalesDeliverymanTotalizer(whereCondition: object | undefined): Promise<Totalizer> {
    const payoutsAvailableTotalizer = await this.payoutRepository.getPayoutsValueTotalizer(
      whereCondition,
      EPayoutOwner.deliveryman,
      EPayoutStatus.available,
    );

    const payoutsExecutedTotalizer = await this.payoutRepository.getPayoutsValueTotalizer(
      whereCondition,
      EPayoutOwner.deliveryman,
      EPayoutStatus.executed,
    );

    const totalizer = await this.database.aggregate({
      where: whereCondition,
      _sum: {
        routeLength: true,
        totalPrice: true,
        price: true,
        shippingPrice: true,
      },
      _count: { id: true },
    });

    return {
      ...totalizer._count,
      ...totalizer._sum,
      payoutsAvailable: payoutsAvailableTotalizer,
      payoutsExecuted: payoutsExecutedTotalizer,
    };
  }

  async getSalesShopkeeperTotalizer(whereCondition: object | undefined): Promise<Totalizer> {
    const payoutsAvailableTotalizer = await this.payoutRepository.getPayoutsValueTotalizer(
      whereCondition,
      EPayoutOwner.store,
      EPayoutStatus.available,
    );

    const payoutsExecutedTotalizer = await this.payoutRepository.getPayoutsValueTotalizer(
      whereCondition,
      EPayoutOwner.store,
      EPayoutStatus.executed,
    );

    const totalizer = await this.database.aggregate({
      where: whereCondition,
      _sum: {
        totalPrice: true,
        price: true,
        shippingPrice: true,
      },
      _count: { id: true },
    });

    return {
      ...totalizer._count,
      ...totalizer._sum,
      payoutsAvailable: payoutsAvailableTotalizer,
      payoutsExecuted: payoutsExecutedTotalizer,
    };
  }

  async getDeliverymanUserByOrderId(orderId: string): Promise<IOrderDeliverymanDetailsDTO | null> {
    const result = await this.database.findFirst({
      where: {
        id: orderId,
      },
      select: {
        id: true,
        deliveryman: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    return result;
  }

  async getOrderBackOfficeDetails(orderId: string, isSales?: boolean): Promise<IOrderDetailsBackOfficeDTO | null> {
    const result = await this.database.findFirst({
      where: {
        id: orderId,
      },
      select: {
        id: true,
        code: true,
        totalPrice: true,
        shippingPrice: true,
        price: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
          },
        },
        orderStatus: {
          orderBy: {
            createdAt: "desc",
          },
          select: {
            id: true,
            createdAt: true,
            orderStatusType: true,
            current: true,
            observation: true,
          },
        },
        deliveryman: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
                cpf: true,
              },
            },
          },
        },
        address: {
          select: {
            id: true,
            nickname: true,
            street: true,
            number: true,
            district: true,
            city: true,
            state: true,
            complement: true,
            country: true,
            postcode: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
            cnpj: true,
            phone: true,
            email: true,
          },
        },
        orderItem: {
          select: isSales
            ? {
                id: true,
                observation: true,
                quantity: true,
                totalPrice: true,
                unitPrice: true,
                orderId: true,
                productId: true,
                product: true,
              }
            : undefined,
        },
      },
    });

    return result;
  }

  async getLastOrderIdByUserIdAndStoreId(userId: string, storeId: string): Promise<string | null> {
    const result = await this.database.findFirst({
      where: {
        userId: userId,
        storeId: storeId,
      },
      select: {
        id: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
    console.log("result?.id", result?.id);
    return result ? result.id : null;
  }

  async deleteOrderItem(orderId: string): Promise<void> {
    await this.databaseClient.$queryRawUnsafe(
      `
      DELETE FROM "order_items"
      WHERE "orderId" = CAST($1 AS UUID)
      `,
      orderId,
    );
  }

  async createOrderItem(orderId: string, data: IOrder): Promise<boolean | void> {
    const result = await this.transaction(async (client) => {
      if (data.orderItem) {
        await Promise.all(
          data.orderItem.map(async (orderItem) => {
            await client.orderItem.$create({
              data: {
                ...orderItem,
                productId: undefined,
                orderId: undefined,
                product: {
                  connect: {
                    id: orderItem.productId,
                  },
                },
                order: {
                  connect: {
                    id: orderId,
                  },
                },
                orderItemProductAttributeOption: {
                  createMany: {
                    data: orderItem.orderItemProductAttributeOption
                      ? orderItem.orderItemProductAttributeOption.map((orderItemProductAttributeOption) => ({
                          productAttributeOptionId: orderItemProductAttributeOption.productAttributeOptionId,
                        }))
                      : [],
                  },
                },
              },
              userId: this.userContext.userId,
            });
          }),
        );
      }
    });
    return result;
  }
}
