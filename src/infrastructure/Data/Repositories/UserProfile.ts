import { <PERSON>risma } from "@prisma/client";
import { injectable } from "inversify";
import { IUserProfile } from "src/business/Interfaces/Prisma/IUserProfile";
import { IUserProfileClient, IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class UserProfileRepository
  extends BaseRepository<
    IUserProfile,
    Prisma.UserProfileFindManyArgs,
    Prisma.UserProfileFindUniqueOrThrowArgs,
    Prisma.UserProfileUpdateManyArgs,
    Prisma.UserProfileDeleteManyArgs,
    Prisma.UserProfileInclude
  >
  implements IUserProfileRepository
{
  database: IUserProfileClient;

  constructor() {
    super("userProfile");
    this.database = this.databaseModel;
  }

  async updateUserProfile(userId: string, profileId: string, client?: IUserProfileClient): Promise<void> {
    const database = this.getDatabaseClient<IUserProfileClient>(client);

    const userProfile = await database.findMany({
      where: { userId, profileId },
    });

    if (userProfile.length === 0) {
      await database.$create({ data: { userId, profileId }, userId: this.userContext.userId });
    }
  }

  async clearRelations(userId: string): Promise<number> {
    const result = await this.database.$deleteMany({
      where: { userId },
      userId: this.userContext.userId,
    });

    return result.count;
  }

  async getUsersIdsByDeliveryProfile(): Promise<IUserProfile[]> {
    const result = await this.database.findMany({
      where: { profile: { name: "deliveryman" } },
    });

    return result;
  }

  async updateUserFkInUserProfile(updatedId: string, userIds: string[], client?: IUserProfileClient): Promise<number> {
    const database = this.getDatabaseClient<IUserProfileClient>(client);

    const result = await database.$updateMany({
      data: { userId: updatedId },
      where: {
        userId: { in: userIds },
      },
    });

    return result.count;
  }
}
