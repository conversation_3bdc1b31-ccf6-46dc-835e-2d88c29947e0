import { Prisma } from "@prisma/client";
import { injectable } from "inversify";
import { IAttributeDTO } from "src/business/DTOs/Attribute/IAttribute";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IAttribute } from "src/business/Interfaces/Prisma/IAttribute";
import { IAttributeClient, IAttributeRepository } from "src/business/Interfaces/Repository/IAttribute";
import { BaseRepository } from "src/infrastructure/Data/Repositories/Base";

@injectable()
export class AttributeRepository
  extends BaseRepository<
    IAttribute,
    Prisma.AttributeFindManyArgs,
    Prisma.AttributeFindUniqueOrThrowArgs,
    Prisma.AttributeUpdateManyArgs,
    Prisma.AttributeDeleteManyArgs,
    Prisma.AttributeInclude
  >
  implements IAttributeRepository
{
  database: IAttributeClient;

  constructor() {
    super("attribute");
    this.database = this.databaseModel;
  }

  async create(item: IAttribute): Promise<IAttribute> {
    const attributeOptionsInput = {
      create: item.attributeOption?.map((item) => ({ value: item.value })),
    };

    const result = await this.database.$create({
      data: {
        ...item,
        attributeOption: attributeOptionsInput,
        productAttribute: undefined,
      },
      userId: this.userContext.userId,
    });

    return result;
  }

  async getWithProductAttributeOption(productId: string): Promise<IAttribute[]> {
    const attribute = await this.database.findMany({
      include: {
        attributeOption: {
          where: {
            productAttributeOption: {
              some: {
                productAttribute: {
                  productId,
                },
              },
            },
          },
        },
      },
      where: {
        productAttribute: {
          some: { productId },
        },
      },
    });

    return attribute;
  }

  async getPagedWithAllAttributeOption(
    productId: string,
    currentPage: number,
    pageSize: number,
    filter: string = "",
  ): Promise<PagedResult<IAttributeDTO>> {
    const page = currentPage > 0 ? currentPage : 1;
    const skipIndex = (page - 1) * pageSize;
    const productAttributeOptionClient = this.databaseClient.productAttributeOption;
    let result: IAttributeDTO[] = [];

    const whereCondition: Prisma.AttributeWhereInput = {
      OR: [
        {
          name: {
            contains: filter,
            mode: "insensitive",
          },
        },
        {
          attributeOption: {
            some: {
              value: {
                contains: filter,
                mode: "insensitive",
              },
            },
          },
        },
      ],
    };

    const attributes: IAttributeDTO[] = await this.database.findMany({
      select: {
        id: true,
        name: true,
        shortDescription: true,
        required: true,
        type: true,
        attributeOption: {
          select: { id: true, value: true },
        },
      },
      where: whereCondition,
      skip: skipIndex,
      take: pageSize,
    });

    if (productId) {
      result = await Promise.all(
        attributes.map(async (attribute) => {
          const newAttributeObject: IAttributeDTO = { ...attribute };

          const productAttribute = await this.database.findFirst({
            where: {
              productAttribute: {
                some: {
                  attributeId: attribute.id,
                  active: true,
                  productId,
                },
              },
            },
          });

          if (productAttribute) newAttributeObject.checked = true;

          const attributeOptions = await productAttributeOptionClient.findMany({
            where: {
              AND: [
                {
                  productAttribute: {
                    attributeId: attribute.id,
                    active: true,
                    productId,
                  },
                },
                {
                  attributeOptionId: {
                    in: attribute.attributeOption.map((x) => x.id),
                  },
                },
              ],
            },
          });

          const attributeOptionsWithChecked = newAttributeObject.attributeOption.map((option) => {
            const attributeOption = attributeOptions.find((attOption) => attOption.attributeOptionId === option.id);

            if (attributeOption && attributeOption.active) {
              option.checked = true;
            } else {
              option.checked = false;
            }

            return option;
          });

          return {
            ...newAttributeObject,
            attributeOption: attributeOptionsWithChecked,
          };
        }),
      );
    } else {
      result = attributes;
    }

    const totalCount = await this.database.count({
      where: whereCondition,
    });

    const totalPages = Math.ceil(totalCount / pageSize);

    result.sort((a, b) => {
      if (a.checked === b.checked) {
        return a.name.localeCompare(b.name);
      }

      return a.checked ? -1 : 1;
    });

    return {
      result,
      totalCount,
      totalPages,
    };
  }

  async getWithAttributeOption(attributeId: string): Promise<IAttribute | null> {
    const attribute = await this.database.findUnique({
      include: { attributeOption: true },
      where: {
        id: attributeId,
      },
    });

    return attribute;
  }
}
