import { ESettingsType } from "@prisma/client";
import { ICreateSettingsDTO } from "src/business/DTOs/Settings/ICreateSettings";

export const settingsData: ICreateSettingsDTO[] = [
  {
    name: "minAmountWithCard",
    value: "10",
    type: ESettingsType.money,
  },
  {
    name: "minutesForPixExpirationDate",
    value: "60",
    type: ESettingsType.int,
  },
  {
    name: "minTimeToGetLocation",
    value: "60000",
    type: ESettingsType.int,
  },
  {
    name: "shippingPrice",
    value: "6",
    type: ESettingsType.money,
  },
  {
    name: "timeToPermanentlyDeleteUser",
    value: "1",
    type: ESettingsType.int,
  },
  {
    name: "maxUserInactiveTime",
    value: "10",
    type: ESettingsType.int,
  },
  {
    name: "daysToNextRate",
    value: "7",
    type: ESettingsType.int,
  },
  {
    name: "administrativeFee",
    value: "1.5",
    type: ESettingsType.decimal,
  },
  {
    name: "cooperativePixKey",
    value: "<EMAIL>",
    type: ESettingsType.string,
  },
  {
    name: "ageAllowed",
    value: "16",
    type: ESettingsType.int,
  },
];
