import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IDefaultPrismaClient } from "src/business/Interfaces/Database/IDefault";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { PrismaClientExtensions } from "src/infrastructure/PrismaClientExtensions";

@injectable()
export class ExtendedPrismaClient implements IExtendedPrismaClient {
  private _client: IExtendedPrismaClient["client"];

  constructor(
    @inject(TOKENS.DefaultPrismaClient) prismaClient: IDefaultPrismaClient,
    @inject(TOKENS.LoggerService) loggerService: ILoggerService,
  ) {
    this._client = PrismaClientExtensions.extends(prismaClient.client, loggerService);
  }

  get client() {
    return this._client;
  }
}
