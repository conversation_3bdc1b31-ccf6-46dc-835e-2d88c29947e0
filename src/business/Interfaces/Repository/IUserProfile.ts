import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IUserProfile } from "src/business/Interfaces/Prisma/IUserProfile";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IUserProfileClient = IExtendedPrismaClient["client"]["userProfile"];

export interface IUserProfileRepository
  extends IBaseRepository<
    IUserProfile,
    Prisma.UserProfileFindManyArgs,
    Prisma.UserProfileFindUniqueOrThrowArgs,
    Prisma.UserProfileUpdateManyArgs,
    Prisma.UserProfileDeleteManyArgs,
    Prisma.UserProfileInclude
  > {
  updateUserProfile: (userId: string, profileId: string, client?: IUserProfileClient) => Promise<void>;
  clearRelations(userId: string): Promise<number>;
  getUsersIdsByDeliveryProfile(): Promise<IUserProfile[]>;
  updateUserFkInUserProfile(updatedId: string, userIds: string[], client?: IUserProfileClient): Promise<number>;
}
