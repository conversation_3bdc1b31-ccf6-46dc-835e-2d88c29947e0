import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IStoreSettings } from "src/business/Interfaces/Prisma/IStoreSettings";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IStoreSettingsClient = IExtendedPrismaClient["client"]["storeSettings"];

export interface IStoreSettingsRepository
  extends IBaseRepository<
    IStoreSettings,
    Prisma.StoreSettingsFindManyArgs,
    Prisma.StoreSettingsFindUniqueOrThrowArgs,
    Prisma.StoreSettingsUpdateManyArgs,
    Prisma.StoreSettingsDeleteManyArgs,
    Prisma.StoreSettingsInclude
  > {
  create(storeSettings: IStoreSettings): Promise<IStoreSettings | null>;
  getSettingsByStoreId(storeId: string): Promise<IStoreSettings | null>;
  updateSetting(storeId: string, setting: IStoreSettings): Promise<IStoreSettings>;
}
