import { EBorderoStatus, Prisma } from "@prisma/client";
import IBorderoListDTO from "src/business/DTOs/Bordero/IBordero";
import IBorderoPaymentDTO from "src/business/DTOs/Bordero/IBorderoPayment";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IBordero } from "src/business/Interfaces/Prisma/IBordero";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IBorderoClient = IExtendedPrismaClient["client"]["bordero"];

export interface IBorderoRepository
  extends IBaseRepository<
    IBordero,
    Prisma.BorderoFindManyArgs,
    Prisma.BorderoFindUniqueOrThrowArgs,
    Prisma.BorderoUpdateManyArgs,
    Prisma.BorderoDeleteManyArgs,
    Prisma.BorderoInclude
  > {
  create(data: IBordero): Promise<IBordero | null>;
  getBorderosPaged(
    currentPage: number,
    pageSize: number,
    filterStatus: EBorderoStatus,
    filterCpfCnpj?: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    orderBy?: string,
    sortDirection?: string,
  ): Promise<PagedResult<IBorderoListDTO>>;
  getBorderoDetails(borderoId: string): Promise<IBorderoPaymentDTO | null>;
}
