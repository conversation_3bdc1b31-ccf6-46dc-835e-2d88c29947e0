import { Prisma } from "@prisma/client";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IOrderStatus } from "src/business/Interfaces/Prisma/IOrderStatus";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IOrderStatusClient = IExtendedPrismaClient["client"]["orderStatus"];

export interface IOrderStatusRepository
  extends IBaseRepository<
    IOrderStatus,
    Prisma.OrderStatusFindManyArgs,
    Prisma.OrderStatusFindUniqueOrThrowArgs,
    Prisma.OrderStatusUpdateManyArgs,
    Prisma.OrderStatusDeleteManyArgs,
    Prisma.OrderStatusInclude
  > {
  getAllStatusByOrderId(orderId: string): Promise<IOrderStatus[]>;
  changeOrderStatus(orderId: string, statusTypeId: string, observation?: string): Promise<IOrderStatus>;
  getLastByOrderId(orderId: string): Promise<IOrderStatus | null>;
  getLastOrderStatusTypeByOrderId(orderId: string): Promise<EOrderStatusValue | null>;
  createOrUpdateOrderStatus(orderId: string, statusTypeId: string): Promise<IOrderStatus>;
  changeOrderStatusTransaction(orderId: string, statusTypeId: string, observation?: string): Promise<boolean>
}
