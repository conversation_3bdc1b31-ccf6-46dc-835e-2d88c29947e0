import { Prisma } from "@prisma/client";
import { IExportOrdersReportDTO } from "src/business/DTOs/Order/IExportOrdersReport";
import { IOrderByStatusDTO } from "src/business/DTOs/Order/IOrderByStatus";
import { IOrderDeliverymanDetailsDTO, IOrderDetailsDTO } from "src/business/DTOs/Order/IOrderDetails";
import { IOrderDetailsBackOfficeDTO } from "src/business/DTOs/Order/IOrderDetailsBackOffice";
import { IOrderUsersDTO } from "src/business/DTOs/Order/IOrderUsers";
import { IUserOrdersDTO } from "src/business/DTOs/Order/IUserOrders";
import { PagedResult, Totalizer } from "src/business/DTOs/PagedResult";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IOrderClient = IExtendedPrismaClient["client"]["order"];

export interface IOrderRepository
  extends IBaseRepository<
    IOrder,
    Prisma.OrderFindManyArgs,
    Prisma.OrderFindUniqueOrThrowArgs,
    Prisma.OrderUpdateManyArgs,
    Prisma.OrderDeleteManyArgs,
    Prisma.OrderInclude
  > {
  create(data: IOrder): Promise<IOrder>;
  getProductAttributeOptionIds(
    productId: string,
    attributeId: string[],
    attributeOptionId: string[],
  ): Promise<{ id: string }[]>;
  getOrdersByStatus(
    storeId: string,
    currentPage: number,
    pageSize: number,
    status?: string,
  ): Promise<PagedResult<IOrderByStatusDTO>>;
  getAllPaymentMadeStatus(): Promise<IOrder[]>;
  getOrdersByStatusAndDeliverymanId(
    userId: string,
    currentPage: number,
    pageSize: number,
    status?: string,
  ): Promise<PagedResult<IOrderByStatusDTO>>;
  getOrdersByUserId(userId: string, currentPage: number, pageSize: number): Promise<PagedResult<IUserOrdersDTO>>;
  getOrdersToExport(deliveredStatusId: string): Promise<IExportOrdersReportDTO[]>;
  getOrdersPaged(
    currentPage: string,
    pageSize: string,
    storeFilter: string,
    deliveredStatusId?: string,
  ): Promise<PagedResult<IOrder>>;
  getOrderWithItems(id: string): Promise<IOrder | null>;
  getOrderWithStore(id: string): Promise<IOrder | null>;
  getUsers(id: string): Promise<IOrderUsersDTO | null>;
  getCode(id: string): Promise<number | null>;
  getFinancialConsolidationPaged(
    page: number,
    pageSize: number,
    startDateFilter?: Date,
    endDateFilter?: Date,
    orderBy?: string,
    sortDirection?: string,
    filterValue?: string,
  ): Promise<PagedResult<IOrder>>;
  getOneFinancialConsolidationOrder(orderId: string): Promise<IOrder | null>;
  updateUserFkInOrder(updatedId: string, userIds: string[], client?: IOrderClient): Promise<number>;
  getOrderDetailsById(orderId: string): Promise<IOrderDetailsDTO | null>;
  getTotalOrderAmount(whereCondition: object | undefined): Promise<Totalizer>;
  getTotalPayoutOrderAmount(whereCondition: object | undefined): Promise<Totalizer>;
  getSalesDeliverymanTotalizer(whereCondition: object | undefined): Promise<Totalizer>;
  getSalesShopkeeperTotalizer(whereCondition: object | undefined): Promise<Totalizer>;
  getDeliverymanUserByOrderId(orderId: string): Promise<IOrderDeliverymanDetailsDTO | null>;
  getOrderBackOfficeDetails(orderId: string, isSales?: boolean): Promise<IOrderDetailsBackOfficeDTO | null>;
  getLastOrderIdByUserIdAndStoreId(userId: string, storeId: string): Promise<string | null>;
  deleteOrderItem(orderId: string): Promise<void>;
  createOrderItem(orderId: string, data: IOrder): Promise<boolean | void>;
}
