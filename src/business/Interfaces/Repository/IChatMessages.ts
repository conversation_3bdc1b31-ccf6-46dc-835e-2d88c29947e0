import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IChatMessage } from "src/business/Interfaces/Prisma/IChatMessage";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IChatMessageClient = IExtendedPrismaClient["client"]["chatMessage"];

export interface IChatMessagesRepository
  extends IBaseRepository<
    IChatMessage,
    Prisma.ChatMessageFindManyArgs,
    Prisma.ChatMessageFindUniqueOrThrowArgs,
    Prisma.ChatMessageUpdateManyArgs,
    Prisma.ChatMessageDeleteManyArgs,
    Prisma.ChatMessageInclude
  > {
  create(chatMessages: IChatMessage): Promise<IChatMessage>;
  updateUserFkInChatMessage(updatedId: string, userIds: string[], client?: IChatMessageClient): Promise<number>;
}
