import { Prisma } from "@prisma/client";
import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IProductModeration } from "src/business/Interfaces/Prisma/IProductModeration";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IProductModerationClient = IExtendedPrismaClient["client"]["productModeration"];

export interface IProductModerationRepository
  extends IBaseRepository<
    IProductModeration,
    Prisma.ProductModerationFindManyArgs,
    Prisma.ProductModerationFindUniqueOrThrowArgs,
    Prisma.ProductModerationUpdateManyArgs,
    Prisma.ProductModerationDeleteManyArgs,
    Prisma.ProductModerationInclude
  > {
  create(
    productModeration: ICreateProductModerationDTO,
    client?: IProductModerationClient,
  ): Promise<IProductModeration | null>;
  getByProductId(productId: string): Promise<IProductModeration[]>;
  getMostRecentProductModeration(productId: string): Promise<IProductModeration | null>;
}
