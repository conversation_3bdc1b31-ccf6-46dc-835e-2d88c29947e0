import { Prisma } from "@prisma/client";
import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ICreateProductByImportDTO } from "src/business/DTOs/Product/ICreateProduct";
import { IExportedProductData } from "src/business/DTOs/Product/IExportedProductData";
import { IProductByStoreDTO } from "src/business/DTOs/Product/IProductByStore";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IProductClient = IExtendedPrismaClient["client"]["product"];

export interface IProductRepository
  extends IBaseRepository<
    IProduct,
    Prisma.ProductFindManyArgs,
    Prisma.ProductFindUniqueOrThrowArgs,
    Prisma.ProductUpdateManyArgs,
    Prisma.ProductDeleteManyArgs,
    Prisma.ProductInclude
  > {
  create(product: IProduct, attachments?: string[]): Promise<IProduct | null>;
  getByStore(storeId: string, userId: string): Promise<IProductByStoreDTO[]>;
  getWithCategory(id: string): Promise<IProduct | null>;
  getUserFavoriteProducts(userId: string): Promise<IProduct[]>;
  getPagedProductList(
    productFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
  ): Promise<PagedResult<IProduct>>;
  getWithAllDetails(productId: string): Promise<IProduct | null>;
  getWithStore(productId: string): Promise<IProduct | null>;
  getWithAllDetailsByStore(storeId: string): Promise<IExportedProductData[] | null>;
  deleteByStore(storeId: string): Promise<number>;
  getPagedListFront(
    currentPage: number,
    pageSize: number,
    storeId?: string,
  ): Promise<{
    result: IProduct[];
    totalCount: number;
    totalPages: number;
  }>;
  getWithAllDetailsFront(productId: string): Promise<IProduct | null>;
  getProductSuggestions(userId: string): Promise<IProduct[]>;
  updateWithRelations(data: IProduct): Promise<boolean>;
  updateCategoryAndSubcategory(
    id: string,
    productCategories: IProductCategory[],
    productSubcategories: IProductSubcategory[],
  ): Promise<boolean>;
  updateProductAttributes(
    id: string,
    productAttributes: IProductAttribute[],
    currentProductAttributes: IProductAttribute[],
    newAttributeOption: IProductAttributeOption[],
  ): Promise<boolean>;
  saveProductModeration(
    id: string,
    product: IProduct,
    productModeration: ICreateProductModerationDTO,
  ): Promise<boolean>;
  updateProductCategories(id: string, productCategories: IProductCategory[]): Promise<boolean>;
  updateProductSubcategories(id: string, productSubcategories: IProductSubcategory[]): Promise<boolean>;
  deleteByStoreId(id: string): Promise<boolean>;
  deleteWithRelations(id: string): Promise<boolean>;
  createManyProducts(products: ICreateProductByImportDTO[]): Promise<boolean>;
}
