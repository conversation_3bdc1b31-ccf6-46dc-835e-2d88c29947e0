import { Prisma } from "@prisma/client";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IMailing } from "src/business/Interfaces/Prisma/IMailing";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IMailingClient = IExtendedPrismaClient["client"]["mailing"];

export interface IMailingRepository
  extends IBaseRepository<
  IMailing,
  Prisma.MailingFindManyArgs,
  Prisma.MailingFindUniqueArgs,
  Prisma.MailingUpdateManyArgs,
  Prisma.MailingDeleteManyArgs,
  Prisma.MailingInclude
  > {
  create(Mailing: IMailing, users: IUserMailingDTO[]): Promise<IMailing | null>;
  deleteMailing(id: string): Promise<IMailing | null>;
  updateMailing(mailingId: string, users: IUserMailingDTO[]): Promise<boolean>;
}
