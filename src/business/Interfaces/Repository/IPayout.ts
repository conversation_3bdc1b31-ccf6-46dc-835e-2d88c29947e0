import { EPayoutOwner, EPayoutStatus, Prisma } from "@prisma/client";
import { IAggregatePayoutsDTO } from "src/business/DTOs/Bordero/IAggregatePayouts";
import { PagedResult, Totalizer } from "src/business/DTOs/PagedResult";
import { IPayoutDetailsDTO } from "src/business/DTOs/Payout/IPayoutDetails";
import { IPayoutStatusDTO } from "src/business/DTOs/Payout/IPayoutStatus";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IPayout } from "src/business/Interfaces/Prisma/IPayout";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";
import { IOrder as IOrderSort } from "src/business/DTOs/Order";
import { IPayoutListDTO } from "src/business/DTOs/Payout/IPayoutList";
import { IPayoutReportDTO } from "src/business/DTOs/Payout/IPayoutReport";

export type IPayoutClient = IExtendedPrismaClient["client"]["payout"];

export interface IPayoutRepository
  extends IBaseRepository<
    IPayout,
    Prisma.PayoutFindManyArgs,
    Prisma.PayoutFindUniqueOrThrowArgs,
    Prisma.PayoutUpdateManyArgs,
    Prisma.PayoutDeleteManyArgs,
    Prisma.PayoutInclude
  > {
  create(data: IPayout): Promise<IPayout>;
  getTotalPayoutAmount(whereCondition: object | undefined): Promise<Totalizer>;
  getPayoutDetailsById(id: string): Promise<IPayoutDetailsDTO | null>;
  getPayoutsValueTotalizer(
    whereCondition: object | undefined,
    owner: EPayoutOwner,
    status: EPayoutStatus,
  ): Promise<number>;
  getCountPayoutWithoutBordero(payouts: string[]): Promise<number>;
  getPayoutsTotalizerByIds(payoutIds: string[]): Promise<IAggregatePayoutsDTO>;
  getPayoutsWithoutBorderoByDeliveryman(deliverymanId: string): Promise<number>;
  getPayoutsWithoutBorderoByStore(storeId: string): Promise<number>;
  getPayoutsWithoutBorderoByCooperative(cooperativeId: string): Promise<number>;
  getPayoutsPaginatedToCreateBordero(
    currentPage: number,
    pageSize: number,
    borderoId: string,
    ownerId: string,
    ownerFilter: EPayoutOwner,
    startDateFilter?: Date,
    endDateFilter?: Date,
    orderBy?: string,
    sortDirection?: IOrderSort,
    filterValue?: string,
  ): Promise<PagedResult<IPayoutListDTO>>;
  getCountPayoutsByBordero(borderoId: string): Promise<number>;
  getAllPayoutsByBordero(borderoId: string): Promise<IPayoutListDTO[]>;
  updateStatus(id: string, status: EPayoutStatus): Promise<boolean>;
  getPayoutStatusByOrderId(orderId: string): Promise<IPayoutStatusDTO | null>;
  getPayoutReportData(
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
    statusFilter?: EPayoutStatus,
    ownerFilter?: EPayoutOwner,
  ): Promise<IPayoutReportDTO[]>;
}
