import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IProductAttributeClient = IExtendedPrismaClient["client"]["productAttribute"];

export interface IProductAttributeRepository
  extends IBaseRepository<
    IProductAttribute,
    Prisma.ProductAttributeFindManyArgs,
    Prisma.ProductAttributeFindUniqueOrThrowArgs,
    Prisma.ProductAttributeUpdateManyArgs,
    Prisma.ProductAttributeDeleteManyArgs,
    Prisma.ProductAttributeInclude
  > {
  create(
    productId: string,
    attributeId: string,
    attributeoptionIds?: IProductAttributeOption[],
    client?: IProductAttributeClient,
  ): Promise<IProductAttribute | null>;
  createMany(productId: string, attributeIds: IProductAttribute[], client?: IProductAttributeClient): Promise<number>;
  getByProductAndAttribute(productId: string, attributeId: string): Promise<IProductAttribute | null>;
  getByProductId(productId: string): Promise<IProductAttribute[]>;
  deleteByProductId(productId: string, client?: IProductAttributeClient): Promise<number>;
  deleteByStore(storeId: string, client?: IProductAttributeClient): Promise<number>;
  updateActiveStatusById(status: boolean, ids: string[], client?: IProductAttributeClient): Promise<number>;
  activeProductAttributeById(ids: string[], client?: IProductAttributeClient): Promise<number>;
  inactiveProductAttributeById(ids: string[], client?: IProductAttributeClient): Promise<number>;
}
