import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IProductCategoryClient = IExtendedPrismaClient["client"]["productCategory"];

export interface IProductCategoryRepository
  extends IBaseRepository<
    IProductCategory,
    Prisma.ProductCategoryFindManyArgs,
    Prisma.ProductCategoryFindUniqueOrThrowArgs,
    Prisma.ProductCategoryUpdateManyArgs,
    Prisma.ProductCategoryDeleteManyArgs,
    Prisma.ProductCategoryInclude
  > {
  createMany(productCategory: IProductCategory[], client?: IProductCategoryClient): Promise<number>;
  deleteByProductId(productId: string, client?: IProductCategoryClient): Promise<number>;
  deleteByCategoryId(categoryId: string, client?: IProductCategoryClient): Promise<number>;
  deleteByStore(storeId: string, client?: IProductCategoryClient): Promise<number>;
}
