import { Prisma } from "@prisma/client";
import { IAttributeDTO } from "src/business/DTOs/Attribute/IAttribute";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IAttribute } from "src/business/Interfaces/Prisma/IAttribute";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IAttributeClient = IExtendedPrismaClient["client"]["attribute"];

export interface IAttributeRepository
  extends IBaseRepository<
    IAttribute,
    Prisma.AttributeFindManyArgs,
    Prisma.AttributeFindUniqueOrThrowArgs,
    Prisma.AttributeUpdateManyArgs,
    Prisma.AttributeDeleteManyArgs,
    Prisma.AttributeInclude
  > {
  create(item: IAttribute): Promise<IAttribute | null>;
  getPagedWithAllAttributeOption(
    storeId: string,
    page: number,
    pageSize: number,
    filter: string,
  ): Promise<PagedResult<IAttributeDTO>>;
  getWithAttributeOption(attributeId: string): Promise<IAttribute | null>;
  getWithProductAttributeOption(productId: string): Promise<IAttribute[]>;
}
