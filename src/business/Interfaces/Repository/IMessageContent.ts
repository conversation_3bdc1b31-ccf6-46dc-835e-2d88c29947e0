import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IMessageContent } from "src/business/Interfaces/Prisma/IMessageContent";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IMessageContentClient = IExtendedPrismaClient["client"]["messageContent"];

export interface IMessageContentRepository
  extends IBaseRepository<
    IMessageContent,
    Prisma.MessageContentFindManyArgs,
    Prisma.MessageContentFindUniqueOrThrowArgs,
    Prisma.MessageContentUpdateManyArgs,
    Prisma.MessageContentDeleteManyArgs,
    Prisma.MessageContentInclude
  > {}
