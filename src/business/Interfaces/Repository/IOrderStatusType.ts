import { Prisma } from "@prisma/client";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IOrderStatusType } from "src/business/Interfaces/Prisma/IOrderStatusType";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IOrderStatusTypeClient = IExtendedPrismaClient["client"]["orderStatusType"];

export interface IOrderStatusTypeRepository
  extends IBaseRepository<
    IOrderStatusType,
    Prisma.OrderStatusTypeFindManyArgs,
    Prisma.OrderStatusTypeFindUniqueOrThrowArgs,
    Prisma.OrderStatusTypeUpdateManyArgs,
    Prisma.OrderStatusTypeDeleteManyArgs,
    Prisma.OrderStatusTypeInclude
  > {
  create(data: IOrderStatusType): Promise<IOrderStatusType>;
  findByStatus(status: EOrderStatusValue): Promise<IOrderStatusType | null>;
}
