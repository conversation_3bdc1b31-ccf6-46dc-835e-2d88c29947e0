import { Prisma } from "@prisma/client";
import { ILogDTO } from "src/business/DTOs/Log/ILog";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ILog } from "src/business/Interfaces/Prisma/ILog";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ILogClient = IExtendedPrismaClient["client"]["log"];

export interface ILogRepository
  extends IBaseRepository<
    ILog,
    Prisma.LogFindManyArgs,
    Prisma.LogFindUniqueArgs,
    Prisma.LogUpdateManyArgs,
    Prisma.LogDeleteManyArgs,
    null
  > {
  getLogDetailsById(
    logId: string,
    entity?: string,
    entityId?: string,
    createdAt?: Date,
    action?: string,
  ): Promise<ILogDTO | null>;
}
