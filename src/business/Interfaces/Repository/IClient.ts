import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IClient } from "src/business/Interfaces/Prisma/IClient";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IClientClient = IExtendedPrismaClient["client"]["client"];

export interface IClientRepository
  extends IBaseRepository<
    IClient,
    Prisma.ClientFindManyArgs,
    Prisma.ClientFindUniqueOrThrowArgs,
    Prisma.ClientUpdateManyArgs,
    Prisma.ClientDeleteManyArgs,
    Prisma.ClientInclude
  > {
  create(data: IClient, client?: IClientClient): Promise<IClient>;
  updateClientProfile(userId: string, data: IClient, profileId: string): Promise<boolean>;
  deleteUserClientRelation(userIds: string[], client?: IClientClient): Promise<number>;
}
