import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IFinancialConsolidation } from "src/business/Interfaces/Prisma/IFinancialConsolidation";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IFinancialConsolidationClient = IExtendedPrismaClient["client"]["financialConsolidation"];

export interface IFinancialConsolidationRepository
  extends IBaseRepository<
    IFinancialConsolidation,
    Prisma.FinancialConsolidationFindManyArgs,
    Prisma.FinancialConsolidationFindUniqueOrThrowArgs,
    Prisma.FinancialConsolidationUpdateManyArgs,
    Prisma.FinancialConsolidationDeleteManyArgs,
    null
  > {
  create(data: IFinancialConsolidation): Promise<IFinancialConsolidation>;
}
