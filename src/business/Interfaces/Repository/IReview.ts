import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IReviewClient = IExtendedPrismaClient["client"]["review"];

export interface IReviewRepository
  extends IBaseRepository<
    IReview,
    Prisma.ReviewFindManyArgs,
    Prisma.ReviewFindUniqueOrThrowArgs,
    Prisma.ReviewUpdateManyArgs,
    Prisma.ReviewDeleteManyArgs,
    Prisma.ReviewInclude
  > {
  create(review: IReview): Promise<IReview | null>;
  getStoreReviews(storeId: string): Promise<IReview[]>;
  getUserReviews(userId: string): Promise<IReview[]>;
  getOrderReviews(orderId: string, deliverymanReview?: boolean): Promise<IReview | null>;
  updateUserFkInReview(updatedId: string, userIds: string[], client?: IReviewClient): Promise<number>;
}
