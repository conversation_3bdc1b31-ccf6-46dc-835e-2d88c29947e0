import { EFile, EFileType, Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IFileClient = IExtendedPrismaClient["client"]["file"];

export interface IFileRepository
  extends IBaseRepository<
    IFile,
    Prisma.FileFindManyArgs,
    Prisma.FileFindUniqueOrThrowArgs,
    Prisma.FileUpdateManyArgs,
    Prisma.FileDeleteManyArgs,
    null
  > {
  create(data: IFile): Promise<IFile>;
  getFilesByEntityId(entityId: string, entity: EFile): Promise<IFile[]>;
  getFilesByEntityAndType(entityId: string, entity: EFile, type: EFileType): Promise<IFile[]>;
  getPhotosByEntityId(entityId: string, entity: EFile): Promise<IFile[]>;
  selectFilesUntied(dateLimit: Date): Promise<IFile[]>;
  relateEntityFiles(entityId: string, filesId: string[], client?: IFileClient): Promise<number>;
}
