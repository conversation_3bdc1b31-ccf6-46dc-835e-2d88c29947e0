import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";

export type TransactionCallback<T> = (client: IExtendedPrismaClient["client"]) => Promise<T>;

export interface IBaseRepository<T, FindManyOptions, FindUniqueOptions, UpdateOptions, DeleteOptions, Include = null> {
  find(options: FindManyOptions): Promise<T[]>;
  findOne(options: FindUniqueOptions): Promise<T | null>;
  findFirst(options: FindManyOptions): Promise<T | null>;
  getById(id: string, includes?: Include): Promise<T | null>;
  getAll(): Promise<T[]>;
  update(id: string, item: Partial<T>, client?: any): Promise<number>;
  updateMany(options: UpdateOptions, client?: any): Promise<number>;
  delete(id: string, client?: any): Promise<boolean>;
  deleteMany(options: DeleteOptions, client?: any): Promise<number>;
  getPaged(options: FindManyOptions): Promise<{
    result: T[];
    totalCount: number;
    totalPages: number;
  }>;
  transaction<T>(fn: TransactionCallback<T>): Promise<T | boolean>;
}
