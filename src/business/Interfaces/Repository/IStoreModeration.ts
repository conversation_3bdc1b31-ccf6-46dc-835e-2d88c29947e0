import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IStoreModeration } from "src/business/Interfaces/Prisma/IStoreModeration";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";
import { ICreateStoreModerationDTO } from "src/business/DTOs/StoreModeration/IStoreModeration";

export type IStoreModerationClient = IExtendedPrismaClient["client"]["storeModeration"];

export interface IStoreModerationRepository
  extends IBaseRepository<
    IStoreModeration,
    Prisma.StoreModerationFindManyArgs,
    Prisma.StoreModerationFindUniqueOrThrowArgs,
    Prisma.StoreModerationUpdateManyArgs,
    Prisma.StoreModerationDeleteManyArgs,
    Prisma.StoreModerationInclude
  > {
  create(storeModeration: ICreateStoreModerationDTO): Promise<IStoreModeration | null>;
  getByStoreId(storeId: string): Promise<IStoreModeration[]>;
  getMostRecentStoreModeration(storeId: string): Promise<IStoreModeration | null>;
}
