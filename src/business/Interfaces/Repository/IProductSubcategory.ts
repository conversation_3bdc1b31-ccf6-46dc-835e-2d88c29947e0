import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IProductSubcategoryClient = IExtendedPrismaClient["client"]["productSubcategory"];

export interface IProductSubcategoryRepository
  extends IBaseRepository<
    IProductSubcategory,
    Prisma.ProductSubcategoryFindManyArgs,
    Prisma.ProductSubcategoryFindUniqueOrThrowArgs,
    Prisma.ProductSubcategoryUpdateManyArgs,
    Prisma.ProductSubcategoryDeleteManyArgs,
    Prisma.ProductSubcategoryInclude
  > {
  createMany(productSubcategory: IProductSubcategory[], client?: IProductSubcategoryClient): Promise<number>;
  deleteByProductId(productId: string, client?: IProductSubcategoryClient): Promise<number>;
  deleteByStore(storeId: string, client?: IProductSubcategoryClient): Promise<number>;
}
