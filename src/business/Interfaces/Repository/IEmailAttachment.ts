import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IEmailAttachment } from "src/business/Interfaces/Prisma/IEmailAttachment";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IEmailAttachmentClient = IExtendedPrismaClient["client"]["emailAttachment"];

export interface IEmailAttachmentRepository
  extends IBaseRepository<
    IEmailAttachment,
    Prisma.EmailAttachmentFindManyArgs,
    Prisma.EmailAttachmentFindUniqueOrThrowArgs,
    Prisma.EmailAttachmentUpdateManyArgs,
    Prisma.EmailAttachmentDeleteManyArgs,
    Prisma.EmailAttachmentInclude
  > {
  create(data: IEmailAttachment): Promise<IEmailAttachment | null>;
}
