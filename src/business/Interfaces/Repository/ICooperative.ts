import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ICooperative } from "src/business/Interfaces/Prisma/ICooperative";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ICooperativeClient = IExtendedPrismaClient["client"]["cooperative"];

export interface ICooperativeRepository
  extends IBaseRepository<
    ICooperative,
    Prisma.CooperativeFindManyArgs,
    Prisma.CooperativeFindUniqueOrThrowArgs,
    Prisma.CooperativeUpdateManyArgs,
    Prisma.CooperativeDeleteManyArgs,
    Prisma.CooperativeInclude
  > {
  create(data: ICooperative): Promise<boolean>;
  updateCooperative(cooperativeId: string, data: ICooperative): Promise<boolean>;
}
