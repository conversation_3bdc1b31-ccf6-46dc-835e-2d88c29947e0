import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ICard } from "src/business/Interfaces/Prisma/ICard";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ICardClient = IExtendedPrismaClient["client"]["card"];

export interface ICardRepository
  extends IBaseRepository<
    ICard,
    Prisma.CardFindManyArgs,
    Prisma.CardFindUniqueOrThrowArgs,
    Prisma.CardUpdateManyArgs,
    Prisma.CardDeleteManyArgs,
    Prisma.CardInclude
  > {
  getCardsByUserId(userId: string): Promise<ICard[]>;
  create(data: ICard): Promise<ICard | null>;
  updateUserFkInCard(updatedId: string, userIds: string[], client?: ICardClient): Promise<number>;
}
