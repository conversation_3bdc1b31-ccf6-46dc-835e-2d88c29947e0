import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IProfile } from "src/business/Interfaces/Prisma/IProfile";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IProfileClient = IExtendedPrismaClient["client"]["profile"];

export interface IProfileRepository
  extends IBaseRepository<
    IProfile,
    Prisma.ProfileFindManyArgs,
    Prisma.ProfileFindUniqueOrThrowArgs,
    Prisma.ProfileUpdateManyArgs,
    Prisma.ProfileDeleteManyArgs,
    Prisma.ProfileInclude
  > {
  create(profile: IProfile): Promise<IProfile>;
  findByIdWithPermission(id: string): Promise<IProfile | null>;
  relateProfilePermission(profileId: string, permissionId: string): Promise<void>;
}
