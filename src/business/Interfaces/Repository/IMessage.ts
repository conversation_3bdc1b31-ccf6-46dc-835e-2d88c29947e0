import { EMessageStatus, Prisma } from "@prisma/client";
import { IListNotificationMessage } from "src/business/DTOs/Message/IListNotificationMessage";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IMessageClient = IExtendedPrismaClient["client"]["message"];

export interface IMessageRepository
  extends IBaseRepository<
    IMessage,
    Prisma.MessageFindManyArgs,
    Prisma.MessageFindUniqueOrThrowArgs,
    Prisma.MessageUpdateManyArgs,
    Prisma.MessageDeleteManyArgs,
    Prisma.MessageInclude
  > {
  create(message: IMessage): Promise<IMessage | null>;
  getUserMessages(
    id: string,
    filterType: "received" | "sended" | "all",
    sendingType?: EMessageSendingType,
  ): Promise<IMessage[]>;

  getUserNotificationMessages(
    userId: string,
    profileId: string,
    currentPage: number,
    pageSize: number,
    filterByStatus?: EMessageStatus,
    dateToFilter?: string,
    orderBy?: "asc" | "desc",
    origin?: "web",
  ): Promise<PagedResult<IListNotificationMessage>>;
  updateUserFkInMessage(updatedId: string, userIds: string[], client?: IMessageClient): Promise<number>;
}
