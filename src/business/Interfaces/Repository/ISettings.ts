import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ISettings } from "src/business/Interfaces/Prisma/ISettings";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ISettingClient = IExtendedPrismaClient["client"]["settings"];

export interface ISettingsRepository
  extends IBaseRepository<
    ISettings,
    Prisma.SettingsFindManyArgs,
    Prisma.SettingsFindUniqueOrThrowArgs,
    Prisma.SettingsUpdateManyArgs,
    Prisma.SettingsDeleteManyArgs,
    Prisma.SettingsInclude
  > {
  createMany(settings: ISettings[]): Promise<boolean>;
  getByName(name: string): Promise<ISettings | null>;
}
