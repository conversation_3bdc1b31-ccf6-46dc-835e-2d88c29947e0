import { Prisma } from "@prisma/client";
import { IChatMessageDTO } from "src/business/DTOs/Chat/IChatMessage";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IChat } from "src/business/Interfaces/Prisma/IChat";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IChatClient = IExtendedPrismaClient["client"]["chat"];

export interface IChatRepository
  extends IBaseRepository<
    IChat,
    Prisma.ChatFindManyArgs,
    Prisma.ChatFindUniqueOrThrowArgs,
    Prisma.ChatUpdateManyArgs,
    Prisma.ChatDeleteManyArgs,
    Prisma.ChatInclude
  > {
  create(chat: IChat): Promise<IChat>;
  getByOrderId(orderId: string): Promise<IChat | null>;
  getChatMessages(orderId: string): Promise<IChatMessageDTO[]>;
}
