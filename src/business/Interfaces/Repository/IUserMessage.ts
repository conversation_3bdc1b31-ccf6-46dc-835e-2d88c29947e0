import { EMessageStatus, Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IUserMessage } from "src/business/Interfaces/Prisma/IUserMessage";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IUserMessageClient = IExtendedPrismaClient["client"]["userMessage"];

export interface IUserMessageRepository
  extends IBaseRepository<
    IUserMessage,
    Prisma.UserMessageFindManyArgs,
    Prisma.UserMessageFindUniqueArgs,
    Prisma.UserMessageUpdateManyArgs,
    Prisma.UserMessageDeleteManyArgs,
    Prisma.UserMessageInclude
  > {
  updateStatus(messageId: string, profileId: string, status: EMessageStatus): Promise<number>;
  updateUserFkInUserMessage(updatedId: string, userIds: string[], client?: IUserMessageClient): Promise<number>;
}
