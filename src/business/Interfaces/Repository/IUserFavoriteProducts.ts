import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IUserFavoriteProducts } from "src/business/Interfaces/Prisma/IUserFavoriteProducts";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IUserFavoriteProductsClient = IExtendedPrismaClient["client"]["userFavoriteProducts"];

export interface IUserFavoriteProductsRepository
  extends IBaseRepository<
    IUserFavoriteProducts,
    Prisma.UserFavoriteProductsFindManyArgs,
    Prisma.UserFavoriteProductsFindUniqueOrThrowArgs,
    Prisma.UserFavoriteProductsUpdateManyArgs,
    Prisma.UserFavoriteProductsDeleteManyArgs,
    Prisma.UserFavoriteProductsInclude
  > {
  updateUserFkInFavProducts(
    updatedId: string,
    userIds: string[],
    client?: IUserFavoriteProductsClient,
  ): Promise<number>;
}
