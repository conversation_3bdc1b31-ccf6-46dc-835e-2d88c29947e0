import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IStoreCategoryClient = IExtendedPrismaClient["client"]["storeCategory"];

export interface IStoreCategoryRepository
  extends IBaseRepository<
    IStoreCategory,
    Prisma.StoreCategoryFindManyArgs,
    Prisma.StoreCategoryFindUniqueOrThrowArgs,
    Prisma.StoreCategoryUpdateManyArgs,
    Prisma.StoreCategoryDeleteManyArgs,
    Prisma.StoreCategoryInclude
  > {
  createMany(storeId: string, storeCategories: IStoreCategory[], client?: IStoreCategoryClient): Promise<number>;
  deleteByStoreId(storeId: string, client?: IStoreCategoryClient): Promise<number>;
  deleteByCategoryId(categoryId: string, client?: IStoreCategoryClient): Promise<number>;
}
