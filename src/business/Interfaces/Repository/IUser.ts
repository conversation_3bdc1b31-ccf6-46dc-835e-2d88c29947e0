import { Prisma } from "@prisma/client";
import { IDeleteUser } from "src/api/ViewModels/User/IDeleteUser";
import { IFilterMailingDTO } from "src/business/DTOs/FilterMailing/IFilterMailing";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IReactivateAccountViewModel } from "src/api/ViewModels/User/IReactivateAccount";
import IPermanentDeleteUserDTO from "src/business/DTOs/User/IPermanentDelete";
import { IUserWithProfilePictureDTO } from "src/business/DTOs/User/IUserWithProfilePicture";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";
import { IOrder } from "src/business/DTOs/Order";

export type IUserClient = IExtendedPrismaClient["client"]["user"];

export interface IUserRepository
  extends IBaseRepository<
    IUser,
    Prisma.UserFindManyArgs,
    Prisma.UserFindUniqueArgs,
    Prisma.UserUpdateManyArgs,
    Prisma.UserDeleteManyArgs,
    Prisma.UserInclude
  > {
  create(user: IUser): Promise<IUser>;
  getWithProfilePictureById(id: string): Promise<IUserWithProfilePictureDTO | null>;
  getWithAddress(id: string): Promise<IUser | null>;
  deleteUserAddress(id: string): Promise<number>;
  deleteFavoriteStore(id: string, storeId: string): Promise<number>;
  deleteFavoriteProduct(id: string, productId: string): Promise<number>;
  getByPhone(phone: string): Promise<IUser | null>;
  getByEmail(email: string): Promise<IUser | null>;
  getWithProfileData(id: string): Promise<IUser | null>;
  relateUserAddress(id: string, addressId: string): Promise<void>;
  relateUserFavoriteStore(id: string, storeId: string): Promise<void>;
  relateUserFavoriteProduct(id: string, productId: string): Promise<void>;
  getByCognitoId(cognitoId: string, relations?: string[]): Promise<IUser | null>;
  getWithProfile(id: string): Promise<IUser | null>;
  getNotificationQuantity(id: string, profileId: string): Promise<number>;
  saveCognitoIdGoogle(id: string, cognitoIdGoogle: string): Promise<void>;
  getByCognitoIdGoogle(cognitoIdGoogle: string): Promise<IUser | null>;
  getAllPaged(
    currentPage: number,
    pageSize: number,
    filterName?: string,
    filterProfile?: string,
    filterStatus?: string,
    sortDirection?: IOrder,
  ): Promise<PagedResult<IUser>>;
  updateByUserId(userId: string, data: IUser): Promise<boolean | null>;
  deleteByUserId(userId: string): Promise<boolean | null>;
  disableUser(data: IDeleteUser): Promise<boolean | null>;
  getByMailingFilter(filter: IFilterMailingDTO): Promise<IUserMailingDTO[] | null>;
  deleteUsersPastRecoverLimit(ids: string[]): Promise<number | null>;
  getPermanentlyDeletedUser(): Promise<boolean | null>;
  getUsersToDeletePermanently(dateLimit: Date): Promise<IPermanentDeleteUserDTO[] | null>;
  getByTemporallyDeletedStatus(id: string): Promise<IUser | null>;
  updateDeletedAndReactivateAccount(id: string): Promise<IUser | null>;
  getByEmailAndCpf({ email, cpf }: IReactivateAccountViewModel): Promise<IUser | null>;
  getEmailsByUsersIds(usersIds: string[]): Promise<{ email: string }[]>;
  getUserDetailsBackOffice(id: string): Promise<IUser | null>;
}
