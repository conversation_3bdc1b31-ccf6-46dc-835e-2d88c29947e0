import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IUserAddress } from "src/business/Interfaces/Prisma/IUserAddress";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IUserAddressClient = IExtendedPrismaClient["client"]["userAddress"];

export interface IUserAddressRepository
  extends IBaseRepository<
    IUserAddress,
    Prisma.UserAddressFindManyArgs,
    Prisma.UserAddressFindUniqueOrThrowArgs,
    Prisma.UserAddressUpdateManyArgs,
    Prisma.UserAddressDeleteManyArgs,
    Prisma.UserAddressInclude
  > {
  updateUserFkInUserAddress(updatedId: string, userIds: string[], client?: IUserAddressClient): Promise<number>;
}
