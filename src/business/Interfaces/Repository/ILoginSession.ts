import { Prisma } from "@prisma/client";
import { ILoginSessionAggregateDTO } from "src/business/DTOs/LoginSession/ILoginSession";
import { ILoginSession } from "src/business/Interfaces/Prisma/ILoginSession";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";

export type ILoginSessionClient = IExtendedPrismaClient["client"]["loginSession"];

export interface ILoginSessionRepository
  extends IBaseRepository<
    ILoginSession,
    Prisma.LoginSessionFindManyArgs,
    Prisma.LoginSessionFindUniqueOrThrowArgs,
    Prisma.LoginSessionUpdateManyArgs,
    Prisma.LoginSessionDeleteManyArgs,
    Prisma.LoginSessionInclude
  > {
  create(session: ILoginSession): Promise<ILoginSession>;
  getActiveLoginSession(userId: string, limitDate: Date): Promise<ILoginSession | null>;
  updateLastAccess(userId: string): Promise<boolean>;
  getAllLoginSessionsInAWeek(firstWeekDay: Date, lastWeekDay: Date): Promise<ILoginSession[]>;
  getNumberOfAccessByDay(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]>;
  getNumberOfAccessByTime(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]>;
  getTotalNumberOfAccess(firstWeekDay: Date, lastWeekDay: Date): Promise<ILoginSessionAggregateDTO>;
  getHourWithMostAccess(firstWeekDay: Date, lastWeekDay: Date): Promise<ILoginSessionAggregateDTO[]>;
  updateUserFkInLoginSession(updatedId: string, userIds: string[], client?: ILoginSessionClient): Promise<number>;
}
