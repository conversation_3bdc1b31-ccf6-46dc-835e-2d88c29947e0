import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ICustomerProfile } from "src/business/Interfaces/Prisma/ICustomerProfile";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ICustomerProfileClient = IExtendedPrismaClient["client"]["customerProfile"];

export interface ICustomerProfileRepository
  extends IBaseRepository<
    ICustomerProfile,
    Prisma.CustomerProfileFindManyArgs,
    Prisma.CustomerProfileFindUniqueOrThrowArgs,
    Prisma.CustomerProfileUpdateManyArgs,
    Prisma.CustomerProfileDeleteManyArgs,
    null
  > {}
