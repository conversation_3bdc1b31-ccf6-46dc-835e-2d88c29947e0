import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ICategorySubcategory } from "src/business/Interfaces/Prisma/ICategorySubcategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ICategorySubcategoryClient = IExtendedPrismaClient["client"]["categorySubcategory"];

export interface ICategorySubcategoryRepository
  extends IBaseRepository<
    ICategorySubcategory,
    Prisma.CategorySubcategoryFindManyArgs,
    Prisma.CategorySubcategoryFindUniqueOrThrowArgs,
    Prisma.CategorySubcategoryUpdateManyArgs,
    Prisma.CategorySubcategoryDeleteManyArgs,
    Prisma.CategorySubcategoryInclude
  > {
  create(data: ICategorySubcategory): Promise<ICategorySubcategory | null>;
  createMany(data: ICategorySubcategory[], client?: ICategorySubcategoryClient): Promise<number>;
  deleteByCategoryId(categoryId: string, client?: ICategorySubcategoryClient): Promise<number>;
  deleteBySubcategoryId(subcategoryId: string, client?: ICategorySubcategoryClient): Promise<number>;
}
