import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ITransactionCard } from "src/business/Interfaces/Prisma/ITransactionCard";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ITransactionCardClient = IExtendedPrismaClient["client"]["transactionCard"];

export interface ITransactionCardRepository
  extends IBaseRepository<
    ITransactionCard,
    Prisma.TransactionCardFindManyArgs,
    Prisma.TransactionCardFindUniqueOrThrowArgs,
    Prisma.TransactionCardUpdateManyArgs,
    Prisma.TransactionCardDeleteManyArgs,
    Prisma.TransactionCardInclude
  > {
  create(data: ITransactionCard): Promise<ITransactionCard>;
}
