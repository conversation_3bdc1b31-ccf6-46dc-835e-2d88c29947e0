import { Prisma } from "@prisma/client";
import { IAddressOptionDTO } from "src/business/DTOs/Address/IAddressOption";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IAddressClient = IExtendedPrismaClient["client"]["address"];

export interface IAddressRepository
  extends IBaseRepository<
    IAddress,
    Prisma.AddressFindManyArgs,
    Prisma.AddressFindUniqueOrThrowArgs,
    Prisma.AddressUpdateManyArgs,
    Prisma.AddressDeleteManyArgs,
    Prisma.AddressInclude
  > {
  create(data: IAddress, userId?: string): Promise<IAddress>;
  // getPaged(currentPage: number, pageSize: number, userId: string, filter?: string): Promise<PagedResult<IAddress>>;
  getAddressesByUserId(userId: string): Promise<IAddress[]>;
  getAddresseOptionsByUserId(userId: string): Promise<IAddressOptionDTO[]>;
  getDefaultAddress(userId: string): Promise<IAddress | null>;
}
