import { EContentManagementType, Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IContentManagement } from "src/business/Interfaces/Prisma/IContentManagement";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IContentManagementClient = IExtendedPrismaClient["client"]["contentManagement"];

export interface IContentManagementRepository
  extends IBaseRepository<
    IContentManagement,
    Prisma.ContentManagementFindManyArgs,
    Prisma.ContentManagementFindUniqueOrThrowArgs,
    Prisma.ContentManagementUpdateManyArgs,
    Prisma.ContentManagementDeleteManyArgs,
    Prisma.ContentManagementCreateArgs
  > {
  getByType(type: EContentManagementType): Promise<IContentManagement | null>;
  create(data: IContentManagement): Promise<IContentManagement | null>;
}
