import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IProductAttributeOptionClient = IExtendedPrismaClient["client"]["productAttributeOption"];

export interface IProductAttributeOptionRepository
  extends IBaseRepository<
    IProductAttributeOption,
    Prisma.ProductAttributeOptionFindManyArgs,
    Prisma.ProductAttributeOptionFindUniqueOrThrowArgs,
    Prisma.ProductAttributeOptionUpdateManyArgs,
    Prisma.ProductAttributeOptionDeleteManyArgs,
    Prisma.ProductAttributeOptionInclude
  > {
  create(productAttributeId: string, attributeOptionId: string): Promise<IProductAttributeOption | null>;
  getByProductAttributeAndAttributeOption(
    productAttributeId: string,
    attributeOptionId: string,
  ): Promise<IProductAttributeOption | null>;
  deleteByProductAttributeId(productAttributeId: string, client?: IProductAttributeOptionClient): Promise<number>;
  deleteByStore(storeId: string, client?: IProductAttributeOptionClient): Promise<number>;
  updateActiveStatusById(status: boolean, ids: string[], client?: IProductAttributeOptionClient): Promise<number>;
  activeProductAttributeOptById(ids: string[], client?: IProductAttributeOptionClient): Promise<number>;
  inactiveProductAttributeOptById(ids: string[], client?: IProductAttributeOptionClient): Promise<number>;
}
