import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ISubcategory } from "src/business/Interfaces/Prisma/ISubcategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ISubcategoryClient = IExtendedPrismaClient["client"]["subcategory"];

export interface ISubcategoryRepository
  extends IBaseRepository<
    ISubcategory,
    Prisma.SubcategoryFindManyArgs,
    Prisma.SubcategoryFindUniqueOrThrowArgs,
    Prisma.SubcategoryUpdateManyArgs,
    Prisma.SubcategoryDeleteManyArgs,
    Prisma.SubcategoryInclude
  > {
  create(data: ISubcategory): Promise<ISubcategory | null>;
  // getWithCategory(storeId: string): Promise<ISubcategory[]>;
  deleteWithRelations(id: string): Promise<boolean>;
}
