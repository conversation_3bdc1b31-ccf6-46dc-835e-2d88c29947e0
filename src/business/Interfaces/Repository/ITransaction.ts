import { ETransactionStatus, Prisma } from "@prisma/client";
import { IListTransactionByOrderDTO } from "src/business/DTOs/Transactions/ListTransactionByOrder";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ITransaction } from "src/business/Interfaces/Prisma/ITransaction";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ITransactionClient = IExtendedPrismaClient["client"]["transaction"];

export interface ITransactionRepository
  extends IBaseRepository<
    ITransaction,
    Prisma.TransactionFindManyArgs,
    Prisma.TransactionFindUniqueArgs,
    Prisma.TransactionUpdateManyArgs,
    Prisma.TransactionDeleteManyArgs,
    Prisma.TransactionInclude
  > {
  getLastTransactionByUser(userId: string): Promise<ITransaction | null>;
  getLastTransactionByOrder(orderId: string): Promise<ITransaction | null>;
  getTransactionByUserId(userId: string): Promise<ITransaction[]>;
  getTransactionByOrderId(orderId: string): Promise<IListTransactionByOrderDTO[]>;
  create(transaction: ITransaction): Promise<ITransaction | null>;
  isCanceled(orderId: string): Promise<boolean>;
  getTransactionStatusByOrderId(orderId: string): Promise<ETransactionStatus | null>;
}
