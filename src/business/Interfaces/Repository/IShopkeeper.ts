import { EProfileStatus, Prisma } from "@prisma/client";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IShopkeeper } from "src/business/Interfaces/Prisma/IShopkeeper";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IShopkeeperClient = IExtendedPrismaClient["client"]["shopkeeper"];

export interface IShopkeeperRepository
  extends IBaseRepository<
    IShopkeeper,
    Prisma.ShopkeeperFindManyArgs,
    Prisma.ShopkeeperFindUniqueOrThrowArgs,
    Prisma.ShopkeeperUpdateManyArgs,
    Prisma.ShopkeeperDeleteManyArgs,
    Prisma.ShopkeeperInclude
  > {
  create(data: IShopkeeper, client?: IShopkeeperClient): Promise<IShopkeeper>;
  getUserShopkeeperByStatus(
    currentPage: number,
    pageSize: number,
    status: EProfileStatus,
  ): Promise<PagedResult<IShopkeeper>>;
  isShopkeeperApproved(userId: string): Promise<boolean>;
  updateShopkeeperProfile(userId: string, data: IShopkeeper, profileId: string): Promise<boolean>;
  getByUserId(userId: string): Promise<IShopkeeper | null>;
  deleteUserShopkeeperRelation(userIds: string[], client?: IShopkeeperClient): Promise<number>;
}
