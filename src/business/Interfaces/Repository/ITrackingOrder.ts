import { Prisma } from "@prisma/client";
import { ITrackingOrderDTO } from "src/business/DTOs/TrackingOrder/ITrackingOrder";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ITrackingOrder } from "src/business/Interfaces/Prisma/ITrackingOrder";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ITrackingOrderClient = IExtendedPrismaClient["client"]["trackingOrder"];

export interface ITrackingOrderRepository
  extends IBaseRepository<
    ITrackingOrder,
    Prisma.TrackingOrderFindManyArgs,
    Prisma.TrackingOrderFindUniqueOrThrowArgs,
    Prisma.TrackingOrderUpdateManyArgs,
    Prisma.TrackingOrderDeleteManyArgs,
    Prisma.TrackingOrderInclude
  > {
  create(trackingOrder: ITrackingOrderDTO): Promise<ITrackingOrder | null>;
  trackOrderDeliveryById(orderId: string): Promise<ITrackingOrder[]>;
  deleteByOrder(orderId: string): Promise<number>;
}
