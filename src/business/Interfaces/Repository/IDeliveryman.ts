import { EProfileStatus, Prisma } from "@prisma/client";
import { ILocationViewModel } from "src/api/ViewModels/Location/IViewModel";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IDeliveryman } from "src/business/Interfaces/Prisma/IDeliveryman";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IDeliverymanClient = IExtendedPrismaClient["client"]["deliveryman"];

export interface IDeliverymanRepository
  extends IBaseRepository<
    IDeliveryman,
    Prisma.DeliverymanFindManyArgs,
    Prisma.DeliverymanFindUniqueOrThrowArgs,
    Prisma.DeliverymanUpdateManyArgs,
    Prisma.DeliverymanDeleteManyArgs,
    Prisma.DeliverymanInclude
  > {
  create(data: <PERSON>eliveryman, client?: IDeliverymanClient): Promise<IDeliveryman>;
  getUserDeliverymanByStatus(
    currentPage: number,
    pageSize: number,
    status: EProfileStatus,
  ): Promise<PagedResult<IDeliveryman>>;
  isDeliverymanApproved(userId: string): Promise<boolean>;
  updateDeliverymanProfile(userId: string, data: IDeliveryman, profileId: string): Promise<boolean>;
  getByUserId(userId: string): Promise<IDeliveryman | null>;
  deleteUserDeliverymanRelation(userIds: string[], client?: IDeliverymanClient): Promise<number>;
  getDeliverymanLocationByCPF(cpf: string): Promise<IDeliveryman | null>;
  updateDeliverymanLocation(userId: string, location: ILocationViewModel): Promise<boolean>;
  getCheckPixKey(userId:string)
}
