import { Prisma } from "@prisma/client";
import { ICategoryDTO } from "src/business/DTOs/Category/ICategory";
import { ICategorySubcategoryDTO } from "src/business/DTOs/Category/ICategorySubcategory";
import { ICreateCategory } from "src/business/DTOs/Category/ICreateCategory";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { ICategory } from "src/business/Interfaces/Prisma/ICategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type ICategoryClient = IExtendedPrismaClient["client"]["category"];

export interface ICategoryRepository
  extends IBaseRepository<
    ICategory,
    Prisma.CategoryFindManyArgs,
    Prisma.CategoryFindUniqueOrThrowArgs,
    Prisma.CategoryUpdateManyArgs,
    Prisma.CategoryDeleteManyArgs,
    Prisma.CategoryInclude
  > {
  create(data: ICreateCategory, attachments?: string[]): Promise<ICategory | null>;
  getPagedWithStoreOptions(
    storeId: string,
    page: number,
    pageSize: number,
    filter: string,
  ): Promise<PagedResult<ICategoryDTO>>;
  getWithSubcategoryByStore(storeId: string): Promise<ICategory[]>;
  getAllCategoriesWithSubCategory(): Promise<ICategory[]>;
  getCategoriesWithIcon(currentPage: string, pageSize: string): Promise<PagedResult<ICategory>>;
  getCategoryByProductWithSubcategory(storeId: string, productId?: string): Promise<ICategorySubcategoryDTO[]>;
  createCategoriesWithSubcategories(): Promise<void>;
  // validateCreation(data: ICategory): Promise<boolean>;
  deleteWithRelations(id: string): Promise<boolean>;
  updateCategoryAndSubcategoryRelation(
    data: ICategory,
    subcategoryIds: {
      id: string;
    }[],
  ): Promise<boolean>;
  getSelect(categoryName: string, currentPage: number, pageSize: number): Promise<PagedResult<ISelectDTO>>;
  getCategoryDetails(id: string): Promise<ICategory | null>;
}
