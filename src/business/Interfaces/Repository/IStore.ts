import { EDayOfWeek, Prisma } from "@prisma/client";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ExportStoreDTO } from "src/business/DTOs/Store/ExportStore";
import { ICreateStoreByExportDTO } from "src/business/DTOs/Store/ICreateStore";
import { IListStoresDTO } from "src/business/DTOs/Store/IListStores";
import { IListUserStoreDTO } from "src/business/DTOs/Store/IListUserStore";
import { IStoreNameDTO } from "src/business/DTOs/Store/IStoreName";
import { IStoreShowcaseDTO } from "src/business/DTOs/Store/IStoreShowcase";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IStoreClient = IExtendedPrismaClient["client"]["store"];

export interface IStoreRepository
  extends IBaseRepository<
    IStore,
    Prisma.StoreFindManyArgs,
    Prisma.StoreFindUniqueOrThrowArgs,
    Prisma.StoreUpdateManyArgs,
    Prisma.StoreDeleteManyArgs,
    Prisma.StoreInclude
  > {
  create(store: IStore): Promise<IStore | null>;
  createManyStores(stores: ICreateStoreByExportDTO[]): Promise<boolean>;
  getStoresNameByUserId(userId: string): Promise<IStoreNameDTO[]>;
  getStoreInfoById(storeId: string): Promise<IStore | null>;
  relateStoreUser(storeId: string, userId: string): Promise<boolean>;
  getPagedListWithFavoriteStores(
    currentDay: EDayOfWeek,
    currentTime: Date,
    storeFilter: string,
    categoryIdFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
    latitude: number,
    longitude: number,
  ): Promise<PagedResult<IListStoresDTO>>;
  getBySlug(slug: string): Promise<IStore[]>;
  getByCnpj(cnpj: string): Promise<IStore | null>;
  update(id: string, data: IStore): Promise<number>;
  getWithUser(id: string): Promise<IStore | null>;
  getWithCategory(id: string): Promise<IStore | null>;
  getWithAddress(id: string): Promise<IStore | null>;
  getStoresByUserId(userId: string): Promise<IListUserStoreDTO[]>;
  getStoreInfoById(id: string): Promise<IStore | null>;
  getPagedListFront(currentPage: number, pageSize: number): Promise<PagedResult<IStore>>;
  getWithAllDetailsFront(storeId: string): Promise<IStore | null>;
  checkAvailabilityByStoreId(storeId: string): Promise<IStore | null>;
  updateStoreCategories(id: string, storeCategories: IStoreCategory[]): Promise<boolean>;
  deleteWithRelations(id: string): Promise<boolean>;
  getStoreShowcase(id: string): Promise<IStoreShowcaseDTO | null>;
  getAllStoresToExport(): Promise<ExportStoreDTO[] | null>;
  getSelect(storeName: string, currentPage: number, pageSize: number): Promise<PagedResult<ISelectDTO>>;
  checkIfUserIsStoreOwnerByOrderId(orderId: string, userId: string): Promise<boolean>;
  filterStoresByUserDistance(latitude: number, longitude: number, distance: number): Promise<string[]>;
  getHasStoreMissingPixKey(userId: string): Promise<boolean>
}
