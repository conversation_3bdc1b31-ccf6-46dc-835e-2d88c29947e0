import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IAttributeOptionClient = IExtendedPrismaClient["client"]["attributeOption"];

export interface IAttributeOptionRepository
  extends IBaseRepository<
    IAttributeOption,
    Prisma.AttributeOptionFindManyArgs,
    Prisma.AttributeOptionFindUniqueOrThrowArgs,
    Prisma.AttributeOptionUpdateManyArgs,
    Prisma.AttributeOptionDeleteManyArgs,
    Prisma.AttributeOptionInclude
  > {
  create(item: IAttributeOption): Promise<IAttributeOption | null>;
}
