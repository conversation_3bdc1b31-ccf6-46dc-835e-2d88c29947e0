import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IDevice } from "src/business/Interfaces/Prisma/IDevice";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IDeviceClient = IExtendedPrismaClient["client"]["device"];

export interface IDeviceRepository
  extends IBaseRepository<
    IDevice,
    Prisma.DeviceFindManyArgs,
    Prisma.DeviceFindUniqueOrThrowArgs,
    Prisma.DeviceUpdateManyArgs,
    Prisma.DeviceDeleteManyArgs,
    Prisma.DeviceInclude
  > {
  create(devices: IDevice): Promise<IDevice>;
  createMany(devices: IDevice[]): Promise<number>;
  getDeliverymanDevices(deliveryManProfileId: string, topicArn?: string): Promise<IDevice[]>;
  updateDevice(device: IDevice): Promise<number>;
  getByDeviceId(deviceId: string): Promise<IDevice[]>;
  updateDeviceLanguage(device: IDevice): Promise<number>;
  deleteUserDeviceRelation(userIds: string[], client?: IDeviceClient): Promise<number>;
  getUsersDevicesByUserId(userIds: string[]): Promise<IDevice[] | null>;
  getUserDevicesByUserId(userId: string): Promise<IDevice[] | null>;
  disableOldDevices(userId: string, deviceId: string, profileId: string): Promise<void>;
}
