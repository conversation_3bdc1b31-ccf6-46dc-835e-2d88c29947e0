import { Prisma } from "@prisma/client";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IResultMailing } from "src/business/Interfaces/Prisma/IResultMailing";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IResultMailingClient = IExtendedPrismaClient["client"]["resultMailing"];

export interface IResultMailingRepository
  extends IBaseRepository<
  IResultMailing,
  Prisma.ResultMailingFindManyArgs,
  Prisma.ResultMailingFindUniqueArgs,
  Prisma.ResultMailingUpdateManyArgs,
  Prisma.ResultMailingDeleteArgs,
  Prisma.ResultMailingInclude
  > {
  getUsersIdByMailingId(MailingId: string): Promise<{ userId: string | null }[]>;
  getUsersIdsByMailingIdPaged(
    currentPage: number,
    pageSize: number,
    MailingId: string,
    filterName?: string,
  ): Promise<PagedResult<IUserMailingDTO>>;
}
