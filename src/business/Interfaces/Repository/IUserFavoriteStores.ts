import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IUserFavoriteStores } from "src/business/Interfaces/Prisma/IUserFavoriteStores";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IUserFavoriteStoresClient = IExtendedPrismaClient["client"]["userFavoriteStores"];

export interface IUserFavoriteStoresRepository
  extends IBaseRepository<
    IUserFavoriteStores,
    Prisma.UserFavoriteStoresFindManyArgs,
    Prisma.UserFavoriteStoresFindUniqueOrThrowArgs,
    Prisma.UserFavoriteStoresUpdateManyArgs,
    Prisma.UserFavoriteStoresDeleteManyArgs,
    Prisma.UserFavoriteStoresInclude
  > {
  updateUserFkInFavStores(updatedId: string, userIds: string[], client?: IUserFavoriteStoresClient): Promise<number>;
}
