import { Prisma } from "@prisma/client";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IStoreHoursClient = IExtendedPrismaClient["client"]["storeHours"];

export interface IStoreHoursRepository
  extends IBaseRepository<
    IStoreHours,
    Prisma.StoreHoursFindManyArgs,
    Prisma.StoreHoursFindUniqueOrThrowArgs,
    Prisma.StoreHoursUpdateManyArgs,
    Prisma.StoreHoursDeleteManyArgs,
    Prisma.StoreHoursInclude
  > {
  createMany(idj: string, storeHours: IStoreHours[], client?: IStoreHoursClient): Promise<number>;
  deleteByStoreId(id: string, client?: IStoreHoursClient): Promise<number>;
  getStoreHoursByStoreId(id: string): Promise<IStoreHours[]>;
  updateByStore(storeId: string, storeHours: IStoreHours[]): Promise<boolean>;
}
