import { EStoreModeratorStatus, Prisma } from "@prisma/client";
import { IStoreModeratorDTO } from "src/business/DTOs/StoreUser/IStoreModerator";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IStoreUser } from "src/business/Interfaces/Prisma/IStoreUser";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";

export type IStoreUserClient = IExtendedPrismaClient["client"]["storeUser"];

export interface IStoreUserRepository
  extends IBaseRepository<
    IStoreUser,
    Prisma.StoreUserFindManyArgs,
    Prisma.StoreUserFindUniqueOrThrowArgs,
    Prisma.StoreUserUpdateManyArgs,
    Prisma.StoreUserDeleteManyArgs,
    Prisma.StoreUserInclude
  > {
  create(storeUser: IStoreUser): Promise<IStoreUser | null>;
  getStoreUsersByStoreId(storeId: string): Promise<IStoreUser[]>;
  getModeratorByStoreId(storeId: string): Promise<IStoreModeratorDTO[]>;
  setStatus(storeUserId: string, status: EStoreModeratorStatus): Promise<boolean>;
  updateUserFkInStoreUser(updatedId: string, userIds: string[], client?: IStoreUserClient): Promise<number>;
}
