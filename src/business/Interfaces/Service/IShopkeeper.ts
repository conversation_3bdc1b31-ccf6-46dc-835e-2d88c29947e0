import { PagedResult } from "src/business/DTOs/PagedResult";
import { UnknownObject } from "src/business/DTOs/UnknownObject";
import { IShopkeeper } from "src/business/Interfaces/Prisma/IShopkeeper";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IShopkeeperService extends IBaseService<IShopkeeper> {
  updateShopkeeperProfile(userId: string, data: IShopkeeper, attachments?: { id: string }[]): Promise<boolean>;
  validate(data: UnknownObject): Promise<IShopkeeper>;
  getShopkeeperByStatus(currentPage: string, pageSize: string, status: string): Promise<PagedResult<IShopkeeper>>;
  getShopkeeper(userId: string): Promise<IShopkeeper | null>;
  update(shopkeeper: IShopkeeper): Promise<boolean>;
}
