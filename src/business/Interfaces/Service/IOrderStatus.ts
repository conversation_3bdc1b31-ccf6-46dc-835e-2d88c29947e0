import { ICreateOrderStatusViewModel } from "src/api/ViewModels/OrderStatus/ICreate";
import INotificationDTO from "src/business/DTOs/Notification";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IOrderStatus } from "src/business/Interfaces/Prisma/IOrderStatus";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IOrderStatusService extends IBaseService<IOrderStatus> {
  getAllStatusByOrderId(orderId: string): Promise<IOrderStatus[]>;
  addOrderStatus(orderId: string, status: ICreateOrderStatusViewModel, profile?: EProfile): Promise<boolean>;
  sendChangedOrderStatusNotification(orderId: string, statusValue: EOrderStatusValue, statusKey: string): Promise<void>;
}
