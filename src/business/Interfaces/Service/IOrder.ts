import { Xlsx } from "exceljs";
import { ICreateOrderViewModel } from "src/api/ViewModels/Order/ICreateOrder";
import { IUpdateOrderViewModel } from "src/api/ViewModels/Order/IUpdateOrder";
import { ICreateOrderResultDTO } from "src/business/DTOs/Order/ICreateOrderResult";
import { IOrderFinancialConsolidationDTO } from "src/business/DTOs/Order/IFinancialConsolidation";
import { IOrderByStatusDTO } from "src/business/DTOs/Order/IOrderByStatus";
import { IOrderDetailsDTO } from "src/business/DTOs/Order/IOrderDetails";
import { IOrderDetailsBackOfficeDTO } from "src/business/DTOs/Order/IOrderDetailsBackOffice";
import { IOrderSalesDTO } from "src/business/DTOs/Order/IOrderSales";
import { IUpdateOrderResultDTO } from "src/business/DTOs/Order/IUpdateOrderResult";
import { IUserOrdersDTO } from "src/business/DTOs/Order/IUserOrders";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { EFinancialConsolidation } from "src/business/Enums/EFinancialConsolidation";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IOrderService extends IBaseService<IOrder> {
  create(data: ICreateOrderViewModel): Promise<ICreateOrderResultDTO>;
  updateOrderOnFailTransaction(data: IUpdateOrderViewModel): Promise<IUpdateOrderResultDTO>;
  updateDeliverymanId(orderId: string, userId: string): Promise<boolean | null>;
  getOrdersByStatus(
    storeId: string,
    page: string,
    pageSize: string,
    statusValue?: EOrderStatusValue,
  ): Promise<PagedResult<IOrderByStatusDTO>>;
  getOrdersByStatusAndDeliverymanId(
    userId: string,
    page: string,
    pageSize: string,
    statusValue?: EOrderStatusValue,
  ): Promise<PagedResult<IOrderByStatusDTO>>;
  getOrdersByUserId(userId: string, currentPage: number, pageSize: number): Promise<PagedResult<IUserOrdersDTO>>;
  getOrderWithItems(id: string): Promise<IOrder | null>;
  // getDeliveredOrders(): Promise<ExportOrdersReport[] | null>;
  getOrdersPaged(
    page: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    storeFilter?: string,
    getOnlyDelivered?: boolean,
    orderBy?: string,
    sortDirection?: string,
  ): Promise<PagedResult<IOrder>>;
  cancelOrder(orderId: string): Promise<boolean>;
  // getPagedList(
  //   page: number,
  //   pageSize: number,
  //   columnFilter: string,
  //   filterOperator: string,
  //   filterValue: string,
  // ): Promise<PagedResult<IOrder>>;
  updateCustomerCode(id: string, customerCode: string): Promise<boolean>;
  validateCustomerCode(id: string, customerCode: string): Promise<boolean>;
  createOrderReportFile(): Promise<Xlsx | null>;
  updateDeliverymanRouteLength(orderId: string, routeLength: number): Promise<boolean>;
  getFinancialConsolidationPaged(
    currentPage: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue?: string,
    orderBy?: string,
    sortDirection?: string,
    statusFilter?: EFinancialConsolidation,
  ): Promise<PagedResult<IOrderFinancialConsolidationDTO>>;
  getOneFinancialConsolidationOrder(orderId: string): Promise<IOrder | null>;
  getOrderDetailsById(orderId: string): Promise<IOrderDetailsDTO | null>;
  getDeliverymanAndShopkeeperSales(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: string,
    pageSize: string,
    profile: EProfile,
    statusValue?: EOrderStatusValue,
  ): Promise<PagedResult<IOrderSalesDTO>>;
  getOrderBackOfficeDetails(orderId: string, isSales?: boolean): Promise<IOrderDetailsBackOfficeDTO | null>;
  getLastOrderIdByUserIdAndStoreId(userId: string, storeId: string): Promise<string | null>;
  createFinancialConsolidation(): Promise<boolean>;
}
