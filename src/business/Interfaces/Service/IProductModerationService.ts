import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import { IProductModeration } from "src/business/Interfaces/Prisma/IProductModeration";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IProductModerationService extends IBaseService<IProductModeration> {
  create(moderation: ICreateProductModerationDTO): Promise<boolean>;
  getAllModeration(): Promise<IProductModeration[]>;
  getByProductId(productId: string): Promise<IProductModeration[] | null>;
  getMostRecentProductModeration(productId: string): Promise<IProductModeration | null>;
}
