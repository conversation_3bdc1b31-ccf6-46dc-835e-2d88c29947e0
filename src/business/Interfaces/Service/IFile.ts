import { EFile, EFileType } from "@prisma/client";
import { Express } from "express";
import { ICreateFileViewModel } from "src/api/ViewModels/File/ICreateFile";
import { IResultCreateFile } from "src/api/ViewModels/File/IResultCreateFile";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IFileService extends IBaseService<IFile> {
  createFile(metadata: ICreateFileViewModel, file: Express.Multer.File): Promise<IResultCreateFile | null>;
  deleteFilesUntied(): Promise<number>;
  getFilesByEntityId(entityId: string, entity: EFile): Promise<IFile[]>;
  getFilesByEntityAndType(entityId: string, entity: EFile, type: EFileType): Promise<IFile[]>;
  getPhotosByEntityId(entityId: string, entity: EFile): Promise<IFile[]>;
  deleteByEntities(entityIds: string[], entity: EFile): Promise<number>;
}
