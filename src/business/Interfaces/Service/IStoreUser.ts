import { EStoreModeratorStatus } from "@prisma/client";
import { IStoreModeratorDTO } from "src/business/DTOs/StoreUser/IStoreModerator";
import { IValidateEmailDTO } from "src/business/DTOs/StoreUser/IValidateEmail";
import { IStoreUser } from "src/business/Interfaces/Prisma/IStoreUser";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IStoreUserService extends IBaseService<IStoreUser> {
  create(storeId: string, email: string): Promise<boolean>;
  getStoreUsersByStoreId(storeId: string): Promise<IStoreUser[]>;
  getModeratorsByStoreId(storeId: string): Promise<IStoreModeratorDTO[]>;
  setStatus(storeUserId: string, status: EStoreModeratorStatus): Promise<boolean>;
  deleteStoreUser(storeUserId: string, userId: string): Promise<boolean>;
  refuseInvite(storeUserId: string): Promise<boolean>;
  acceptInvite(storeUserId: string): Promise<boolean>;
  validateEmail(email: string, storeId: string): Promise<IValidateEmailDTO>;
  sendStatusChangeNotificationToUser(
    userId: string,
    message: string,
    storeName: string,
    storeId: string,
    blocked?: boolean,
  ): void;
}
