import { GetEndpointAttributesCommandOutput, ListSubscriptionsByTopicResponse } from "@aws-sdk/client-sns";
import { IDevice } from "src/business/Interfaces/Prisma/IDevice";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { ELanguageOptions } from "@prisma/client";

export interface IDeviceService extends IBaseService<IDevice> {
  hasDeviceRegisteredByUserId(userId: string): Promise<boolean>;
  getUserActiveDevices(userId: string): Promise<IDevice[]>;
  /**
   * - If an array of device is set, subscribe those specific devices, otherwise subscribe every device in the database,
   */
  subscribeMultipleEndpointsToTopic(topicArn: string, devices?: IDevice[]): Promise<IDevice[]>;
  /**
   * - If an array of device is set unsubscribe those specific devices, otherwise unsubscribe every device in the database,
  */
 unSubscribeMultipleEndpointsFromTopic(devices?: IDevice[]): Promise<void>;
 getSubscriptionsDevicesFromTopic(
   topicArn: string,
   ): Promise<ListSubscriptionsByTopicResponse["Subscriptions"] | undefined>;
   getDeliverymanDevices(deliveryManProfileId: string, topicArn?: string, ): Promise<IDevice[]>;
   subscribeMultipleDeliverymenToTopic(topicArn: string, devices?: IDevice[]): Promise<IDevice[]>;
  registerNotificationService(
    userId: string,
    deviceToken: string,
    deviceUniqueId: string,
    deviceType: string,
  ): Promise<boolean>;
  getNotificationEndpointAttributes(
    userId: string,
    deviceId: string,
  ): Promise<GetEndpointAttributesCommandOutput["Attributes"] | null>;
  disableUserDevice(userId: string, deviceId: string): Promise<boolean>;
  getUserDevice(userId: string, deviceId: string): Promise<IDevice | null>;
  getByDeviceId(deviceId: string): Promise<IDevice[]>;
  updateDeviceLanguage(deviceId: string, language: ELanguageOptions): Promise<boolean>;
  updateDeviceDefaultProfile(deviceId: string, profile: EProfile): Promise<boolean>;
  getUserActiveDevicesByProfile(userId: string, profile: EProfile): Promise<IDevice[]>;
  getDeviceLanguage(userId: string, deviceId: string): Promise<ELanguageOptions>;
  getUsersDevicesByUserId(userIds: string[]): Promise<IDevice[] | null>;
  getUserDevicesByUserId(userId: string): Promise<IDevice[] | null>;
  associateDevicesUserToTopic(topicArn: string, userId: string): Promise<boolean>;
  subscribeMultipleDevicesToTopic(topicArn: string, devices?: IDevice[]);
}
