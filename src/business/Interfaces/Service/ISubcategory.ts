import { <PERSON><PERSON>rde<PERSON> } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ISubcategory } from "src/business/Interfaces/Prisma/ISubcategory";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface ISubcategoryService extends IBaseService<ISubcategory> {
  create(data: ISubcategory): Promise<boolean>;
  deleteWithRelations(id: string): Promise<boolean>;
  getPagedWithCategory(
    currentpage?: string,
    pagesize?: string,
    filtervalue?: string,
    columnfilter?: string,
    fieldorder?: string,
    order?: IOrder,
  ): Promise<PagedResult<ISubcategory>>;
}
