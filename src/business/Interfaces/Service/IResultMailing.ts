import { PagedResult } from "src/business/DTOs/PagedResult";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IResultMailing } from "src/business/Interfaces/Prisma/IResultMailing";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IResultMailingService extends IBaseService<IResultMailing> {
  getUsersIdByMailingId(MailingId: string): Promise<string[]>;
  getUsersIdsByMailingIdPaged(
    currentPage: number,
    pageSize: number,
    MailingId: string,
    filterName?: string,
  ): Promise<PagedResult<IUserMailingDTO>>;
}
