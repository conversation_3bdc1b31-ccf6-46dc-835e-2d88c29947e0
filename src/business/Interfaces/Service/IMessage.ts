import { Express } from "express";
import { IListNotificationMessage } from "src/business/DTOs/Message/IListNotificationMessage";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";
import { IFile } from "src/business/Interfaces/Controllers/IFile";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IMessageService extends IBaseService<IMessage> {
  create(data: IMessage, files?: IFile[]): Promise<boolean>;
  getUserMessages(
    id: string,
    filterType: "received" | "sended" | "all",
    sendingType?: EMessageSendingType,
  ): Promise<IMessage[]>;
  getUserNotificationMessages(
    userId: string,
    profile: EProfile,
    currentPage: string,
    pageSize: string,
    filterByStatus?: string,
    dateToFilter?: string,
    orderBy?: "asc" | "desc",
    origin?: "web",
  ): Promise<PagedResult<IListNotificationMessage>>;
  updateNotificationMessageStatus(messageId: string, profile: EProfile): Promise<number>;
  updateMultipleNotificationMessagesStatus(messageId: string[], profile: EProfile): Promise<number>;
}
