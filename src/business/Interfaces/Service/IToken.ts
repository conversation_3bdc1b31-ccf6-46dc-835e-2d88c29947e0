import { CognitoIdTokenPayload } from "aws-jwt-verify/jwt-model";
import { UserAuthenticationResult } from "src/business/DTOs/UserAuthenticationResult";

export interface ITokenService {
  refreshToken(
    cognitoId: string,
    refreshToken: string,
    cognitoUsername?: string,
  ): Promise<UserAuthenticationResult | null>;
  revokeAccess(refreshToken: string): Promise<void>;
  exchangeCode(code: string): Promise<UserAuthenticationResult | null>;
  getIdTokenPayload(idToken: string): Promise<CognitoIdTokenPayload>;
  saveCognitoIdGoogle(userId: string, cognitoIdGoogle: string): Promise<void>;
}
