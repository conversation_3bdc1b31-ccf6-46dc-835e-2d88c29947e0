import { EContentManagementType } from "@prisma/client";
import { IContentManagement } from "src/business/Interfaces/Prisma/IContentManagement";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IContentManagementService extends IBaseService<IContentManagement> {
  create(contentData: IContentManagement): Promise<boolean>;
  getByType(type: EContentManagementType): Promise<IContentManagement | null>;
}
