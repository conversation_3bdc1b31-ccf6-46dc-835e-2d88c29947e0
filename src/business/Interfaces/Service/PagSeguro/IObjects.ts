export enum ChargeStatus {
  AUTHORIZED = "AUTHORIZED",
  PAID = "PAID",
  IN_ANALYSIS = "IN_ANALYSIS",
  DECLINED = "DECLINED",
  CANCELED = "CANCELED",
}

export enum Brand {
  VISA = "VISA",
  MASTERCARD = "MASTERCARD",
  AMEX = "AMEX",
  ELO = "ELO",
  HIPERCARD = "HIPERCARD",
  HIPER = "HIPER",
  DINERS = "DINERS",
}

export type PagSeguroPaymentMethodType = "CREDIT_CARD" | "DEBIT_CARD" | "PIX";

export type LinksType = "GET" | "POST" | "DELETE" | "PUT";

export type EnvPagSeguro = "PROD" | "SANDBOX";

type CustomerType = "MOBILE" | "BUSINESS" | "HOME";

export interface IAmount {
  value: number;
  currency: string;
}

export interface IItemOrder {
  reference_id: string;
  name: string;
  quantity: number;
  unit_amount: number;
}

export interface ISession {
  session: string;
  expires_at: number;
  errors?: string;
}

export interface ICustomer {
  name: string;
  email: string;
  tax_id: string;
  phones: {
    country: string;
    area: string;
    number: string;
    type: CustomerType;
  }[];
}

export interface IAddress {
  street: string;
  number: string;
  complement?: string;
  locality?: string;
  city: string;
  region_code: string;
  country: string;
  postal_code: string;
}
