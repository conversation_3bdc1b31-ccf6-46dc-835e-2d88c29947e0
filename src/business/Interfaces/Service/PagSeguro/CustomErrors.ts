import { IResponseError } from "src/business/Interfaces/Service/PagSeguro/IResponsePayment";

const ERROR_LOADING_LIB: IResponseError = {
  error_messages: {
    code: 500,
    description: "Error loading payment library",
    errors: "Error loading library",
    parameter_name: "library",
  },
};

const ERROR_CREATING_SESSION: IResponseError = {
  error_messages: {
    code: 500,
    description: "Error creating session for 3DS authentication",
    errors: "Error creating session ",
    parameter_name: "session",
  },
};

const ERROR_UNDEFINED_PAYMENT_METHOD: IResponseError = {
  error_messages: {
    code: 500,
    description: "Undefined payment method",
    errors: "Undefined payment method",
    parameter_name: "payment method",
  },
};

const ERROR_UNDEFINED_BILLING_ADDRESS: IResponseError = {
  error_messages: {
    code: 500,
    description: "Undefined billing address",
    errors: "Undefined billing address",
    parameter_name: "billing address",
  },
};

const ERROR_UNDEFINED_SHIPPING_ADDRESS: IResponseError = {
  error_messages: {
    code: 500,
    description: "Undefined shipping address",
    errors: "Undefined shipping address",
    parameter_name: "shipping address",
  },
};

const ERROR_CREATING_PAYMENT_OBJECT: IResponseError = {
  error_messages: {
    code: 500,
    description: "Error creating payment object",
    errors: "Error creating payment object",
    parameter_name: "object request payment",
  },
};

const ERROR_CARD_AUTH_NOT_SUPPORTED: IResponseError = {
  error_messages: {
    code: 500,
    description: "Card not eligible for 3DS for the `DEBIT` payment method",
    errors: "Card not eligible for 3DS",
    parameter_name: "Card 3DS",
  },
};

const ERROR_CARD_CHANGE_PAYMENT_METHOD: IResponseError = {
  error_messages: {
    code: 500,
    description: "Change the payment method, it will not be accepted in the collection",
    errors: "Payment method declined",
    parameter_name: "Card 3DS",
  },
};

const ERROR_3DS_PAYMENT_METHOD: IResponseError = {
  error_messages: {
    code: 500,
    description: "Payment error with 3DS authentication",
    errors: "Payment error with 3DS authentication",
    parameter_name: "3DS",
  },
};

export {
  ERROR_LOADING_LIB,
  ERROR_CREATING_SESSION,
  ERROR_UNDEFINED_BILLING_ADDRESS,
  ERROR_UNDEFINED_SHIPPING_ADDRESS,
  ERROR_CREATING_PAYMENT_OBJECT,
  ERROR_UNDEFINED_PAYMENT_METHOD,
  ERROR_CARD_AUTH_NOT_SUPPORTED,
  ERROR_CARD_CHANGE_PAYMENT_METHOD,
  ERROR_3DS_PAYMENT_METHOD,
};
