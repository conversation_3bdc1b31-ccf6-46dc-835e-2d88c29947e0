export interface IPostTransactionalNotification {
  notificationCode: string;
  notificationType: string;
}

interface INotificationLink {
  rel: string;
  href: string;
  media: string;
  type: string;
}

interface INotificationQRCode {
  id: string;
  amount: {
    value: number;
  };
  text: string;
  links: INotificationLink[];
}

interface INotificationHolder {
  name: string;
  tax_id: string;
}

interface INotificationPix {
  notification_id: string;
  end_to_end_id: string;
  holder: INotificationHolder;
}

interface INotificationPaymentMethod {
  type: string;
  pix?: INotificationPix;
}

interface INotificationPaymentResponse {
  code: string;
  message: string;
  reference: string;
}

interface INotificationSummary {
  total: number;
  paid: number;
  refunded: number;
}

interface INotificationAmount {
  value: number;
  currency: string;
  summary: INotificationSummary;
}

interface INotificationCharge {
  id: string;
  reference_id: string;
  status: string;
  created_at: string;
  paid_at: string;
  description: string;
  amount: INotificationAmount;
  payment_response: INotificationPaymentResponse;
  payment_method: INotificationPaymentMethod;
  links: INotificationLink[];
}

interface INotificationPhone {
  country: string;
  area: string;
  number: string;
  type: string;
}

interface INotificationCustomer {
  name: string;
  email: string;
  tax_id: string;
  phones: INotificationPhone[];
}

interface INotificationItem {
  reference_id: string;
  name: string;
  quantity: number;
  unit_amount: number;
}

interface INotificationAddress {
  street: string;
  number: string;
  complement: string;
  locality: string;
  city: string;
  region_code: string;
  country: string;
  postal_code: string;
}

interface INotificationShipping {
  address: INotificationAddress;
}

export interface ITransactionalNotification {
  id: string;
  reference_id: string;
  created_at: string;
  shipping: INotificationShipping;
  items: INotificationItem[];
  customer: INotificationCustomer;
  charges: INotificationCharge[];
  qr_code: INotificationQRCode[];
  links: INotificationLink[];
}

export type INotificationPagSeguro = ITransactionalNotification | IPostTransactionalNotification;
