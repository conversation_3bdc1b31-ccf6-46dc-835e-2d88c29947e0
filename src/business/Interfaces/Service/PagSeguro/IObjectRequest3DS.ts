import { IAmount, ICustomer, PagSeguroPaymentMethodType } from "src/business/Interfaces/Service/PagSeguro/IObjects";

export interface ICardPagSeguro {
  id?: string;
  encrypted?: string;
  number?: string;
  expMonth?: string;
  expYear?: string;
  holder: {
    name: string;
  };
}

export interface IPaymentMethodDebtCard {
  type: PagSeguroPaymentMethodType;
  installments: number;
  card: ICardPagSeguro;
}

export interface CardAddress {
  street: string;
  number: string;
  complement?: string | null;
  city: string;
  regionCode: string;
  country: string;
  postalCode: string;
}

export interface IObjectRequest3DS {
  customer: ICustomer;
  paymentMethod: IPaymentMethodDebtCard;
  amount: IAmount;
  billingAddress: CardAddress;
  shippingAddress: CardAddress;
  dataOnly: boolean; // Flag indicando o fluxo 3DS Data-Only (Deve ser sempre false)
  notification_urls: string[];
}
