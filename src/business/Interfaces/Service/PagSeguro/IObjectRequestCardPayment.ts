import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ustomer,
  <PERSON>tem<PERSON><PERSON>r,
  PagSeguroPaymentMethodType,
} from "src/business/Interfaces/Service/PagSeguro/IObjects";

export interface ICardPagSeguro {
  id?: string;
  encrypted?: string;
  security_code?: string; // Validar necessidade do cvv nas compra com cartão de debt
  holder: {
    name: string;
  };
  store: boolean;
}

interface IPaymentMethodCard {
  type: PagSeguroPaymentMethodType;
  installments: number;
  capture: boolean;
  soft_descriptor: string; // NOTE-Nome na Fatura do cliente (0-17 characters)
  card: ICardPagSeguro;
  authentication_method?: {
    type: string;
    id: string;
  };
}

export interface Charge {
  reference_id: string;
  description: string;
  amount: IAmount;
  payment_method: IPaymentMethodCard;
}

export interface IObjectRequestPayment {
  reference_id: string;
  customer: ICustomer;
  items: IItemOrder[];
  qr_codes?: [
    {
      amount: {
        value: number;
      };
      expiration_date: Date;
    },
  ];
  shipping: { address: IAddress };
  notification_urls?: string[];
  charges?: Charge[];
}
