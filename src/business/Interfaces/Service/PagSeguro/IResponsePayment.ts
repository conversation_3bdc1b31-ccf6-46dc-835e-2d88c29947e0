import {
  <PERSON>,
  <PERSON><PERSON>tat<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  II<PERSON><PERSON>rder,
  PagSeguroPaymentMethodType,
  LinksType,
} from "src/business/Interfaces/Service/PagSeguro/IObjects";

export interface Ilink {
  rel: string;
  href: string;
  media: string;
  type: LinksType;
}

export interface ICharge {
  id: string;
  status: ChargeStatus;
  created_at: Date;
  paid_at: Date;
  reference_id: string;
  description: string;
  amount: {
    value: number;
    currency: string;
    summary: {
      total: number;
      paid: number;
      refunded: number;
    };
  };
  payment_response: {
    code: number;
    message: string;
    reference: string;
  };
  payment_method: {
    type: PagSeguroPaymentMethodType;
    installments: number;
    capture: boolean;
    soft_descriptor: string; // NOTE-Nome na Fatura do cliente
    card: {
      id: string;
      number: string;
      exp_month: number;
      exp_year: number;
      security_code: string;
      store: boolean;
      brand: Brand;
      first_digits: string;
      last_digits: string;
      holder: {
        name: string;
        tax_id: string;
      };
    };
  };
  notification_urls: string[];
  metadata: any;
  links: <PERSON><PERSON>;
}

export interface IQRCode {
  id: string;
  expiration_date: Date;
  amount: {
    value: number;
  };
  text: string;
  links: Ilink[];
}

export interface IResponsePayment {
  id: string;
  reference_id: string;
  created_at: Date;
  shipping: {
    address: IAddress;
  };
  items: IItemOrder[];
  amount?: {
    currency: string;
    additional: number;
    discount: number;
  };
  customer: ICustomer;
  charges: ICharge[];
  qr_codes?: IQRCode[];
  links: Ilink[];
}

export interface IResponseCancelPayment {
  id: string;
  reference_id: string;
  status: string;
  created_at: Date;
  paid_at: Date;
  description: string;
  amount: {
    value: number;
    currency: string;
    summary: {
      total: number;
      paid: number;
      refunded: number;
    };
  };
  payment_response: {
    code: string;
    message: string;
    reference: string;
  };
  payment_method: {
    type: PagSeguroPaymentMethodType;
    installments: number;
    capture: boolean;
    card: {
      brand: Brand;
      first_digits: string;
      last_digits: string;
      exp_month: string;
      exp_year: string;
      holder: {
        name: string;
      };
    };
  };
  links: string[];
  notification_urls: string[];
  metadata: any;
}

export interface IResponseError {
  error_messages: {
    code: number;
    description: string;
    parameter_name: string;
    errors: string;
  };
  traceId?: string;
}

export type statusResponse3DS = "AUTH_FLOW_COMPLETED" | "AUTH_NOT_SUPPORTED" | "CHANGE_PAYMENT_METHOD";

export interface IResponse3DSSucess {
  status: statusResponse3DS;
  id?: string;
}

export interface IResponse3DSFailure {
  httpStatus: number;
  traceId: string;
  message: string;
  errorMessages?: {
    code: string;
    description: string;
    parameterName: string;
  };
}

export function isResponseError(object: unknown): object is IResponseError {
  if (object !== null && typeof object === "object") {
    return "error_messages" in object;
  }
  return false;
}

export function isResponse3DSError(object: unknown): object is IResponse3DSFailure {
  if (object !== null && typeof object === "object") {
    return "traceId" in object;
  }
  return false;
}

export function dataCardPagSeguroToExpiration(expMonth: number, expYear: number) {
  return `${expMonth}${String(expYear).substring(2, 4)}`;
}
