export interface IFinancialDetails {
  horaInicialTransacao: string;
  dataVendaAjuste: string;
  horaVendaAjuste: string;
  tipoEvento: string;
  tipoTransacao: string;
  nsu: null | string;
  filler1: string;
  cardBin: null | string;
  cardHolder: null | string;
  autorizacao: null | string;
  cv: null | number;
  numeroSerieLeitor: null | number;
  usoInternoPS: string;
  tipo_registro: string;
  estabelecimento: string;
  data_inicial_transacao: string;
  codigo_transacao: string;
  codigo_venda: null | string;
  valor_total_transacao: number;
  valor_parcela: number;
  pagamento_prazo: string;
  plano: string;
  parcela: string;
  quantidade_parcela: string;
  data_movimentacao: string;
  taxa_parcela_comprador: null | string;
  tarifa_boleto_compra: null | string;
  valor_original_transacao: number;
  taxa_parcela_vendedor: null | string;
  taxa_intermediacao: null | number;
  tarifa_intermediacao: null | number;
  tarifa_boleto_vendedor: null | string;
  taxa_rep_aplicacao: null | string;
  valor_liquido_transacao: number;
  taxa_antecipacao: number;
  valor_liquido_antecipacao: number;
  status_pagamento: string;
  identificador_revenda: null | string;
  meio_pagamento: string;
  instituicao_financeira: string;
  canal_entrada: string;
  leitor: null | string;
  meio_captura: string;
  cod_banco: string;
  banco_agencia: string;
  conta_banco: string;
  num_logico: null | string;
  tx_id?: string;
}
