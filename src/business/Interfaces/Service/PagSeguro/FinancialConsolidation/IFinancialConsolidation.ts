import { IFinancialBalances } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialBalances";
import { IFinancialDetails } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialDetails";
import { IPagination } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IPagination";

export interface IFinancialConsolidationResponse {
  detalhes: IFinancialDetails[];
  saldos: IFinancialBalances[];
  pagination: IPagination;
}
