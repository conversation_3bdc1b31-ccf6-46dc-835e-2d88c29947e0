import { ECardMethod, ETransactionStatus } from "@prisma/client";
import { ICreateTransactionViewModel } from "src/api/ViewModels/Transaction/ICreate";
import { EPagSeguroType } from "src/business/Enums/EPagSeguroType";
import { EPaymentMethod } from "src/business/Enums/Models/EPaymentMethod";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { IFinancialConsolidationResponse } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialConsolidation";
import { INotificationResponse } from "src/business/Interfaces/Service/PagSeguro/INotificationResponse";
import { IObjectRequest3DS } from "src/business/Interfaces/Service/PagSeguro/IObjectRequest3DS";
import { IObjectRequestPayment } from "src/business/Interfaces/Service/PagSeguro/IObjectRequestCardPayment";
import {
  ChargeStatus,
  EnvPagSeguro,
  ISession,
  PagSeguroPaymentMethodType,
} from "src/business/Interfaces/Service/PagSeguro/IObjects";
import {
  IResponse3DSSucess,
  IResponseCancelPayment,
  IResponseError,
  IResponsePayment,
} from "src/business/Interfaces/Service/PagSeguro/IResponsePayment";

export type SetUpParams = {
  session: string;
  env: EnvPagSeguro;
};

export type EncryptCardParams = {
  publicKey: string;
  holder: string;
  number: string;
  expMonth: string;
  expYear: string;
  securityCode: string;
};

export type Authenticate3DSParams = {
  data: IObjectRequest3DS;
};
export interface ILibPagSeguro {
  PagSeguroError: any;
  authenticate3DS: (params: Authenticate3DSParams) => Promise<IResponse3DSSucess>;
  encryptCard: (params: EncryptCardParams) => any;
  env: EnvPagSeguro;
  setUp: (params: SetUpParams) => void;
}
export interface IPagSeguroService {
  paymentOrder(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    userAdress?: IAddress,
  ): Promise<IResponsePayment | IResponseError>;
  flowCreditCard(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
  ): Promise<IResponseError | IResponsePayment>;
  flowDebtCard(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    userAdress?: IAddress,
  ): Promise<IResponseError | IResponsePayment>;
  flowPix(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
  ): Promise<IResponseError | IResponsePayment>;
  preparingObjectRequest(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    qrCodes: boolean,
    response3DS?: IResponse3DSSucess,
    notificationUrls?: string[],
  ): IObjectRequestPayment | IResponseError;
  preparing3DSObjectRequest(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    billingAddress?: IAddress,
  ): IObjectRequest3DS | IResponseError;
  requestPagSeguro(data: IObjectRequestPayment): Promise<IResponsePayment | IResponseError>;
  cancelPaymentPagSeguro(chargeId: string, amount: number): Promise<IResponseCancelPayment | IResponseError>;
  resquest3DSPagSeguro(data: IObjectRequest3DS): Promise<IResponse3DSSucess | IResponseError>;
  getLibPagSeguro(): Promise<ILibPagSeguro | undefined>;
  createSession(): Promise<ISession>;
  paymentMethodToPagSeguroMethod(paymentMethod: EPaymentMethod): PagSeguroPaymentMethodType;
  pagSeguroMethodToCardMethod(pgMethod: PagSeguroPaymentMethodType): ECardMethod;
  cardMethodToPagSeguroMethod(localMethod: ECardMethod): PagSeguroPaymentMethodType;
  pagSeguroStatusToInternalStatusTransaction(pgStatus: ChargeStatus): ETransactionStatus;
  decimalToInt(value: number): number;
  getNotification(notificationCode: string): Promise<INotificationResponse | undefined>;
  getTransaction(transactionPlatformId: string): Promise<IResponsePayment | undefined>;
  getFinancialConsolidation(
    date: string,
    page: number,
    pageSize: number,
    type: EPagSeguroType,
  ): Promise<IFinancialConsolidationResponse | null>;
}
