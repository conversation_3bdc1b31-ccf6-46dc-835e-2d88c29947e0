import { IntegrationHubCitiesDTO } from "src/business/DTOs/IntegrationHub/IntegrationHubCities";
import { IntegrationHubDistrictsDTO } from "src/business/DTOs/IntegrationHub/IntegrationHubDistricts";
import IntegrationHubZipCodeDTO from "src/business/DTOs/IntegrationHub/IntegrationHubZipCode";
import { IResponseErrorIntegrationHub } from "src/business/Interfaces/Tools/IIntegrationErrorResponse";

export interface IIntegrationHubService {
  getStates(limit?: number, page?: number, name?: string): Promise<string[] | IResponseErrorIntegrationHub>;
  getCitiesByStateShortName(
    stateShortName: string,
    limit?: number,
    page?: number,
    name?: string,
  ): Promise<IntegrationHubCitiesDTO[] | IResponseErrorIntegrationHub>;
  getDistrictsByCityId(
    cityId: string,
    limit?: number,
    page?: number,
    name?: string,
  ): Promise<IntegrationHubDistrictsDTO[] | IResponseErrorIntegrationHub>;

  getByZipCode(zipCode: string): Promise<IntegrationHubZipCodeDTO | IResponseErrorIntegrationHub>;
}
