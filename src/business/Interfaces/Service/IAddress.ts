import { IAddressOptionDTO } from "src/business/DTOs/Address/IAddressOption";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IAddressService extends IBaseService<IAddress> {
  create(address: IAddress, userId?: string): Promise<string>;
  createWithoutRelate(address: IAddress): Promise<string | boolean>;
  // getPaged(currentPage: number, pageSize: number, userId: string, name?: string): Promise<PagedResult<IAddress>>;
  getAddressesByUserId(userId: string): Promise<IAddress[]>;
  getAddresseOptionsByUserId(userId: string): Promise<IAddressOptionDTO[]>;
  // deleteUserAddress(id: string): Promise<DeleteResult[]>;
  getDefaultAddress(userId: string): Promise<IAddress | null>;
  updateSwitchDefaulAddress(addressUpdate: IAddress, userId: string): Promise<number>;
}
