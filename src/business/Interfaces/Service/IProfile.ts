import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { IProfile } from "src/business/Interfaces/Prisma/IProfile";

export interface IProfileService extends IBaseService<IProfile> {
  create(data: IProfile): Promise<boolean>;
  getWithPermission(id: string): Promise<IProfile | null>;
  loadSingleton(): Promise<void>;
  getApprovedProfiles(userId: string): Promise<IProfile[]>;
}
