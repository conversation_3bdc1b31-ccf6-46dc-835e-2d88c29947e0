import { ILoginSessionAggregateDTO } from "src/business/DTOs/LoginSession/ILoginSession";
import { ILoginSessionInfoDTO } from "src/business/DTOs/LoginSession/ILoginSessionInfo";
import { ILoginSession } from "src/business/Interfaces/Prisma/ILoginSession";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface ILoginSessionService extends IBaseService<ILoginSession> {
  create(session: ILoginSession): Promise<boolean>;
  getAllLoginSessionsInAWeek(): Promise<ILoginSession[]>;
  updateLastAccess(userId: string): Promise<boolean>;
  getNumberOfAccessByDay(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]>;
  getNumberOfAccessByTime(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]>;
  getNumberOfAccessAndMaxHour(): Promise<ILoginSessionInfoDTO>;
}
