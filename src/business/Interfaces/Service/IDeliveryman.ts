import { ILocationViewModel } from "src/api/ViewModels/Location/IViewModel";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { UnknownObject } from "src/business/DTOs/UnknownObject";
import { IDeliveryman } from "src/business/Interfaces/Prisma/IDeliveryman";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IDeliverymanService extends IBaseService<IDeliveryman> {
  updateDeliverymanProfile(userId: string, data: IDeliveryman, attachments?: { id: string }[]): Promise<boolean>;
  validate(data: UnknownObject): Promise<IDeliveryman>;
  getDeliverymanByStatus(currentPage: string, pageSize: string, status: string): Promise<PagedResult<IDeliveryman>>;
  getDeliveryman(userId: string): Promise<IDeliveryman | null>;
  getDeliverymanLocation(cpf: string): Promise<IDeliveryman | null>;
  updateDeliverymanLocation(userId: string, location: ILocationViewModel): Promise<boolean>;
  update(deliveryman: <PERSON>eliveryman): Promise<boolean>;
  updateDeliverymanPixKey(userId: string, pixKey: string);
  getCheckPixKey(userId: string)
}
