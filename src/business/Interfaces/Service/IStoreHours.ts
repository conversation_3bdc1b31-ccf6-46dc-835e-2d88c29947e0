import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IStoreHoursService extends IBaseService<IStoreHours> {
  create(storeId: string, storeHours: IStoreHours[]): Promise<boolean>;
  updateByStore(storeId: string, storeHours: IStoreHours[]): Promise<boolean>;
  deleteByStoreId(storeId: string): Promise<number>;
  getStoreHoursByStoreId(storeId: string): Promise<IStoreHours[]>;
}
