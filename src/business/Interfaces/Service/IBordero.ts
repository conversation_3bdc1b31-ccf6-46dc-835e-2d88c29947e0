import { EBorderoStatus, EPayoutOwner } from "@prisma/client";
import IBorderoListDTO from "src/business/DTOs/Bordero/IBordero";
import { ICreateBorderoResultDTO } from "src/business/DTOs/Bordero/ICreateBorderoResult";
import { IValidateBorderoResultDTO } from "src/business/DTOs/Bordero/IValidateBorderoResult";
import IBorderoPaymentDTO from "src/business/DTOs/Bordero/IBorderoPayment";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IBordero } from "src/business/Interfaces/Prisma/IBordero";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IBorderoService extends IBaseService<IBordero> {
  create(cpfCnpj: string, payoutOwner: EPayoutOwner): Promise<ICreateBorderoResultDTO>;
  updateBordero(bordero: IBordero, payouts?: string[]): Promise<boolean>;
  delete(borderoId: string): Promise<boolean>;
  getBorderoPaged(
    currentPage: string,
    pageSize: string,
    filterStatus?: EBorderoStatus,
    filterCpfCnpj?: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    orderBy?: string,
    sortDirection?: string,
  ): Promise<PagedResult<IBorderoListDTO>>;
  validate(cpfCnpj: string, payoutOwner: EPayoutOwner): Promise<IValidateBorderoResultDTO>;
  getBorderoDetails(borderoId: string): Promise<IBorderoPaymentDTO | null>;
  changeBorderoStatus(borderoId: string, status: EBorderoStatus): Promise<boolean>;
}
