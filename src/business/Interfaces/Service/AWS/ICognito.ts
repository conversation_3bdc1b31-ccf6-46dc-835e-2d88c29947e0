import {
  AdminConfirmSignUpCommandInput,
  AdminConfirmSignUpCommandOutput,
  AdminCreateUserCommandInput,
  AdminCreateUserCommandOutput,
  AdminDeleteUserCommandInput,
  AdminDeleteUserCommandOutput,
  AdminInitiateAuthCommandInput,
  AdminInitiateAuthCommandOutput,
  AdminRespondToAuthChallengeCommandInput,
  AdminRespondToAuthChallengeCommandOutput,
  AdminUpdateUserAttributesCommandInput,
  AdminUpdateUserAttributesCommandOutput,
  ChangePasswordCommandInput,
  ConfirmForgotPasswordCommandInput,
  ConfirmForgotPasswordCommandOutput,
  ForgotPasswordCommandInput,
  ForgotPasswordCommandOutput,
  RevokeTokenCommandInput,
  RevokeTokenCommandOutput,
  SignUpCommandInput,
  SignUpCommandOutput,
  VerifyUserAttributeCommandInput,
  VerifyUserAttributeCommandOutput,
  GetUserCommandInput,
  GetUserCommandOutput,
  GetUserAttributeVerificationCodeCommandOutput,
  GetUserAttributeVerificationCodeCommandInput,
} from "@aws-sdk/client-cognito-identity-provider";

export interface ExchangeCodeResponse {
  id_token: string;
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

export interface ICognitoService {
  checkIfUserExists(username: string): Promise<boolean>;
  createUser(args: Omit<SignUpCommandInput, "ClientId" | "SecretHash">): Promise<SignUpCommandOutput>;
  adminCreateUser(args: Omit<AdminCreateUserCommandInput, "UserPoolId">): Promise<AdminCreateUserCommandOutput>;
  adminRespondToAuthChallenge(
    args: Omit<AdminRespondToAuthChallengeCommandInput, "UserPoolId" | "ClientId">,
  ): Promise<AdminRespondToAuthChallengeCommandOutput>;
  confirmSignUp(args: Omit<AdminConfirmSignUpCommandInput, "UserPoolId">): Promise<AdminConfirmSignUpCommandOutput>;
  initiateAuth(
    args: Omit<AdminInitiateAuthCommandInput, "UserPoolId" | "ClientId">,
  ): Promise<AdminInitiateAuthCommandOutput>;
  updateUser(
    args: Omit<AdminUpdateUserAttributesCommandInput, "UserPoolId">,
  ): Promise<AdminUpdateUserAttributesCommandOutput>;
  updatePassword(passwordParams: ChangePasswordCommandInput): Promise<void>;
  deleteUser(args: Omit<AdminDeleteUserCommandInput, "UserPoolId">): Promise<AdminDeleteUserCommandOutput>;
  revokeAccess(args: Omit<RevokeTokenCommandInput, "ClientId" | "ClientSecret">): Promise<RevokeTokenCommandOutput>;
  exchangeCode(code: string): Promise<ExchangeCodeResponse | null>;
  resetPassword(
    args: Omit<ForgotPasswordCommandInput, "ClientId" | "SecretHash">,
  ): Promise<ForgotPasswordCommandOutput>;
  resetPassword(
    args: Omit<ForgotPasswordCommandInput, "ClientId" | "SecretHash">,
  ): Promise<ForgotPasswordCommandOutput>;
  confirmPassword(
    args: Omit<ConfirmForgotPasswordCommandInput, "ClientId" | "SecretHash">,
  ): Promise<ConfirmForgotPasswordCommandOutput>;
  resendEmailConfirmation(
    args: GetUserAttributeVerificationCodeCommandInput,
  ): Promise<GetUserAttributeVerificationCodeCommandOutput>;
  verifyUserEmail(args: VerifyUserAttributeCommandInput): Promise<VerifyUserAttributeCommandOutput>;
  getEmailStatus(args: GetUserCommandInput): Promise<GetUserCommandOutput>;
}

export interface IdToken {
  sub: string;
  aud: string;
  email_verified: boolean;
  token_use: string;
  auth_time: number;
  iss: string;
  "cognito:username": string;
  exp: number;
  given_name: string;
  iat: number;
  email: string;
  jti: string;
  origin_jti: string;
  [key: string]: string | number | boolean;
}
