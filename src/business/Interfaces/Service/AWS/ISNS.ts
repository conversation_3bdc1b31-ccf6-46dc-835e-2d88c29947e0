import {
  PublishCommandInput,
  PublishCommandOutput,
  CreatePlatformEndpointCommandInput,
  CreatePlatformEndpointCommandOutput,
  DeleteEndpointCommandInput,
  DeleteEndpointCommandOutput,
  GetEndpointAttributesCommandInput,
  GetEndpointAttributesCommandOutput,
  CreateTopicCommandInput,
  CreateTopicCommandOutput,
  GetTopicAttributesCommandInput,
  GetTopicAttributesCommandOutput,
  ListTopicsCommandInput,
  ListTopicsCommandOutput,
  SubscribeCommandInput,
  SubscribeCommandOutput,
  UnsubscribeCommandInput,
  UnsubscribeCommandOutput,
  ListSubscriptionsCommandInput,
  ListSubscriptionsCommandOutput,
  ListSubscriptionsByTopicCommandInput,
  ListSubscriptionsByTopicCommandOutput,
  GetSubscriptionAttributesCommandInput,
  GetSubscriptionAttributesCommandOutput,
  DeleteTopicCommandInput,
  DeleteTopicCommandOutput,
} from "@aws-sdk/client-sns";

export interface ISNSService {
  publish(args: PublishCommandInput): Promise<PublishCommandOutput>;
  createEndpoint(args: CreatePlatformEndpointCommandInput): Promise<CreatePlatformEndpointCommandOutput>;
  deleteEndpoint(args: DeleteEndpointCommandInput): Promise<DeleteEndpointCommandOutput>;
  getEndpointAttributes(args: GetEndpointAttributesCommandInput): Promise<GetEndpointAttributesCommandOutput>;
  createTopic(args: CreateTopicCommandInput): Promise<CreateTopicCommandOutput>;
  getTopicAttributes(args: GetTopicAttributesCommandInput): Promise<GetTopicAttributesCommandOutput>;
  getTopicList(args: ListTopicsCommandInput): Promise<ListTopicsCommandOutput>;
  subscribeDeviceToATopic(args: SubscribeCommandInput): Promise<SubscribeCommandOutput>;
  unsubscribeDeviceFromTopic(args: UnsubscribeCommandInput): Promise<UnsubscribeCommandOutput>;
  getSubscriptionsDevices(args: ListSubscriptionsCommandInput): Promise<ListSubscriptionsCommandOutput>;
  getSubscriptionsDevicesFromTopic(
    args: ListSubscriptionsByTopicCommandInput,
  ): Promise<ListSubscriptionsByTopicCommandOutput>;
  getSubscriptionAttributes(
    args: GetSubscriptionAttributesCommandInput,
  ): Promise<GetSubscriptionAttributesCommandOutput>;
  deleteTopic(args: DeleteTopicCommandInput): Promise<DeleteTopicCommandOutput>;
  listTopic(args: ListTopicsCommandInput) : Promise<ListTopicsCommandOutput>;
}
