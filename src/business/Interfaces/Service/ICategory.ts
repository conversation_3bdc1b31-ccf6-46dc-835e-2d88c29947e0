import { ICategoryDTO } from "src/business/DTOs/Category/ICategory";
import { ICategorySubcategoryDTO } from "src/business/DTOs/Category/ICategorySubcategory";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { ICategory } from "../Prisma/ICategory";

export interface ICategoryService extends IBaseService<ICategory> {
  create(category: ICategory, attachments?: string[]): Promise<boolean>;
  // TODO Check if we can use createMany
  createSeveralCategories(category: ICategory[]): Promise<ICategory[]>;
  getWithSubcategoryByStore(storeId: string): Promise<ICategory[]>;
  getAllCategoriesWithSubCategory(): Promise<ICategory[]>;
  getPagedWithStoreOptions(
    storeId: string,
    currentPage: number,
    pageSize: number,
    filter: string,
  ): Promise<PagedResult<ICategoryDTO>>;
  // getAllFilteredPaginated(filter: string, currentPage: number, pageSize: number): Promise<FilteredCategoryViewModel>;
  createDefaultCategories(): Promise<void>;
  deleteWithRelations(id: string): Promise<boolean>;
  getCategoryByProductWithSubcategory(storeId: string, productId?: string): Promise<ICategorySubcategoryDTO[]>;
  updateCategoryAndSubcategoryRelation(data: ICategory, subcategoryIds: { id: string }[]): Promise<boolean>;
  getPagedWithSubcategory(
    currentPage?: string,
    pageSize?: string,
    filterValue?: string,
    columnFilter?: string,
    fieldOrder?: string,
    order?: IOrder,
  ): Promise<PagedResult<ICategory>>;
  getPagedWithIcon(currentPage?: string, pageSize?: string): Promise<PagedResult<ICategory>>;
  getSelect(categoryName: string, currentPage: string, pageSize: string): Promise<PagedResult<ISelectDTO>>;
  getCategoryDetails(id: string): Promise<ICategory | null>;
}
