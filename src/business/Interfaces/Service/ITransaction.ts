import { ICreateTransactionViewModel } from "src/api/ViewModels/Transaction/ICreate";
import { ICreatePaymentResultDTO } from "src/business/DTOs/ICreatePaymentResult";
import { IListTransactionByOrderDTO } from "src/business/DTOs/Transactions/ListTransactionByOrder";
import { EPagSeguroType } from "src/business/Enums/EPagSeguroType";
import { ECardMethod } from "src/business/Enums/Models/ECardMethod";
import { EPaymentMethod } from "src/business/Enums/Models/EPaymentMethod";
import { ITransaction } from "src/business/Interfaces/Prisma/ITransaction";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { IFinancialConsolidationResponse } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialConsolidation";
import { INotificationPagSeguro } from "src/business/Interfaces/Service/PagSeguro/INotification";
import { ISession } from "src/business/Interfaces/Service/PagSeguro/IObjects";

export interface ITransactionService extends IBaseService<ITransaction> {
  createPayment(
    userId: string,
    orderId: string,
    transaction: ICreateTransactionViewModel,
  ): Promise<ICreatePaymentResultDTO>;
  // checkTransactionStatus(notification: INotificationPagSeguro): Promise<void>;
  handleWebhook(notification: INotificationPagSeguro): Promise<void>;
  cancelPayment(transaction: ITransaction): Promise<boolean>;
  getLastTransactionByUser(userId: string, storeId?: string): Promise<ITransaction | null>;
  getLastTransactionByOrder(orderId: string): Promise<ITransaction | null>;
  isCanceled(orderId: string): Promise<boolean>;
  getTransactionByUserId(userId: string): Promise<ITransaction[]>;
  getTransactionByOrderId(orderId: string): Promise<IListTransactionByOrderDTO[]>;
  createSessionPagSeguro(): Promise<ISession>;
  paymentMethodToCardMethod(paymentMethod: EPaymentMethod): ECardMethod;
  getFinancialConsolidation(
    date: string,
    page: number,
    pageSize: number,
    type: EPagSeguroType,
  ): Promise<IFinancialConsolidationResponse | null>;
}
