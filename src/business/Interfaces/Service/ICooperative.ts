import { ICooperative } from "src/business/Interfaces/Prisma/ICooperative";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface ICooperativeService extends IBaseService<ICooperative> {
  createOrUpdate(data: ICooperative): Promise<boolean>;
  getCooperativeInfoWithAddress(): Promise<ICooperative | null>;
  getCooperativeByCNPJ(cnpj: string): Promise<ICooperative | null>;
}
