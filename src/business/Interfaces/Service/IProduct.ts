import { EAttributeType } from "@prisma/client";
import { Xlsx } from "exceljs";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import { IUpdateCategorySubcategoryDTO } from "src/business/DTOs/Product/IUpdateCategorySubCategories";
import { AttributesAndOptionsIdsViewModel } from "src/api/ViewModels/Attribute/IAttributesAndOptionsIds";
import { IOrder } from "src/business/DTOs/Order";
import { IProductByStoreDTO } from "src/business/DTOs/Product/IProductByStore";
import { IProductByStoreWithFavoriteDTO } from "src/business/DTOs/Product/IProductByStoreWithFavorite";
import { IProductSearchDTO } from "src/business/DTOs/Product/IProductSearch";
import { IProductsFilterDTO } from "src/api/ViewModels/Product/IFilterProducts";

export interface IProductService extends IBaseService<IProduct> {
  create(product: IProduct, attachments?: { id: string }[]): Promise<boolean>;
  getWithCategory(id: string): Promise<IProduct | null>;
  getPagedProductList(
    productFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
  ): Promise<PagedResult<IProduct>>;
  // update(productUpdate: IProduct): Promise<number>;
  updateWithRelations(productUpdate: IProduct): Promise<boolean>;
  getByStore(storeId: string, userId: string, userType?: string): Promise<IProductByStoreWithFavoriteDTO[]>;
  relateProductAttribute(id: string, attributeId: string): Promise<boolean>;
  relateProductAttributeOption(id: string, attributeId: string, atributeOptionId: string): Promise<boolean>;
  getUserFavoriteProducts(userId: string): Promise<IProduct[]>;
  getWithAllDetails(productId: string): Promise<IProduct | null>;
  relateProductFiles(productId: string, filesId: string[]): Promise<boolean>;
  exportByStore(storeId: string): Promise<Xlsx | null>;
  importByStore(storeId: string, xlsx: any): Promise<string>;
  deleteByStore(storeId: string): Promise<number>;
  handleCategory(name: string, description: string): Promise<string | null>;
  handleSubCategory(categoryId: string, name: string, description: string): Promise<string | null>;
  handleAttribute(
    name: string,
    shortDescription: string,
    required: boolean,
    type: EAttributeType,
  ): Promise<string | null>;
  handleAttributeOption(attributeId: string, value: string): Promise<string | null>;
  getPagedListFront(
    currentPage: string,
    pageSize: string,
    storeId?: string,
    filterValue?: string,
    orderBy?: string,
    sortDirection?: IOrder,
    categoryFilter?: string,
    storeFilter?: string,
  ): Promise<PagedResult<IProduct>>;
  saveProductModeration(productModeration: ICreateProductModerationDTO): Promise<boolean>;
  getWithAllDetailsFront(productId: string): Promise<IProduct | null>;
  getProductsSuggestions(userId: string): Promise<IProduct[]>;
  updateProductCategories(id: string, categoryIds: string[]): Promise<boolean>;
  updateProductSubcategories(id: string, productSubcategory: IProductSubcategory[]): Promise<boolean>;
  updateProductAttributes(id: string, attributes: AttributesAndOptionsIdsViewModel[]): Promise<boolean>;
  updateCategoryAndSubcategory(data: IUpdateCategorySubcategoryDTO): Promise<boolean>;
  updateActiveStatus(id: string, status: boolean): Promise<boolean>;
  getById(id: string): Promise<IProduct | null>;
  deleteWithRelations(id: string): Promise<boolean>;
  deleteProductsFiles(storeId: string): Promise<boolean>;
  getProductSearch(
    userId: string,
    currentPage: string,
    pageSize: string,
    filterValue?: string,
    latitude?: number,
    longitude?: number,
    locationFilter?: number,
    minPriceFilter?: string,
    maxPriceFilter?: string,
    category?: string[],
    subcategory?: string[],
  ): Promise<PagedResult<IProductsFilterDTO>>;
}
