import { EPayoutStatus, EPayoutOwner } from "@prisma/client";
import { IPayout } from "src/business/Interfaces/Prisma/IPayout";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { IOrder as IOrderSort } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IPayoutDetailsDTO } from "src/business/DTOs/Payout/IPayoutDetails";
import { IAggregatePayoutsDTO } from "src/business/DTOs/Bordero/IAggregatePayouts";
import { IPayoutListDTO } from "src/business/DTOs/Payout/IPayoutList";
import { IPayoutStatusDTO } from "src/business/DTOs/Payout/IPayoutStatus";
import { Xlsx } from "exceljs";

export interface IPayoutService extends IBaseService<IPayout> {
  createPayout(orderId: string): Promise<boolean>;
  getPagedPayout(
    currentPage: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
    statusFilter?: EPayoutStatus,
    ownerFilter?: EPayoutOwner,
    showOrderTotalizer?: boolean,
    borderoNullFilter?: boolean,
  ): Promise<PagedResult<IPayout>>;
  getPayoutsPaginatedToCreateBordero(
    currentPage: string,
    pageSize: string,
    borderoId: string,
    ownerId: string,
    ownerFilter: EPayoutOwner,
    startDateFilter?: Date,
    endDateFilter?: Date,
    orderBy?: string,
    sortDirection?: IOrderSort,
    filterValue?: string,
  ): Promise<PagedResult<IPayoutListDTO>>;
  getPayoutDetailsById(id: string): Promise<IPayoutDetailsDTO | null>;
  getPayoutsValueTotalizer(
    whereCondition: object | undefined,
    owner: EPayoutOwner,
    status: EPayoutStatus,
  ): Promise<number>;
  getPagedPayoutByBordero(
    currentPage: string,
    pageSize: string,
    borderoId: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
    filterValue?: string,
  ): Promise<PagedResult<IPayout>>;
  getAllPayoutsByBordero(borderoId: string): Promise<IPayoutListDTO[]>;
  updatePayoutsBordero(borderoId: string, payouts: string[]): Promise<boolean>;
  getPayoutsTotalizerByIds(payoutIds?: string[]): Promise<IAggregatePayoutsDTO>;
  getPayoutsWithoutBorderoByDeliveryman(deliverymanId: string): Promise<number>;
  getPayoutsWithoutBorderoByStore(storeId: string): Promise<number>;
  getPayoutsWithoutBorderoByCooperative(cooperativeId: string): Promise<number>;
  updatePayoutsClearBordero(borderoId: string): Promise<number>;
  getCountPayoutsByBordero(borderoId: string): Promise<number>;
  updatePayoutStatus(id: string, status: EPayoutStatus): Promise<boolean>;
  updatePayoutStatusByBordero(borderoId: string, status: EPayoutStatus): Promise<boolean>;
  getPayoutStatusByOrderId(orderId: string): Promise<IPayoutStatusDTO | null>;
  generatePayoutReport(
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
    statusFilter?: EPayoutStatus,
    ownerFilter?: EPayoutOwner,
  ): Promise<Xlsx>;
}
