import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IMailing } from "src/business/Interfaces/Prisma/IMailing";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IMailingService extends IBaseService<IMailing> {
  create(Mailing: IMailing): Promise<boolean | null>;
  delete(id: string): Promise<boolean>;
  executeMailing(MailingId: string): Promise<boolean>;
  associateDevicesToTopic(topicArn: string, usersIds: string[]): Promise<boolean>;
  sendNotificationToMailingTopic(Mailing: IMailing, topicArn: string, usersIds: string[]): Promise<void>;
  getUsersFilteredByMailing(Mailing: IMailing): Promise<IUserMailingDTO[] | null>;
  sendNotificationToUsersEmail(Mailing: IMailing, usersEmails: string[]): Promise<void>;
  sendNotification(Mailing: IMailing, topicArn: string, usersIds: string[]): Promise<boolean>;
  getMailingDetailsById(MailingId: string): Promise<IMailing | null>;
}
