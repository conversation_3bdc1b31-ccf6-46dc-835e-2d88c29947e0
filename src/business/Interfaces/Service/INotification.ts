import IOrderNotificationDTO from "src/business/DTOs/IOrderNotification";
import INotificationDTO from "src/business/DTOs/Notification";
import { EProfile } from "src/business/Enums/Models/EProfile";

export interface INotificationService {
  sendNotificationToUser(userId: string, notification: INotificationDTO): Promise<void>;
  sendNotificationToTopic(
    usersId: string[],
    notification: INotificationDTO,
    topicArn: string,
    mailingId?: string,
  ): Promise<void>;
  sendNotificationToOrderTopic(
    usersId: {userId: string, userProfile: EProfile}[],
    notification: IOrderNotificationDTO,
    topicArn: string,
  ): Promise<any>;
}
