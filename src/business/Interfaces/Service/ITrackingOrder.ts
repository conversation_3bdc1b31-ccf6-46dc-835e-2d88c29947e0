import ITrackingDeliverymanDTO from "src/business/DTOs/TrackingOrder/ITrackingDeliveryman";
import { ITrackingOrder } from "src/business/Interfaces/Prisma/ITrackingOrder";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface ITrackingOrderService extends IBaseService<ITrackingOrder> {
  create(trackingOrder: ITrackingOrder): Promise<boolean>;
  trackOrderDeliveryById(orderId: string): Promise<ITrackingDeliverymanDTO>;
  deleteByOrderId(orderId: string): Promise<boolean>;
}
