import { IDeleteUser } from "src/api/ViewModels/User/IDeleteUser";
import { IReactivateAccountViewModel } from "src/api/ViewModels/User/IReactivateAccount";
import { IVerifyEmailCodeViewModel } from "src/api/ViewModels/User/IVerifyEmailCodeViewModel";
import { IUserInfoViewModel } from "src/api/ViewModels/User/UserInfo.vm";
import { IFilterMailingDTO } from "src/business/DTOs/FilterMailing/IFilterMailing";
import { InvitedUserAuthenticationResult } from "src/business/DTOs/InvitedUserAuthenticationResult";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IEmailVerificationStatusDTO } from "src/business/DTOs/User/IEmailVerificationStatus";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IUserRegisterDTO } from "src/business/DTOs/User/IUserRegister";
import { IUserWithProfilePictureDTO } from "src/business/DTOs/User/IUserWithProfilePicture";
import { UserAuthenticationResult } from "src/business/DTOs/UserAuthenticationResult";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { IUser } from "../Prisma/IUser";

export interface IUserService extends IBaseService<IUser> {
  login({
    email,
    password,
    origin,
  }: {
    email: string;
    password: string;
    origin?: "web" | "app";
  }): Promise<UserAuthenticationResult | InvitedUserAuthenticationResult | null>;
  register(data: IUserRegisterDTO, origin?: "web" | "app"): Promise<UserAuthenticationResult | null>;
  invite(email: string): Promise<boolean | null>;
  getByCognitoId(cognitoId: string): Promise<IUser | null>;
  getByCognitoIdGoogle(cognitoIdGoogle: string): Promise<IUser | null>;
  getWithProfile(id: string): Promise<IUser | null>;
  getWithProfilePictureById(id: string): Promise<IUserWithProfilePictureDTO | null>;
  getByEmail(email: string): Promise<IUser | null>;
  getWithAddress(id: string): Promise<IUser | null>;
  relateUserAddress(userId: string, addressId: string): Promise<void>;
  relateUserFavoriteStore(userId: string, storeId: string): Promise<void>;
  relateUserFavoriteProduct(userId: string, productId: string): Promise<void>;
  deleteUserAddress(addressId: string): Promise<number>;
  deleteFavoriteStore(userId: string, storeId: string): Promise<number>;
  deleteFavoriteProduct(userId: string, productId: string): Promise<number>;
  resetPassword(email: string): Promise<boolean | null>;
  confirmPassword(username: string, verificationCode: string, newPassword: string): Promise<boolean | null>;
  updateUser(data: IUser): Promise<number | null>;
  updatePassword(accessToken: string, previousPassword: string, proposedPassword: string): Promise<boolean | null>;
  getUserProfilesData(userId: string): Promise<IUser | null>;
  resendEmailVerification(clientId: string): Promise<void>;
  verifyEmailCode(verifyEmailProps: IVerifyEmailCodeViewModel): Promise<void>;
  emailStatus(token: string): Promise<IEmailVerificationStatusDTO>;
  relateUserAddress(userId: string, addressId: string): Promise<void | null>;
  relateUserFavoriteStore(userId: string, storeId: string): Promise<void | null>;
  relateUserFavoriteProduct(userId: string, productId: string): Promise<void | null>;
  resetPassword(email: string): Promise<boolean | null>;
  confirmPassword(username: string, verificationCode: string, newPassword: string): Promise<boolean | null>;
  loadDefaultUser(): Promise<void>;
  getQuantityNotification(userId: string, profile: EProfile): Promise<number | null>;
  createUserBySocialLogin(data: IUser): Promise<IUserInfoViewModel | null>;
  notifySocialLoginUser(userId: string): Promise<void>;
  getAllPaged(
    currentPage: string,
    pageSize: string,
    filterName?: string,
    filterProfile?: string,
    filterStatus?: string,
    sortDirection?: string,
  ): Promise<PagedResult<IUser>>;
  updateByUserId(userId: string, data: IUser): Promise<boolean | null>;
  deleteByUserId(userId: string): Promise<boolean | null>;
  disableUser(data: IDeleteUser): Promise<boolean | null>;
  getByMailingFilter(filter: IFilterMailingDTO): Promise<IUserMailingDTO[] | null>;
  createDefaultForDeletedUsers(): Promise<boolean | null>;
  deleteUsersPastRecoverLimit(): Promise<number | null>;
  getPermanentlyDeletedUser(): Promise<boolean | null>;
  getByTemporallyDeletedStatus(email: string): Promise<boolean | null>;
  updateDeletedAndReactivateAccount({ email, cpf }: IReactivateAccountViewModel): Promise<boolean | null>;
  resendEmailConfirmationCode({ email, cpf }: IReactivateAccountViewModel): Promise<boolean>;
  confirmSignUpCode(data: IReactivateAccountViewModel): Promise<boolean>;
  generateSixDigitsCode(): string;
  getEmailsByUsersIds(usersIds: string[]): Promise<string[]>;
  getByCpf(cpf: string): Promise<IUser | null>;
  getByCpfWithDeliveryman(cpf: string): Promise<IUser | null>;
  getUserDetailsBackOffice(id: string): Promise<IUser | null>;
}
