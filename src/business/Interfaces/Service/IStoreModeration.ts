import { ICreateStoreModerationDTO } from "src/business/DTOs/StoreModeration/IStoreModeration";
import { IStoreModeration } from "src/business/Interfaces/Prisma/IStoreModeration";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IStoreModerationService extends IBaseService<IStoreModeration> {
  create(moderation: ICreateStoreModerationDTO): Promise<boolean>;
  getAllModeration(): Promise<IStoreModeration[]>;
  getByStoreId(storeId: string): Promise<IStoreModeration[] | null>;
  getMostRecentStoreModeration(storeId: string): Promise<IStoreModeration | null>;
}
