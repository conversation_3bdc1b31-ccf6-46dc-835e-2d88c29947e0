
import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import { IBaseService } from "src/business/Interfaces/Service/IBase";


export interface IAttributeOptionService
  extends IBaseService<IAttributeOption> {
  // TODO Check createQueue method and compare with createMany
  //createQueue(options: AttributeOption[]): Promise<AttributeOption[]>;
  create(storeSettings: IAttributeOption): Promise<boolean>;
}
