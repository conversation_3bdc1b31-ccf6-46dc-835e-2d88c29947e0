import { EDayOfWeek } from "@prisma/client";
import { Xlsx } from "exceljs";
import { IPaymentMethodsDTO } from "src/business/DTOs/IPaymentMethods";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IListStoresDTO } from "src/business/DTOs/Store/IListStores";
import { IStoreNameDTO } from "src/business/DTOs/Store/IStoreName";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { ICreateStoreModerationDTO } from "src/business/DTOs/StoreModeration/IStoreModeration";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { IStoreShowcaseDTO } from "src/business/DTOs/Store/IStoreShowcase";
import { IListUserStoreDTO } from "src/business/DTOs/Store/IListUserStore";
import { IStoreShowcaseWithUrlsDTO } from "src/business/DTOs/Store/IStoreShowcaseWithUrls";

export interface IStoreService extends IBaseService<IStore> {
  create(store: IStore, attachments?: { id: string }[]): Promise<boolean>;
  getStoreInfoById(storeId: string): Promise<IStore | null>;
  getStoresByUserId(userId: string): Promise<IListUserStoreDTO[]>;
  getStoresNameByUserId(userId: string): Promise<IStoreNameDTO[]>;
  relateStoreUser(storeId: string, userId: string): Promise<boolean>;
  getPagedListWithFavoriteStores(
    currentDay: EDayOfWeek,
    currentTime: Date,
    storeFilter: string,
    categoryIdFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
    latitude: number,
    longitude: number,
  ): Promise<PagedResult<IListStoresDTO>>;
  getPaymentMethodsByStoreAndUser(storeId: string, userId: string): Promise<IPaymentMethodsDTO | null>;
  getPagedListFront(
    currentPage: string,
    pageSize: string,
    filterValue?: string,
    orderBy?: string,
    sortDirection?: IOrder,
  ): Promise<{
    result: IStore[];
    totalCount: number;
    totalPages: number;
  }>;
  getWithAllDetailsFront(storeId: string): Promise<IStore | null>;
  saveStoreModeration(storeModeration: ICreateStoreModerationDTO): Promise<boolean>;
  updateStoreCategories(id: string, storeCategory: IStoreCategory[]): Promise<boolean>;
  checkAvailabilityByStoreId(storeId: string): Promise<boolean>;
  // FIXME Check transaction
  exportStores(): Promise<Xlsx | null>;
  // FIXME Check transaction
  importStores(xlsx: any): Promise<string>;
  deleteWithRelations(id: string): Promise<boolean>;
  getStoreShowcase(storeId: string): Promise<IStoreShowcaseWithUrlsDTO | null>;
  getSelect(storeName: string, currentPage: number, pageSize: number): Promise<PagedResult<ISelectDTO>>;
  filterStoresByUserDistance(latitude: number, longitude: number, distance: number): Promise<string[]>;
  getStoreByCpfCnpj(cpfCnpj: string): Promise<IStore | null>;
  getHasStoreMissingPixKey(userId: string): Promise<boolean>
}
