import { EMessageStatus } from "@prisma/client";
import { IUserMessage } from "src/business/Interfaces/Prisma/IUserMessage";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IUserMessageService extends IBaseService<IUserMessage> {
  updateStatus(messageId: string, profileId: string, status: EMessageStatus): Promise<number>;
  updateMultipleMessages(messageIds: string[], profileId: string, status: EMessageStatus): Promise<number>;
}
