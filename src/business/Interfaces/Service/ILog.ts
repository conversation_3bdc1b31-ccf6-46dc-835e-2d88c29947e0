import { PagedResult } from "src/business/DTOs/PagedResult";
import { IOrder as IOrderSort } from "src/business/DTOs/Order";
import { ILog } from "src/business/Interfaces/Prisma/ILog";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { ILogDTO } from "src/business/DTOs/Log/ILog";

export interface ILogService extends IBaseService<ILog> {
  getPagedLog(
    currentPage: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
  ): Promise<PagedResult<ILog>>;
  getLogDetailsById(
    logId: string,
    entity: string,
    entityId: string,
    createdAt: Date,
    action: string,
  ): Promise<ILogDTO | null>;
}
