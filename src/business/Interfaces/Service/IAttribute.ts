import { IAttributeDTO } from "src/business/DTOs/Attribute/IAttribute";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IAttribute } from "src/business/Interfaces/Prisma/IAttribute";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IAttributeService extends IBaseService<IAttribute> {
  // TODO Check createQueue method and compare with createMany
  // createQueue(attributes: IAttribute[]): Promise<IAttribute[]>;
  getWithAttributeOption(attributeId: string): Promise<IAttribute | null>;
  getWithProductAttributeOption(productId: string): Promise<IAttribute[]>;
  create(address: IAttribute): Promise<boolean>;
  getPagedWithAllAttributeOption(
    productId: string,
    currentPage: string,
    pageSize: string,
    filter: string
  ): Promise<PagedResult<IAttributeDTO>>;
}
