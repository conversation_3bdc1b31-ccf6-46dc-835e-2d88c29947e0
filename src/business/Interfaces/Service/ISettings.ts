import { ISettingsViewModel } from "src/api/ViewModels/Settings/ISettings";
import { ISettings } from "src/business/Interfaces/Prisma/ISettings";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface ISettingsService extends IBaseService<ISettings> {
  getFormatted(): Promise<ISettings[]>;
  updateByUserId(userId: string, settingUpdate: ISettingsViewModel): Promise<boolean>;
  loadSettings(): void;
  getByName(name: string): Promise<ISettings | null>;
}
