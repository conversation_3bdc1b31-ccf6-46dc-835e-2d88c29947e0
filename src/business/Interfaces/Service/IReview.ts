import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IReviewService extends IBaseService<IReview> {
  create(review: IReview): Promise<boolean>;
  getStoreReviews(storeId: string): Promise<IReview[]>;
  getUserReviews(userId: string): Promise<IReview[]>;
  getOrderReviews(orderId: string, deliverymanReview?: boolean): Promise<IReview | null>;
}
