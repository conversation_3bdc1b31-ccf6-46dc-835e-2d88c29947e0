import { IChatMessageDTO } from "src/business/DTOs/Chat/IChatMessage";
import { IChat } from "src/business/Interfaces/Prisma/IChat";
import { IBaseService } from "src/business/Interfaces/Service/IBase";

export interface IChatService extends IBaseService<IChat> {
  create(chat: IChat): Promise<boolean>;
  getByOrderId(orderId: string): Promise<IChat | null>;
  getChatMessages(orderId: string): Promise<IChatMessageDTO[]>;
  sendChatNotifications(orderId: string, socketIds: string[]): Promise<void>;
}
