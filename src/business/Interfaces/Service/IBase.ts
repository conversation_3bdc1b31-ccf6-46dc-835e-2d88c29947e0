import { <PERSON>Orde<PERSON> } from "src/business/DTOs/Order";
import { PagedResultWithCondition } from "src/business/DTOs/PagedResult";
import { PagedSearchWhereCondtion } from "src/business/DTOs/PagedWhereCondition";

export interface IBaseService<T> {
  update(item: T): Promise<boolean>;
  getById(id: string): Promise<T | null>;
  delete(id: string): Promise<boolean>;
  getAll(): Promise<T[]>;
  getPaged(
    columnFilter?: string,
    filterValue?: string,
    currentPage?: string,
    pageSize?: string,
    field?: string,
    order?: IOrder,
    includes?: any,
    where?: PagedSearchWhereCondtion,
    select?: any,
  ): Promise<PagedResultWithCondition<T>>;
  isValid(): boolean;
}
