import { EMessageSendingType, EMessageType } from "@prisma/client";
import { IMessageContent } from "src/business/Interfaces/Prisma/IMessageContent";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { IUserMessage } from "src/business/Interfaces/Prisma/IUserMessage";

export interface IMessage {
  id?: string;
  title: string;
  type: EMessageType;
  sendingType: EMessageSendingType[];
  senderId?: string | null;
  directMailId?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  sender?: Partial<IUser> | null;
  userMessage?: IUserMessage[];
  content?: IMessageContent[];
}

// export class Message extends Base {
//   @Column()
//   title: string;

//   @Column({
//     type: "enum",
//     enum: EMessageType,
//   })
//   type: EMessageType;

//   @Column({
//     type: "enum",
//     enum: EMessageSendingType,
//     array: true,
//   })
//   sendingType: EMessageSendingType[];

//   @Column({ nullable: true })
//   senderId?: string;

//   @OneToOne(() => User)
//   @JoinColumn({ name: "senderId" })
//   sender: User;

//   @Column({ nullable: true, type: "uuid" })
//   directMailId?: string;

//   @CreateDateColumn()
//   createdAt: Date;

//   @UpdateDateColumn()
//   updatedAt: Date;

//   @OneToMany(() => MessageContent, (messageContent) => messageContent.message, {
//     cascade: ["insert", "update"],
//   })
//   content: MessageContent[];

//   @OneToMany(() => UserMessage, (userMessage) => userMessage.message, {
//     cascade: ["insert", "update"],
//   })
//   userMessage: UserMessage[];
// }
