import { <PERSON>ardStatus, ECardMethod, ECardFlag } from "@prisma/client";
import { ITransactionCard } from "src/business/Interfaces/Prisma/ITransactionCard";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

export interface ICard {
  id: string;
  userId: string;
  idCardPaymentPlatform?: string | null;
  method: ECardMethod;
  cardHolder?: string | null;
  cardNumberLastDigits: string;
  cardNumber?: string | null;
  expiration?: string | null;
  flag: ECardFlag;
  status: ECardStatus;
  saved: boolean;
  createdAt: Date;
  updatedAt: Date;
  user?: IUser | null;
  transactionCard?: ITransactionCard[] | null;
}
