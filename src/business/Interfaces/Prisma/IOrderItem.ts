import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IOrderItemProductAttributeOption } from "src/business/Interfaces/Prisma/IOrderItemProductAttributeOption";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";

export interface IOrderItem {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  observation?: string | null;
  product: IProduct;
  order?: IOrder;
  orderItemProductAttributeOption: IOrderItemProductAttributeOption[];
}
