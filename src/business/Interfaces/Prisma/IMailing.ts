import { EMailingSendingType } from "@prisma/client";
import { IFiltersMailing } from "src/business/Interfaces/Prisma/IFiltersMailing";
import { IResultMailing } from "src/business/Interfaces/Prisma/IResultMailing";

export interface IMailing {
  id: string;
  title: string;
  topicArn?: string | null;
  messageContent: string;
  sendingType: EMailingSendingType;
  description?: string | null;
  createdAt: Date;
  filtersMailing?: IFiltersMailing;
  resultMailing?: IResultMailing[];
}
