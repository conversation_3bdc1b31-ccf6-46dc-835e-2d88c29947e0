import { EBorderoStatus, EPayoutOwner } from "@prisma/client";
import { ICooperative } from "src/business/Interfaces/Prisma/ICooperative";
import { IPayout } from "src/business/Interfaces/Prisma/IPayout";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

export interface IBordero {
  id: string;
  status: EBorderoStatus;
  payoutOwner: EPayoutOwner;
  userId?: string | null;
  userIdAdmin: string;
  storeId?: string | null;
  cooperativeId?: string | null;
  createdAt: Date;
  statusDate: Date;
  quantityOrders: number;
  sumOrderValue: number;
  sumAdministrativeFeeValue: number;
  sumTransferValue: number;
  user?: IUser | null;
  userAdmin?: IUser;
  store?: IStore | null;
  payout?: IPayout[];
  cooperative?: ICooperative | null;
}
