import { IChat } from "src/business/Interfaces/Prisma/IChat";
import { IProfile } from "src/business/Interfaces/Prisma/IProfile";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

export interface IChatMessage {
  id?: string;
  chatId: string;
  userId: string;
  profileId: string;
  message: string;
  createdAt?: Date;
  updatedAt?: Date;
  chat?: IChat;
  profile?: IProfile;
  user?: IUser;
}
