import { IUser } from "src/business/Interfaces/Prisma/IUser";

export interface ILog {
  id?: string;
  level: string;
  message: string;
  details: string;
  userId?: string | null;
  entityId?: string | null;
  entity?: string | null;
  action?: string | null;
  createdAt?: Date;
  currentData?: string | null;
  detailsHash?: string | null;
  currentDataHash?: string | null;
  user?: IUser | null;
}
