import { EAttributeType } from "@prisma/client";
import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";

export interface IAttribute {
  id: string;
  name: string;
  shortDescription: string;
  required: boolean;
  type: EAttributeType;
  createdAt?: Date;
  updatedAt?: Date;
  attributeOption?: IAttributeOption[];
  productAttribute?: IProductAttribute[];
}
