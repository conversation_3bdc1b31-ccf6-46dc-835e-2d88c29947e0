import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { IDeliveryman } from "./IDeliveryman";

export interface IReview {
  id: string;
  rate: number;
  customerReview: string;
  storeResponse?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  userId: string;
  storeId?: string | null;
  orderId?: string | null;
  deliverymanId?: string | null;
  store?: Partial<IStore> | null;
  order?: Partial<IOrder> | null;
  deliveryman?: Partial<IDeliveryman> | null;
  user?: Partial<IUser> | null;
}
