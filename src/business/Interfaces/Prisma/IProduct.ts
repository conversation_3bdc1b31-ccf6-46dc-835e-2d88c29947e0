import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IOrderItem } from "src/business/Interfaces/Prisma/IOrderItem";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IProductModeration } from "src/business/Interfaces/Prisma/IProductModeration";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IUserFavoriteProducts } from "src/business/Interfaces/Prisma/IUserFavoriteProducts";

export interface IProduct {
  id: string;
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  salePrice: number;
  sku: string;
  active: boolean;
  activeByAdmin: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  storeId: string;
  preparationTime?: number;
  orderItem?: IOrderItem[];
  store?: IStore | null;
  productAttribute?: IProductAttribute[];
  productCategory?: IProductCategory[];
  productSubcategory?: IProductSubcategory[];
  userFavoriteProducts?: IUserFavoriteProducts[];
  productModeration?: IProductModeration[];
  files?: IFile[];
}
