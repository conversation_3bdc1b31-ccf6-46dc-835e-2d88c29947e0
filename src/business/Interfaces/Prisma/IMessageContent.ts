import { EMessageSendingType, EMessageStatus } from "@prisma/client";
import { IEmailAttachment } from "src/business/Interfaces/Prisma/IEmailAttachment";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";

export interface IMessageContent {
  id?: string;
  body: string;
  status?: EMessageStatus;
  sendingType: EMessageSendingType;
  message?: IMessage;
  emailAttachment?: IEmailAttachment[];
}
