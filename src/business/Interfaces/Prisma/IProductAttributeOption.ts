import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import { IOrderItemProductAttributeOption } from "src/business/Interfaces/Prisma/IOrderItemProductAttributeOption";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";

export interface IProductAttributeOption {
  id: string;
  productAttributeId: string;
  attributeOptionId: string;
  active: boolean;
  orderItemProductAttributeOption?: IOrderItemProductAttributeOption[];
  attributeOption?: IAttributeOption;
  productAttribute?: IProductAttribute;
}
