import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";
import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";
import { IStoreModeration } from "src/business/Interfaces/Prisma/IStoreModeration";
import { IStoreSettings } from "src/business/Interfaces/Prisma/IStoreSettings";
import { IStoreUser } from "src/business/Interfaces/Prisma/IStoreUser";
import { IUserFavoriteStores } from "src/business/Interfaces/Prisma/IUserFavoriteStores";

export interface IStore {
  id: string;
  name: string;
  cnpj: string;
  slug: string | null;
  email: string;
  phone: string;
  description: string;
  open: boolean;
  active: boolean;
  activeByAdmin: boolean;
  addressId: string | null;
  createdAt: Date;
  updatedAt: Date;
  order?: IOrder[];
  products?: IProduct[];
  reviews?: IReview[];
  storeHours?: IStoreHours[];
  storeSettings?: IStoreSettings | null;
  address?: IAddress | null;
  storeCategory?: IStoreCategory[];
  storeUsers?: IStoreUser[];
  userFavoriteStores?: IUserFavoriteStores[];
  files?: IFile[];
  storeModeration?: IStoreModeration[];
  userFavoriteStoreId?: string;
  pixKey?: string | null;
}
