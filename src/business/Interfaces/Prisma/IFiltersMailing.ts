import { ENumberComparison, EPaymentMethod } from "@prisma/client";
import { IMailing } from "src/business/Interfaces/Prisma/IMailing";

export interface IFiltersMailing {
  id: string;
  district?: string;
  city?: string;
  state?: string;
  category?: string[];
  orderPrice?: number;
  orderPriceComparison?: ENumberComparison;
  paymentMethod?: EPaymentMethod;
  birthDayStartDate?: Date;
  birthDayEndDate?: Date;
  MailingId: string;
  Mailing?: IMailing;
}
