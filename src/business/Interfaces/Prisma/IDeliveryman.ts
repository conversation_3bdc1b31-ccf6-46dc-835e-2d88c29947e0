import { EProfileStatus } from "@prisma/client";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

export interface IDeliveryman {
  id: string;
  type: string;
  modality: string;
  status?: EProfileStatus | null;
  userId: string;
  latitude?: number | null;
  longitude?: number | null;
  createdAt?: Date;
  updatedAt?: Date;
  user?: IUser;
  orders?: IOrder[];
  review?: IReview[];
  annotation: string | null;
  files?: IFile[];
  pixKey?: string | null;
}
