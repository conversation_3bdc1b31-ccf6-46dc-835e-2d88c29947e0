import { EFinancialTransactions } from "@prisma/client";
import { IOrder } from "src/business/DTOs/Order";
import { IFinancialConsolidation } from "src/business/Interfaces/Prisma/IFinancialConsolidation";

export interface IFinancialConsolidationTransactions {
  id: string;
  createdAt: Date;
  status: EFinancialTransactions;
  storeName: string;
  platformId: string;
  chargeId: string;
  totalPrice: number;
  administrativeFee: number;
  consolidationId: string;
  consolidation: IFinancialConsolidation;
  orderId: string;
  order?: IOrder;
}
