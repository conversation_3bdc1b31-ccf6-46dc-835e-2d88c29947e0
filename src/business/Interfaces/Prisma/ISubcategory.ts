import { ICategorySubcategory } from "src/business/Interfaces/Prisma/ICategorySubcategory";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";

export interface ISubcategory {
    id: string,
    name: string,
    description: string,
    createdAt: Date,
    updatedAt: Date,
    categorySubcategory?: ICategorySubcategory[],
    productSubcategory?: IProductSubcategory[]
}