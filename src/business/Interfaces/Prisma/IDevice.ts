import { EDeviceType, ELanguageOptions } from "@prisma/client";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

export interface IDevice {
  id?: string;
  deviceId: string;
  deviceToken: string;
  endpointArn: string;
  subscriptionArn?: string | null;
  type: EDeviceType;
  active: boolean;
  userId: string;
  language: ELanguageOptions;
  profileId: string;
  deliverymanSubscriptionArn?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  user?: IUser;
}
