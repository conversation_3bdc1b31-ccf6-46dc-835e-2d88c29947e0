import { IAttribute } from "src/business/Interfaces/Prisma/IAttribute";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";

export interface IProductAttribute {
  id: string;
  productId: string;
  attributeId: string;
  active: boolean;
  attribute?: IAttribute;
  product?: IProduct;
  productAttributeOption?: IProductAttributeOption[];
}
