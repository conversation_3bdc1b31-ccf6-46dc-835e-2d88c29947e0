import { IOrderItem } from "src/business/Interfaces/Prisma/IOrderItem";
import { IDeliveryman } from "src/business/Interfaces/Prisma/IDeliveryman";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IOrderStatus } from "src/business/Interfaces/Prisma/IOrderStatus";
import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { ITransaction } from "src/business/Interfaces/Prisma/ITransaction";
import { IFinancialConsolidationTransactions } from "src/business/Interfaces/Prisma/IFinancialConsolidationTxn";

export interface IOrder {
  id: string;
  userId?: string;
  code: number;
  storeId: string;
  addressId: string;
  deliverymanId?: string | null;
  price: number;
  shippingPrice: number;
  totalPrice: number;
  quantityItems: number;
  quantityProducts: number;
  estimatedDeliveryTime: number;
  customerCode?: string | null;
  routeLength: number;
  createdAt?: Date;
  orderItem?: IOrderItem[];
  store?: IStore | null;
  user?: IUser | null;
  address?: IAddress | null;
  deliveryman?: IDeliveryman | null;
  orderStatus?: IOrderStatus[];
  review?: IReview[];
  transaction?: ITransaction[] | null;
  financialConsolidation?: IFinancialConsolidationTransactions;
}
