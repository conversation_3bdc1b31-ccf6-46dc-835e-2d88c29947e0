import { IStore } from "src/business/Interfaces/Prisma/IStore";

export interface IStoreSettings {
  id: string;
  toleranceOrder?: number | null;
  deliveryRange: number;
  cash: boolean;
  debt: boolean;
  credit: boolean;
  ticket: boolean;
  pix: boolean;
  facebook?: string | null;
  instagram?: string | null;
  tiktok?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  storeId: string;
  store?: IStore | null;
}
