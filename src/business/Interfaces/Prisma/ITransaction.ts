import { ETransactionType, ETransactionStatus, EPaymentMethod } from "@prisma/client";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { ITransactionCard } from "src/business/Interfaces/Prisma/ITransactionCard";

export interface ITransaction {
  id: string;
  transactionPlatformId?: string | null;
  type: ETransactionType;
  status: ETransactionStatus;
  paymentMethod: EPaymentMethod;
  amount?: number | null;
  authorizationCode?: string | null;
  installments?: number | null;
  statusDetail?: string | null;
  customerId?: string | null;
  traceId?: string | null;
  chargeId?: string | null;
  pixKey?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  orderId: string;
  order?: IOrder | null;
  transactionCard?: ITransactionCard[] | null;
}
