import { ICategorySubcategory } from "src/business/Interfaces/Prisma/ICategorySubcategory";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IFile } from "./IFile";

export interface ICategory {
  id: string;
  name: string;
  description: string;
  createdAt?: Date;
  updatedAt?: Date;
  categorySubcategory?: ICategorySubcategory[];
  productCategories?: IProductCategory[];
  storeCategory?: IStoreCategory[];
  icon?: IFile | null;
}
