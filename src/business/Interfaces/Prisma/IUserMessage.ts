import { EMessageStatus } from "@prisma/client";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { IUserProfile } from "src/business/Interfaces/Prisma/IUserProfile";

export interface IUserMessage {
  id?: string;
  userId: string;
  messageId?: string;
  status?: EMessageStatus;
  profileId?: string | null;
  user?: IUser;
  message?: IMessage;
  profile?: IUserProfile;
}
