import { EPayoutOwner, EPayoutStatus } from "@prisma/client";
import { ICooperative } from "src/business/Interfaces/Prisma/ICooperative";
import { IOrder } from "./IOrder";

export interface IPayout {
  id: string;
  orderId: string;
  cooperativeId?: string | null;
  administrativeFeePercent: number;
  administrativeFeeValue: number;
  transferValue: number;
  createdAt: Date;
  statusDate: Date;
  status: EPayoutStatus;
  payoutOwner: EPayoutOwner;
  order?: IOrder;
  cooperative?: ICooperative;
}
