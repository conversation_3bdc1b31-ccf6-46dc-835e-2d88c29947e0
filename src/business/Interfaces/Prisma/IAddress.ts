import { EAddress } from "@prisma/client";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IUserAddress } from "src/business/Interfaces/Prisma/IUserAddress";

export interface IAddress {
  id: string;
  street: string;
  streetHash?: string | null;
  number?: string | null;
  numberHash?: string | null;
  complement?: string | null;
  complementHash?: string | null;
  district: string;
  districtHash?: string | null;
  country: string;
  countryHash?: string | null;
  city: string;
  cityHash?: string | null;
  state: string;
  stateHash?: string | null;
  latitude: number;
  longitude: number;
  postcode: string;
  postcodeHash?: string | null;
  nickname?: string | null;
  nicknameHash?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  type: EAddress;
  orderAddress?: IOrder[];
  userAddress?: IUserAddress[];
  stores?: IStore[];
  isDefault: boolean;
}
