import { IClient } from "src/business/Interfaces/Prisma/IClient";
import { IDeliveryman } from "src/business/Interfaces/Prisma/IDeliveryman";
import { IDevice } from "src/business/Interfaces/Prisma/IDevice";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { ILoginSession } from "src/business/Interfaces/Prisma/ILoginSession";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IShopkeeper } from "src/business/Interfaces/Prisma/IShopkeeper";
import { IStoreUser } from "src/business/Interfaces/Prisma/IStoreUser";
import { IUserAddress } from "src/business/Interfaces/Prisma/IUserAddress";
import { IUserFavoriteProducts } from "src/business/Interfaces/Prisma/IUserFavoriteProducts";
import { IUserFavoriteStores } from "src/business/Interfaces/Prisma/IUserFavoriteStores";
import { IUserPermission } from "src/business/Interfaces/Prisma/IUserPermission";
import { IUserProfile } from "src/business/Interfaces/Prisma/IUserProfile";

export interface IUser {
  id: string;
  cognitoId?: string | null;
  cognitoIdGoogle?: string | null;
  firstName: string;
  lastName: string;
  cpf: string;
  email: string;
  phone: string;
  contactByEmail: boolean;
  contactBySms: boolean;
  contactByWhatsapp: boolean;
  deleted?: boolean;
  disabled?: boolean;
  permanentlyDeleted?: boolean;
  deletedAt?: Date | null;
  createdAt?: Date;
  updatedAt?: Date;
  dateOfBirth: Date;
  reactivationCode?: string | null;
  client?: IClient | null;
  deliveryman?: IDeliveryman | null;
  shopkeeper?: IShopkeeper | null;
  order?: IOrder[];
  reviewUser?: IReview[];
  storeUsers?: IStoreUser[];
  userAddress?: IUserAddress[];
  userFavoriteProducts?: IUserFavoriteProducts[];
  userFavoriteStores?: IUserFavoriteStores[];
  userPermissions?: IUserPermission[];
  userProfiles?: IUserProfile[];
  devices?: IDevice[];
  // TODO Message should be one to many
  messages?: IMessage;
  userMessage?: IMessage[];
  loginSession?: ILoginSession[];
  files?: IFile[];
}
