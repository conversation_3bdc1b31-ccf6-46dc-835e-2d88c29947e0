import { injectable } from "inversify";
import { IUserContext } from "src/business/Interfaces/Tools/IUserContext";

@injectable()
export class UserContext implements IUserContext {
  private _userId: string;

  private _cognitoId: string;

  private _cognitoIdGoogle?: string;

  get userId(): string {
    return this._userId;
  }

  set userId(value: string) {
    this._userId = value;
  }

  get cognitoId(): string {
    return this._cognitoId;
  }

  set cognitoId(value: string) {
    this._cognitoId = value;
  }

  get cognitoIdGoogle(): string | undefined {
    return this._cognitoIdGoogle;
  }

  set cognitoIdGoogle(value: string | undefined) {
    this._cognitoIdGoogle = value;
  }
}
