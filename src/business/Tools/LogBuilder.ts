import { AxiosError } from "axios";
import { injectable } from "inversify";
import { ILog } from "src/business/Interfaces/Prisma/ILog";
import { ILogBuilder } from "src/business/Interfaces/Tools/ILogBuilder";

@injectable()
export class LogBuilder implements ILogBuilder {
  buildError(error: any): ILog {
    const log = {
      level: "error",
      message: "Internal Server Error",
    } as ILog;

    if (error instanceof AxiosError) {
      log.message = error.message;
      log.details = JSON.stringify(error?.response?.data);
    } else if (error instanceof Error) {
      log.message = error.message;
      log.details = JSON.stringify(error?.stack);
    } else {
      log.details = JSON.stringify(error);
    }

    return log;
  }
}
