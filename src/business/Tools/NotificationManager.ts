import { injectable } from "inversify";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { INotification } from "../Interfaces/Tools/INotificationManager";

@injectable()
export default class NotificationManager implements INotificationManager {
  private notifications: INotification[] = [];

  getList(): INotification[] {
    return this.notifications;
  }

  add(key: string, message: string): void {
    this.notifications.push({ [key]: message });
  }

  addMany(notificationObject: { [key: string]: string }): void {
    Object.entries(notificationObject).forEach(([key, value]) => this.notifications.push({ [key]: value }));
  }

  // addFromTypeorm(model: string, errors: ValidationError[]): void {
  //   errors.map((error) =>
  //     Object.entries(error).forEach(([validation, errorObj]) => {
  //       if (validation === "constraints") {
  //         Object.values(errorObj).forEach((message) =>
  //           this.notifications.push({
  //             [`model_validation.${model}`]: message as string,
  //           })
  //         );
  //       }
  //     })
  //   );
  // }

  check(key: string): boolean {
    return !!this.notifications.find((i) => Object.keys(i)[0] === key);
  }
}
