import { CognitoJwtVerifier } from "aws-jwt-verify";
import { Request } from "express";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { IAuthenticateRequest } from "src/business/Interfaces/Tools/IAuthenticateRequest";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { IUserContext } from "src/business/Interfaces/Tools/IUserContext";

@injectable()
export class AuthenticateRequest implements IAuthenticateRequest {
  @inject(TOKENS.IUserService)
  private userService: IUserService;

  @inject(TOKENS.NotificationManager)
  private notificationManager: INotificationManager;

  @inject(TOKENS.UserContext)
  private userContext: IUserContext;

  @inject(TOKENS.LoggerService)
  private loggerService: ILoggerService;

  async handle(req: Request): Promise<boolean> {
    const rawToken = req.headers.authorization || req.cookies.authorization;

    const token = rawToken?.replace("Bearer ", "");

    const verifier = CognitoJwtVerifier.create({
      clientId: process.env.AWS_COGNITO_CLIENT_ID!,
      userPoolId: process.env.AWS_COGNITO_USER_POOL_ID!,
      tokenUse: "access",
    });

    if (token) {
      try {
        const payload = await verifier.verify(token, { tokenUse: "access" });

        console.log("Authentication successfull for user:", payload.sub);

        if (payload.username.includes("google")) {
          const user = await this.userService.getByCognitoIdGoogle(payload.sub);

          if (user) {
            this.userContext.userId = user.id;
          }

          this.userContext.cognitoIdGoogle = payload.sub;
        } else {
          const user = await this.userService.getByCognitoId(payload.sub);

          if (!user) {
            this.notificationManager.add("generic.errors", "UserNotFound");
            return false;
          }

          this.userContext.userId = user.id;
          this.userContext.cognitoId = payload.sub;
        }

        return true;
      } catch (error) {
        console.log(error);

        this.loggerService.logError(error);

        this.notificationManager.add("generic.errors", "TokenValidationError");
        return false;
      }
    }

    this.notificationManager.add("generic.errors", "TokenNotFound");
    return false;
  }
}
