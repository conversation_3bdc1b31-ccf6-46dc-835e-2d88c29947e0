// https://firebase.google.com/docs/cloud-messaging/http-server-ref#:~:text=for%20more%20information.-,Table%202b.%20Android%20%E2%80%94%20keys%20for%20notification%20messages,-Parameter

import INotificationDTO from "src/business/DTOs/Notification";

const generateFCMNotificationMessage = (notification: INotificationDTO) =>
  JSON.stringify({
    GCM: JSON.stringify({
      fcmV1Message: {
        message: {
          notification: {
            title: notification.title,
            body: notification.body,
          },
          data: {
            storeId: notification.storeId,
            params: JSON.stringify(notification.params),
            profile: notification.profile,
            type: notification.type,
          },
          android: {
            channel_id: process.env.NOTIFICATION_ANDROID_CHANNEL,
          },
        },
      },
    }),
  });

export default generateFCMNotificationMessage;
