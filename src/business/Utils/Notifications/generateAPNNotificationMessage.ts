// https://firebase.google.com/docs/cloud-messaging/http-server-ref#:~:text=iOS%20and%20Android.-,Table%202a.%20iOS%20%E2%80%94%20keys%20for%20notification%20messages,-Parameter

import INotificationDTO from "src/business/DTOs/Notification";

const generateAPNNotificationMessage = (notification: INotificationDTO) =>
  JSON.stringify({
    APNS: JSON.stringify({
      notification: {
        title: notification.title,
        body: notification.body,
        android_channel_id: process.env.NOTIFICATION_ANDROID_CHANNEL,
        storeId: notification.storeId,
        params: notification.params,
        profile: notification.profile,
        type: notification.type,
      },
    }),
  });

export default generateAPNNotificationMessage;
