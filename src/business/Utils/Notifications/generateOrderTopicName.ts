export const GenerateOrderTopicName = (orderId: string, createdAt: Date): string => {
    const shortOrderId = orderId.split('-')[0];
    const formattedCreatedAt = createdAt.toISOString().replace(/[\s:]/g, '_').split('.')[0];
    let topicName = `order_topic_${shortOrderId}_${formattedCreatedAt}`;
    if (topicName.length > 256) {
        topicName = topicName.substring(0, 256);
    }
    
    return topicName;
}

export const GenerateOrderTopicArn = (orderId: string, createdAt: Date): string => {
    const topicName = GenerateOrderTopicName(orderId, createdAt);
    const arnPrefix = process.env.AWS_SNS_ARN_PREFIX!;
    
    return `${arnPrefix}${topicName}`;
}
