import IOrderNotificationDTO from "src/business/DTOs/IOrderNotification";

const generateMultiplePlatformNotificationMessage = (notificationData: IOrderNotificationDTO) =>
JSON.stringify({
  default: JSON.stringify({
    notification: {
      title: notificationData.title,
      body: notificationData.body,
      android_channel_id: process.env.NOTIFICATION_ANDROID_CHANNEL,
    },
    data: {
      title: notificationData.title,
      body: notificationData.body,
      params: JSON.stringify(notificationData.params),
      type: notificationData.type,
      userId: notificationData.userId
    }
  }),
  APNS: JSON.stringify({
    notification: {
      title: notificationData.title,
      body: notificationData.body,
    },
    data: {
      title: notificationData.title,
      body: notificationData.body,
      params: JSON.stringify(notificationData.params),
      type: notificationData.type,
      userId: notificationData.userId
    }
    
  }),
  GCM: JSON.stringify({
    notification: {
      title: notificationData.title,
      body: notificationData.body,
      android_channel_id: process.env.NOTIFICATION_ANDROID_CHANNEL,
    },
    data: {
      title: notificationData.title,
      body: notificationData.body,
      params: JSON.stringify(notificationData.params),
      type: notificationData.type,
      userId: notificationData.userId
    }
  }),
});

export default generateMultiplePlatformNotificationMessage;
