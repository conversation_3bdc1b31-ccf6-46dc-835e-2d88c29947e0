import INotificationDTO from "src/business/DTOs/Notification";

const generateMultiplePlatformNotificationMessage = (notificationData: INotificationDTO) =>
  JSON.stringify({
    default: JSON.stringify({
      notification: {
        title: notificationData.title,
        body: notificationData.body,
        android_channel_id: process.env.NOTIFICATION_ANDROID_CHANNEL,
        params: notificationData.params,
        profile: notificationData.profile,
        type: notificationData.type,
        userId: notificationData.userId
      },
      data: {
        title: notificationData.title,
        body: notificationData.body,
        params: JSON.stringify(notificationData.params),
        type: notificationData.type,
        userId: notificationData.userId,
        profile: notificationData.profile
      }
    }),
    APNS: JSON.stringify({
      notification: {
        title: notificationData.title,
        body: notificationData.body,
        params: notificationData.params,
        profile: notificationData.profile,
        type: notificationData.type,
        userId: notificationData.userId
      },
      data: {
        title: notificationData.title,
        body: notificationData.body,
        params: JSON.stringify(notificationData.params),
        type: notificationData.type,
        userId: notificationData.userId,
        profile: notificationData.profile
      }
    }),
    GCM: JSON.stringify({
      notification: {
        title: notificationData.title,
        body: notificationData.body,
        android_channel_id: process.env.NOTIFICATION_ANDROID_CHANNEL,
        params: notificationData.params,
        profile: notificationData.profile,
        type: notificationData.type,
        userId: notificationData.userId
      },
      data: {
        title: notificationData.title,
        body: notificationData.body,
        params: JSON.stringify(notificationData.params),
        type: notificationData.type,
        userId: notificationData.userId,
        profile: notificationData.profile
      }
    }),
  });

export default generateMultiplePlatformNotificationMessage;
