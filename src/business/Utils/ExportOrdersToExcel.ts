/* eslint-disable no-nested-ternary */
/* eslint-disable no-dupe-keys */
/* eslint-disable no-plusplus */
import ExcelJS from "exceljs";
import { IExportOrdersReportDTO } from "src/business/DTOs/Order/IExportOrdersReport";
import formatData from "src/business/Utils/FormatData";

export const exportOrdersToExcel = async (data: IExportOrdersReportDTO[]) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Relatório de Vendas");

  worksheet.columns = [
    {
      header: "Código",
      key: "orderCode",
      width: 10,
    },
    {
      header: "Loja",
      key: "storeName",
      width: 20,
    },
    {
      header: "CNPJ",
      key: "storeCnpj",
      width: 20,
    },
    {
      header: "Preço Total",
      key: "orderTotalPrice",
      width: 15,
    },
    {
      header: "Preço da Entrega",
      key: "orderShippingPrice",
      width: 20,
    },
    {
      header: "Quantidade de Items",
      key: "orderQuantityItems",
      width: 20,
    },
    {
      header: "Quantidade de Produtos",
      key: "orderQuantityProducts",
      width: 23,
    },
    {
      header: "Produto",
      key: "productName",
      width: 23,
    },
    {
      header: "Preço Unitário",
      key: "orderItemUnityPrice",
      width: 20,
    },
    {
      header: "Quantidade",
      key: "orderItemQuantity",
      width: 15,
    },
    {
      header: "Atributo",
      key: "attributeName",
      width: 20,
    },
    {
      header: "Opção do Atributo",
      key: "attributeOptionValue",
      width: 20,
    },
    {
      header: "Observação",
      key: "orderItemObservation",
      width: 15,
    },
    {
      header: "Método de Pagamento",
      key: "paymentMethod",
      width: 20,
    },
    {
      header: "Data",
      key: "orderCreatedAt",
      width: 20,
    },
  ];

  data.forEach((order) => {
    worksheet.addRow({
      ...order,
      payment_method: order.paymentMethod === "credit" ? "crédito" : order.paymentMethod === "debit" ? "débito" : "pix",
      order_created_at: formatData(order.orderCreatedAt),
    });
  });

  return workbook.xlsx;
};
