import { JSEncrypt } from "nodejs-jsencrypt";
import { ICard } from "src/business/Interfaces/Prisma/ICard";

const encryptJS = (data: string, key: string) => {
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(key);
  return encrypt.encrypt(data);
};

const decryptJS = (data: string, key: string) => {
  const decrypt = new JSEncrypt();
  decrypt.setPrivateKey(key);
  return decrypt.decrypt(data);
};

const encryptToSendApp = (data: string) => {
  if (!process.env.APP_CARD_DATA_PUBLIC_KEY) return false;
  return encryptJS(data, process.env.APP_CARD_DATA_PUBLIC_KEY);
};

const decryptFromApp = (data: string) => {
  if (!process.env.SERVER_CARD_DATA_PRIVATE_KEY) return false;
  return decryptJS(data, process.env.SERVER_CARD_DATA_PRIVATE_KEY);
};

const encryptToSendDB = (data: string) => {
  if (!process.env.DB_CARD_DATA_PUBLIC_KEY) return false;
  return encryptJS(data, process.env.DB_CARD_DATA_PUBLIC_KEY);
};

const decryptFromDB = (data: string) => {
  if (!process.env.DB_CARD_DATA_PRIVATE_KEY) return false;
  return decryptJS(data, process.env.DB_CARD_DATA_PRIVATE_KEY);
};

const decryptCardFromDB = (card: ICard) => {
  const newCard: ICard = {
    ...card,
    cardHolder: card.cardHolder ? decryptFromDB(card.cardHolder) || "" : "",
    cardNumber: card.cardNumber ? decryptFromDB(card.cardNumber) || "" : "",
    expiration: card.expiration ? decryptFromDB(card.expiration) || "" : "",
  };

  return newCard;
};

const encryptCardToSendApp = (card: ICard) => {
  const newCard: ICard = {
    ...card,
    cardHolder: card.cardHolder ? encryptToSendApp(card.cardHolder) || "" : "",
    cardNumber: card.cardNumber ? encryptToSendApp(card.cardNumber) || "" : "",
    expiration: card.expiration ? encryptToSendApp(card.expiration) || "" : "",
  };

  return newCard;
};

export {
  encryptJS,
  encryptToSendApp,
  encryptToSendDB,
  encryptCardToSendApp,
  decryptJS,
  decryptFromApp,
  decryptFromDB,
  decryptCardFromDB,
};
