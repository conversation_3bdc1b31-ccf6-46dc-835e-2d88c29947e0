/* eslint-disable array-callback-return */

import { IFavoriteProductViewModel } from "src/api/ViewModels/Product/IFavorite";

interface IFirstTreatment {
  storeName: string;
  data: IFavoriteProductViewModel;
}

export type IFormattedFavoriteProducts = {
  storeName: string;
  data: IFavoriteProductViewModel[];
};

export const handleProducts = (
  rawStoreProducts: IFavoriteProductViewModel[]
) => {
  const firstTreatment: IFirstTreatment[] = [];

  rawStoreProducts.forEach((data) => {
    firstTreatment.push({
      storeName: data.store.name,
      data,
    });
  });

  const finalTreatment: IFormattedFavoriteProducts[] = [];

  firstTreatment.forEach((item) => {
    const index = finalTreatment.findIndex(
      (obj) => obj.storeName === item.storeName
    );
    if (index < 0) {
      finalTreatment.push({
        storeName: item.storeName,
        data: [item.data],
      });
    } else {
      finalTreatment[index].data = [...finalTreatment[index].data, item.data];
    }
  });
  return finalTreatment;
};
