import { ETransactionStatus } from "@prisma/client";
import { ETransactionNotificationStatus } from "src/business/Enums/ETransactionNotificationStatus";

export const transactionStatusMap = {
  [ETransactionNotificationStatus.AWAITING_PAYMENT]: ETransactionStatus.created,
  [ETransactionNotificationStatus.IN_ANALYSIS]: ETransactionStatus.pending,
  [ETransactionNotificationStatus.PAID]: ETransactionStatus.approved,
  [ETransactionNotificationStatus.AVAILABLE]: ETransactionStatus.authorized,
  [ETransactionNotificationStatus.IN_DISPUTE]: ETransactionStatus.in_process,
  [ETransactionNotificationStatus.REFUNDED]: ETransactionStatus.in_mediation,
  [ETransactionNotificationStatus.CANCELED]: ETransactionStatus.cancelled,
  [ETransactionNotificationStatus.DEBITED]: ETransactionStatus.refunded,
  [ETransactionNotificationStatus.TEMPORARILY_WITHHELD]: ETransactionStatus.charged_back,
};

export const transactionNotificationStatusMap = {
  [ETransactionStatus.created]: ETransactionNotificationStatus.AWAITING_PAYMENT,
  [ETransactionStatus.pending]: ETransactionNotificationStatus.IN_ANALYSIS,
  [ETransactionStatus.approved]: ETransactionNotificationStatus.PAID,
  [ETransactionStatus.authorized]: ETransactionNotificationStatus.AVAILABLE,
  [ETransactionStatus.in_process]: ETransactionNotificationStatus.IN_DISPUTE,
  [ETransactionStatus.in_mediation]: ETransactionNotificationStatus.REFUNDED,
  [ETransactionStatus.cancelled]: ETransactionNotificationStatus.CANCELED,
  [ETransactionStatus.refunded]: ETransactionNotificationStatus.DEBITED,
  [ETransactionStatus.charged_back]: ETransactionNotificationStatus.TEMPORARILY_WITHHELD,
};
