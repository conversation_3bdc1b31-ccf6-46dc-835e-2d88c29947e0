/* eslint-disable no-nested-ternary */
/* eslint-disable no-dupe-keys */
/* eslint-disable no-plusplus */
import ExcelJS from "exceljs";
import { ExportStoresData } from "src/business/DTOs/ExportedStoresData";

export const exportStoresToExcel = async (data: ExportStoresData[]) => {
  const workbook = new ExcelJS.Workbook();

  workbook.creator = "CoopDeliveryBahia";
  workbook.created = new Date();

  const worksheet = workbook.addWorksheet("Data", {
    headerFooter: { firstHeader: "Store Data" }
  });

  worksheet.columns = [
    {
      header: "Name", key: "name", width: 20,
    },
    {
      header: "CNPJ", key: "cnpj", width: 20,
    },
    {
      header: "Slug", key: "slug", width: 15,
    },
    {
      header: "Email", key: "email", width: 20,
    },
    {
      header: "Phone", key: "phone", width: 20,
    },
    {
      header: "Description", key: "description", width: 23,
    },
    {
      header: "District", key: "district", width: 20,
    },
    {
      header: "Street", key: "street", width: 23,
    },
    {
      header: "Number", key: "number", width: 20,
    },
    {
      header: "Complement", key: "complement", width: 15,
    },
    {
      header: "City", key: "city", width: 15,
    },
    {
      header: "State", key: "state", width: 20,
    },
    {
      header: "Country", key: "country", width: 20,
    },
    {
      header: "Postcode", key: "postcode", width: 20,
    },
    {
      header: "Nickname", key: "nickname", width: 20,
    },
    {
      header: "CategoryName", key: "categoryName", width: 20,
    },
  ]

  data.forEach(order => {
    worksheet.addRow({
      ...order
    })
  })

  return workbook.xlsx;

}