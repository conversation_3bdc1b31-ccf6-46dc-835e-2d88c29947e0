import { TreeViewListViewModel } from "src/api/ViewModels/List/TreeViewList.vm";
import { CategoryQueryResult } from "src/business/DTOs/CategoryQueryResult";

const formatCategoryTreeView = (result: CategoryQueryResult[]) =>
  result.reduce((treeViewList, category) => {
    const treeViewData = {
      id: category.category_id,
      name: category.category_name,
      isOpen: false,
      checked: category.category_checked,
      options: [] as any[],
    };
    const treeViewOptions = {
      id: category.subcategory_id,
      name: category.subcategory_name,
      checked: category.subcategory_checked,
    };
    const indicie = treeViewList.findIndex(
      (item) => item.id === treeViewData.id
    );
    if (indicie > -1) {
      if (treeViewOptions.id) {
        treeViewList[indicie].options.push(treeViewOptions);
        // NOTE: foi necessário comentar a linha abaixo para que a categoria não inicialize expandida por default
        // treeViewList[indicie].isOpen = !!treeViewList[indicie].options.find(item => item.checked === true)
      }
    } else {
      if (treeViewOptions.id) {
        treeViewData.options.push(treeViewOptions);
      }
      treeViewList.push(treeViewData);
    }
    return treeViewList;
  }, [] as TreeViewListViewModel[]);

export default formatCategoryTreeView;
