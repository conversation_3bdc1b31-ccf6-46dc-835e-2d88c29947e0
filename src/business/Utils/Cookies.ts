import { ICookie } from "src/business/Interfaces/Controllers/ICookie";

export default class Cookies {
  private cookiesArr: ICookie[] = [];

  getAll(): ICookie[] {
    return this.cookiesArr;
  }

  setCookie({ name, options, value }: Omit<ICookie, "remove">) {
    this.cookiesArr.push({ name, options, value });
  }

  clearCookie({ name, options }: Omit<ICookie, "remove" | "value">) {
    this.cookiesArr.push({ name, options, remove: true });
  }
}
