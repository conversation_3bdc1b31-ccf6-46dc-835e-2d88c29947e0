import { UserAuthenticationResult } from "src/business/DTOs/UserAuthenticationResult";
import { ExchangeCodeResponse } from "src/business/Interfaces/Service/AWS/ICognito";

const formatExchangeCodeResponse = (response: ExchangeCodeResponse) => {
  const entries = Object.entries(response);
  const entriesMapped = entries.map((entry) => {
    const key = entry[0];
    const keyFormatted = key
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join("");
    entry[0] = keyFormatted;
    return entry;
  });

  const result: UserAuthenticationResult = {
    cognitoAuthenticationResult: Object.fromEntries(entriesMapped),
    userInfo: {
      id: "",
      profiles: [],
    },
  };

  return result;
};

export default formatExchangeCodeResponse;
