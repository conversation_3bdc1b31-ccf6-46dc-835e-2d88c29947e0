import { ENumberComparison, Prisma } from "@prisma/client";

export const handleENumberComparison = (value: number, comparisonType: ENumberComparison): Prisma.FloatFilter => {
  const comparisonMapping = {
    [ENumberComparison.greaterThan]: { gt: value },
    [ENumberComparison.greaterThanOrEqual]: { gte: value },
    [ENumberComparison.lessThan]: { lt: value },
    [ENumberComparison.lessThanOrEqual]: { lte: value },
    [ENumberComparison.equal]: { equals: value },
  };

  return comparisonMapping[comparisonType] || undefined;
};
