/* eslint-disable no-plusplus */
/* eslint-disable quotes */

import { format } from "date-fns";
import ExcelJS from "exceljs";
import { IPayoutExportReportDTO } from "src/business/DTOs/Payout/IPayoutExportReport";
import { payoutStatusTranslationMap } from "src/business/Utils/PayoutStatusMap";

export const exportPayoutsToExcel = async (data: IPayoutExportReportDTO[]) => {
  const workbook = new ExcelJS.Workbook();
  const worksheetReport = workbook.addWorksheet("Relatório de Repasses");

  const dataFlat = data.map((payout) => ({
    orderTotalPrice: payout.order?.totalPrice,
    orderPrice: payout.order?.price,
    orderShippingPrice: payout.order?.shippingPrice,
    payoutAdministrativeFeePercent: payout.administrativeFeePercent / 100,
    payoutAdministrativeFeeValue: payout.administrativeFeeValue,
    payoutTransferValue: payout.transferValue,
    payoutCreatedAt: payout.createdAt,
    payoutStatus: payoutStatusTranslationMap[payout.status],
    payoutStatusDate: payout.statusDate,
    deliverymanName: payout.order?.deliveryman?.user?.firstName,
    deliverymanCpf: payout.order?.deliveryman?.user?.cpf,
    storeName: payout.order?.store?.name,
    storeCnpj: payout.order?.store?.cnpj,
    cooperativeName: payout.cooperative?.name,
    cooperativeCnpj: payout.cooperative?.cnpj,
  }));

  const totalizers = dataFlat.reduce(
    (acc, curr) => {
      acc.ordersQuantity++;
      acc.totalOrderPrice += curr.orderPrice || 0;
      acc.totalOrderShippingPrice += curr.orderShippingPrice || 0;
      acc.totalPayoutAdministrativeFeeValue += curr.payoutAdministrativeFeeValue;
      acc.totalPayoutTransferValue += curr.payoutTransferValue;

      if (curr.payoutCreatedAt < acc.periodStart) {
        acc.periodStart = curr.payoutCreatedAt;
      }

      if (curr.payoutCreatedAt > acc.periodEnd) {
        acc.periodEnd = curr.payoutCreatedAt;
      }

      return acc;
    },
    {
      ordersQuantity: 0,
      totalOrderPrice: 0,
      totalOrderShippingPrice: 0,
      totalPayoutAdministrativeFeeValue: 0,
      totalPayoutTransferValue: 0,
      periodStart: dataFlat[0].payoutCreatedAt,
      periodEnd: dataFlat[dataFlat.length - 1].payoutCreatedAt,
    },
  );

  // Totalizers header
  worksheetReport.getRow(1).values = [
    "Período",
    "Quantidade de Pedidos",
    "Total de Pedidos",
    "Total de Frete",
    "Total de Taxa Administrativa",
    "Total de Repasses",
  ];

  // Totalizers values
  worksheetReport.getRow(2).values = [
    `${format(totalizers.periodStart, "dd/MM/yyyy")} - ${format(totalizers.periodEnd, "dd/MM/yyyy")}`,
    totalizers.ordersQuantity,
    totalizers.totalOrderPrice,
    totalizers.totalOrderShippingPrice,
    totalizers.totalPayoutAdministrativeFeeValue,
    totalizers.totalPayoutTransferValue,
  ];

  // Totalizers masks
  worksheetReport.getCell("C2").numFmt = '"R$"#,##0.00';
  worksheetReport.getCell("D2").numFmt = '"R$"#,##0.00';
  worksheetReport.getCell("E2").numFmt = '"R$"#,##0.00';
  worksheetReport.getCell("F2").numFmt = '"R$"#,##0.00';

  worksheetReport.getRow(5).values = [
    "Cooperativa",
    "Loja",
    "CNPJ",
    "Entregador",
    "CPF do Entregador",
    "Valor Total do Pedido",
    "Valor total dos Itens",
    "Valor do Frete",
    "Taxa Administrativa (%)",
    "Taxa Administrativa (R$)",
    "Valor do Repasse",
    "Data de criação",
    "Status",
    "Data de pagamento",
  ];

  // Styles
  worksheetReport.getRow(1).font = { bold: true };
  worksheetReport.getRow(5).font = { bold: true };

  worksheetReport.columns = [
    {
      key: "Cooperativa",
      width: 25,
    },
    {
      key: "Loja",
      width: 25,
    },
    {
      key: "CNPJ",
      width: 25,
    },
    {
      key: "Entregador",
      width: 20,
    },
    {
      key: "CPF do Entregador",
      width: 20,
    },
    {
      key: "Valor Total do Pedido",
      width: 30,
    },
    {
      key: "Valor total dos Itens",
      width: 20,
    },
    {
      key: "Valor do Frete",
      width: 15,
    },
    {
      key: "Taxa Administrativa (%)",
      width: 25,
    },
    {
      key: "Taxa Administrativa (R$)",
      width: 25,
    },
    {
      key: "Valor do Repasse",
      width: 20,
    },
    {
      key: "Data de criação",
      width: 15,
    },
    {
      key: "Status",
      width: 15,
    },
    {
      key: "Data de pagamento",
      width: 20,
    },
  ];

  worksheetReport.getColumn(6).numFmt = '"R$"#,##0.00';
  worksheetReport.getColumn(7).numFmt = '"R$"#,##0.00';
  worksheetReport.getColumn(8).numFmt = '"R$"#,##0.00';
  worksheetReport.getColumn(9).numFmt = "0.00%";
  worksheetReport.getColumn(10).numFmt = '"R$"#,##0.00';
  worksheetReport.getColumn(11).numFmt = '"R$"#,##0.00';
  worksheetReport.getColumn(12).numFmt = "dd/mm/yyyy";
  worksheetReport.getColumn(14).numFmt = "dd/mm/yyyy";

  dataFlat.forEach((order) => {
    worksheetReport.addRow({
      Cooperativa: order.cooperativeName,
      Loja: order.storeName,
      CNPJ: order.storeCnpj || order.cooperativeCnpj,
      Entregador: order.deliverymanName,
      "CPF do Entregador": order.deliverymanCpf,
      "Valor Total do Pedido": order.orderTotalPrice,
      "Valor total dos Itens": order.orderPrice,
      "Valor do Frete": order.orderShippingPrice,
      "Taxa Administrativa (%)": order.payoutAdministrativeFeePercent,
      "Taxa Administrativa (R$)": order.payoutAdministrativeFeeValue,
      "Valor do Repasse": order.payoutTransferValue,
      "Data de criação": order.payoutCreatedAt,
      Status: order.payoutStatus,
      "Data de pagamento": order.payoutStatusDate,
    });
  });

  return workbook.xlsx;
};
