import { EPayoutStatus } from "@prisma/client";
import { ETransactionNotificationStatus } from "src/business/Enums/ETransactionNotificationStatus";

export const payoutStatusMap = {
  [ETransactionNotificationStatus.PAID]: EPayoutStatus.waiting,
  [ETransactionNotificationStatus.AWAITING_PAYMENT]: EPayoutStatus.waiting,
  [ETransactionNotificationStatus.AVAILABLE]: EPayoutStatus.available,
  [ETransactionNotificationStatus.REFUNDED]: EPayoutStatus.returned,
  [ETransactionNotificationStatus.CANCELED]: EPayoutStatus.returned,
  [ETransactionNotificationStatus.TEMPORARILY_WITHHELD]: EPayoutStatus.temporaryRetention,
};

export const payoutStatusTranslationMap = {
  [EPayoutStatus.available]: "Disponível",
  [EPayoutStatus.returned]: "Devolvid<PERSON>",
  [EPayoutStatus.temporaryRetention]: "Retenção Temporária",
  [EPayoutStatus.waiting]: "A<PERSON>ando",
  [EPayoutStatus.executed]: "Executado",
};
