import { isNaN, isNull, isString } from "lodash";

export function validateColumn(row: number, col: number, value: any, format: number): string {
  let error = "";
  switch (format) {
    case 1: // String not empty
      error = !!value && isString(value) ? "" : "Tipo de dado inválido";
      break;
    case 2: // Number not empty
      error = !!value && !isNaN(parseFloat(value)) ? "" : "Tipo de dado inválido";
      break;
    case 3: // String
      error = isString(value) || isNull(value) ? "" : "Tipo de dado inválido";
      break;
    case 4: // Number
      error = !isNaN(Number(value)) || isNull(value) ? "" : "Tipo de dado inválido";
      break;
    default:
      error = "";
  }
  return error ? `Erro na linha ${row} - coluna ${col} (${value}): ${error}.\n` : "";
}