/* eslint-disable no-underscore-dangle */
import { injectable } from "inversify";
import { IProfile } from "src/business/Interfaces/Prisma/IProfile";

interface ISingletonProfile {
  id: string;
  name: string;
}

@injectable()
export default class ProfilesSingleton {
  private _manager: ISingletonProfile;

  private _shopkeeper: ISingletonProfile;

  private _deliveryman: ISingletonProfile;

  private _client: ISingletonProfile;

  public load(profiles: IProfile[]) {
    const manager = profiles.find((p) => p.name === "manager");
    const shopkeeper = profiles.find((p) => p.name === "shopkeeper");
    const deliveryman = profiles.find((p) => p.name === "deliveryman");
    const client = profiles.find((p) => p.name === "client");

    if (
      !manager ||
      !shopkeeper ||
      !deliveryman ||
      !client ||
      !manager?.id ||
      !shopkeeper?.id ||
      !deliveryman?.id ||
      !client?.id
    ) {
      throw new Error("Uninitialized Profiles singleton.");
    } else {
      console.log("ProfilesSingleton loaded.");
    }

    this._manager = { id: manager.id, name: manager.name };
    this._shopkeeper = { id: shopkeeper.id, name: shopkeeper.name };
    this._deliveryman = { id: deliveryman.id, name: deliveryman.name };
    this._client = { id: client.id, name: client.name };
  }

  public get manager(): ISingletonProfile {
    return this._manager;
  }

  public set manager(value: ISingletonProfile) {
    this._manager = value;
  }

  public get shopkeeper(): ISingletonProfile {
    return this._shopkeeper;
  }

  public set shopkeeper(value: ISingletonProfile) {
    this._shopkeeper = value;
  }

  public get deliveryman(): ISingletonProfile {
    return this._deliveryman;
  }

  public set deliveryman(value: ISingletonProfile) {
    this._deliveryman = value;
  }

  public get client(): ISingletonProfile {
    return this._client;
  }

  public set client(value: ISingletonProfile) {
    this._client = value;
  }

  public getByName(name: string): ISingletonProfile {
    switch (name) {
      case "manager":
        return this.manager;
      case "shopkeeper":
        return this.shopkeeper;
      case "deliveryman":
        return this.deliveryman;
      case "client":
        return this.client;
      default:
        throw new Error("Invalid profile name");
    }
  }
}
