/* eslint-disable no-underscore-dangle */
import { injectable } from "inversify";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { IOrderStatusType } from "src/business/Interfaces/Prisma/IOrderStatusType";

interface ISingletonOrderStatusType {
  id: string;
  value: string;
}

@injectable()
export default class OrderStatusTypeSingleton {
  private _placedOrder: ISingletonOrderStatusType;

  private _pendingPayment: ISingletonOrderStatusType;

  private _canceledPaymentFailure: ISingletonOrderStatusType;

  private _paymentMade: ISingletonOrderStatusType;

  private _canceled: ISingletonOrderStatusType;

  private _rejected: ISingletonOrderStatusType;

  private _preparing: ISingletonOrderStatusType;

  private _waitingForTheDeliveryPerson: ISingletonOrderStatusType;

  private _onRouteToStore: ISingletonOrderStatusType;

  private _onDeliveryRoute: ISingletonOrderStatusType;

  private _canceledDelivery: ISingletonOrderStatusType;

  private _delivered: ISingletonOrderStatusType;

  public load(orderStatusType: IOrderStatusType[]) {
    const placedOrder = orderStatusType.find((p) => p.value === EOrderStatusValue.placed_order);
    const pendingPayment = orderStatusType.find((p) => p.value === EOrderStatusValue.pending_payment);
    const canceledPaymentFailure = orderStatusType.find((p) => p.value === EOrderStatusValue.canceled_payment_failure);
    const paymentMade = orderStatusType.find((p) => p.value === EOrderStatusValue.payment_made);
    const canceled = orderStatusType.find((p) => p.value === EOrderStatusValue.canceled);
    const rejected = orderStatusType.find((p) => p.value === EOrderStatusValue.rejected);
    const preparing = orderStatusType.find((p) => p.value === EOrderStatusValue.preparing);
    const waitingForTheDeliveryPerson = orderStatusType.find(
      (p) => p.value === EOrderStatusValue.waiting_for_the_delivery_person,
    );
    const onRouteToStore = orderStatusType.find((p) => p.value === EOrderStatusValue.on_route_to_store);
    const onDeliveryRoute = orderStatusType.find((p) => p.value === EOrderStatusValue.on_delivery_route);
    const canceledDelivery = orderStatusType.find((p) => p.value === EOrderStatusValue.canceled_delivery);
    const delivered = orderStatusType.find((p) => p.value === EOrderStatusValue.delivered);

    if (
      !placedOrder ||
      !pendingPayment ||
      !canceledPaymentFailure ||
      !paymentMade ||
      !preparing ||
      !canceled ||
      !rejected ||
      !waitingForTheDeliveryPerson ||
      !onRouteToStore ||
      !onDeliveryRoute ||
      !canceledDelivery ||
      !delivered
    ) {
      throw new Error("Uninitialized order status type singleton.");
    } else {
      console.log("OrderStatusTypeSingleton loaded.");
    }

    this._placedOrder = { id: placedOrder.id, value: placedOrder.value };
    this._pendingPayment = { id: pendingPayment.id, value: pendingPayment.value };
    this._canceledPaymentFailure = { id: canceledPaymentFailure.id, value: canceledPaymentFailure.value };
    this._paymentMade = { id: paymentMade.id, value: paymentMade.value };
    this._preparing = { id: preparing.id, value: preparing.value };
    this._canceled = { id: canceled.id, value: canceled.value };
    this._rejected = { id: rejected.id, value: rejected.value };
    this._waitingForTheDeliveryPerson = {
      id: waitingForTheDeliveryPerson.id,
      value: waitingForTheDeliveryPerson.value,
    };
    this._onRouteToStore = {
      id: onRouteToStore.id,
      value: onRouteToStore.value,
    };
    // this._acceptedByDeliveryPerson = {
    //   id: acceptedByDeliveryPerson.id,
    //   value: acceptedByDeliveryPerson.value,
    // };
    this._onDeliveryRoute = {
      id: onDeliveryRoute.id,
      value: onDeliveryRoute.value,
    };
    this._canceledDelivery = {
      id: canceledDelivery.id,
      value: canceledDelivery.value,
    };
    this._delivered = { id: delivered.id, value: delivered.value };
  }

  public get placed_order(): ISingletonOrderStatusType {
    return this._placedOrder;
  }

  public set placed_order(value: ISingletonOrderStatusType) {
    this._placedOrder = value;
  }

  public get pending_payment(): ISingletonOrderStatusType {
    return this._pendingPayment;
  }

  public set pending_payment(value: ISingletonOrderStatusType) {
    this._pendingPayment = value;
  }

  public get canceled_payment_failure(): ISingletonOrderStatusType {
    return this._canceledPaymentFailure;
  }

  public set canceled_payment_failure(value: ISingletonOrderStatusType) {
    this._canceledPaymentFailure = value;
  }

  public get payment_made(): ISingletonOrderStatusType {
    return this._paymentMade;
  }

  public set payment_made(value: ISingletonOrderStatusType) {
    this._paymentMade = value;
  }

  public get canceled(): ISingletonOrderStatusType {
    return this._canceled;
  }

  public set canceled(value: ISingletonOrderStatusType) {
    this._canceled = value;
  }

  public get rejected(): ISingletonOrderStatusType {
    return this._rejected;
  }

  public set rejected(value: ISingletonOrderStatusType) {
    this._rejected = value;
  }

  public get preparing(): ISingletonOrderStatusType {
    return this._preparing;
  }

  public set preparing(value: ISingletonOrderStatusType) {
    this._preparing = value;
  }

  public get waiting_for_the_delivery_person(): ISingletonOrderStatusType {
    return this._waitingForTheDeliveryPerson;
  }

  public set waiting_for_the_delivery_person(value: ISingletonOrderStatusType) {
    this._waitingForTheDeliveryPerson = value;
  }

  public get on_route_to_store(): ISingletonOrderStatusType {
    return this._onRouteToStore;
  }

  public set on_route_to_store(value: ISingletonOrderStatusType) {
    this._onRouteToStore = value;
  }

  public get on_delivery_route(): ISingletonOrderStatusType {
    return this._onDeliveryRoute;
  }

  public set on_delivery_route(value: ISingletonOrderStatusType) {
    this._onDeliveryRoute = value;
  }

  public get delivered(): ISingletonOrderStatusType {
    return this._delivered;
  }

  public set delivered(value: ISingletonOrderStatusType) {
    this._delivered = value;
  }

  public get canceled_delivery(): ISingletonOrderStatusType {
    return this._canceledDelivery;
  }

  public set canceled_delivery(value: ISingletonOrderStatusType) {
    this._canceledDelivery = value;
  }
}
