import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IStoreSettings } from "src/business/Interfaces/Prisma/IStoreSettings";
import { IStoreSettingsRepository } from "src/business/Interfaces/Repository/IStoreSettings";
import { IStoreSettingsService } from "src/business/Interfaces/Service/IStoreSettings";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class StoreSettingsService extends BaseService<IStoreSettings> implements IStoreSettingsService {
  private storeSettingsRepository: IStoreSettingsRepository;

  constructor(
    @inject(TOKENS.IStoreSettingsRepository)
    storeSettingsRepository: IStoreSettingsRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(storeSettingsRepository, notificationManager);
    this.storeSettingsRepository = storeSettingsRepository;
  }

  async create(item: IStoreSettings): Promise<boolean> {
    const result = await this.storeSettingsRepository.create(item);

    if (result) return true;

    return false;
  }

  async update(item: IStoreSettings): Promise<boolean> {
    const result = await this.storeSettingsRepository.updateSetting(item.storeId, item);

    return !!result;
  }

  async getSettingsByStoreId(idStore: string): Promise<IStoreSettings | null> {
    const setting = this.storeSettingsRepository.getSettingsByStoreId(idStore);

    return setting;
  }
}
