import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IMessageContent } from "src/business/Interfaces/Prisma/IMessageContent";
import { IMessageContentRepository } from "src/business/Interfaces/Repository/IMessageContent";
import { IMessageContentService } from "src/business/Interfaces/Service/IMessageContent";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class MessageContentService extends BaseService<IMessageContent> implements IMessageContentService {
  constructor(
    @inject(TOKENS.IMessageContentRepository)
    private messageContentRepository: IMessageContentRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(messageContentRepository, notificationManager);
    this.messageContentRepository = messageContentRepository;
  }
}
