import { injectable, inject } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICooperative } from "src/business/Interfaces/Prisma/ICooperative";
import { ICooperativeRepository } from "src/business/Interfaces/Repository/ICooperative";
import { ICooperativeService } from "src/business/Interfaces/Service/ICooperative";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class CooperativeService extends BaseService<ICooperative> implements ICooperativeService {
  constructor(
    @inject(TOKENS.ICooperativeRepository)
    private cooperativeRepository: ICooperativeRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(cooperativeRepository, notificationManager);
    this.cooperativeRepository = cooperativeRepository;
  }

  async createOrUpdate(data: ICooperative): Promise<boolean> {
    const existCooperative = await this.cooperativeRepository.findFirst({});

    if (existCooperative) {
      const updatedResult = await this.cooperativeRepository.updateCooperative(existCooperative.id, data);

      if (!updatedResult) this.notificationManager.add("generic.errors", "update_cooperative");

      return updatedResult;
    }

    const result = await this.cooperativeRepository.create(data);

    if (!result) this.notificationManager.add("generic.errors", "create_cooperative");

    return result;
  }

  async getCooperativeInfoWithAddress(): Promise<ICooperative | null> {
    const result = await this.cooperativeRepository.findFirst({
      include: {
        address: true,
      },
    });

    if (!result) {
      this.notificationManager.add("generic.errors", "cooperative_notFound");
    }

    return result;
  }

  async getCooperativeByCNPJ(cnpj: string): Promise<ICooperative | null> {
    const result = await this.cooperativeRepository.findFirst({
      where: {
        cnpj,
      },
    });

    return result;
  }
}
