import { ELanguageOptions } from "@prisma/client";
import { inject, injectable } from "inversify";
import en from "src/business/Assets/Language/en.json";
import pt from "src/business/Assets/Language/pt.json";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import INotificationDTO from "src/business/DTOs/Notification";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { IResourcesService } from "src/business/Interfaces/Service/IResources";
import getResourceNotificationKey from "src/business/Utils/Resources/GetResourceNotificationKey";
import getResourceWithParameters from "src/business/Utils/Resources/GetResourceWithParameters";
import IOrderNotificationDTO from "src/business/DTOs/IOrderNotification";

@injectable()
export class ResourcesService implements IResourcesService {
  constructor(
    @inject(TOKENS.IDeviceService)
    private deviceService: IDeviceService,
  ) {
    this.deviceService = deviceService;
  }

  translateNotificationResources(language: string, notification: INotificationDTO): INotificationDTO {
    const resources = language === ELanguageOptions.PT ? pt : en;
    const titleKey = getResourceNotificationKey(notification.title);
    const bodyKey = getResourceNotificationKey(notification.body);

    const notificationTitle = notification.params?.title
      ? getResourceWithParameters(resources.notification[titleKey], notification.params.title)
      : resources.notification[titleKey];

    const notificationBody = notification.params?.body
      ? getResourceWithParameters(resources.notification[bodyKey], notification.params.body)
      : resources.notification[bodyKey];

    const translatedNotification: INotificationDTO = {
      title: notificationTitle,
      body: notificationBody,
      storeId: notification.storeId,
      userId: notification.userId,
      profile: notification.profile,
      params: notification.params,
      type: notification.type,
    };

    return translatedNotification;
  }
  translateOrderNotificationResources(language: string, notification: IOrderNotificationDTO): IOrderNotificationDTO {
    const resources = language === ELanguageOptions.PT ? pt : en;
    const titleKey = getResourceNotificationKey(notification.title);
    const bodyKey = getResourceNotificationKey(notification.body);

    const notificationTitle = notification.params?.title
      ? getResourceWithParameters(resources.notification[titleKey], notification.params.title)
      : resources.notification[titleKey];

    const notificationBody = notification.params?.body
      ? getResourceWithParameters(resources.notification[bodyKey], notification.params.body)
      : resources.notification[bodyKey];

    const translatedNotification: IOrderNotificationDTO = {
      title: notificationTitle,
      body: notificationBody,
      storeId: notification.storeId,
      userId: notification.userId,
      params: notification.params,
      type: notification.type,
    };

    return translatedNotification;
  }
}
