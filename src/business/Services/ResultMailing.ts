import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IResultMailing } from "src/business/Interfaces/Prisma/IResultMailing";
import { IResultMailingRepository } from "src/business/Interfaces/Repository/IResultMailing";
import { IResultMailingService } from "src/business/Interfaces/Service/IResultMailing";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class ResultMailingService extends BaseService<IResultMailing> implements IResultMailingService {
  protected notificationManager: INotificationManager;

  constructor(
    @inject(TOKENS.IResultMailingRepository)
    private resultMailingRepository: IResultMailingRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(resultMailingRepository, notificationManager);
    this.notificationManager = notificationManager;
  }

  async getUsersIdByMailingId(MailingId: string): Promise<string[]> {
    const result = await this.resultMailingRepository.getUsersIdByMailingId(MailingId);

    const usersIds = result.map((resultMailing) => resultMailing.userId || "");

    return usersIds;
  }

  async getUsersIdsByMailingIdPaged(
    currentPage: number,
    pageSize: number,
    MailingId: string,
    filterName?: string | undefined,
  ): Promise<PagedResult<IUserMailingDTO>> {
    const result = await this.resultMailingRepository.getUsersIdsByMailingIdPaged(
      Number(currentPage),
      Number(pageSize),
      MailingId,
      filterName,
    );

    return result;
  }
}
