import { inject, injectable } from "inversify";

import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ISubcategory } from "src/business/Interfaces/Prisma/ISubcategory";
import { ICategorySubcategoryRepository } from "src/business/Interfaces/Repository/ICategorySubcategory";
import { ISubcategoryRepository } from "src/business/Interfaces/Repository/ISubcategory";
import { ISubcategoryService } from "src/business/Interfaces/Service/ISubcategory";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class SubcategoryService extends BaseService<ISubcategory> implements ISubcategoryService {
  private subcategoryRepository: ISubcategoryRepository;

  private categorySubcategoryRepository: ICategorySubcategoryRepository;

  constructor(
    @inject(TOKENS.ISubcategoryRepository)
    subcategoryRepository: ISubcategoryRepository,
    @inject(TOKENS.ICategorySubcategoryRepository)
    categorySubcategoryRepository: ICategorySubcategoryRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(subcategoryRepository, notificationManager);
    this.subcategoryRepository = subcategoryRepository;
    this.categorySubcategoryRepository = categorySubcategoryRepository;
  }

  async deleteWithRelations(id: string): Promise<boolean> {
    const result = await this.subcategoryRepository.deleteWithRelations(id);

    return result;
  }

  async create(data: ISubcategory): Promise<boolean> {
    const alreadyExists = await this.subcategoryRepository.findOne({
      where: {
        name: data.name,
      },
    });

    if (alreadyExists) {
      this.notificationManager.add("errors", "subcategory_already_exists");
      return false;
    }
    const result = await this.subcategoryRepository.create(data);

    return !!result;
  }

  async update(data: ISubcategory): Promise<boolean> {
    if (!data.id) {
      return false;
    }

    const result = this.subcategoryRepository.update(data.id, data);

    return !!result;
  }

  async getPagedWithCategory(
    currentPage: string = "",
    pageSize: string = "",
    filterValue: string = "",
    orderBy: string = "",
    sortDirection: IOrder = "asc",
  ): Promise<PagedResult<ISubcategory>> {
    const includes = undefined;

    const columnFilter: string = "name,description";
    const { data } = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      includes,
    );

    return data;
  }
}
