import crypto from "node:crypto";
import { EAttributeType, EFile, EFileType, EMessageType } from "@prisma/client";
import ExcelJS, { Xlsx } from "exceljs";
import { inject, injectable } from "inversify";
import { AttributesAndOptionsIdsViewModel } from "src/api/ViewModels/Attribute/IAttributesAndOptionsIds";
import pt from "src/business/Assets/Language/pt.json";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import INotificationDTO from "src/business/DTOs/Notification";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult, PagedResultWithCondition } from "src/business/DTOs/PagedResult";
import { ICreateProductByImportDTO } from "src/business/DTOs/Product/ICreateProduct";
import { IProductByStoreWithFavoriteDTO } from "src/business/DTOs/Product/IProductByStoreWithFavorite";
import { IProductSearchDTO } from "src/business/DTOs/Product/IProductSearch";
import { IUpdateCategorySubcategoryDTO } from "src/business/DTOs/Product/IUpdateCategorySubCategories";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IAttribute } from "src/business/Interfaces/Prisma/IAttribute";
import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { ISubcategory } from "src/business/Interfaces/Prisma/ISubcategory";
import { IAttributeRepository } from "src/business/Interfaces/Repository/IAttribute";
import { IAttributeOptionRepository } from "src/business/Interfaces/Repository/IAttributeOption";
import { ICategoryRepository } from "src/business/Interfaces/Repository/ICategory";
import { ICategorySubcategoryRepository } from "src/business/Interfaces/Repository/ICategorySubcategory";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { IProductRepository } from "src/business/Interfaces/Repository/IProduct";
import { IProductAttributeRepository } from "src/business/Interfaces/Repository/IProductAttribute";
import { IProductAttributeOptionRepository } from "src/business/Interfaces/Repository/IProductAttributeOption";
import { IProductCategoryRepository } from "src/business/Interfaces/Repository/IProductCategory";
import { IProductModerationRepository } from "src/business/Interfaces/Repository/IProductModerationRepository";
import { IProductSubcategoryRepository } from "src/business/Interfaces/Repository/IProductSubcategory";
import { ISubcategoryRepository } from "src/business/Interfaces/Repository/ISubcategory";
import { IS3Service } from "src/business/Interfaces/Service/AWS/IS3";
import { ICustomerProfileService } from "src/business/Interfaces/Service/ICustomerProfile";
import { IFileService } from "src/business/Interfaces/Service/IFile";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IProductService } from "src/business/Interfaces/Service/IProduct";
import { IStoreService } from "src/business/Interfaces/Service/IStore";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import formatData from "src/business/Utils/FormatData";
import { validateColumn } from "src/business/Utils/ValidateColumn";
import { v4 as uuid } from "uuid";
import { IProductsFilterDTO } from "src/api/ViewModels/Product/IFilterProducts";
import { IProductSearchViewModel } from "src/api/ViewModels/Product/IProductSearch";

@injectable()
export class ProductService extends BaseService<IProduct> implements IProductService {
  constructor(
    @inject(TOKENS.IProductRepository)
    private productRepository: IProductRepository,
    @inject(TOKENS.IProductCategoryRepository)
    private productCategoryRepository: IProductCategoryRepository,
    @inject(TOKENS.IProductSubcategoryRepository)
    private productSubcategoryRepository: IProductSubcategoryRepository,
    @inject(TOKENS.IAttributeRepository)
    private attributeRepository: IAttributeRepository,
    @inject(TOKENS.IAttributeOptionRepository)
    private attributeOptionRepository: IAttributeOptionRepository,
    @inject(TOKENS.IProductAttributeRepository)
    private productAttributeRepository: IProductAttributeRepository,
    @inject(TOKENS.IProductAttributeOptionRepository)
    private productAttributeOptionRepository: IProductAttributeOptionRepository,
    @inject(TOKENS.IFileRepository)
    private fileRepository: IFileRepository,
    @inject(TOKENS.ICategoryRepository)
    private categoryRepository: ICategoryRepository,
    @inject(TOKENS.ISubcategoryRepository)
    private subcategoryRepository: ISubcategoryRepository,
    @inject(TOKENS.ICategorySubcategoryRepository)
    private categorySubcategoryRepository: ICategorySubcategoryRepository,
    @inject(TOKENS.IFileService)
    private fileService: IFileService,
    @inject(TOKENS.IS3Service)
    private s3Service: IS3Service,
    @inject(TOKENS.ICustomerProfileService)
    private customerProfileService: ICustomerProfileService,
    @inject(TOKENS.IProductModerationRepository)
    private productModerationRepository: IProductModerationRepository,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.LoggerService)
    private loggerService: ILoggerService,
    @inject(TOKENS.IStoreService)
    private storeService: IStoreService,
  ) {
    super(productRepository, notificationManager);
  }

  async getWithAllDetails(productId: string): Promise<IProduct | null> {
    const result = await this.productRepository.getWithAllDetails(productId);
    return result;
  }

  async getByStore(storeId: string, userId: string): Promise<IProductByStoreWithFavoriteDTO[]> {
    const products = await this.productRepository.getByStore(storeId, userId);

    return products.map((product) => ({
      ...product,
      isFavorite: product.userFavoriteProducts.some((userFavorite) => userFavorite.userId === userId),
    }));
  }

  async create(product: IProduct, attachments?: { id: string }[]): Promise<boolean> {
    // const errors = await validate(product);

    // if (errors.length > 0) {
    //   this.notificationManager.addFromTypeorm("product", errors);
    //   return false;
    // }
    const attachmentIds = attachments?.map((item) => item.id);

    if (!product.sku) {
      product.sku = crypto.randomBytes(4).toString("hex");
    } else {
      const skuExists = await this.productRepository.find({
        where: { sku: product.sku, storeId: product.storeId },
      });

      if (skuExists.length > 0) {
        this.notificationManager.add("product.create.errors", "product_sku_already_exists");
        return false;
      }
    }

    const response = await this.productRepository.create(product, attachmentIds);

    return response !== null;
  }

  async updateWithRelations(data: IProduct): Promise<boolean> {
    const product = await this.getById(data.id);

    if (!product?.sku) data.sku = uuid();

    if (!product) {
      this.notificationManager.add("product.create.errors", "product_notFound");
      return false;
    }

    const response = await this.productRepository.updateWithRelations(data);

    return response;
  }

  async getPagedProductList(
    productFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
  ): Promise<PagedResult<IProduct>> {
    const data = await this.productRepository.getPagedProductList(productFilter, favoriteFilter, currentPage, pageSize);

    return {
      result: data.result,
      totalCount: data.totalCount,
      totalPages: data.totalPages,
    };
  }

  async getWithCategory(id: string): Promise<IProduct | null> {
    const result = await this.productRepository.getWithCategory(id);

    if (!result) {
      this.notificationManager.add("product.create.errors", "product_notFound");
    }

    return result;
  }

  async updateCategoryAndSubcategory(data: IUpdateCategorySubcategoryDTO): Promise<boolean> {
    const productCategories: IProductCategory[] = [];
    const productSubcategories: IProductSubcategory[] = [];

    data.categories.forEach((item) => {
      productCategories.push({
        productId: data.productId,
        categoryId: item.id,
      } as IProductCategory);
      item.subcategoriesIds.forEach((subCategory) => {
        productSubcategories.push({
          productId: data.productId,
          subcategoryId: subCategory,
        } as IProductSubcategory);
      });
    });

    const response = this.productRepository.updateCategoryAndSubcategory(
      data.productId,
      productCategories,
      productSubcategories,
    );

    return response;
  }

  // FIXME - Validar
  async relateProductAttribute(id: string, attributeId: string): Promise<boolean> {
    const product = await this.productRepository.getById(id);
    const attribute = await this.attributeRepository.getById(attributeId);
    const productAttributeIsExist = !!(await this.productAttributeRepository.getByProductAndAttribute(id, attributeId));

    if (product == null) {
      this.notificationManager.add("product.create.errors", "product_notFound");
    }

    if (attribute == null) {
      this.notificationManager.add("attribute.errors", "attribute_notFound");
    }

    if (productAttributeIsExist) {
      this.notificationManager.add("attribute.errors", "product_attribute_alreadyExists");
    }

    if (product && attribute && !productAttributeIsExist && this.isValid()) {
      const productAttribute = await this.productAttributeRepository.create(id, attributeId);
      return !!productAttribute;
    }

    return false;
  }

  async relateProductAttributeOption(id: string, attributeId: string, atributeOptionId: string): Promise<boolean> {
    const productAttribute = await this.productAttributeRepository.getByProductAndAttribute(id, attributeId);
    const attributeOption = await this.attributeOptionRepository.getById(atributeOptionId);
    let productAttributeOptionExists: boolean = false;

    if (productAttribute == null) {
      this.notificationManager.add("attribute.errors", "product_attribute_notFound");
    } else {
      productAttributeOptionExists =
        !!(await this.productAttributeOptionRepository.getByProductAttributeAndAttributeOption(
          productAttribute.id,
          atributeOptionId,
        ));
    }

    if (productAttributeOptionExists) {
      this.notificationManager.add("attribute_option.errors", "product_attribute_option_alreadyExists");
    }
    if (attributeOption == null) {
      this.notificationManager.add("attribute_option.errors", "attribute_option_notFound");
    }

    if (productAttribute && attributeOption && !productAttributeOptionExists && this.isValid()) {
      const productAttributeOption = await this.productAttributeOptionRepository.create(
        productAttribute.id,
        atributeOptionId,
      );
      return !!productAttributeOption;
    }

    return false;
  }

  async getUserFavoriteProducts(userId: string): Promise<IProduct[]> {
    const result = await this.productRepository.getUserFavoriteProducts(userId);

    return result;
  }

  async relateProductFiles(productId: string, filesId: string[]): Promise<boolean> {
    const result = await this.fileRepository.relateEntityFiles(productId, filesId);
    return result > 0;
  }

  async importByStore(storeId: string, xlsx: Buffer): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(xlsx);
    const sheet = workbook.getWorksheet("Data");

    let errors = "";
    if (sheet) {
      const products: ICreateProductByImportDTO[] = [];
      const rows = sheet.getRows(1, sheet.rowCount);
      if (!rows) {
        return "Erro: arquivo vazio.";
      }
      // eslint-disable-next-line no-restricted-syntax
      for (const row of rows) {
        // eslint-disable-next-line no-await-in-loop
        errors += await this.handleRowImport(row, storeId, products);
      }

      if (!errors) {
        await this.productRepository.deleteByStore(storeId);
        await this.productRepository.createManyProducts(products);
      }
    }
    return errors;
  }

  async handleRowImport(row: ExcelJS.Row, storeId: string, products: ICreateProductByImportDTO[]): Promise<string> {
    let errors = "";

    console.log("att required", JSON.stringify(row.getCell(15).value));
    console.log("active", JSON.stringify(row.getCell(7).value));
    if (row.number !== 1) {
      errors += validateColumn(row.number, 1, row.getCell(1).value, 1); // name
      errors += validateColumn(row.number, 2, row.getCell(2).value, 1); // short_description
      errors += validateColumn(row.number, 3, row.getCell(3).value, 1); // description
      errors += validateColumn(row.number, 4, row.getCell(4).value, 2); // price
      errors += validateColumn(row.number, 5, row.getCell(5).value, 2); // sale_price
      errors += validateColumn(row.number, 6, row.getCell(6).value, 1); // sku
      errors += validateColumn(row.number, 7, row.getCell(7).value, 2); // active
      errors += validateColumn(row.number, 8, row.getCell(8).value, 4); // preparation_time
      errors += validateColumn(row.number, 9, row.getCell(9).value, 3); // category_name
      errors += validateColumn(row.number, 10, row.getCell(10).value, 3); // category_description
      errors += validateColumn(row.number, 11, row.getCell(11).value, 3); // category_subcategory_name
      errors += validateColumn(row.number, 12, row.getCell(12).value, 3); // category_subcategory_description
      errors += validateColumn(row.number, 13, row.getCell(13).value, 3); // attribute_name
      errors += validateColumn(row.number, 14, row.getCell(14).value, 3); // attribute_shortdescription
      errors += validateColumn(row.number, 15, row.getCell(15).value, 4); // attribute_required
      errors += validateColumn(row.number, 16, row.getCell(16).value, 3); // attribute_type
      errors += validateColumn(row.number, 17, row.getCell(17).value, 3); // attribute_option_value

      if (!errors) {
        const newProduct: ICreateProductByImportDTO = {
          storeId,
          name: String(row.getCell(1).value),
          shortDescription: String(row.getCell(2).value),
          description: String(row.getCell(3).value),
          price: parseFloat(String(row.getCell(4).value).replace(",", ".")),
          salePrice: parseFloat(String(row.getCell(5).value).replace(",", ".")),
          sku: String(row.getCell(6).value),
          active: Boolean(Number(row.getCell(7).value)),
          preparationTime: Number(row.getCell(8).value),
          productCategory: [],
          productSubcategory: [],
          productAttribute: [],
        };

        const indexProduct = products.findIndex(
          (product) =>
            product.name === newProduct.name &&
            product.description === newProduct.description &&
            product.sku === newProduct.sku &&
            product.price === newProduct.price &&
            product.salePrice === newProduct.salePrice,
        );

        const categoryName = String(row.getCell(9).value);

        const categoryDescription = String(row.getCell(10).value);

        if (categoryName !== "" && categoryName !== "null") {
          const categoryId = await this.handleCategory(categoryName, categoryDescription);
          if (indexProduct === -1 && categoryId) {
            if (newProduct.productCategory?.findIndex((pc) => pc.categoryId === categoryId) === -1) {
              newProduct.productCategory.push({ categoryId });
            }
          } else if (
            products[indexProduct].productCategory?.findIndex((pc) => pc.categoryId === categoryId) === -1 &&
            categoryId
          ) {
            products[indexProduct].productCategory?.push({ categoryId });
          }

          const subCategoryName = String(row.getCell(11).value);
          const subCategoryDescription = String(row.getCell(12).value);

          if (subCategoryName !== "" && subCategoryName !== "null" && categoryId) {
            const subcategoryId = await this.handleSubCategory(categoryId, subCategoryName, subCategoryDescription);
            if (indexProduct === -1 && subcategoryId) {
              if (newProduct.productSubcategory?.findIndex((psc) => psc.subcategoryId === subcategoryId) === -1) {
                newProduct.productSubcategory.push({ subcategoryId });
              }
            } else if (
              products[indexProduct].productSubcategory?.findIndex((psc) => psc.subcategoryId === subcategoryId) ===
                -1 &&
              subcategoryId
            ) {
              products[indexProduct].productSubcategory?.push({
                subcategoryId,
              });
            }
          }
        }

        const attributeName = String(row.getCell(13).value);
        const shortDescription = String(row.getCell(14).value);
        if (attributeName !== "" && attributeName !== "null") {
          const attributeId = await this.handleAttribute(
            attributeName,
            shortDescription,
            Boolean(row.getCell(15).value),
            EAttributeType[String(row.getCell(16).value)],
          );

          if (indexProduct === -1) {
            if (newProduct.productAttribute?.findIndex((pa) => pa.attributeId === attributeId) === -1 && attributeId) {
              newProduct.productAttribute.push({
                attributeId,
                productAttributeOption: [],
              });
            }
          } else if (
            products[indexProduct].productAttribute?.findIndex((pa) => pa.attributeId === attributeId) === -1 &&
            attributeId
          ) {
            products[indexProduct].productAttribute?.push({
              attributeId,
              productAttributeOption: [],
            });
          }

          const attributeOptionValue = String(row.getCell(17).value);
          if (attributeOptionValue !== "" && attributeOptionValue !== "null" && attributeId) {
            const attributeOptionId = await this.handleAttributeOption(attributeId, attributeOptionValue);

            if (indexProduct === -1) {
              if (newProduct.productAttribute) {
                const indexProductAttribute = newProduct.productAttribute.findIndex(
                  (pa) => pa.attributeId === attributeId,
                );
                if (
                  indexProductAttribute > -1 &&
                  newProduct.productAttribute[indexProductAttribute].productAttributeOption?.findIndex(
                    (pao) => pao.attributeOptionId === attributeOptionId,
                  ) === -1 &&
                  attributeOptionId
                ) {
                  newProduct.productAttribute[indexProductAttribute].productAttributeOption?.push({
                    attributeOptionId,
                  });
                }
              }
            } else if (products[indexProduct].productAttribute) {
              const indexProductAttribute = products[indexProduct].productAttribute?.findIndex(
                (pa) => pa.attributeId === attributeId,
              );

              if (
                indexProductAttribute &&
                products[indexProduct].productAttribute![indexProductAttribute].productAttributeOption?.findIndex(
                  (pao) => pao.attributeOptionId === attributeOptionId,
                ) === -1 &&
                attributeOptionId
              ) {
                products[indexProduct].productAttribute![indexProductAttribute].productAttributeOption?.push({
                  attributeOptionId,
                });
              }
            }
          }
        }

        if (indexProduct === -1) {
          products.push(newProduct);
        }
      }
    }
    return errors;
  }

  async handleCategory(name: string, description: string): Promise<string | null> {
    const category = await this.categoryRepository.findFirst({
      where: { name, description },
    });

    if (category) {
      return category.id;
    }

    const newCategory = await this.categoryRepository.create({
      name,
      description,
    });

    return newCategory?.id || null;
  }

  async handleSubCategory(categoryId: string, name: string, description: string): Promise<string | null> {
    const subCategory = await this.subcategoryRepository.findFirst({
      where: { name, description },
    });

    if (subCategory) {
      const categorySubcategory = await this.categorySubcategoryRepository.findFirst({
        where: { categoryId, subcategoryId: subCategory.id },
      });

      if (!categorySubcategory) {
        await this.categorySubcategoryRepository.create({
          categoryId,
          subcategoryId: subCategory.id,
        });
      }
      return subCategory.id;
    }

    const newSubcategory = await this.subcategoryRepository.create({
      name,
      description,
    } as ISubcategory);

    if (newSubcategory)
      await this.categorySubcategoryRepository.create({
        categoryId,
        subcategoryId: newSubcategory.id,
      });

    return newSubcategory?.id || null;
  }

  async handleAttribute(
    name: string,
    shortDescription: string,
    required: boolean,
    type: EAttributeType,
  ): Promise<string | null> {
    const attribute = await this.attributeRepository.findFirst({
      where: { name },
    });

    if (attribute) {
      return attribute.id;
    }

    const newAttribute = await this.attributeRepository.create({
      name,
      shortDescription,
      required,
      type,
    } as IAttribute);

    return newAttribute?.id || null;
  }

  async handleAttributeOption(attributeId: string, value: string): Promise<string | null> {
    const attributeOption = await this.attributeOptionRepository.findFirst({
      where: { attributeId, value },
    });

    if (attributeOption) {
      return attributeOption.id;
    }

    const newAttributeOption = await this.attributeOptionRepository.create({
      attributeId,
      value,
    } as IAttributeOption);

    return newAttributeOption?.id || null;
  }

  async getProductsSuggestions(userId: string): Promise<IProduct[]> {
    const response = this.productRepository.getProductSuggestions(userId);

    return response;
  }

  async updateActiveStatus(id: string, status: boolean): Promise<boolean> {
    try {
      await this.productRepository.update(id, { active: status });
    } catch (error) {
      this.notificationManager.add("generic.errors", "generic_message");
      return false;
    }
    return true;
  }

  async getPagedListFront(
    currentPage: string,
    pageSize: string,
    storeId?: string,
    filterValue?: string,
    orderBy?: string,
    sortDirection?: IOrder,
    categoryFilter?: string,
    storeFilter?: string,
  ): Promise<PagedResult<IProduct>> {
    // const where = storeId
    //   ? {
    //       storeId,
    //     }
    //   : undefined;

    const where = {
      productCategory: categoryFilter
        ? {
            some: {
              category: {
                name: categoryFilter,
              },
            },
          }
        : undefined,
      storeId: storeId || undefined,
      store: storeFilter ? { name: storeFilter } : undefined,
    };

    const includes = {
      store: true,
    };

    const columnFilter = "name,shortDescription,sku,store.name";

    const { data } = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      includes,
      where,
    );

    // const result = await this.productRepository.getPagedListFront(currentPage, pageSize, storeId);

    return data;
  }

  async getWithAllDetailsFront(productId: string): Promise<IProduct | null> {
    const result = await this.productRepository.getWithAllDetailsFront(productId);

    return result;
  }

  async saveProductModeration(productModeration: ICreateProductModerationDTO): Promise<boolean> {
    const { productId } = productModeration;

    const product = await this.productRepository.getWithStore(productId);

    if (product) {
      product.activeByAdmin = productModeration.activeByAdmin;

      const response = await this.productRepository.saveProductModeration(
        productId,
        { ...product, store: undefined },
        productModeration,
      );

      if (response && product.store && product.store.storeUsers) {
        const notification: INotificationDTO = {
          title: pt.notification.product_moderation_title,
          storeId: product.storeId,
          body: `${product.sku} - ${product.name}: ${productModeration.moderation.reason}`,
          profile: EProfile.shopkeeper,
          type: EMessageType.moderation,
        };
        await Promise.all(
          product.store.storeUsers.map(async (storeUser) =>
            this.notificationService.sendNotificationToUser(storeUser.userId!, notification),
          ),
        );
      }
      return response;
    }

    return false;
  }

  async getById(id: string): Promise<IProduct | null> {
    const response = await this.repository.getById(id, { store: true });
    return response;
  }

  async updateProductCategories(id: string, categoryIds: string[]): Promise<boolean> {
    const productCategories = categoryIds.map((item) => ({ id, categoryId: item } as IProductCategory));

    const response = await this.productRepository.updateProductCategories(id, productCategories);

    return response;
  }

  async updateProductSubcategories(id: string, productSubcategory: IProductSubcategory[]): Promise<boolean> {
    const response = await this.productRepository.updateProductSubcategories(id, productSubcategory);

    return response;
  }

  async updateProductAttributes(id: string, attributes: AttributesAndOptionsIdsViewModel[]): Promise<boolean> {
    const product = await this.productRepository.getById(id);

    if (product === null) {
      this.notificationManager.add("product.create.errors", "product_notFound");
      return false;
    }

    const currentProductAttributes = await this.productAttributeRepository.getByProductId(id);
    const newAttributesOpt: IProductAttributeOption[] = [];
    const newProductAtt: IProductAttribute[] = [];

    // Updating saved attributes in db
    const updatedProductAttributes = currentProductAttributes.map((prodAtt) => {
      const attributeIndex = attributes.findIndex((attribute) => attribute.id === prodAtt.attributeId);
      prodAtt.active = attributeIndex !== -1;

      if (attributeIndex === -1) {
        prodAtt.productAttributeOption = prodAtt.productAttributeOption?.map((item) => ({ ...item, active: false }));
      }

      if (attributeIndex !== -1) {
        const attOption = prodAtt.productAttributeOption?.map((prodAttOpt) => {
          const attOptIndex =
            attributes[attributeIndex] && attributes[attributeIndex].optionsIds
              ? attributes[attributeIndex].optionsIds.findIndex((id) => id === prodAttOpt.attributeOptionId)
              : -1;
          prodAttOpt.active = attOptIndex !== -1;
          return prodAttOpt;
        });
        prodAtt.productAttributeOption = attOption;
      }
      return prodAtt;
    });

    // Inserting new attributes in db
    attributes.map((newProdAtt) => {
      const index = updatedProductAttributes.findIndex((updatedAtt) => updatedAtt.attributeId === newProdAtt.id);

      if (index === -1) {
        newProductAtt.push({
          productId: id,
          attributeId: newProdAtt.id,
          productAttributeOption: newProdAtt.optionsIds.map((optId) => ({
            attributeOptionId: optId,
          })),
        } as IProductAttribute);
      }

      if (index !== -1) {
        newProdAtt.optionsIds?.map((itemOpt) => {
          const index2 = updatedProductAttributes[index]?.productAttributeOption?.findIndex(
            (updatedOpt) => updatedOpt.attributeOptionId === itemOpt,
          );

          if (index2) {
            if (index2 === -1) {
              newAttributesOpt.push({
                attributeOptionId: itemOpt,
                productAttributeId: updatedProductAttributes[index].id,
              } as IProductAttributeOption);
            }
          }
          return itemOpt;
        });
      }
      return newProdAtt;
    });

    const response = await this.productRepository.updateProductAttributes(
      id,
      newProductAtt,
      updatedProductAttributes,
      newAttributesOpt,
    );

    return response;
  }

  async deleteByStore(storeId: string): Promise<number> {
    const response = await this.productRepository.deleteByStore(storeId);

    return response;
  }

  async exportByStore(storeId: string): Promise<Xlsx | null> {
    const products = await this.productRepository.getWithAllDetailsByStore(storeId);

    if (!products) return null;

    const workbook = new ExcelJS.Workbook();

    workbook.creator = "CoopDeliveryBahia";
    workbook.created = new Date();

    const sheet = workbook.addWorksheet("Data", {
      headerFooter: { firstHeader: "Product Data" },
    });

    sheet.columns = [
      { header: "name", key: "name", width: 20 },
      { header: "short_description", key: "short_description", width: 30 },
      { header: "description", key: "description", width: 30 },
      { header: "price", key: "price", width: 10 },
      { header: "sale_price", key: "sale_price", width: 10 },
      { header: "sku", key: "sku", width: 20 },
      { header: "createdAt", key: "createdAt", width: 20 },
      { header: "updatedAt", key: "updatedAt", width: 20 },
      { header: "active", key: "active", width: 10 },
      { header: "preparation_time", key: "preparation_time", width: 20 },
      { header: "category_name", key: "category_name", width: 20 },
      {
        header: "category_description",
        key: "category_description",
        width: 30,
      },
      {
        header: "category_subcategory_name",
        key: "category_subcategory_name",
        width: 20,
      },
      {
        header: "category_subcategory_description",
        key: "category_subcategory_description",
        width: 30,
      },
      { header: "attribute_name", key: "attribute_name", width: 20 },
      {
        header: "attribute_shortdescription",
        key: "attribute_shortdescription",
        width: 30,
      },
      {
        header: "attribute_required",
        key: "attribute_required",
        width: 20,
      },
      {
        header: "attribute_type",
        key: "attribute_type",
        width: 10,
      },
      {
        header: "attribute_option_value",
        key: "attribute_option_value",
        width: 20,
      },
    ];

    const Real = new Intl.NumberFormat("pt-BR", {
      style: "decimal",
    });

    products.forEach((product) => {
      sheet.addRow({
        ...product,
        price: Real.format(product.price),
        sale_price: Real.format(product.sale_price),
        active: Number(product.active),
        preparation_time: Number(product.preparation_time),
        createdAt: formatData(product.createdAt),
        updatedAt: formatData(product.updatedAt),
        attibute_required: Number(product.attribute_required),
      });
    });

    return workbook.xlsx;
  }

  async deleteWithRelations(id: string): Promise<boolean> {
    const result = await this.productRepository.deleteWithRelations(id);

    return result;
  }

  async deleteProductsFiles(storeId: string): Promise<boolean> {
    try {
      const products = await this.find({ where: { storeId } });
      const productIds = products.map((p) => p.id);

      const files = await this.fileRepository.find({
        where: { entityId: { in: productIds }, entity: EFile.product },
      });

      await Promise.all(
        files.map(async (file) => {
          const resultS3 = await this.s3Service.deleteObject({
            Key: file.key,
          });

          if (resultS3) {
            await this.fileRepository.delete(file.id);
          }
        }),
      );

      return true;
    } catch (error) {
      console.log(error);

      this.loggerService.logError(error);

      return false;
    }
  }

  async getProductSearch(
    userId: string,
    currentPage: string,
    pageSize: string,
    filterValue?: string,
    latitude?: number,
    longitude?: number,
    locationFilter?: number,
    minPriceFilter?: string,
    maxPriceFilter?: string,
    category?: string[],
    subcategory?: string[],
  ): Promise<PagedResult<IProductsFilterDTO>> {
    const storeIds =
      latitude && longitude && locationFilter
        ? await this.storeService.filterStoresByUserDistance(latitude, longitude, locationFilter)
        : undefined;
    const where = {
      OR: [
        { store: filterValue ? { name: { contains: filterValue, mode: "insensitive" } } : undefined },
        { name: filterValue ? { contains: filterValue, mode: "insensitive" } : undefined },
      ],
      salePrice:
        minPriceFilter && maxPriceFilter
          ? {
              lte: parseFloat(maxPriceFilter.replace(",", ".")),
              gte: parseFloat(minPriceFilter.replace(",", ".")),
            }
          : undefined,
      storeId:
        storeIds && storeIds.length > 0
          ? {
              in: storeIds,
            }
          : undefined,
      productCategory:
        category && category.length > 0
          ? {
              some: {
                categoryId: {
                  in: category,
                },
              },
            }
          : undefined,
      productSubcategory:
        subcategory && subcategory.length > 0
          ? {
              some: {
                subcategoryId: {
                  in: subcategory,
                },
              },
            }
          : undefined,
    };

    const select = {
      id: true,
      name: true,
      description: true,
      salePrice: true,
      preparationTime: true,
      store: {
        select: {
          id: true,
          name: true,
          userFavoriteStores: {
            where: {
              userId,
            },
          },
          reviews: true,
          address: {
            select: {
              id: true,
              latitude: true,
              longitude: true,
            },
          },
        },
      },
    };

    const { data } = (await this.getPaged(
      "",
      filterValue,
      currentPage,
      pageSize,
      undefined,
      undefined,
      undefined,
      where,
      select,
    )) as unknown as PagedResultWithCondition<IProductSearchDTO>;

    const productsResult = await Promise.all(
      data.result.map(async (product) => {
        const filesProducts = await this.fileRepository.find({
          where: {
            AND: [{ entity: EFile.product }, { entityId: product.id }, { type: EFileType.photo }],
          },
          take: 1,
        });
        const filesStore = await this.fileRepository.find({
          where: { AND: [{ entity: EFile.store }, { entityId: product.store.id }, { type: EFileType.icon }] },
          take: 1,
        });

        return {
          ...product,
          iconUrl: filesProducts.length > 0 ? filesProducts[0].url : undefined,
          store: product.store && { ...product.store, iconUrl: filesStore.length > 0 ? filesStore[0].url : undefined },
        };
      }),
    );

    const response = productsResult.reduce((list, current) => {
      const index = list?.findIndex((obj) => obj.store?.name === current.store.name);

      const product: IProductSearchViewModel = {
        id: current.id,
        description: current.description,
        name: current.name,
        preparationTime: current.preparationTime,
        price: current.price,
        salePrice: current.salePrice,
        iconUrl: current.iconUrl,
      };

      if (index < 0) {
        let reviewAverage;
        const ratingSum = current.store.reviews?.reduce((soma, rate) => soma + rate.rate, 0);
        if (ratingSum && current.store.reviews) {
          const average = ratingSum / current.store.reviews.length;
          reviewAverage = average.toFixed(2);
        }

        list.push({
          store: {
            id: current.store.id,
            address: current.store.address,
            name: current.store.name,
            iconUrl: current.store.iconUrl,
            isFavorite: current.store?.userFavoriteStores?.some((userFavorite) => userFavorite.userId === userId),
            reviewAverage,
          },
          products: [product],
        });
      } else list[index].products = [...list[index].products, product];

      return list;
    }, [] as IProductsFilterDTO[]);

    return { ...data, result: response };
  }
}
