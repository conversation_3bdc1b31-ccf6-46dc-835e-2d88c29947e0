import { Express } from "express";
import { EFile, EFileType } from "@prisma/client";
import { inject, injectable } from "inversify";
import mime from "mime-types";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { IS3Service } from "src/business/Interfaces/Service/AWS/IS3";
import { IFileService } from "src/business/Interfaces/Service/IFile";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { ICreateFileViewModel } from "src/api/ViewModels/File/ICreateFile";
import { IResultCreateFile } from "src/api/ViewModels/File/IResultCreateFile";

@injectable()
export class FileService extends BaseService<IFile> implements IFileService {
  private fileRepository: IFileRepository;

  private s3Service: IS3Service;

  constructor(
    @inject(TOKENS.IFileRepository) fileRepository: IFileRepository,
    @inject(TOKENS.IS3Service) s3Service: IS3Service,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(fileRepository, notificationManager);
    this.fileRepository = fileRepository;
    this.s3Service = s3Service;
  }

  async getFilesByEntityAndType(entityId: string, entity: EFile, type: EFileType): Promise<IFile[]> {
    const files = await this.fileRepository.getFilesByEntityAndType(entityId, entity, type);
    return files;
  }

  async getPhotosByEntityId(entityId: string, entity: EFile): Promise<IFile[]> {
    const files = await this.fileRepository.getFilesByEntityId(entityId, entity);
    return files;
  }

  async deleteFilesUntied(): Promise<number> {
    console.log("Starting delete files routine");
    const filesUntied = await this.fileRepository.selectFilesUntied(new Date());

    let totalFilesDeleted = 0;

    filesUntied.forEach(async (file) => {
      const resultS3 = await this.s3Service.deleteObject({
        Key: file.key,
      });

      if (resultS3) {
        const deleteCount = await this.fileRepository.delete(file.id);

        if (deleteCount) {
          totalFilesDeleted += 1;
        }
      }
    });

    console.log(totalFilesDeleted, "Files deleted from database");
    return totalFilesDeleted;
  }

  async getFilesByEntityId(entityId: string, entity: EFile): Promise<IFile[]> {
    const files = await this.fileRepository.getFilesByEntityId(entityId, entity);
    return files;
  }

  async createFile(item: ICreateFileViewModel, file: Express.Multer.File): Promise<IResultCreateFile | null> {
    const extension = mime.extension(file.mimetype);

    const fileName = item.entityId ? item.entityId : file.originalname.replaceAll(" ", "");

    const key = extension
      ? `files/${fileName}-${new Date().getTime()}.${extension}`
      : `files/${fileName}-${new Date().getTime()}`;

    console.log("Starting file upload to AWS");

    await this.s3Service.putObject({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    const fileData = {
      url: `${process.env.AWS_CLOUDFRONT_DOMAIN}/${key}`,
      key,
      bucket: process.env.AWS_BUCKET_NAME || "",
      originalName: item.name ? item.name : file.originalname,
      entity: item.entity,
      extension: extension || "",
      entityId: item.entityId,
      type: item.type,
      size: item.size,
      master: true,
    } as IFile;

    console.log(`Upload requested for file ${fileData.originalName}, size ${fileData.size} and type ${fileData.type}`);

    if (this.isValid()) {
      const result = await this.fileRepository.create(fileData);
      return {
        id: result.id,
        url: result.url,
      };
    }

    return null;
  }

  async delete(id: string): Promise<boolean> {
    const file = await this.fileRepository.getById(id);

    if (file) {
      const s3DeleteResponse = await this.s3Service.deleteObject({
        Key: file.key,
      });

      if (!s3DeleteResponse) {
        this.notificationManager.add("attachmentFile.errors", "remove_file");
      }

      if (this.isValid()) {
        const response = await this.fileRepository.delete(id);

        return response;
      }
    }

    return false;
  }

  async deleteByEntities(entityIds: string[], entity: EFile): Promise<number> {
    const files = await this.fileRepository.find({ where: { entityId: { in: entityIds }, entity } });

    let deleteCount = 0;
    files.forEach(async (file) => {
      const resultS3 = await this.s3Service.deleteObject({
        Key: file.key,
      });

      if (resultS3) {
        const deleteResponse = await this.fileRepository.delete(file.id);

        if (deleteResponse) {
          deleteCount += 1;
        }
      }
    });

    return deleteCount;
  }
}
