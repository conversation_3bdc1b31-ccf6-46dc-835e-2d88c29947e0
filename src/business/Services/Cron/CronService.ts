import { inject, injectable } from "inversify";
import schedule from "node-schedule";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICronService } from "src/business/Interfaces/Service/Cron/ICronService";
import IScheduleType from "src/business/Interfaces/Service/Cron/IScheduleType";
import { IFileService } from "src/business/Interfaces/Service/IFile";
import { IUserService } from "src/business/Interfaces/Service/IUser";

@injectable()
export class CronService implements ICronService {
  private userService: IUserService;

  private fileService: IFileService;

  constructor(
    @inject(TOKENS.IUserService)
    userService: IUserService,
    @inject(TOKENS.IFileService)
    fileService: IFileService,
  ) {
    this.userService = userService;
    this.fileService = fileService;
  }

  execute(newRule: IScheduleType, callback: schedule.JobCallback): void {
    schedule.scheduleJob(newRule, callback);
  }

  executeUserPermanentDeleteJob(): void {
    schedule.scheduleJob({ hour: 1, minute: 0, tz: "Etc/UTC" }, () => {
      this.userService.deleteUsersPastRecoverLimit();
    });
  }

  executeDeleteFilesJob(): void {
    schedule.scheduleJob({ hour: 1, minute: 0, tz: "Etc/UTC" }, () => {
      this.fileService.deleteFilesUntied();
    });
  }

  executeGracefulShutdown(): void {
    schedule.gracefulShutdown();
  }

  executeCancelJob(job: schedule.Job): void {
    job.cancel();
  }
}
