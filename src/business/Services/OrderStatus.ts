/* eslint-disable @typescript-eslint/no-unused-vars */
import { EMessageType } from "@prisma/client";
import { inject, injectable } from "inversify";
import { cloneDeep } from "lodash";
import { ICreateOrderStatusViewModel } from "src/api/ViewModels/OrderStatus/ICreate";
import pt from "src/business/Assets/Language/pt.json";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IOrderStatus } from "src/business/Interfaces/Prisma/IOrderStatus";
import { IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IOrderStatusRepository } from "src/business/Interfaces/Repository/IOrderStatus";
import { IOrderStatusTypeRepository } from "src/business/Interfaces/Repository/IOrderStatusType";
import { IStoreRepository } from "src/business/Interfaces/Repository/IStore";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { IMessageService } from "src/business/Interfaces/Service/IMessage";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IOrderStatusService } from "src/business/Interfaces/Service/IOrderStatus";
import { IPayoutService } from "src/business/Interfaces/Service/IPayout";
import { IResourcesService } from "src/business/Interfaces/Service/IResources";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { IUserContext } from "src/business/Interfaces/Tools/IUserContext";
import { BaseService } from "src/business/Services/Base";
import OrderStatusTypeSingleton from "src/business/Singletons/OrderStatusType";
import getEnumIndex from "src/business/Utils/GetEnumIndex";
import { ISNSService } from "src/business/Interfaces/Service/AWS/ISNS";
import ProfilesSingleton from "src/business/Singletons/Profile";
import { GenerateOrderTopicArn } from "src/business/Utils/Notifications/generateOrderTopicName";

@injectable()
export class OrderStatusService extends BaseService<IOrderStatus> implements IOrderStatusService {
  constructor(
    @inject(TOKENS.IOrderStatusRepository)
    private orderStatusRepository: IOrderStatusRepository,
    @inject(TOKENS.IOrderStatusTypeRepository)
    private orderStatusTypeRepository: IOrderStatusTypeRepository,
    @inject(TOKENS.OrderStatusTypeSingleton)
    private orderStatusTypeSingleton: OrderStatusTypeSingleton,
    @inject(TOKENS.IOrderRepository)
    private orderRepository: IOrderRepository,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.IDeviceService)
    private deviceService: IDeviceService,
    @inject(TOKENS.IMessageService)
    private messageService: IMessageService,
    @inject(TOKENS.IResourcesService)
    private resourcesService: IResourcesService,
    @inject(TOKENS.IStoreRepository)
    private storeRepository: IStoreRepository,
    @inject(TOKENS.IPayoutService)
    private payoutService: IPayoutService,
    @inject(TOKENS.UserContext)
    private userContext: IUserContext,
    @inject(TOKENS.ISNSService)
    private snsService: ISNSService,
    @inject(TOKENS.ProfilesSingleton)
    private profilesSingleton: ProfilesSingleton,
  ) {
    super(orderStatusRepository, notificationManager);
    this.orderStatusRepository = orderStatusRepository;
    this.orderStatusTypeRepository = orderStatusTypeRepository;
    this.orderStatusTypeSingleton = orderStatusTypeSingleton;
    this.snsService = snsService;
  }

  async getAllStatusByOrderId(orderId: string): Promise<IOrderStatus[]> {
    const response = await this.orderStatusRepository.getAllStatusByOrderId(orderId);

    return response;
  }

  async addOrderStatus(orderId: string, status: ICreateOrderStatusViewModel, profile?: EProfile): Promise<boolean> {
    const statusTypeId = this.orderStatusTypeSingleton[status.value]?.id;

    const { userId } = this.userContext;

    const currentOrderStatus = await this.orderStatusRepository.getLastOrderStatusTypeByOrderId(orderId);

    if (
      currentOrderStatus &&
      (currentOrderStatus === EOrderStatusValue.canceled ||
        currentOrderStatus === EOrderStatusValue.rejected ||
        currentOrderStatus === EOrderStatusValue.delivered ||
        currentOrderStatus === EOrderStatusValue.canceled_delivery ||
        currentOrderStatus === EOrderStatusValue.canceled_payment_failure) &&
      profile !== EProfile.manager
    ) {
      console.log("Condition met, adding notification...");
      this.notificationManager.add("orders.errors", "not_allowed");
      return false;
    }
    

    if (!profile || (profile !== EProfile.manager && profile !== EProfile.deliveryman)) {
      const isAllowed = await this.checkIfUserIsAllowedToChangeOrderStatus(orderId, userId);

      if (!isAllowed) {
        this.notificationManager.add("orders.errors", "not_allowed");
        return false;
      }
    } 

    if (!statusTypeId) {
      this.notificationManager.add("orders.errors", "status_not_found");
      return false;
    }

    if (status.value === EOrderStatusValue.on_route_to_store) {
      const resultTransaction = await this.orderStatusRepository.changeOrderStatusTransaction(orderId, statusTypeId, status.observation);
      if(!resultTransaction){
        this.notificationManager.add("orders.errors", "order_already_taken");
        return false;
      }
      return true
    }

    await this.orderStatusRepository.changeOrderStatus(orderId, statusTypeId, status.observation);

    if (status.value === EOrderStatusValue.delivered) {
      await this.payoutService.createPayout(orderId);
    }

    await this.sendChangedOrderStatusNotification(orderId, status.value);

    return true;
  }

  private async checkIfUserIsAllowedToChangeOrderStatus(orderId: string, userId: string): Promise<boolean> {
    const result = await this.storeRepository.checkIfUserIsStoreOwnerByOrderId(orderId, userId);

    return result;
  }

  async sendChangedOrderStatusNotification(orderId: string, statusValue: EOrderStatusValue): Promise<void> {
    const order = await this.orderRepository.getOrderWithStore(orderId);
    if (order && order.userId) {
      const notification = {
        title: pt.notification.client_status_title,
        body: pt.notification[`status_${statusValue}`],
        params: {
          title: order.code,
          order: {
            messageType: EMessageType.order_status,
            orderId: order.id,
          },
        },
        type: EMessageType.order_status,
      };

      const orderTopicArn = GenerateOrderTopicArn(order.id, order.createdAt!);
      const orderUserIds: { userId: string; userProfile: EProfile }[] = [];
      orderUserIds.push({ userId: order.userId, userProfile: EProfile.client });

      const statusTypeIndex = getEnumIndex(EOrderStatusValue, statusValue);
      const pending_payment = getEnumIndex(EOrderStatusValue, EOrderStatusValue.pending_payment);
      const waiting_for_the_delivery_person = getEnumIndex(
        EOrderStatusValue,
        EOrderStatusValue.waiting_for_the_delivery_person,
      );
      const delivered = getEnumIndex(EOrderStatusValue, EOrderStatusValue.delivered);

      if (statusTypeIndex > pending_payment) {
        order.store?.storeUsers?.forEach((storeUser) => {
          this.deviceService.associateDevicesUserToTopic(orderTopicArn, storeUser.userId);
          orderUserIds.push({ userId: storeUser.userId, userProfile: EProfile.shopkeeper });
        });
      }
      if (statusTypeIndex == waiting_for_the_delivery_person) {
        const deliverymanNotification = { profile: EProfile.deliveryman, ...cloneDeep(notification) };
        deliverymanNotification.title = pt.notification.deliveryman_status_title;
        const deliverymanTopicExists = await this.getIfDeliverymanTopicExist(
          process.env.AWS_SNS_DELIVERYMAN_TOPIC_NOTIFICATIONS_ARN!,
        );
        if (!deliverymanTopicExists) {
          this.createDeliverymanTopic(process.env.AWS_SNS_DELIVERYMAN_TOPIC_NOTIFICATIONS_ARN!);
        }
        const deliverymen = await this.deviceService.getDeliverymanDevices(
          this.profilesSingleton.deliveryman.id,
          process.env.AWS_SNS_TOPIC_NOTIFICATIONS_ARN!,
        );

        if (deliverymen.length > 0) {
          await this.deviceService.subscribeMultipleDeliverymenToTopic(
            process.env.AWS_SNS_DELIVERYMAN_TOPIC_NOTIFICATIONS_ARN!,
            deliverymen,
          );
        }
        await this.notificationService.sendNotificationToTopic(
          deliverymen.map((item) => item.userId),
          deliverymanNotification,
          process.env.AWS_SNS_DELIVERYMAN_TOPIC_NOTIFICATIONS_ARN!,
        );
      }
      if (statusTypeIndex > waiting_for_the_delivery_person) {
        if (order.deliverymanId) {
          const deliverymanUserdatails = await this.orderRepository.getDeliverymanUserByOrderId(order.id);
          if (deliverymanUserdatails?.deliveryman) {
            this.deviceService.associateDevicesUserToTopic(orderTopicArn, deliverymanUserdatails?.deliveryman.user.id);
            orderUserIds.push({
              userId: deliverymanUserdatails?.deliveryman.user.id,
              userProfile: EProfile.deliveryman,
            });
          }
        }
      }

      await this.notificationService.sendNotificationToOrderTopic(orderUserIds, notification, orderTopicArn);

      if (statusTypeIndex == delivered) {
        this.snsService.deleteTopic({ TopicArn: orderTopicArn });
      }
    }
  }

  async getIfDeliverymanTopicExist(deliverymanTopic: string) {
    const { Topics } = await this.snsService.listTopic({});
    let deliverymanTopicExists = false;
    if (Topics) {
      deliverymanTopicExists = Topics.some((value) => value.TopicArn?.includes(deliverymanTopic));

      return deliverymanTopicExists;
    }
    return deliverymanTopicExists;
  }

  async createDeliverymanTopic(deliverymanTopic: string) {
    const result = await this.snsService.createTopic({ Name: deliverymanTopic });
  }
}
