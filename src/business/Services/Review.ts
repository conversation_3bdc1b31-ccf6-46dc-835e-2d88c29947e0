import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IReviewRepository } from "src/business/Interfaces/Repository/IReview";
import { IReviewService } from "src/business/Interfaces/Service/IReview";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class ReviewService extends BaseService<IReview> implements IReviewService {
  private reviewRepository: IReviewRepository;

  constructor(
    @inject(TOKENS.IReviewRepository) reviewRepository: IReviewRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(reviewRepository, notificationManager);
    this.reviewRepository = reviewRepository;
  }

  async create(review: IReview): Promise<boolean> {
    const result = await this.reviewRepository.create(review);
    if (!result) return false;
    return true;
  }

  async getStoreReviews(storeId: string): Promise<IReview[]> {
    const result = await this.reviewRepository.getStoreReviews(storeId);

    return result;
  }

  async getUserReviews(userId: string): Promise<IReview[]> {
    const result = await this.reviewRepository.getUserReviews(userId);

    return result;
  }

  async getOrderReviews(orderId: string, deliverymanReview?: boolean): Promise<IReview | null> {
    const result = await this.reviewRepository.getOrderReviews(orderId, deliverymanReview);

    return result;
  }

  async update(reviewUpdate: IReview): Promise<boolean> {
    const review = await this.reviewRepository.getById(reviewUpdate.id);

    if (!review) {
      this.notificationManager.add("review.update.errors", "not_found");
    }

    let result = 0;
    if (this.isValid()) {
      result = await this.reviewRepository.update(reviewUpdate.id, reviewUpdate);
    }

    return !!result;
  }
}
