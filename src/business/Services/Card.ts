import { validate } from "class-validator";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICard } from "src/business/Interfaces/Prisma/ICard";
import { ICardRepository } from "src/business/Interfaces/Repository/ICard";
import { ICardService } from "src/business/Interfaces/Service/ICard";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class CardService extends BaseService<ICard> implements ICardService {
  private cardRepository: ICardRepository;

  constructor(
    @inject(TOKENS.ICardRepository) cardRepository: ICardRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(cardRepository, notificationManager);
    this.cardRepository = cardRepository;
  }

  async getCardsByUserId(userId: string): Promise<ICard[]> {
    const files = await this.cardRepository.getCardsByUserId(userId);
    if (files) {
      return files;
    }
    return [];
  }

  async create(item: ICard): Promise<ICard | null> {
    const errors = await validate(item);

    if (errors.length > 0) {
      console.log(errors);
      this.notificationManager.add("forms.card.toasts", "create_error");
    }

    if (this.isValid()) {
      const newCard = await this.cardRepository.create(item);
      return newCard;
    }

    return null;
  }
}
