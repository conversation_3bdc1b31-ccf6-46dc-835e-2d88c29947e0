/* eslint-disable prefer-destructuring */
import axios from "axios";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IntegrationHubCitiesDTO } from "src/business/DTOs/IntegrationHub/IntegrationHubCities";
import { IntegrationHubDistrictsDTO } from "src/business/DTOs/IntegrationHub/IntegrationHubDistricts";
import { IntegrationHubStatesDTO } from "src/business/DTOs/IntegrationHub/IntegrationHubStates";
import IntegrationHubZipCodeDTO from "src/business/DTOs/IntegrationHub/IntegrationHubZipCode";
import { IIntegrationHubService } from "src/business/Interfaces/Service/IntegrationHub/IntegrationHub";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import {
  ICustomResponseIntegrationHub,
  IResponseErrorIntegrationHub,
} from "src/business/Interfaces/Tools/IIntegrationErrorResponse";

@injectable()
export default class IntegrationHubService implements IIntegrationHubService {
  @inject(TOKENS.LoggerService)
  private loggerService: ILoggerService;

  private auth = {
    headers: {
      Authorization: `token ${process.env.INTEGRATION_HUB_TOKEN}`,
    },
    data: {
      "Grant Type": " Client Credentials",
    },
    auth: {
      username: process.env.INTEGRATION_HUB_CLIENT_ID!,
      password: process.env.INTEGRATION_HUB_CLIENT_SECRET!,
    },
  };

  async getStates(limit?: number, page?: number, name?: string): Promise<string[] | IResponseErrorIntegrationHub> {
    const endpoint = `${process.env.INTEGRATION_HUB_URL!}/v1/addresses/states`;
    const response = await axios.get<ICustomResponseIntegrationHub<IntegrationHubStatesDTO[]>>(endpoint, this.auth);
    const { items } = response.data.data;
    const states = items.map((item) => item.code);
    return states;
  }

  async getCitiesByStateShortName(
    stateShortName: string,
    limit?: number,
    page?: number,
    name?: string,
  ): Promise<IntegrationHubCitiesDTO[] | IResponseErrorIntegrationHub> {
    try {
      const endpoint = `${process.env.INTEGRATION_HUB_URL!}/v1/addresses/states/${stateShortName}/cities`;
      const response = await axios.get<ICustomResponseIntegrationHub<IntegrationHubCitiesDTO[]>>(endpoint, this.auth);
      const { items } = response.data.data;
      return items;
    } catch (error) {
      this.loggerService.logError(error);
      return error.response.data;
    }
  }

  async getDistrictsByCityId(
    cityId: string,
    limit?: number | undefined,
    page?: number | undefined,
    name?: string | undefined,
  ): Promise<IntegrationHubDistrictsDTO[] | IResponseErrorIntegrationHub> {
    try {
      const endpoint = `${process.env.INTEGRATION_HUB_URL!}/v1/addresses/states/cities/${cityId}/neighborhoods`;
      const response = await axios.get<ICustomResponseIntegrationHub<IntegrationHubDistrictsDTO[]>>(
        endpoint,
        this.auth,
      );
      const { items } = response.data.data;
      return items;
    } catch (error) {
      this.loggerService.logError(error);

      return error.response.data;
    }
  }

  async getByZipCode(zipCode: string): Promise<IntegrationHubZipCodeDTO | IResponseErrorIntegrationHub> {
    try {
      const endpoint = `${process.env.INTEGRATION_HUB_URL!}/v1/addresses/get-by-zip-code/${zipCode}`;
      const response = await axios.get(endpoint, this.auth);
      return response.data.data;
    } catch (error) {
      this.loggerService.logError(error);

      return error.response.data;
    }
  }
}
