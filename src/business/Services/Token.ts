import { AuthFlowType } from "@aws-sdk/client-cognito-identity-provider";
import { EDayOfWeek } from "@prisma/client";
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { CognitoIdTokenPayload } from "aws-jwt-verify/jwt-model";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { UserAuthenticationResult } from "src/business/DTOs/UserAuthenticationResult";
import { ILoginSession } from "src/business/Interfaces/Prisma/ILoginSession";
import { IUserRepository } from "src/business/Interfaces/Repository/IUser";
import { ICognitoService } from "src/business/Interfaces/Service/AWS/ICognito";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { ILoginSessionService } from "src/business/Interfaces/Service/ILoginSession";
import { IProfileService } from "src/business/Interfaces/Service/IProfile";
import { ITokenService } from "src/business/Interfaces/Service/IToken";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import formatExchangeCodeResponse from "src/business/Utils/FormatExchangeCodeResponse";

@injectable()
export class TokenService implements ITokenService {
  constructor(
    @inject(TOKENS.ICognitoService)
    private cognitoService: ICognitoService,
    @inject(TOKENS.NotificationManager)
    private notificationManager: INotificationManager,
    @inject(TOKENS.IDeviceService)
    private deviceService: IDeviceService,
    @inject(TOKENS.IUserRepository)
    private userRepository: IUserRepository,
    @inject(TOKENS.IProfileService)
    private profileService: IProfileService,
    @inject(TOKENS.LoggerService)
    private loggerService: ILoggerService,
    @inject(TOKENS.ILoginSessionService)
    private loginSessionService: ILoginSessionService,
  ) {
    this.cognitoService = cognitoService;
    this.userRepository = userRepository;
  }

  async refreshToken(
    cognitoId: string,
    refreshToken: string,
    cognitoUsername?: string,
  ): Promise<UserAuthenticationResult | null> {
    try {
      const user = await this.userRepository.findFirst({
        where: {
          OR: [
            {
              cognitoId,
            },
            {
              cognitoIdGoogle: cognitoId,
            },
          ],
        },
        include: { userProfiles: true },
      });

      if (!user) {
        this.notificationManager.add("users.errors", "user_notFound");
        return null;
      }

      const authUserResult = await this.cognitoService.initiateAuth({
        AuthFlow: AuthFlowType.REFRESH_TOKEN_AUTH,
        AuthParameters: {
          USERNAME: cognitoUsername || cognitoId,
          REFRESH_TOKEN: refreshToken,
        },
      });

      const profiles = await this.profileService.getApprovedProfiles(user.id);

      if (authUserResult.AuthenticationResult) {
        const response: UserAuthenticationResult = {
          cognitoAuthenticationResult: authUserResult.AuthenticationResult,
          userInfo: {
            id: user.id,
            profiles: profiles.map((profile) => profile.name),
          },
        };

        return response;
      }
    } catch (error) {
      console.log(error);

      this.loggerService.logError(error);

      this.notificationManager.add("generic.errors", "refreshing");
    }

    return null;
  }

  async revokeAccess(refreshToken: string): Promise<void> {
    const response = await this.cognitoService.revokeAccess({
      Token: refreshToken,
    });
    console.log(response);
  }

  async exchangeCode(code: string): Promise<UserAuthenticationResult | null> {
    const response = await this.cognitoService.exchangeCode(code);

    if (response && response.id_token) {
      const payload = await this.getIdTokenPayload(response.id_token);
      const user = await this.userRepository.getByEmail(payload.email as string);

      const result = formatExchangeCodeResponse(response);

      if (user) {
        if (!user.cognitoIdGoogle) {
          this.saveCognitoIdGoogle(user.id, payload.sub);
        }

        const profiles = await this.profileService.getApprovedProfiles(user.id);
        await this.loginSessionService.create({
          userId: user.id,
          startAccessDay: new Date().toLocaleString("en", { weekday: "long" }).toLowerCase() as EDayOfWeek,
          startAccessHour: new Date().getUTCHours(),
        } as ILoginSession);

        result.userInfo = {
          id: user.id,
          profiles: profiles.map((profile) => profile.name),
          isDeleted: user.deleted,
        };
      }

      return result;
    }

    return null;
  }

  async getIdTokenPayload(idToken: string): Promise<CognitoIdTokenPayload> {
    const verifier = CognitoJwtVerifier.create({
      clientId: process.env.AWS_COGNITO_CLIENT_ID || "",
      userPoolId: process.env.AWS_COGNITO_USER_POOL_ID || "",
      tokenUse: "id",
    });
    const payload = await verifier.verify(idToken, { tokenUse: "id" });
    return payload;
  }

  async saveCognitoIdGoogle(userId: string, cognitoIdGoogle: string): Promise<void> {
    await this.userRepository.saveCognitoIdGoogle(userId, cognitoIdGoogle);
  }
}
