import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAddressOptionDTO } from "src/business/DTOs/Address/IAddressOption";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IAddressRepository } from "src/business/Interfaces/Repository/IAddress";
import { IAddressService } from "src/business/Interfaces/Service/IAddress";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class AddressService extends BaseService<IAddress> implements IAddressService {
  private addressRepository: IAddressRepository;

  constructor(
    @inject(TOKENS.IAddressRepository)
    addressRepository: IAddressRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(addressRepository, notificationManager);
    this.addressRepository = addressRepository;
  }

  async getDefaultAddress(userId: string): Promise<IAddress | null> {
    const result = await this.addressRepository.getDefaultAddress(userId);

    return result;
  }

  // async deleteUserAddress(id: string): Promise<DeleteResult[]> {
  //   const result = await this.addressRepository.deleteUserAddress(id);
  //   return result;
  // }

  async create(item: IAddress, userId?: string): Promise<string> {
    const response = await this.addressRepository.create(item, userId);

    if (response) {
      return response.id;
    }

    return "";
  }

  async createWithoutRelate(address: IAddress): Promise<string | boolean> {
    const response = await this.addressRepository.create(address);
    if (response.id) {
      return response.id;
    }
    return false;
  }

  async updateSwitchDefaulAddress(addressUpdate: IAddress, userId: string): Promise<number> {
    const address = await this.getById(addressUpdate.id);
    let result: number = 0;

    if (address) {
      if (addressUpdate.isDefault) {
        const hasDefault = await this.getDefaultAddress(userId);
        if (hasDefault) {
          hasDefault.isDefault = false;
          await this.addressRepository.update(hasDefault.id, hasDefault);
        }
      }

      result = await this.addressRepository.update(addressUpdate.id, addressUpdate);
    } else {
      this.notificationManager.add("address.edit.errors", "address_notFound");
    }
    // const address = Address.create(newItem);

    // if (newItem.isDefault) {
    //   const hasDefault = await this.getDefaultAddress(userId);

    //   if (hasDefault) {
    //     hasDefault.address!.isDefault = false;

    //     await this.addressRepository.update(hasDefault.address!.id, hasDefault.address!);
    //   }
    // }

    // const localStoredAddress = await this.addressRepository.getById(id);

    // if (!localStoredAddress) {
    //   this.notificationManager.add("address.edit.errors", "address_notFound");
    // }

    // let result: UpdateResult = { generatedMaps: [], raw: "", affected: 0 };
    // if (this.isValid()) {
    //   result = await this.addressRepository.update(id, address);
    // }

    return result;
  }

  // async getPaged(currentPage: number, pageSize: number, userId: string, name?: string): Promise<PagedResult<IAddress>> {
  //   const response = await this.addressRepository.getPaged(currentPage, pageSize, userId, name);

  //   return response;
  // }

  async getAddressesByUserId(userId: string): Promise<IAddress[]> {
    const response = await this.addressRepository.getAddressesByUserId(userId);

    return response;
  }

  async getAddresseOptionsByUserId(userId: string): Promise<IAddressOptionDTO[]> {
    const response = await this.addressRepository.getAddresseOptionsByUserId(userId);

    return response;
  }
}
