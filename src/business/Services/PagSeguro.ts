/* eslint-disable default-param-last */
import { ECardMethod, EPaymentMethod, ETransactionStatus } from "@prisma/client";
import axios from "axios";
import browserEnv from "browser-env";
import { inject, injectable } from "inversify";
import { ICreateTransactionViewModel } from "src/api/ViewModels/Transaction/ICreate";
import { EPagSeguroType } from "src/business/Enums/EPagSeguroType";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import {
  ERROR_3DS_PAYMENT_METHOD,
  ERROR_CARD_AUTH_NOT_SUPPORTED,
  ERROR_CARD_CHANGE_PAYMENT_METHOD,
  ERROR_CREATING_PAYMENT_OBJECT,
  ERROR_CREATING_SESSION,
  ERROR_LOADING_LIB,
  ERROR_UNDEFINED_BILLING_ADDRESS,
  ERROR_UNDEFINED_PAYMENT_METHOD,
  ERROR_UNDEFINED_SHIPPING_ADDRESS,
} from "src/business/Interfaces/Service/PagSeguro/CustomErrors";
import { IFinancialConsolidationResponse } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialConsolidation";
import { INotificationResponse } from "src/business/Interfaces/Service/PagSeguro/INotificationResponse";
import { IObjectRequest3DS, IPaymentMethodDebtCard } from "src/business/Interfaces/Service/PagSeguro/IObjectRequest3DS";
import { Charge, IObjectRequestPayment } from "src/business/Interfaces/Service/PagSeguro/IObjectRequestCardPayment";
import {
  ChargeStatus,
  EnvPagSeguro,
  IItemOrder,
  ISession,
  PagSeguroPaymentMethodType,
} from "src/business/Interfaces/Service/PagSeguro/IObjects";
import { ILibPagSeguro, IPagSeguroService } from "src/business/Interfaces/Service/PagSeguro/IPagSeguro";
import {
  IResponse3DSSucess,
  IResponseCancelPayment,
  IResponseError,
  IResponsePayment,
  isResponse3DSError,
  isResponseError,
} from "src/business/Interfaces/Service/PagSeguro/IResponsePayment";
import xml2js from "xml2js";
import { IFinancialConsolidationError } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialError";

const CUSTOMER_COUNTRY = "55";
const CHARGE_AMOUNT_CURRENCY = "BRL";
const SHIPPING_ADDRESS_COUNTRY = "BRA";
const AUTHENTICATION_METHOD_TYPE = "THREEDS";
const MAX_LENGTH_SOFT_DESCRIPTOR_PAYMENT_METHOD = 17;
const MINUTES_FOR_PIX_EXPIRATION_DATE = 2;
const PAGSEGURO_TIMEOUT = 60000;

@injectable()
export class PagSeguroService implements IPagSeguroService {
  @inject(TOKENS.LoggerService)
  private loggerService: ILoggerService;

  async paymentOrder(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    userAdress?: IAddress,
  ): Promise<IResponseError | IResponsePayment> {
    let response;
    switch (transactionData.paymentMethod) {
      case EPaymentMethod.credit:
        response = await this.flowCreditCard(transactionData, user, order);
        break;
      case EPaymentMethod.debt:
        response = await this.flowDebtCard(transactionData, user, order, userAdress);
        break;
      case EPaymentMethod.pix:
        response = await this.flowPix(transactionData, user, order);
        break;
      default:
        response = {
          error_menssages: {
            code: 500,
            description: "não impletado",
            parameter_name: "não impletado",
            errors: "não impletado",
          },
        };
        break;
    }
    return response;
  }

  async flowCreditCard(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
  ): Promise<IResponseError | IResponsePayment> {
    const dataRequest = this.preparingObjectRequest(transactionData, user, order);
    if (isResponseError(dataRequest)) {
      return dataRequest;
    }

    const response = await this.requestPagSeguro(dataRequest);

    return response;
  }

  async flowDebtCard(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    userAdress?: IAddress,
  ): Promise<IResponseError | IResponsePayment> {
    const object3DSRequest = this.preparing3DSObjectRequest(transactionData, user, order, userAdress);

    if (isResponseError(object3DSRequest)) {
      return object3DSRequest;
    }

    const result3DS = await this.resquest3DSPagSeguro(object3DSRequest);

    if (isResponseError(result3DS)) {
      return result3DS;
    }

    const dataRequest = this.preparingObjectRequest(transactionData, user, order, false, result3DS);
    if (isResponseError(dataRequest)) {
      return dataRequest;
    }
    const response = await this.requestPagSeguro(dataRequest);
    return response;
  }

  async flowPix(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
  ): Promise<IResponseError | IResponsePayment> {
    const dataRequest = this.preparingObjectRequest(transactionData, user, order, true);
    if (isResponseError(dataRequest)) {
      return dataRequest;
    }

    const response = await this.requestPagSeguro(dataRequest);

    return response;
  }

  decimalToInt(value: number): number {
    const result = Math.trunc(value * 100);
    return result;
  }

  preparingObjectRequest(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    qrCodes: boolean = false,
    response3DS?: IResponse3DSSucess,
  ): IObjectRequestPayment | IResponseError {
    if (!order.address || !order.store || !order.orderItem) {
      return ERROR_CREATING_PAYMENT_OBJECT;
    }

    let charge = {} as Charge;

    if (transactionData.newCard || transactionData.paymentMethod === EPaymentMethod.debt) {
      charge = {
        reference_id: order.id,
        description: order.store.name,
        amount: {
          value: this.decimalToInt(order.totalPrice),
          currency: CHARGE_AMOUNT_CURRENCY,
        },
        payment_method: {
          type: this.paymentMethodToPagSeguroMethod(transactionData.paymentMethod),
          installments: transactionData.installments,
          capture: true,
          soft_descriptor: order.store.name.substring(0, MAX_LENGTH_SOFT_DESCRIPTOR_PAYMENT_METHOD),
          card: {
            encrypted: transactionData.hashCardPaymentPlatform,
            holder: {
              name: transactionData.cardHolder || "",
            },
            store: transactionData.newCard ? transactionData.newCard.saveCard : false,
          },
        },
      };
      if (response3DS && response3DS.id) {
        charge.payment_method.authentication_method = {
          type: AUTHENTICATION_METHOD_TYPE,
          id: response3DS.id,
        };
      }
    }

    if (transactionData.savedCard && transactionData.paymentMethod === EPaymentMethod.credit) {
      charge = {
        reference_id: order.id,
        description: order.store.name,
        amount: {
          value: this.decimalToInt(order.totalPrice),
          currency: CHARGE_AMOUNT_CURRENCY,
        },
        payment_method: {
          type: this.paymentMethodToPagSeguroMethod(transactionData.paymentMethod),
          installments: transactionData.installments,
          capture: true,
          soft_descriptor: order.store.name.substring(0, MAX_LENGTH_SOFT_DESCRIPTOR_PAYMENT_METHOD),
          card: {
            id: transactionData.savedCard.idCardPaymentPlatform,
            holder: {
              name: transactionData.cardHolder || "",
            },
            security_code: transactionData.savedCard.securityCode,
            store: true,
          },
        },
      };
      if (response3DS && response3DS.id) {
        charge.payment_method.authentication_method = {
          type: AUTHENTICATION_METHOD_TYPE,
          id: response3DS.id,
        };
      }
    }

    const data: IObjectRequestPayment = {
      reference_id: order.id,
      customer: {
        name: `${user.firstName} ${user.lastName}`,
        tax_id: user.cpf,
        email: user.email,
        phones: [
          {
            country: CUSTOMER_COUNTRY,
            type: "MOBILE",
            area: user.phone.substring(0, 2),
            number: user.phone.substring(2),
          },
        ],
      },
      items: order.orderItem.map((item) => {
        const itemOrder: IItemOrder = {
          reference_id: item.id,
          name: item.product.name,
          quantity: item.quantity,
          unit_amount: this.decimalToInt(item.unitPrice),
        };
        return itemOrder;
      }),
      shipping: {
        address: {
          street: order.address.street,
          number: String(order.address.number),
          complement: order.address.complement || "",
          locality: order.address.state,
          city: order.address.city,
          region_code: order.address.state.toUpperCase(),
          country: SHIPPING_ADDRESS_COUNTRY,
          postal_code: String(order.address.postcode),
        },
      },
      charges: [charge],
    };

    if (!data.shipping.address.complement) delete data.shipping.address.complement;
    if (!data.shipping.address.locality) delete data.shipping.address.locality;

    if (qrCodes) {
      delete data.charges;
      const expirationDate = new Date();
      expirationDate.setTime(expirationDate.getTime() + MINUTES_FOR_PIX_EXPIRATION_DATE * 60 * 1000); // Minutos
      data.qr_codes = [
        {
          amount: {
            value: this.decimalToInt(order.totalPrice),
          },
          expiration_date: expirationDate,
        },
      ];
    }

    data.notification_urls = [`${process.env.API_EXTERNAL_URL}`];

    return data;
  }

  preparing3DSObjectRequest(
    transactionData: ICreateTransactionViewModel,
    user: IUser,
    order: IOrder,
    billingAddress?: IAddress,
  ): IObjectRequest3DS | IResponseError {
    const paymentMethod: IPaymentMethodDebtCard = {
      type: "DEBIT_CARD",
      installments: transactionData.installments,
      card: {
        holder: {
          name: "",
        },
      },
    };

    paymentMethod.card.encrypted = transactionData.hashCardPaymentPlatform;
    paymentMethod.card.holder.name = transactionData.cardHolder || "";

    if (paymentMethod.card.holder.name === "") {
      return ERROR_UNDEFINED_PAYMENT_METHOD;
    }

    if (!billingAddress) {
      return ERROR_UNDEFINED_BILLING_ADDRESS;
    }

    if (!order.address) {
      return ERROR_UNDEFINED_SHIPPING_ADDRESS;
    }

    const sum = user.email.indexOf("+");
    const atsign = user.email.indexOf("@");

    const data: IObjectRequest3DS = {
      customer: {
        name: `${user.firstName} ${user.lastName}`,
        tax_id: user.cpf,
        email: sum >= 0 && atsign >= 0 ? user.email.slice(0, sum) + user.email.slice(atsign) : user.email,
        phones: [
          {
            country: CUSTOMER_COUNTRY,
            type: "MOBILE",
            area: user.phone.substring(0, 2),
            number: user.phone.substring(2),
          },
        ],
      },
      paymentMethod,
      amount: {
        value: this.decimalToInt(order.totalPrice),
        currency: CHARGE_AMOUNT_CURRENCY,
      },
      billingAddress: {
        street: billingAddress.street,
        number: String(billingAddress.number),
        complement: billingAddress.complement,
        city: billingAddress.city,
        regionCode: billingAddress.district.toUpperCase(),
        country: SHIPPING_ADDRESS_COUNTRY,
        postalCode: String(billingAddress.postcode),
      },
      shippingAddress: {
        street: order.address.street,
        number: String(order.address.number),
        complement: order.address.complement,
        city: order.address.city,
        regionCode: order.address.state.toUpperCase(),
        country: SHIPPING_ADDRESS_COUNTRY,
        postalCode: String(order.address.postcode),
      },
      dataOnly: false,
      notification_urls: [`${process.env.API_EXTERNAL_URL}`],
    };

    if (!data.billingAddress.complement) delete data.billingAddress.complement;
    if (!data.shippingAddress.complement) delete data.shippingAddress.complement;

    return data;
  }

  async cancelPaymentPagSeguro(chargeId: string, amount: number): Promise<IResponseError | IResponseCancelPayment> {
    try {
      const data = { amount: { value: this.decimalToInt(amount) } };
      const url = `${process.env.PAGSEGURO_CHARGES_URL!}/${chargeId}/cancel`;
      const result = await axios.post(url, data, {
        headers: {
          Authorization: `Bearer ${process.env.PAGSEGURO_TOKEN}`,
          "Content-type": "application/json",
        },
        timeout: PAGSEGURO_TIMEOUT,
      });
      return result.data;
    } catch (error) {
      console.log("🚀 ~ file: PagSeguro.ts:419 ~ PagSeguroService ~ error:", error.response.data);

      this.loggerService.logError(error);

      return error.response.data;
    }
  }

  async requestPagSeguro(data: IObjectRequestPayment): Promise<IResponsePayment | IResponseError> {
    try {
      const result = await axios.post(process.env.PAGSEGURO_ORDERS_URL!, data, {
        headers: {
          Authorization: `Bearer ${process.env.PAGSEGURO_TOKEN}`,
          "Content-type": "application/json",
        },
        timeout: PAGSEGURO_TIMEOUT,
      });
      return result.data;
    } catch (error) {
      console.log("🚀 ~ PagSeguroService ~ requestPagSeguro ~ error:", JSON.stringify(error, null, 2));
      this.loggerService.logError(error);

      return error.response.data;
    }
  }

  async resquest3DSPagSeguro(data: IObjectRequest3DS): Promise<IResponse3DSSucess | IResponseError> {
    browserEnv();
    try {
      const PagSeguro = await this.getLibPagSeguro();
      if (!PagSeguro) {
        return ERROR_LOADING_LIB;
      }

      const session = await this.createSession();

      if (session.errors) {
        return ERROR_CREATING_SESSION;
      }

      PagSeguro.setUp({
        session: session.session,
        env: process.env.PAGSEGURO_MODE! as EnvPagSeguro,
      });

      // eslint-disable-next-line no-undef
      window.confirm = () => true;

      const response = await PagSeguro.authenticate3DS({ data });

      if (response.status === "AUTH_NOT_SUPPORTED") {
        return ERROR_CARD_AUTH_NOT_SUPPORTED;
      }

      if (response.status === "CHANGE_PAYMENT_METHOD") {
        return ERROR_CARD_CHANGE_PAYMENT_METHOD;
      }

      return response;
    } catch (error) {
      console.log("🚀 ~ PagSeguroService ~ resquest3DSPagSeguro ~ error:", JSON.stringify(error, null, 2));

      this.loggerService.logError(error);

      const result = ERROR_3DS_PAYMENT_METHOD;
      if (isResponse3DSError(error)) {
        result.traceId = error.traceId;
      }

      return result;
    }
  }

  async getLibPagSeguro(): Promise<ILibPagSeguro | undefined> {
    browserEnv(["navigator", "window"]);
    try {
      const response = await fetch(process.env.PAGSEGURO_LIB_URL!, {
        method: "GET",
      });

      const textFunc = await (await response.blob()).text();

      // eslint-disable-next-line no-new-func
      const funcPagSeguro: ILibPagSeguro = await new Function(`${textFunc} return PagSeguro`)();

      return funcPagSeguro;
    } catch (error) {
      console.error("ERROR getLibPagSeguro catch: ", JSON.stringify(error, null, 2));

      this.loggerService.logError(error);
    }
    return undefined;
  }

  async createSession(): Promise<ISession> {
    try {
      const result = await axios.post(
        process.env.PAGSEGURO_SESSIONS_URL!,
        {},
        {
          headers: {
            Authorization: `Bearer ${process.env.PAGSEGURO_TOKEN}`,
            "Content-type": "application/json",
          },
          timeout: PAGSEGURO_TIMEOUT,
        },
      );
      return result.data;
    } catch (error) {
      console.log(
        "🚀 ~ file: PagSeguro.ts:454 ~ PagSeguroService ~ createSession ~ error:",
        JSON.stringify(error, null, 2),
      );
      const failure: ISession = {
        session: "",
        expires_at: 0,
        errors: error.response.data.message,
      };

      this.loggerService.logError(error);

      return failure;
    }
  }

  pagSeguroMethodToCardMethod(pgMethod: PagSeguroPaymentMethodType) {
    let type: ECardMethod;
    switch (pgMethod) {
      case "CREDIT_CARD":
        type = ECardMethod.credit;
        break;
      case "DEBIT_CARD":
        type = ECardMethod.debt;
        break;
      default:
        type = ECardMethod.credit;
    }
    return type;
  }

  cardMethodToPagSeguroMethod(localMethod: ECardMethod) {
    let type: PagSeguroPaymentMethodType;
    switch (localMethod) {
      case ECardMethod.credit:
        type = "CREDIT_CARD";
        break;
      case ECardMethod.debt:
        type = "DEBIT_CARD";
        break;
      default:
        type = "CREDIT_CARD";
    }
    return type;
  }

  pagSeguroStatusToInternalStatusTransaction(pgStatus: ChargeStatus) {
    let status: ETransactionStatus;
    switch (pgStatus) {
      case "AUTHORIZED":
        status = ETransactionStatus.authorized;
        break;
      case "PAID":
        status = ETransactionStatus.approved;
        break;
      case "IN_ANALYSIS":
        status = ETransactionStatus.in_process;
        break;
      case "DECLINED":
        status = ETransactionStatus.rejected;
        break;
      case "CANCELED":
        status = ETransactionStatus.cancelled;
        break;
      default:
        status = ETransactionStatus.pending;
    }
    return status;
  }

  paymentMethodToPagSeguroMethod(paymentMethod: EPaymentMethod): PagSeguroPaymentMethodType {
    let type: PagSeguroPaymentMethodType;
    switch (paymentMethod) {
      case EPaymentMethod.credit:
        type = "CREDIT_CARD";
        break;
      case EPaymentMethod.debt:
        type = "DEBIT_CARD";
        break;
      default:
        type = "CREDIT_CARD";
    }
    return type;
  }

  async getNotification(notificationCode: string): Promise<any> {
    let notificationResult: INotificationResponse | undefined;
    try {
      const result = await axios.get(`${process.env.PAGSEGURO_NOTIFICATION_URL}/${notificationCode}/`, {
        params: {
          email: process.env.PAGSEGURO_EMAIL,
          token: process.env.PAGSEGURO_TOKEN,
        },
        timeout: PAGSEGURO_TIMEOUT,
      });
      const parser = new xml2js.Parser();
      parser.parseString(result.data, (err, result) => {
        const { code, reference, type, status, grossAmount } = result.transaction;
        notificationResult = {
          code: code[0],
          reference: reference[0],
          type: Number(type[0]),
          status: Number(status[0]),
          grossAmount: parseFloat(grossAmount[0]),
        };
      });
    } catch (error) {
      console.log(
        "🚀 ~ file: PagSeguro.ts:552 ~ PagSeguroService ~ getNotification ~ error:",
        JSON.stringify(error, null, 2),
      );

      this.loggerService.logError(error);
    }
    return notificationResult;
  }

  async getTransaction(transactionPlatformId: string): Promise<IResponsePayment | undefined> {
    try {
      const response = await axios.get(`${process.env.PAGSEGURO_ORDERS_URL}/${transactionPlatformId}`, {
        headers: {
          Authorization: `Bearer ${process.env.PAGSEGURO_TOKEN}`,
          "Content-type": "application/json",
        },
        timeout: PAGSEGURO_TIMEOUT,
      });
      return response.data as IResponsePayment;
    } catch (error) {
      console.log(
        "🚀 ~ file: PagSeguro.ts:575 ~ PagSeguroService ~ getTransaction ~ error:",
        JSON.stringify(error, null, 2),
      );

      this.loggerService.logError(error);
    }
    return undefined;
  }

  async getFinancialConsolidation(
    date: string,
    page: number,
    pageSize: number,
    type: EPagSeguroType,
  ): Promise<IFinancialConsolidationResponse | null> {
    try {
      const response = await axios.get(
        `${process.env.PAGSEGURO_FINANCIAL_URL}/movimentos?dataMovimento=${date}&pageNumber=${page}&pageSize=${pageSize}&tipoMovimento=${type}`,
        {
          headers: {
            "Content-type": "application/json",
          },
          auth: {
            password: process.env.PAGSEGURO_FINANCIAL_TOKEN!,
            username: process.env.PAGSEGURO_FINANCIAL_USERNAME!,
          },
          timeout: PAGSEGURO_TIMEOUT,
        },
      );
      return response.data as IFinancialConsolidationResponse;
    } catch (error) {
      console.log("Financial Consolidation ERROR:", error.response.data);
      return null;
    }
  }
}
