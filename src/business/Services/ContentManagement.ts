import { EContentManagementType } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IContentManagement } from "src/business/Interfaces/Prisma/IContentManagement";
import { IContentManagementRepository } from "src/business/Interfaces/Repository/IContentManagement";
import { IContentManagementService } from "src/business/Interfaces/Service/IContentManagement";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class ContentManagementService extends BaseService<IContentManagement> implements IContentManagementService {
  private contentManagementRepository: IContentManagementRepository;

  constructor(
    @inject(TOKENS.IContentManagementRepository) contentManagementRepository: IContentManagementRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(contentManagementRepository, notificationManager);
    this.contentManagementRepository = contentManagementRepository;
  }

  async getByType(type: EContentManagementType): Promise<IContentManagement | null> {
    const contents = await this.contentManagementRepository.getByType(type);
    return contents;
  }

  async create(item: IContentManagement): Promise<boolean> {
    const result = await this.contentManagementRepository.create(item);
    if (result) {
      return true;
    }

    return false;
  }
}
