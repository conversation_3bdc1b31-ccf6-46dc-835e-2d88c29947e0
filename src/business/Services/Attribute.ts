import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAttributeDTO } from "src/business/DTOs/Attribute/IAttribute";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IAttribute } from "src/business/Interfaces/Prisma/IAttribute";
import { IAttributeRepository } from "src/business/Interfaces/Repository/IAttribute";
import { IAttributeService } from "src/business/Interfaces/Service/IAttribute";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class AttributeService
  extends BaseService<IAttribute>
  implements IAttributeService
{
  private attributeRepository: IAttributeRepository;

  constructor(
    @inject(TOKENS.IAttributeRepository)
    attributeRepository: IAttributeRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager
  ) {
    super(attributeRepository, notificationManager);
    this.attributeRepository = attributeRepository;
  }

  async getWithProductAttributeOption(
    productId: string
  ): Promise<IAttribute[]> {
    const attributes = await this.attributeRepository.getWithProductAttributeOption(productId);    
    return attributes;
  }

  async create(item: IAttribute): Promise<boolean> {
    const response = await this.attributeRepository.create(item);

    return !!response;
  }

  async getWithAttributeOption(
    attributeId: string
  ): Promise<IAttribute | null> {
    const attribute = await this.attributeRepository.getWithAttributeOption(
      attributeId
    );

    if (attribute) {
      return attribute;
    }

    this.notificationManager.add("attribute.errors", "attribute_notFound");

    return null;
  }

  async getPagedWithAllAttributeOption(
    productId: string,
    currentPage: string,
    pageSize: string,
    filter: string
  ): Promise<PagedResult<IAttributeDTO>> {
    const response =
      await this.attributeRepository.getPagedWithAllAttributeOption(
        productId,
        Number(currentPage),
        Number(pageSize),
        filter
      );

    return response;
  }
}
