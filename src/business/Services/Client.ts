import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { UnknownObject } from "src/business/DTOs/UnknownObject";
import { IClient } from "src/business/Interfaces/Prisma/IClient";
import { IClientRepository } from "src/business/Interfaces/Repository/IClient";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { IClientService } from "src/business/Interfaces/Service/IClient";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import ProfilesSingleton from "src/business/Singletons/Profile";

@injectable()
export class ClientService extends BaseService<IClient> implements IClientService {
  constructor(
    @inject(TOKENS.IClientRepository)
    private clientRepository: IClientRepository,
    @inject(TOKENS.ProfilesSingleton)
    private profilesSingleton: ProfilesSingleton,
    @inject(TOKENS.IUserProfileRepository)
    private userProfileRepository: IUserProfileRepository,
    @inject(TOKENS.NotificationManager)
    protected notificationManager: INotificationManager,
  ) {
    super(clientRepository, notificationManager);
  }

  async updateClientProfile(userId: string, data: IClient): Promise<boolean> {
    // const profile = await this.validate(data);
    // if (!this.isValid() && !profile) {
    //   return null;
    // }
    const response = await this.clientRepository.updateClientProfile(userId, data, this.profilesSingleton.client.id);

    return response;
  }

  // TODO Fix validation
  async validate(data: UnknownObject): Promise<IClient> {
    // const client = Client.create(data);
    // const errors = await validate(client);
    // if (errors.length > 0) {
    //   this.notificationManager.addFromTypeorm("client", errors);
    // }
    // return client;
    return {} as IClient;
  }
}
