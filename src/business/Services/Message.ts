import { Express } from "express";
import { EMessageStatus } from "@prisma/client";
import { inject, injectable } from "inversify";
import mime from "mime-types";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";
import { IMessageRepository } from "src/business/Interfaces/Repository/IMessage";
import { IS3Service } from "src/business/Interfaces/Service/AWS/IS3";
import { IEmailAttachmentService } from "src/business/Interfaces/Service/IEmailAttachment";
import { IMessageService } from "src/business/Interfaces/Service/IMessage";
import { IUserMessageService } from "src/business/Interfaces/Service/IUserMessage";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { IListNotificationMessage } from "src/business/DTOs/Message/IListNotificationMessage";
import { EProfile } from "src/business/Enums/Models/EProfile";
import ProfilesSingleton from "src/business/Singletons/Profile";

@injectable()
export class MessageService extends BaseService<IMessage> implements IMessageService {
  private messageRepository: IMessageRepository;

  constructor(
    @inject(TOKENS.IMessageRepository) messageRepository: IMessageRepository,
    @inject(TOKENS.IUserMessageService)
    private userMessageService: IUserMessageService,
    @inject(TOKENS.IEmailAttachmentService)
    private emailAttachmentService: IEmailAttachmentService,
    @inject(TOKENS.IS3Service) private s3Service: IS3Service,
    @inject(TOKENS.ProfilesSingleton)
    private profilesSingleton: ProfilesSingleton,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(messageRepository, notificationManager);
    this.messageRepository = messageRepository;
  }

  async getUserMessages(
    id: string,
    filterType: "received" | "sended" | "all",
    sendingType?: EMessageSendingType,
  ): Promise<IMessage[]> {
    const result = await this.messageRepository.getUserMessages(id, filterType, sendingType);

    return result;
  }

  // TODO Verify return
  async create(data: IMessage, files?: Express.Multer.File[]): Promise<boolean> {
    if (data.content?.length === 0) {
      this.notificationManager.add("message.errors", "message_without_content");
      return false;
    }

    const result = await this.messageRepository.create(data);

    if (!result) {
      this.notificationManager.add("message.errors", "message_not_saved");
      return false;
    }

    if (result.content && files) {
      const messageContentId = result.content.find((item) => item.sendingType === EMessageSendingType.email)?.id;

      files.forEach(async (file) => {
        const type = mime.extension(file.mimetype);
        const key = type
          ? `files/${messageContentId}-${new Date().getTime()}.${type}`
          : `files/${messageContentId}-${new Date().getTime()}`;

        await this.s3Service
          .putObject({
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: key,
            Body: file.buffer,
            ContentType: file.mimetype,
          })
          .then(async () => {
            // TODO Test
            await this.emailAttachmentService.create({
              url: `${process.env.AWS_CLOUDFRONT_DOMAIN}/${key}`,
              messageContentId,
            });
          });
      });
    }

    return true;
  }

  async getUserNotificationMessages(
    userId: string,
    profile: EProfile,
    currentPage: string,
    pageSize: string,
    filterByStatus?: string,
    dateToFilter?: string,
    orderBy?: "asc" | "desc",
    origin?: "web",
  ): Promise<PagedResult<IListNotificationMessage>> {
    const handleFilter = filterByStatus || undefined;
    const profileId = this.profilesSingleton[profile].id;

    const filter = handleFilter ? EMessageStatus[handleFilter] : undefined;

    const result = await this.messageRepository.getUserNotificationMessages(
      userId,
      profileId,
      Number(currentPage),
      Number(pageSize),
      filter,
      dateToFilter,
      orderBy,
      origin,
    );

    return result;
  }

  async updateNotificationMessageStatus(messageId: string, profile: EProfile): Promise<number> {
    const profileId = this.profilesSingleton[profile].id;
    const result = await this.userMessageService.updateStatus(messageId, profileId, EMessageStatus.read);

    return result;

    // this.notificationManager.add("message.errors", "message_status_not_updated");
  }

  async updateMultipleNotificationMessagesStatus(messageId: string[], profile: EProfile): Promise<number> {
    const profileId = this.profilesSingleton[profile].id;
    const result = await this.userMessageService.updateMultipleMessages(messageId, profileId, EMessageStatus.read);

    return result;

    // this.notificationManager.add("message.errors", "message_status_not_updated");
  }
}
