import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IDefaultPrismaClient } from "src/business/Interfaces/Database/IDefault";
import { ILog } from "src/business/Interfaces/Prisma/ILog";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { ILogBuilder } from "src/business/Interfaces/Tools/ILogBuilder";
import { createLogger, format, Logger } from "winston";
import { PrismaWinstonTransporter } from "../../Configs/Winston/PrismaTransporter";

@injectable()
export class LoggerService implements ILoggerService {
  private prismaClient: IDefaultPrismaClient["client"];

  private logger: Logger;

  private logBuilder: ILogBuilder;

  constructor(
    @inject(TOKENS.LogBuilder) logBuilder: ILogBuilder,
    @inject(TOKENS.DefaultPrismaClient) defaultPrismaClient: IDefaultPrismaClient,
  ) {
    this.prismaClient = defaultPrismaClient.client;

    this.logBuilder = logBuilder;

    this.logger = createLogger({
      format: format.combine(format.timestamp(), format.json()),
      transports: [new PrismaWinstonTransporter({ prisma: this.prismaClient, tableName: "log" })],
    });
  }

  public log(log: ILog): void {
    this.logger.log(log);
  }

  public logError(error: any) {
    const log = this.logBuilder.buildError(error);

    this.logger.log(log);
  }
}
