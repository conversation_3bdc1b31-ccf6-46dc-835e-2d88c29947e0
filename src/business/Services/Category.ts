import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICategoryDTO } from "src/business/DTOs/Category/ICategory";
import { ICategorySubcategoryDTO } from "src/business/DTOs/Category/ICategorySubcategory";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ICategoryRepository } from "src/business/Interfaces/Repository/ICategory";
import { ICategorySubcategoryRepository } from "src/business/Interfaces/Repository/ICategorySubcategory";
import { IStoreCategoryRepository } from "src/business/Interfaces/Repository/IStoreCategory";
import { ICategoryService } from "src/business/Interfaces/Service/ICategory";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { ICategory } from "../Interfaces/Prisma/ICategory";

@injectable()
export class CategoryService extends BaseService<ICategory> implements ICategoryService {
  constructor(
    @inject(TOKENS.ICategoryRepository)
    private categoryRepository: ICategoryRepository,
    @inject(TOKENS.ICategorySubcategoryRepository)
    private categorySubcategoryRepository: ICategorySubcategoryRepository,
    @inject(TOKENS.IStoreCategoryRepository)
    private storeCategoryRepository: IStoreCategoryRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(categoryRepository, notificationManager);
    this.categoryRepository = categoryRepository;
    this.categorySubcategoryRepository = categorySubcategoryRepository;
  }

  async createDefaultCategories(): Promise<void> {
    const categories = await this.categoryRepository.getAll();

    if (categories.length < 1) {
      await this.categoryRepository.createCategoriesWithSubcategories();
    }
  }

  async create(category: ICategory, attachments?: string[]): Promise<boolean> {
    const result = await this.categoryRepository.create(category, attachments);

    if (result) {
      // if (attachments && attachments.length > 0 && result.id) {
      //          await this.fileRepository.relateEntityFiles(
      //           result.id,
      //           attachments.map((attachment) => attachment.id),
      //         );
      // }
    }

    if (!result) {
      this.notificationManager.add("category.create.error", "error");
      return false;
    }

    return true;
  }

  // async update(
  //   id: string,
  //   data: UpdateCategoryViewModel
  // ): Promise<UpdateResult | null> {
  //   const categoryMapped = mapper.map(data, UpdateCategoryViewModel, Category);

  //   console.log(categoryMapped);

  //   this.deleteCategorySubcategory(id);
  //   if (
  //     categoryMapped.categorySubcategory &&
  //     categoryMapped.categorySubcategory.length > 0
  //   ) {
  //     this.categorySubcategoryRepository.saveMany(
  //       categoryMapped.categorySubcategory
  //     );
  //   }
  //   const updateCategory = new Category();
  //   updateCategory.name = categoryMapped.name;
  //   updateCategory.description = categoryMapped.description;
  //   const result = this.categoryRepository.update(id, updateCategory);
  //   return result;
  // }

  async createSeveralCategories(category: ICategory[]): Promise<ICategory[]> {
    category.map((category) => this.categoryRepository.create(category));
    return category;
  }

  async getWithSubcategoryByStore(storeId: string): Promise<ICategory[]> {
    const category = await this.categoryRepository.getWithSubcategoryByStore(storeId);

    return category;
  }

  async getAllCategoriesWithSubCategory(): Promise<ICategory[]> {
    const categories = await this.categoryRepository.getAllCategoriesWithSubCategory();

    if (categories) {
      const mappedCategory = categories.map((category) => {
        const item = category;

        delete item.storeCategory;

        return item;
      });
      return mappedCategory;
    }

    if (!categories) {
      this.notificationManager.add("product.create.errors", "category_notFound");
    }

    return categories;
  }

  // async getAllFilteredPaginated(
  //   filter: string,
  //   currentPage: number,
  //   pageSize: number
  // ): Promise<FilteredCategoryViewModel> {
  //   const data: CategoryPagedResult =
  //     await this.categoryRepository.getPagedList(
  //       `LOWER(entity.name) LIKE LOWER('%${filter}%')`,
  //       currentPage,
  //       pageSize,
  //       { field: "name", order: "ASC" }
  //     );

  //   const mappedData = mapper.map(
  //     data,
  //     CategoryPagedResult,
  //     FilteredCategoryViewModel
  //   );

  //   return mappedData;
  // }

  async getPagedWithSubcategory(
    currentPage: string = "",
    pageSize: string = "",
    filterValue: string = "",
    orderBy: string = "",
    sortDirection: IOrder = "asc",
  ): Promise<PagedResult<ICategory>> {
    const columnFilter: string = "name,description";

    const data = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      undefined,
    );

    return data.data;
  }

  async getPagedWithIcon(currentPage: string = "", pageSize: string = ""): Promise<PagedResult<ICategory>> {
    const data = await this.categoryRepository.getCategoriesWithIcon(currentPage, pageSize);

    return data;
  }

  async getPagedWithStoreOptions(
    storeId: string,
    currentPage: number,
    pageSize: number,
    filter: string,
  ): Promise<PagedResult<ICategoryDTO>> {
    const data = await this.categoryRepository.getPagedWithStoreOptions(
      storeId,
      Number(currentPage),
      Number(pageSize),
      filter,
    );

    return data;
  }

  async deleteWithRelations(id: string): Promise<boolean> {
    const response = await this.categoryRepository.deleteWithRelations(id);

    return response;
  }

  async deleteCategorySubcategory(id: string) {
    const category = await this.categorySubcategoryRepository.deleteByCategoryId(id);
    return category;
  }

  async getCategoryByProductWithSubcategory(storeId: string, productId?: string): Promise<ICategorySubcategoryDTO[]> {
    const result = await this.categoryRepository.getCategoryByProductWithSubcategory(storeId, productId);

    return result;
  }

  async updateCategoryAndSubcategoryRelation(data: ICategory, subcategoryIds: { id: string }[]): Promise<boolean> {
    const response = await this.categoryRepository.updateCategoryAndSubcategoryRelation(data, subcategoryIds);

    return response;
  }

  async getSelect(categoryName: string, currentPage: string, pageSize: string): Promise<PagedResult<ISelectDTO>> {
    const result = await this.categoryRepository.getSelect(categoryName, Number(currentPage), Number(pageSize));

    return result;
  }

  async getCategoryDetails(id: string): Promise<ICategory | null> {
    const result = await this.categoryRepository.getCategoryDetails(id);
    return result;
  }
}
