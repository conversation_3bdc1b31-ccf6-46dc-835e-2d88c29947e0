import { EMailingSendingType, EMessageType } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { ETypeEmail } from "src/business/Enums/Models/ETypeEmail";
import { IMailing } from "src/business/Interfaces/Prisma/IMailing";
import { IMailingRepository } from "src/business/Interfaces/Repository/IMailing";
import { ISESService } from "src/business/Interfaces/Service/AWS/ISES";
import { ISNSService } from "src/business/Interfaces/Service/AWS/ISNS";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { IMailingService } from "src/business/Interfaces/Service/IMailing";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class MailingService extends BaseService<IMailing> implements IMailingService {
  constructor(
    @inject(TOKENS.IMailingRepository)
    private MailingRepository: IMailingRepository,
    @inject(TOKENS.NotificationManager)
    protected notificationManager: INotificationManager,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
    @inject(TOKENS.ISNSService)
    private snsService: ISNSService,
    @inject(TOKENS.ISESService)
    private sesService: ISESService,
    @inject(TOKENS.IUserService)
    private userService: IUserService,
    @inject(TOKENS.IDeviceService)
    private deviceService: IDeviceService,
  ) {
    super(MailingRepository, notificationManager);
  }

  async create(Mailing: IMailing): Promise<boolean | null> {
    const users = await this.getUsersFilteredByMailing(Mailing);

    if (users && users.length > 0) {
      const response = await this.MailingRepository.create(Mailing, users);

      if (response) {
        const usersIds = users.map((userId) => userId.id);

        const { TopicArn } = await this.snsService.createTopic({ Name: Mailing.title.replace(/ /g, "").replace(/[^a-zAZ0-9]/g, "") });

        if (TopicArn) {
          const associatedDevices = await this.associateDevicesToTopic(TopicArn, usersIds);

          const updatedRows = await this.MailingRepository.update(response.id, {
            topicArn: TopicArn,
          });

          if (updatedRows > 0 && associatedDevices) {
            await this.sendNotification(response, TopicArn, usersIds);
          } else {
            await this.delete(Mailing.id);
            this.notificationManager.add("generic.errors", "mailingCreate")
            return null;
          }

          return true;
        }
        await this.MailingRepository.delete(Mailing.id);
        this.notificationManager.add("generic.errors", "mailingCreate")
        return null;
      }
    }
    this.notificationManager.add("generic.errors", "mailingUsersNotFound")
    return null;
  }

  async executeMailing(MailingId: string): Promise<boolean> {
    const Mailing = await this.MailingRepository.getById(MailingId, {
      filtersMailing: true,
    });

    if (Mailing && Mailing.filtersMailing && Mailing.topicArn) {
      const users = await this.getUsersFilteredByMailing(Mailing);
      if (users) {
        const updateMailing = await this.MailingRepository.updateMailing(MailingId, users);

        if (updateMailing) {
          const usersIds = users.map((userId) => userId.id);

          await this.associateDevicesToTopic(Mailing.topicArn, usersIds);

          const notificated = await this.sendNotification(Mailing, Mailing.topicArn, usersIds);

          if (notificated) return true;
        }
      }
      this.notificationManager.add("generic.errors", "mailingUsersNotFound")
    }
    this.notificationManager.add("generic.errors", "executeMailing")

    return false;
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.MailingRepository.deleteMailing(id);

    if (result && result.topicArn) {
      await this.snsService.deleteTopic({
        TopicArn: result.topicArn,
      });

      return true;
    }
    return false;
  }

  async associateDevicesToTopic(topicArn: string, usersIds: string[]): Promise<boolean> {
    const devices = await this.deviceService.getUsersDevicesByUserId(usersIds);

    if (devices) {
      const devicesSubscribed = await this.deviceService.subscribeMultipleEndpointsToTopic(topicArn, devices);

      if (devicesSubscribed) {
        return true;
      }
    }

    return false;
  }

  async sendNotificationToMailingTopic(
    Mailing: IMailing,
    topicArn: string,
    usersIds: string[],
  ): Promise<void> {
    await this.notificationService.sendNotificationToTopic(
      usersIds,
      {
        title: Mailing.title,
        body: Mailing.messageContent,
        type: EMessageType.mailing,
        profile: EProfile.client,
      },
      topicArn,
      Mailing.id,
    );
  }

  async getUsersFilteredByMailing(Mailing: IMailing): Promise<IUserMailingDTO[] | null> {
    let users: IUserMailingDTO[] | null = [] as IUserMailingDTO[];


    if (Mailing.filtersMailing) {
      users = await this.userService.getByMailingFilter(Mailing.filtersMailing);

      if (users && users.length > 0) return users;
    }

    return null;
  }

  async sendNotificationToUsersEmail(Mailing: IMailing, usersEmails: string[]): Promise<void> {
    usersEmails.map(async (userEmail) => {
      try {
        const result = await this.sesService.sendEmail({
          toAddresses: [userEmail],
          message: Mailing.messageContent,
          sourceEmail: process.env.AWS_SES_EMAIL_SOURCE!,
          subject: Mailing.title,
          type: ETypeEmail.text,
        });

        return result;
      } catch (error) {
        console.log(error);
        this.notificationManager.add("generic.errors", "internal_error");
        return null;
      }
    });
  }

  async sendNotification(Mailing: IMailing, topicArn: string, usersIds: string[]): Promise<boolean> {
    switch (Mailing.sendingType) {
      case EMailingSendingType.notification:
        await this.sendNotificationToMailingTopic(Mailing, topicArn, usersIds);
        return true;
      case EMailingSendingType.email: {
        const emails = await this.userService.getEmailsByUsersIds(usersIds);
        await this.sendNotificationToUsersEmail(Mailing, emails);

        return true;
      }
      case EMailingSendingType.notification_email: {
        const emails = await this.userService.getEmailsByUsersIds(usersIds);
        await this.sendNotificationToUsersEmail(Mailing, emails);
        await this.sendNotificationToMailingTopic(Mailing, topicArn, usersIds);
        return true;
      }
      default:
        return false;
    }
  }

  async getMailingDetailsById(MailingId: string): Promise<IMailing | null> {
    const response = await this.MailingRepository.getById(MailingId, {
      filtersMailing: true,
    });

    if (response) return response;

    return null;
  }
}
