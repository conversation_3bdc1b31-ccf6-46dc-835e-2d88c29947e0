import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICreateStoreModerationDTO } from "src/business/DTOs/StoreModeration/IStoreModeration";
import { IStoreModeration } from "src/business/Interfaces/Prisma/IStoreModeration";
import { IStoreModerationRepository } from "src/business/Interfaces/Repository/IStoreModeration";
import { IStoreModerationService } from "src/business/Interfaces/Service/IStoreModeration";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class StoreModerationService extends BaseService<IStoreModeration> implements IStoreModerationService {
  private storeModerationRepository: IStoreModerationRepository;

  constructor(
    @inject(TOKENS.IStoreModerationRepository)
    storeModerationRepository: IStoreModerationRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(storeModerationRepository, notificationManager);
    this.storeModerationRepository = storeModerationRepository;
  }

  async getAllModeration(): Promise<IStoreModeration[]> {
    const result = await this.storeModerationRepository.getAll();
    return result;
  }

  async create(moderation: ICreateStoreModerationDTO): Promise<boolean> {
    const result = await this.storeModerationRepository.create(moderation);
    if (result) return true;
    return false;
  }

  async getByStoreId(storeId: string): Promise<IStoreModeration[]> {
    const result = await this.storeModerationRepository.getByStoreId(storeId);

    return result;
  }

  async getMostRecentStoreModeration(storeId: string): Promise<IStoreModeration | null> {
    const result = await this.storeModerationRepository.getMostRecentStoreModeration(storeId);

    return result;
  }
}
