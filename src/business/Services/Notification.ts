import { EDeviceType, ELanguageOptions } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import INotificationDTO from "src/business/DTOs/Notification";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";
import { ISNSService } from "src/business/Interfaces/Service/AWS/ISNS";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { IMessageService } from "src/business/Interfaces/Service/IMessage";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IResourcesService } from "src/business/Interfaces/Service/IResources";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import ProfilesSingleton from "src/business/Singletons/Profile";
import generateAPNNotificationMessage from "src/business/Utils/Notifications/generateAPNNotificationMessage";
import generateFCMNotificationMessage from "src/business/Utils/Notifications/generateFCMNotificationMessage";
import generateMultiplePlatformNotificationMessage from "src/business/Utils/Notifications/generateMultiplePlatformNotificationMessage";
import getResourceWithParameters from "src/business/Utils/Resources/GetResourceWithParameters";
import { EProfile } from "src/business/Enums/Models/EProfile";
import IOrderNotificationDTO from "src/business/DTOs/IOrderNotification";
import generateMultiplePlatformOrderNotificationMessage from "src/business/Utils/Notifications/generateMultiplePlatformOrderNotificationMessage";

@injectable()
export class NotificationService implements INotificationService {
  @inject(TOKENS.ISNSService)
  private snsService: ISNSService;

  @inject(TOKENS.IDeviceService)
  private deviceService: IDeviceService;

  @inject(TOKENS.IResourcesService)
  private resourcesService: IResourcesService;

  @inject(TOKENS.IMessageService)
  private messageService: IMessageService;

  @inject(TOKENS.ProfilesSingleton)
  private profilesSingleton: ProfilesSingleton;

  @inject(TOKENS.LoggerService)
  private loggerService: ILoggerService;

  async sendNotificationToUser(userId: string, notification: INotificationDTO) {
    const devices = await this.deviceService.getUserActiveDevicesByProfile(userId, notification.profile);
    if (devices.length > 0) {
      await this.saveNotificationMessage(
        devices.map((item) => item.userId),
        notification,
      );
      const requestMap = devices.map((device) => {
        const promise = new Promise((res, rej) => {
          const translatedNotification = this.resourcesService.translateNotificationResources(
            device.language,
            notification,
          );

          let message: string;
          if (device.type === EDeviceType.IOS) {
            message = generateAPNNotificationMessage(translatedNotification);
          } else {
            message = generateFCMNotificationMessage(translatedNotification);
          }

          this.snsService
            .publish({
              Message: message,
              TargetArn: device.endpointArn,
              MessageStructure: "json",
            })
            .then(() => {
              res("Message sent");
            })
            .catch((e) => {
              this.loggerService.logError(e);

              rej(e);
            });
        });

        return promise;
      });

      Promise.all(requestMap).catch(() => {
        console.log("Invalid SNS endpoint");
      });
    }
  }

  async sendNotificationToTopic(
    usersId: string[],
    notification: INotificationDTO,
    topicArn: string,
    mailingId?: string,
  ): Promise<void> {
    const promise = new Promise((res, rej) => {
      const translatedNotification = this.resourcesService.translateNotificationResources(
        ELanguageOptions.PT,
        notification,
      );

      const message = generateMultiplePlatformNotificationMessage(mailingId ? notification : translatedNotification);
      this.snsService
        .publish({
          Message: message,
          TopicArn: topicArn,
          MessageStructure: "json",
        })
        .then(async () => {
          res("Message sent");
          await this.saveNotificationMessage(usersId, notification, mailingId);
        })
        .catch((error) => {
          console.log("notification error", error);

          this.loggerService.logError(error);

          rej(error);
        });
    });

    await Promise.resolve(promise).catch(() => {
      console.log("Invalid SNS endpointe");
    });
  }

  async sendNotificationToOrderTopic(
    usersId: { userId: string; userProfile: EProfile }[],
    notification: IOrderNotificationDTO,
    topicArn: string,
  ): Promise<any> {
    const promise = new Promise((res, rej) => {
      const translatedNotification = this.resourcesService.translateOrderNotificationResources(
        ELanguageOptions.PT,
        notification,
      );

      const message = generateMultiplePlatformOrderNotificationMessage(translatedNotification);
      this.snsService
        .publish({
          Message: message,
          TopicArn: topicArn,
          MessageStructure: "json",
        })
        .then(async (a) => {
          res(a);
          await this.saveNotificationOrderMessage(usersId, notification);
        })
        .catch((error) => {
          console.log("notification error", error);

          this.loggerService.logError(error);

          rej(error);
        });
    });

    return promise;
  }

  private async saveNotificationMessage(usersId: string[], notification: INotificationDTO, mailingId?: string) {
    let profileId;
    if (notification.profile) {
      profileId = this.profilesSingleton[notification.profile].id;
    }

    const title = notification.params?.title
      ? getResourceWithParameters(notification.title, notification.params.title)
      : notification.title;

    const body = notification.params?.body
      ? getResourceWithParameters(notification.body, notification.params.body)
      : notification.body;

    await this.messageService.create({
      title,
      type: notification.type,
      userMessage: usersId.map((userId) => ({
        userId,
        profileId,
      })),
      sendingType: [EMessageSendingType.notification],
      content: [{ body, sendingType: EMessageSendingType.notification }],
      directMailId: mailingId,
    });
  }

  private async saveNotificationOrderMessage(
    usersId: { userId: string; userProfile: EProfile }[],
    notification: IOrderNotificationDTO,
  ) {
    const title = notification.params?.title
      ? getResourceWithParameters(notification.title, notification.params.title)
      : notification.title;

    const body = notification.params?.body
      ? getResourceWithParameters(notification.body, notification.params.body)
      : notification.body;

    await this.messageService.create({
      title,
      type: notification.type,
      userMessage: usersId.map((user) => ({
        userId: user.userId,
        profileId: this.profilesSingleton[user.userProfile].id,
      })),
      sendingType: [EMessageSendingType.notification],
      content: [{ body, sendingType: EMessageSendingType.notification }],
    });
  }
}
