import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IChatMessage } from "src/business/Interfaces/Prisma/IChatMessage";
import { IChatMessagesRepository } from "src/business/Interfaces/Repository/IChatMessages";
import { IChatMessagesService } from "src/business/Interfaces/Service/IChatMessages";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class ChatMessagesService extends BaseService<IChatMessage> implements IChatMessagesService {
  private chatMessagesRepository: IChatMessagesRepository;

  constructor(
    @inject(TOKENS.IChatMessagesRepository)
    chatMessagesRepository: IChatMessagesRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(chatMessagesRepository, notificationManager);
    this.chatMessagesRepository = chatMessagesRepository;
  }

  async create(chatMessages: IChatMessage): Promise<IChatMessage> {
    const chatMessageCreated = await this.chatMessagesRepository.create(chatMessages);

    return chatMessageCreated;
  }
}
