import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IFinancialConsolidation } from "src/business/Interfaces/Prisma/IFinancialConsolidation";
import { IFinancialConsolidationRepository } from "src/business/Interfaces/Repository/IFinancialConsolidation";
import IFinancialConsolidationService from "src/business/Interfaces/Service/IFinancialConsolidationService";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class FinancialConsolidationService
  extends BaseService<IFinancialConsolidation>
  implements IFinancialConsolidationService
{
  private financialConsolidationRepository: IFinancialConsolidationRepository;

  constructor(
    @inject(TOKENS.IFinancialConsolidationRepository)
    financialConsolidationRepository: IFinancialConsolidationRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(financialConsolidationRepository, notificationManager);
    this.financialConsolidationRepository = financialConsolidationRepository;
  }

  async createFinancialConsolidation(item: IFinancialConsolidation): Promise<boolean> {
    const result = await this.financialConsolidationRepository.create(item);
    return !!result;
  }
}
