import { EMessageType } from "@prisma/client";
import { inject, injectable } from "inversify";
import pt from "src/business/Assets/Language/pt.json";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IChatMessageDTO } from "src/business/DTOs/Chat/IChatMessage";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IChat } from "src/business/Interfaces/Prisma/IChat";
import { IChatRepository } from "src/business/Interfaces/Repository/IChat";
import { IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IChatService } from "src/business/Interfaces/Service/IChat";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";

import { BaseService } from "src/business/Services/Base";

@injectable()
export class ChatService extends BaseService<IChat> implements IChatService {
  constructor(
    @inject(TOKENS.IChatRepository)
    private chatRepository: IChatRepository,
    @inject(TOKENS.IOrderRepository)
    private orderRepository: IOrderRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
  ) {
    super(chatRepository, notificationManager);
    this.chatRepository = chatRepository;
  }

  async create(chat: IChat): Promise<boolean> {
    try {
      await this.chatRepository.create(chat);

      return true;
    } catch (error) {
      console.log(error);
      this.notificationManager.add("chat.errors", "create_chat_invalid_params");
      return false;
    }
  }

  async getByOrderId(orderId: string): Promise<IChat | null> {
    const result = await this.chatRepository.getByOrderId(orderId);

    if (result) return result;

    this.notificationManager.add("chat.errors", "chat_not_found");
    return null;
  }

  async getChatMessages(orderId: string): Promise<IChatMessageDTO[]> {
    const result = await this.chatRepository.getChatMessages(orderId);

    return result;
  }

  async sendChatNotifications(orderId: string, socketIds: string[]): Promise<void> {
    const result = await this.orderRepository.getUsers(orderId);
    if (!result) return;

    let orderCode: number | undefined;

    const code = await this.orderRepository.getCode(orderId);

    if (code) {
      orderCode = code;
    }

    const {
      store: { storeUsers },
      deliverymanId,
      userId,
    } = result;

    if (userId && !socketIds.find((id) => id === userId)) {
      this.notifyDisconnectedChatUser(userId, orderId, EProfile.client, orderCode);
    }

    if (deliverymanId && !socketIds.find((id) => id === deliverymanId)) {
      this.notifyDisconnectedChatUser(deliverymanId, orderId, EProfile.deliveryman, orderCode);
    }

    storeUsers.forEach((storeUser) => {
      if (storeUser && !socketIds.find((idSocket) => idSocket === storeUser.userId)) {
        this.notifyDisconnectedChatUser(storeUser.userId, orderId, EProfile.shopkeeper, orderCode);
      }
    });
  }

  private notifyDisconnectedChatUser(userId: string, orderId: string, profile: EProfile, title?: number) {
    this.notificationService.sendNotificationToUser(userId, {
      title: pt.notification.chat_title,
      body: pt.notification.chat_body,
      params: {
        title,
        order: {
          messageType: EMessageType.chat,
          orderId,
        },
      },
      type: EMessageType.chat,
      profile,
    });
  }
}
