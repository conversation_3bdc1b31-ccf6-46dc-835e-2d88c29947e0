import { EPayoutOwner, EPayoutStatus } from "@prisma/client";
import { Xlsx } from "exceljs";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAggregatePayoutsDTO } from "src/business/DTOs/Bordero/IAggregatePayouts";
import { IOrder as IOrderSort } from "src/business/DTOs/Order";
import { IPayoutExportReportDTO } from "src/business/DTOs/Payout/IPayoutExportReport";
import { IPayoutListDTO } from "src/business/DTOs/Payout/IPayoutList";
import { IPayoutStatusDTO } from "src/business/DTOs/Payout/IPayoutStatus";
import { ESettingsKey } from "src/business/Enums/ESettingsKey";
import { IPayout } from "src/business/Interfaces/Prisma/IPayout";
import { ICooperativeRepository } from "src/business/Interfaces/Repository/ICooperative";
import { IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IPayoutRepository } from "src/business/Interfaces/Repository/IPayout";
import { ISettingsRepository } from "src/business/Interfaces/Repository/ISettings";
import { ITransactionRepository } from "src/business/Interfaces/Repository/ITransaction";
import { IPayoutService } from "src/business/Interfaces/Service/IPayout";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { exportPayoutsToExcel } from "src/business/Utils/ExportPayoutsToExcel";
import { PagedResult } from "../DTOs/PagedResult";
import { IPayoutDetailsDTO } from "../DTOs/Payout/IPayoutDetails";

@injectable()
export class PayoutService extends BaseService<IPayout> implements IPayoutService {
  constructor(
    @inject(TOKENS.IPayoutRepository)
    private payoutRepository: IPayoutRepository,
    @inject(TOKENS.IOrderRepository)
    private orderRepository: IOrderRepository,
    @inject(TOKENS.ISettingsRepository)
    private settingsRepository: ISettingsRepository,
    @inject(TOKENS.ITransactionRepository)
    private transactionRepository: ITransactionRepository,
    @inject(TOKENS.ICooperativeRepository)
    private cooperativeRepository: ICooperativeRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(payoutRepository, notificationManager);
  }

  async createPayout(orderId: string): Promise<boolean> {
    const order = await this.orderRepository.getById(orderId);
    const cooperativeId = await this.cooperativeRepository.findFirst({});

    if (!order) {
      return false;
    }

    const administrativeFeeSettings = await this.settingsRepository.getByName(ESettingsKey.administrativeFee);

    if (!administrativeFeeSettings) {
      return false;
    }

    // FIXME
    // const transactionStatus = await this.transactionRepository.getTransactionStatusByOrderId(orderId);
    // const payoutStatus = transactionStatus ? payoutStatusMap[transactionStatus] : EPayoutStatus.waiting;
    const payoutStatus = EPayoutStatus.available;

    const administrativeFee = parseFloat(administrativeFeeSettings.value);

    const administrativeFeeValueStore = parseFloat((order.price * (administrativeFee / 100)).toFixed(2));
    const transferValueStore = parseFloat((order.price - administrativeFeeValueStore).toFixed(2));
    const transferValueDeliveryman = parseFloat(order.shippingPrice.toFixed(2));

    const storePayout = {
      orderId,
      cooperativeId: cooperativeId?.id,
      administrativeFeePercent: administrativeFee,
      administrativeFeeValue: administrativeFeeValueStore,
      transferValue: transferValueStore,
      status: payoutStatus,
      payoutOwner: EPayoutOwner.store,
      statusDate: new Date(),
    } as IPayout;

    const deliverymanPayout = {
      orderId,
      cooperativeId: cooperativeId?.id,
      administrativeFeePercent: 0,
      administrativeFeeValue: 0,
      transferValue: transferValueDeliveryman,
      status: payoutStatus,
      payoutOwner: EPayoutOwner.deliveryman,
      statusDate: new Date(),
    } as IPayout;

    const cooperativePayout = {
      orderId,
      cooperativeId: cooperativeId?.id,
      administrativeFeePercent: 0,
      administrativeFeeValue: 0,
      transferValue: administrativeFeeValueStore,
      status: payoutStatus,
      payoutOwner: EPayoutOwner.cooperative,
      statusDate: new Date(),
    } as IPayout;

    Promise.all([
      this.payoutRepository.create(storePayout),
      this.payoutRepository.create(deliverymanPayout),
      this.payoutRepository.create(cooperativePayout),
    ]);

    return true;
  }

  async updatePayoutStatus(id: string, status: EPayoutStatus): Promise<boolean> {
    return this.payoutRepository.updateStatus(id, status);
  }

  async getPayoutStatusByOrderId(orderId: string): Promise<IPayoutStatusDTO | null> {
    return this.payoutRepository.getPayoutStatusByOrderId(orderId);
  }

  async getPagedPayout(
    currentPage: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
    statusFilter?: EPayoutStatus,
    ownerFilter?: EPayoutOwner,
    showOrderTotalizer: boolean = true,
    borderoNullFilter: boolean = false,
  ): Promise<PagedResult<IPayout>> {
    let where: object | undefined = {};

    filterValue = filterValue.toLowerCase();

    if (endDateFilter && startDateFilter) {
      where = {
        createdAt: {
          lte: endDateFilter,
          gte: startDateFilter,
        },
      };
    }

    const select = {
      id: true,
      orderId: true,
      administrativeFeePercent: true,
      administrativeFeeValue: true,
      createdAt: true,
      transferValue: true,
      statusDate: true,
      payoutOwner: true,
      status: true,
      cooperative: {
        select: {
          name: true,
          cnpj: true,
        },
      },
      order: {
        select: {
          price: true,
          shippingPrice: true,
          totalPrice: true,
          store: {
            select: {
              id: true,
              name: true,
              cnpj: true,
            },
          },
          deliveryman: {
            select: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  cpf: true,
                },
              },
            },
          },
        },
      },
    };

    if (borderoNullFilter) {
      where = {
        ...where,
        borderoId: null,
      };
    }

    if (statusFilter) {
      where = {
        ...where,
        status: {
          equals: statusFilter,
        },
      };
    }
    if (ownerFilter) {
      where = {
        ...where,
        payoutOwner: {
          equals: ownerFilter,
        },
      };
    }
    if (filterValue) {
      where = {
        ...where,
        OR: [
          {
            order: {
              store: {
                name: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            order: {
              store: {
                cnpj: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            order: {
              deliveryman: {
                user: {
                  firstName: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
              },
            },
          },
          {
            order: {
              deliveryman: {
                user: {
                  cpf: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
              },
            },
          },
        ],
      };
    }

    const columnFilter = "";
    const orderTotalizer = await this.orderRepository.getTotalOrderAmount({
      AND: [
        {
          OR: [
            {
              store: {
                name: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
            {
              store: {
                cnpj: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
            {
              deliveryman: {
                user: {
                  firstName: {
                    equals: filterValue,
                  },
                },
              },
            },
            {
              deliveryman: {
                user: {
                  cpf: {
                    equals: filterValue,
                  },
                },
              },
            },
          ],
        },
        {
          payout: {
            some: {
              status: statusFilter || {},
              payoutOwner: ownerFilter || {},
            },
          },
        },
      ],
    });

    if (orderBy) {
      switch (orderBy) {
        case "storeName":
          orderBy = "order.store.name";
          break;
        case "deliverymanFirstName":
          orderBy = "order.deliveryman.user.firstName";
          break;
        case "price":
          orderBy = "order.price";
          break;
        case "shippingPrice":
          orderBy = "order.shippingPrice";
          break;
        case "totalPrice":
          orderBy = "order.totalPrice";
          break;
        default:
          break;
      }
    }

    const { data, whereCondition } = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      undefined,
      where,
      select,
    );

    const payoutTotalizer = await this.payoutRepository.getTotalPayoutAmount(whereCondition);

    return {
      result: data.result,
      totalCount: data.totalCount,
      totalPages: data.totalPages,
      totalizer: showOrderTotalizer ? { ...orderTotalizer, ...payoutTotalizer } : { ...payoutTotalizer },
    };
  }

  async getPayoutsPaginatedToCreateBordero(
    currentPage: string,
    pageSize: string,
    borderoId: string,
    ownerId: string,
    ownerFilter: EPayoutOwner,
    startDateFilter?: Date | undefined,
    endDateFilter?: Date | undefined,
    orderBy?: string | undefined,
    sortDirection?: IOrderSort | undefined,
    filterValue?: string | undefined,
  ): Promise<PagedResult<IPayoutListDTO>> {
    const { currentPageInt, pageSizeInt } = this.getPageSizeAndCurrentPage(pageSize, currentPage);

    const data = await this.payoutRepository.getPayoutsPaginatedToCreateBordero(
      currentPageInt,
      pageSizeInt,
      borderoId,
      ownerId,
      ownerFilter,
      startDateFilter,
      endDateFilter,
      orderBy,
      sortDirection,
      filterValue,
    );
    return data;
  }

  async getPayoutDetailsById(id: string): Promise<IPayoutDetailsDTO | null> {
    const result = await this.payoutRepository.getPayoutDetailsById(id);
    return result;
  }

  async getPayoutsValueTotalizer(
    whereCondition: object | undefined,
    owner: EPayoutOwner,
    status: EPayoutStatus,
  ): Promise<number> {
    const result = await this.payoutRepository.getPayoutsValueTotalizer(whereCondition, owner, status);

    return result;
  }

  async getPagedPayoutByBordero(
    currentPage: string,
    pageSize: string,
    borderoId: string,
    orderBy?: string | undefined,
    sortDirection?: IOrderSort | undefined,
    filterValue?: string,
  ): Promise<PagedResult<IPayout>> {
    let where: object | undefined = { borderoId };

    filterValue = filterValue?.toLowerCase();

    if (filterValue) {
      where = {
        ...where,
        OR: [
          {
            order: {
              store: {
                name: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            order: {
              store: {
                cnpj: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            order: {
              deliveryman: {
                user: {
                  firstName: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
              },
            },
          },
          {
            order: {
              deliveryman: {
                user: {
                  cpf: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
              },
            },
          },
        ],
      };
    }

    const columnFilter = "";
    const orderTotalizer = await this.orderRepository.getTotalOrderAmount({
      AND: [
        {
          OR: [
            {
              store: {
                name: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
            {
              store: {
                cnpj: {
                  contains: filterValue,
                  mode: "insensitive",
                },
              },
            },
            {
              deliveryman: {
                user: {
                  firstName: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
              },
            },
            {
              deliveryman: {
                user: {
                  cpf: {
                    contains: filterValue,
                    mode: "insensitive",
                  },
                },
              },
            },
          ],
        },
      ],
    });

    const select = {
      id: true,
      orderId: true,
      administrativeFeePercent: true,
      administrativeFeeValue: true,
      createdAt: true,
      transferValue: true,
      statusDate: true,
      status: true,
      order: {
        select: {
          price: true,
          shippingPrice: true,
          totalPrice: true,
          store: {
            select: {
              name: true,
              cnpj: true,
            },
          },
          deliveryman: {
            select: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  cpf: true,
                },
              },
            },
          },
        },
      },
    };

    const { data, whereCondition } = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      undefined,
      where,
      select,
    );

    const payoutTotalizer = await this.payoutRepository.getTotalPayoutAmount(whereCondition);

    return {
      result: data.result,
      totalCount: data.totalCount,
      totalPages: data.totalPages,
      totalizer: { ...orderTotalizer, ...payoutTotalizer },
    };
  }

  // TODO - test
  async updatePayoutsBordero(borderoId: string, payouts: string[]): Promise<boolean> {
    const countPayouts = await this.payoutRepository.getCountPayoutWithoutBordero(payouts);

    if (countPayouts < payouts.length) {
      this.notificationManager.add("bordero.errors", "payout_associated_failure");
      return false;
    }

    const response = await this.payoutRepository.updateMany({
      data: { borderoId },
      where: { id: { in: payouts } },
    });

    if (response < payouts.length) {
      this.notificationManager.add("bordero.errors", "associate_payout_failed");
      return false;
    }

    return true;
  }

  async getPayoutsTotalizerByIds(payoutIds?: string[]): Promise<IAggregatePayoutsDTO> {
    if (payoutIds && payoutIds.length > 0) {
      const totalizer = await this.payoutRepository.getPayoutsTotalizerByIds(payoutIds);
      return totalizer;
    }
    return { countOrders: 0, sumPrice: 0, sumShipping: 0, sumAdm: 0, sumTransfer: 0 };
  }

  async getPayoutsWithoutBorderoByDeliveryman(deliverymanId: string): Promise<number> {
    const payouts = await this.payoutRepository.getPayoutsWithoutBorderoByDeliveryman(deliverymanId);
    return payouts;
  }

  async getPayoutsWithoutBorderoByStore(storeId: string): Promise<number> {
    const payouts = await this.payoutRepository.getPayoutsWithoutBorderoByStore(storeId);
    return payouts;
  }

  async getPayoutsWithoutBorderoByCooperative(cooperativeId: string): Promise<number> {
    const payouts = await this.payoutRepository.getPayoutsWithoutBorderoByCooperative(cooperativeId);
    return payouts;
  }

  async updatePayoutsClearBordero(borderoId: string): Promise<number> {
    const response = await this.payoutRepository.updateMany({ data: { borderoId: null }, where: { borderoId } });
    return response;
  }

  async getCountPayoutsByBordero(borderoId: string): Promise<number> {
    const count = await this.payoutRepository.getCountPayoutsByBordero(borderoId);
    return count;
  }

  async getAllPayoutsByBordero(borderoId: string): Promise<IPayoutListDTO[]> {
    const data = await this.payoutRepository.getAllPayoutsByBordero(borderoId);
    return data;
  }

  async updatePayoutStatusByBordero(borderoId: string, status: EPayoutStatus): Promise<boolean> {
    const result = await this.payoutRepository.updateMany({ where: { borderoId }, data: { status } });
    return !!result;
  }

  async generatePayoutReport(
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
    statusFilter?: EPayoutStatus,
    ownerFilter?: EPayoutOwner,
  ): Promise<Xlsx> {
    const data = await this.payoutRepository.getPayoutReportData(
      startDateFilter,
      endDateFilter,
      filterValue,
      orderBy,
      sortDirection,
      statusFilter,
      ownerFilter,
    );

    const dataWithOwnerInformationFiltered = data.map((payout) => {
      if (payout.payoutOwner === EPayoutOwner.store) {
        if (payout.order?.deliveryman) {
          payout.order.deliveryman = null;
        }
        if (payout.cooperative) {
          payout.cooperative = null;
        }
      }

      if (payout.payoutOwner === EPayoutOwner.deliveryman) {
        if (payout.order?.store) {
          payout.order.store = null;
        }
        if (payout.cooperative) {
          payout.cooperative = null;
        }
      }

      if (payout.payoutOwner === EPayoutOwner.cooperative) {
        if (payout.order?.store) {
          payout.order.store = null;
        }
        if (payout.order?.deliveryman) {
          payout.order.deliveryman = null;
        }
      }

      return payout;
    });

    const dataWithPaidDate = dataWithOwnerInformationFiltered.map<IPayoutExportReportDTO>((payout) => {
      const newPayout: IPayoutExportReportDTO = payout;

      if (payout.status !== EPayoutStatus.executed) {
        newPayout.statusDate = undefined;
      }

      return payout;
    });

    const xlsx = await exportPayoutsToExcel(dataWithPaidDate);

    return xlsx;
  }
}
