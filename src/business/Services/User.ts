import {
  AdminInitiateAuthCommandOutput,
  AuthenticationResultType,
  AuthFlowType,
} from "@aws-sdk/client-cognito-identity-provider";
import { EAddress, EProfileStatus } from "@prisma/client";
import { sub } from "date-fns";
import { inject, injectable } from "inversify";
import { decode } from "jsonwebtoken";
import { isEqual } from "lodash";
import { IDeleteUser } from "src/api/ViewModels/User/IDeleteUser";
import { IReactivateAccountViewModel } from "src/api/ViewModels/User/IReactivateAccount";
import { IVerifyEmailCodeViewModel } from "src/api/ViewModels/User/IVerifyEmailCodeViewModel";
import { IUserInfoViewModel } from "src/api/ViewModels/User/UserInfo.vm";
import pt from "src/business/Assets/Language/pt.json";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { UserMapper } from "src/business/Configs/Automapper/Profile/User";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IFilterMailingDTO } from "src/business/DTOs/FilterMailing/IFilterMailing";
import { InvitedUserAuthenticationResult } from "src/business/DTOs/InvitedUserAuthenticationResult";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IEmailVerificationStatusDTO } from "src/business/DTOs/User/IEmailVerificationStatus";
import { IUserCreateDefaultCognitoDTO } from "src/business/DTOs/User/IUserCreateDefaulCognito";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IUserRegisterDTO } from "src/business/DTOs/User/IUserRegister";
import { IUserWithProfilePictureDTO } from "src/business/DTOs/User/IUserWithProfilePicture";
import { UserAuthenticationResult } from "src/business/DTOs/UserAuthenticationResult";
import { ESettingsKey } from "src/business/Enums/ESettingsKey";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { ETypeEmail } from "src/business/Enums/Models/ETypeEmail";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";
import { EMessageType } from "src/business/Enums/Models/Message/EMessageType";
import { IUser } from "src/business/Interfaces/Prisma/IUser";
import { IDeviceRepository } from "src/business/Interfaces/Repository/IDevice";
import { IUserRepository } from "src/business/Interfaces/Repository/IUser";
import { ICognitoService, IdToken } from "src/business/Interfaces/Service/AWS/ICognito";
import { ISESService } from "src/business/Interfaces/Service/AWS/ISES";
import { IClientService } from "src/business/Interfaces/Service/IClient";
import { IDeliverymanService } from "src/business/Interfaces/Service/IDeliveryman";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { IMessageService } from "src/business/Interfaces/Service/IMessage";
import { IProfileService } from "src/business/Interfaces/Service/IProfile";
import { ISettingsService } from "src/business/Interfaces/Service/ISettings";
import { IShopkeeperService } from "src/business/Interfaces/Service/IShopkeeper";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import getUserRegisteredEmail from "src/business/Middlewares/Emails/Messages/UserRegistered";
import { BaseService } from "src/business/Services/Base";
import ProfilesSingleton from "src/business/Singletons/Profile";
import getPrismaEnumValue from "src/business/Utils/GetPrismaEnumValue";
import getResourceWithParameters from "src/business/Utils/Resources/GetResourceWithParameters";

@injectable()
export class UserService extends BaseService<IUser> implements IUserService {
  constructor(
    @inject(TOKENS.IUserRepository)
    private userRepository: IUserRepository,
    @inject(TOKENS.ICognitoService)
    private cognitoService: ICognitoService,
    @inject(TOKENS.IProfileService)
    private profileService: IProfileService,
    @inject(TOKENS.ProfilesSingleton)
    private profilesSingleton: ProfilesSingleton,
    @inject(TOKENS.IClientService)
    private clientService: IClientService,
    @inject(TOKENS.IDeliverymanService)
    private deliverymanService: IDeliverymanService,
    @inject(TOKENS.IShopkeeperService)
    private shopkeeperService: IShopkeeperService,
    @inject(TOKENS.IDeviceService)
    private deviceService: IDeviceService,
    @inject(TOKENS.ISESService)
    private sesService: ISESService,
    @inject(TOKENS.NotificationManager)
    protected notificationManager: INotificationManager,
    @inject(TOKENS.IDeviceRepository)
    private deviceRepository: IDeviceRepository,
    @inject(TOKENS.IMessageService)
    private messageService: IMessageService,
    @inject(TOKENS.LoggerService)
    private loggerService: ILoggerService,
    @inject(TOKENS.ISettingsService)
    private settingsService: ISettingsService,
  ) {
    super(userRepository, notificationManager);
  }

  async login({
    email,
    password,
    origin = "app",
  }: {
    email: string;
    password: string;
    origin?: "app" | "web";
  }): Promise<UserAuthenticationResult | InvitedUserAuthenticationResult | null> {
    const user = await this.userRepository.findFirst({
      where: { email },
      include: { userProfiles: true },
    });

    if (origin === "web") {
      let id: string | undefined;

      if (user) {
        const isManagerUser = this.checkUserProfile(user, this.profilesSingleton.manager?.id);
        if (!isManagerUser) return this.handleIncorrectCredentials();

        if (user.deleted) {
          this.notificationManager.add("auth.signin.errors", "user_notFound");
          return null;
        }

        id = user.id;
      }

      const authUserResult = await this.authUser(AuthFlowType.ADMIN_USER_PASSWORD_AUTH, {
        USERNAME: email,
        PASSWORD: password,
      });

      if (!authUserResult) return this.handleIncorrectCredentials();
      const authResponse = await this.buildAuthResponseWeb({
        authResult: authUserResult,
        userId: id,
      });

      return authResponse;
    }

    if (!user) {
      this.notificationManager.add("generic.errors", "user_notFound");
      return null;
    }

    const isClientUser = this.checkUserProfile(user, this.profilesSingleton.client?.id);
    if (!isClientUser && origin === "app") return this.handleIncorrectCredentials();

    if (user.deleted) {
      this.notificationManager.add("generic.errors", "user_notFound");
      return null;
    }

    if (user.disabled) {
      this.notificationManager.add("generic.errors", "userDisabledByAdmin");
      return null;
    }

    const authUserResult = await this.authUser(AuthFlowType.ADMIN_USER_PASSWORD_AUTH, {
      USERNAME: email,
      PASSWORD: password,
    });

    if (!authUserResult) return this.handleIncorrectCredentials();

    const authResponse = await this.buildAuthResponse({
      authResult: authUserResult,
      userId: user.id,
    });

    return authResponse;
  }

  async createDefaultUserCognito(data: IUserCreateDefaultCognitoDTO): Promise<string | null> {
    const cognitoUserExists = await this.cognitoService.checkIfUserExists(data.email);

    if (!cognitoUserExists) {
      const registerCognitoResult = await this.registerCognitoUser(data.email, data.password, data.email, data.phone);

      if (!registerCognitoResult) return null;

      const confirmRegistrationResult = await this.confirmUserSignUp(data.email);

      if (!confirmRegistrationResult) return null;
    }

    const authUserResult = await this.authUser(AuthFlowType.ADMIN_USER_PASSWORD_AUTH, {
      USERNAME: data.email,
      PASSWORD: data.password,
    });

    if (!authUserResult) return null;

    const authenticationResult = authUserResult?.AuthenticationResult;

    const decoded = decode(authenticationResult?.IdToken!) as IdToken;

    return decoded.sub;
  }

  async register(data: IUserRegisterDTO, origin: "web" | "app" = "app"): Promise<UserAuthenticationResult | null> {
    const userIsValid = await this.checkIfUserIsValidForCreation(data.email, data.cpf, data.phone, origin);

    if (!userIsValid) return null;

    let authenticationResult: AuthenticationResultType | undefined;

    if (origin === "web" && data.authChallengeData) {
      try {
        data.authChallengeData = Object.assign(data.authChallengeData, {
          ChallengeResponses: {
            USERNAME: data.email,
            NEW_PASSWORD: data.password,
          },
        });

        const response = await this.cognitoService.adminRespondToAuthChallenge({
          ...data.authChallengeData,
        });

        authenticationResult = response.AuthenticationResult;
      } catch (error) {
        console.log(error);

        this.handleInvitedUserCreationError(error);
      }
    }

    if (origin === "app") {
      const registerCognitoResult = await this.registerCognitoUser(data.email, data.password, data.email, data.phone);

      if (!registerCognitoResult) {
        this.notificationManager.add("users.create.errors", "user_register_cognito_failed");

        return null;
      }
    }

    const user = mapper.map(UserMapper.IUserRegisterDTOToIUser, data);

    // const errors = await validate(user);
    // if (errors.length > 0) {
    //   this.notificationManager.addFromTypeorm("user", errors);
    //   return {} as UserAuthenticationResult;
    // }

    if (user.userAddress && user.userAddress?.[0]?.address) {
      // const errors = await validate(user.userAddress[0].address);
      // if (errors.length > 0) {
      //   this.notificationManager.addFromTypeorm("adress", errors);
      // }
      const index = user.userAddress[0].address.type;

      user.userAddress[0].address.type = getPrismaEnumValue(index, EAddress);
    }

    if (origin === "app") {
      const confirmRegistrationResult = await this.confirmUserSignUp(data.email);

      if (!confirmRegistrationResult) {
        this.deleteCognitoUser(data.email);
        return null;
      }

      const authUserResult = await this.authUser(AuthFlowType.ADMIN_USER_PASSWORD_AUTH, {
        USERNAME: data.email,
        PASSWORD: data.password,
      });

      if (!authUserResult) {
        this.deleteCognitoUser(data.email);
        return null;
      }

      authenticationResult = authUserResult?.AuthenticationResult;
    }

    if (authenticationResult && authenticationResult.IdToken) {
      const decoded = decode(authenticationResult.IdToken) as IdToken;

      const profilesIds = await this.validateProfiles(user, origin);

      if (user.client) {
        user.client.status = EProfileStatus.approved;
      }

      if (user.deliveryman) {
        user.deliveryman.status = EProfileStatus.pendingDocuments;
      }

      if (user.shopkeeper) {
        user.shopkeeper.status = EProfileStatus.pendingDocuments;
      }

      if (!this.isValid()) {
        this.deleteCognitoUser(data.email);
        return null;
      }

      user.userProfiles = profilesIds.map((id) => ({ profileId: id }));

      user.cognitoId = decoded.sub;

      const { id: userId } = await this.userRepository.create(user);

      const response: UserAuthenticationResult = {
        cognitoAuthenticationResult: authenticationResult,
        userInfo: {
          id: userId,
          profiles: ["client"],
        },
      };

      // FIXME
      // if (userCreated && this.profilesDB.client?.id) {
      //   const emailBody = getUserRegisteredEmail(userCreated);
      //   await this.sesService.sendEmail(emailBody);
      //   await this.messageService.create({
      //     title: emailBody.subject,
      //     type: EMessageType.registration,
      //     usersId: [userCreated.id],
      //     content: [
      //       {
      //         body: emailBody.message,
      //         sendingType: EMessageSendingType.email,
      //       },
      //     ],
      //     profileId: this.profilesDB.client.id,
      //   });
      // }

      return response;
    }

    return null;
  }

  private async deleteCognitoUser(username: string) {
    try {
      await this.cognitoService.deleteUser({ Username: username });
    } catch (error) {
      console.log(error);
    }
  }

  async invite(email: string): Promise<boolean | null> {
    try {
      await this.cognitoService.adminCreateUser({
        Username: email,
        DesiredDeliveryMediums: ["EMAIL"],
        UserAttributes: [
          { Name: "email", Value: email },
          { Name: "email_verified", Value: "true" },
        ],
      });

      return true;
    } catch (error) {
      console.log(error);
      this.handleInviteError(error);
    }

    return null;
  }

  async getByCognitoId(id: string): Promise<IUser | null> {
    const result = await this.userRepository.findOne({
      where: { cognitoId: id },
    });

    if (result === null) {
      // TODO Handle notification
    }

    return result;
  }

  async getByCognitoIdGoogle(cognitoIdGoogle: string): Promise<IUser | null> {
    const user = await this.userRepository.getByCognitoIdGoogle(cognitoIdGoogle);

    return user;
  }

  async getWithProfile(id: string): Promise<IUser | null> {
    const result = await this.userRepository.findOne({
      where: { id },
      include: { userProfiles: { include: { profile: true } } },
    });

    if (result === null) {
      // TODO Handle notification
    }

    return result;
  }

  async getWithProfilePictureById(id: string): Promise<IUserWithProfilePictureDTO | null> {
    const result = await this.userRepository.getWithProfilePictureById(id);

    if (result === null) {
      // TODO Handle notification
    }

    return result;
  }

  async getByEmail(email: string): Promise<IUser | null> {
    const result = await this.userRepository.findFirst({ where: { email }, include: { shopkeeper: true } });

    if (result === null) {
      // TODO Handle notification
    }

    return result;
  }

  async updateUser(data: IUser): Promise<number | null> {
    const user = await this.userRepository.getById(data.id);
    if (!user) {
      this.notificationManager.add("users.create.errors", "user_alreadyExists");
      return null;
    }

    const userWithSamePhone = await this.userRepository.getByPhone(data.phone);
    if (userWithSamePhone && userWithSamePhone.id !== data.id) {
      this.notificationManager.add("users.create.errors", "userPhone_alreadyExists");
      return null;
    }

    if (this.isValid()) {
      try {
        if (user.email !== data.email) {
          await this.cognitoService.updateUser({
            Username: user.email,
            UserAttributes: [
              { Name: "email", Value: data.email },
              // FIXME Remove email_verified when email verification is implemented
              { Name: "email_verified", Value: "true" },
            ],
          });
        }

        if (user.phone !== data.phone) {
          await this.cognitoService.updateUser({
            Username: user.email,
            UserAttributes: [
              { Name: "phone_number", Value: data.phone },
              // FIXME Remove phone_number_verified when phone verification is implemented
              { Name: "phone_number_verified", Value: "true" },
            ],
          });
        }

        const updateResult = await this.userRepository.update(data.id, data);

        return updateResult;
      } catch (err) {
        switch (err) {
          case "AliasExistsException":
            this.notificationManager.add("generic.errors", "email.alreadyExists");
            break;
          case "InternalErrorException":
            this.notificationManager.add("generic.errors", "internal_error");
            break;
          case "InvalidParameterException":
            this.notificationManager.add("generic.errors", "invalid_parameter");
            break;
          case "NotAuthorizedException":
            this.notificationManager.add("generic.errors", "not_authorized");
            break;
          case "UserNotFoundException":
            this.notificationManager.add("generic.errors", "user_notFound");
            break;
          default:
            console.log(err);
        }

        this.loggerService.logError(err);
      }
    }

    return null;
  }

  async delete(id: string): Promise<boolean> {
    const localStoredUser = await this.userRepository.getById(id);

    if (!localStoredUser) {
      this.notificationManager.add("users.errors", "user_notFound");
    } else {
      localStoredUser.deleted = true;
      localStoredUser.deletedAt = new Date();
      localStoredUser.reactivationCode = this.generateSixDigitsCode();

      if (this.isValid()) {
        const result = await this.userRepository.update(id, localStoredUser);

        return result > 0;
      }
    }

    return false;
  }

  async getWithAddress(id: string): Promise<IUser | null> {
    try {
      const response = await this.userRepository.getById(id, {
        userAddress: { include: { address: true } },
      });

      return response;
    } catch (error) {
      console.log(error);
      this.notificationManager.add("generic.errors", "internal_error");

      this.loggerService.logError(error);

      return null;
    }
  }

  async relateUserAddress(userId: string, addressId: string): Promise<void> {
    await this.userRepository.relateUserAddress(userId, addressId);
  }

  async relateUserFavoriteStore(userId: string, storeId: string): Promise<void> {
    await this.userRepository.relateUserFavoriteStore(userId, storeId);
  }

  async relateUserFavoriteProduct(userId: string, productId: string): Promise<void> {
    this.userRepository.relateUserFavoriteProduct(userId, productId);
  }

  async deleteUserAddress(addressId: string): Promise<number> {
    const result = await this.userRepository.deleteUserAddress(addressId);

    return result;
  }

  async deleteFavoriteStore(userId: string, storeId: string): Promise<number> {
    const result = await this.userRepository.deleteFavoriteStore(userId, storeId);

    return result;
  }

  async deleteFavoriteProduct(userId: string, productId: string): Promise<number> {
    const result = await this.userRepository.deleteFavoriteProduct(userId, productId);

    return result;
  }

  async resetPassword(email: string): Promise<boolean | null> {
    try {
      await this.cognitoService.resetPassword({
        Username: email,
      });

      return true;
    } catch (err) {
      switch (err.code) {
        case "CodeDeliveryFailureException":
          this.notificationManager.add("generic.errors", "code_delivery_fail");
          break;
        case "ForbiddenException":
          this.notificationManager.add("generic.errors", "forbidden_exception");
          break;
        case "InternalErrorException":
          this.notificationManager.add("generic.errors", "internal_error");
          break;
        case "InvalidParameterException":
          this.notificationManager.add("generic.errors", "invalid_parameter");
          break;
        case "UserNotFoundException":
          this.notificationManager.add("generic.errors", "user_notFound");
          break;
        case "NotAuthorizedException":
          this.notificationManager.add("generic.errors", "not_authorized");
          break;
        default:
          console.log(err);
      }

      this.loggerService.logError(err);
    }
    return null;
  }

  async confirmPassword(username: string, verificationCode: string, newPassword: string): Promise<boolean | null> {
    try {
      await this.cognitoService.confirmPassword({
        Username: username,
        Password: newPassword,
        ConfirmationCode: verificationCode,
      });

      return true;
    } catch (err) {
      this.handleConfirmPasswordError(err);
      throw new Error(err);
    }
  }

  private handleConfirmPasswordError(error: any) {
    switch (error.code) {
      case "CodeMismatchException":
        this.notificationManager.add("generic.errors", "code_mismatch");
        break;
      case "ExpiredCodeException":
        this.notificationManager.add("generic.errors", "expired_code");
        break;
      case "ForbiddenException":
        this.notificationManager.add("generic.errors", "forbidden_exception");
        break;
      case "InternalErrorException":
        this.notificationManager.add("generic.errors", "internal_error");
        break;
      case "InvalidParameterException":
        this.notificationManager.add("generic.errors", "invalid_parameter");
        break;
      case "InvalidPasswordException":
        this.notificationManager.add("generic.errors", "password_invalid");
        break;
      case "UserNotFoundException":
        this.notificationManager.add("generic.errors", "user_notFound");
        break;
      case "NotAuthorizedException":
        this.notificationManager.add("generic.errors", "not_authorized");
        break;
      default:
        console.log(error);
    }

    this.loggerService.logError(error);
  }

  async updatePassword(accessToken: string, previousPassword: string, proposedPassword: string) {
    try {
      await this.cognitoService.updatePassword({
        AccessToken: accessToken,
        PreviousPassword: previousPassword,
        ProposedPassword: proposedPassword,
      });

      return true;
    } catch (error) {
      switch (error.code) {
        default:
          console.log(error);
      }
      console.log(error);

      this.loggerService.logError(error);
      this.notificationManager.add("settings.changePassword.errors", "wrong_old_password");
      return null;
    }
  }

  async getUserProfilesData(userId: string) {
    const data = await this.userRepository.getWithProfileData(userId);

    return data;
  }

  private handleIncorrectCredentials() {
    this.notificationManager.add("auth.signin.errors", "incorrect_credentials");

    return null;
  }

  private async registerCognitoUser(username: string, password: string, email: string, phone: string) {
    try {
      const response = await this.cognitoService.createUser({
        Username: username,
        Password: password,
        UserAttributes: [
          { Name: "email", Value: email },
          { Name: "phone_number", Value: `+55${phone}` },
        ],
      });

      return response;
    } catch (error) {
      switch (error.code) {
        // [ ] Check for generic errors
        case "InvalidPasswordException":
          this.notificationManager.add("generic.errors", "password_invalid");
          break;
        case "ForbiddenException":
          this.notificationManager.add("generic.errors", "forbidden_exception");
          break;
        case "InvalidParameterException":
          this.notificationManager.add("generic.errors", "invalid_parameter");
          break;
        case "NotAuthorizedException":
          this.notificationManager.add("generic.errors", "not_authorized");
          break;
        case "UsernameExistsException":
          this.notificationManager.add("generic.errors", "username_already_exists");
          break;
        default:
          console.log(error);
      }

      console.log(error);

      this.loggerService.logError(error);
    }

    return null;
  }

  private async confirmUserSignUp(userName: string) {
    try {
      const response = await this.cognitoService.confirmSignUp({
        Username: userName,
      });

      return response;
    } catch (error) {
      switch (error.code) {
        case "InvalidParameterException":
          this.notificationManager.add("generic.errors", "invalid_parameter");
          break;
        case "UserNotFoundException":
          this.notificationManager.add("generic.errors", "user_notFound");
          break;

        default:
          console.log(error);
      }
      console.log(error);

      this.loggerService.logError(error);
    }

    return null;
  }

  private async authUser(authFlow: AuthFlowType, authParameters: Record<string, string>) {
    try {
      const response = await this.cognitoService.initiateAuth({
        AuthFlow: authFlow,
        AuthParameters: authParameters,
      });

      return response;
    } catch (error) {
      switch (error.code) {
        case "InvalidParameterException":
          this.notificationManager.add("generic.errors", "invalid_parameter");
          break;
        case "NotAuthorizedException":
          this.notificationManager.add("generic.errors", "not_authorized");
          break;
        case "PasswordResetRequiredException":
          this.notificationManager.add("generic.errors", "password_reset_required");
          break;
        case "UserNotConfirmedException":
          this.notificationManager.add("generic.errors", "user_not_confirmed");
          break;
        case "UserNotFoundException":
          this.notificationManager.add("generic.errors", "user_notFound");
          break;

        default:
          console.log(error);
      }

      console.log(error);

      this.loggerService.logError(error);
    }

    return null;
  }

  private async validateProfiles(user: IUser, origin: "web" | "app") {
    const userProfileIds: string[] = [];

    if (user.client) {
      const clientId = this.profilesSingleton.client?.id;
      if (clientId) userProfileIds.push(clientId);
      await this.clientService.validate(user.client);
    }

    if (user.deliveryman) {
      const deliverymanId = this.profilesSingleton.deliveryman?.id;
      if (deliverymanId) userProfileIds.push(deliverymanId);
      await this.deliverymanService.validate(user.deliveryman);
    }

    if (user.shopkeeper) {
      const shopkeeperId = this.profilesSingleton.shopkeeper?.id;
      if (shopkeeperId) userProfileIds.push(shopkeeperId);
      await this.shopkeeperService.validate(user.shopkeeper);
    }

    if (origin === "web") {
      const managerId = this.profilesSingleton.manager?.id;
      if (managerId) userProfileIds.push(managerId);
    }

    return userProfileIds;
  }

  private async checkIfUserIsValidForCreation(email: string, cpf: string, phone?: string, origin?: "web" | "app") {
    const cognitoUserExists = await this.cognitoService.checkIfUserExists(email);

    if (cognitoUserExists && origin === "app") {
      this.notificationManager.add("users.create.errors", "user_alreadyExists");
      return false;
    }

    const userExistsDatabase = await this.userRepository.find({
      where: { OR: [{ email }, { cpf }] },
    });

    if (userExistsDatabase.length > 0) {
      this.notificationManager.add("users.create.errors", "user_alreadyExists");
      return false;
    }

    if (phone) {
      const phoneAssociated = await this.userRepository.getByPhone(phone);

      if (phoneAssociated) {
        this.notificationManager.add("users.create.errors", "userPhone_alreadyExists");
        return false;
      }
    }

    return true;
  }

  private async buildAuthResponse({
    authResult,
    userId,
  }: {
    authResult: AdminInitiateAuthCommandOutput;
    userId?: string;
  }) {
    const { AuthenticationResult, ChallengeName, ChallengeParameters, Session } = authResult;

    if (AuthenticationResult) {
      if (userId) {
        const profiles = await this.profileService.getApprovedProfiles(userId);

        const isRegisteredForNotifications = await this.deviceService.hasDeviceRegisteredByUserId(userId);

        const response: UserAuthenticationResult = {
          cognitoAuthenticationResult: AuthenticationResult,
          userInfo: {
            id: userId,
            profiles: profiles.map((profile) => profile.name),
          },
        };
        return response;
      }
    }

    const invitedUserResponse: InvitedUserAuthenticationResult = {
      ChallengeName,
      ChallengeParameters,
      Session,
    };

    return invitedUserResponse;
  }

  private async buildAuthResponseWeb({
    authResult,
    userId,
  }: {
    authResult: AdminInitiateAuthCommandOutput;
    userId?: string;
  }) {
    const { AuthenticationResult, ChallengeName, ChallengeParameters, Session } = authResult;

    if (AuthenticationResult) {
      if (userId) {
        const response: UserAuthenticationResult = {
          cognitoAuthenticationResult: AuthenticationResult,
          userInfo: {
            id: userId,
          },
        };
        return response;
      }
    }

    const invitedUserResponse: InvitedUserAuthenticationResult = {
      ChallengeName,
      ChallengeParameters,
      Session,
    };

    return invitedUserResponse;
  }

  private handleInvitedUserCreationError(error: any) {
    // eslint-disable-next-line no-underscore-dangle
    switch (error.__type) {
      case "NotAuthorizedException":
        this.notificationManager.add("generic.errors", "user_register_not_authorized");
        break;
      default:
        this.notificationManager.add("generic.errors", "user_register_cognito_failed");
    }

    this.loggerService.logError(error);
  }

  private handleInviteError(error: any) {
    // eslint-disable-next-line no-underscore-dangle
    switch (error.__type) {
      case "UsernameExistsException":
        this.notificationManager.add("generic.errors", "user_invite_username_exists");
        break;
      default:
        this.notificationManager.add("generic.errors", "user_invite_cognito_failed");
    }

    this.loggerService.logError(error);
  }

  async loadDefaultUser(): Promise<void> {
    const user = await this.getByEmail("<EMAIL>");
    const { manager } = this.profilesSingleton;

    if (!user) {
      const userData = {
        // cognitoId: "dd585fa9-7258-46df-a69d-61848031d11b",
        firstName: "admin",
        lastName: "admin",
        cpf: "45751699009",
        email: "<EMAIL>",
        phone: "55555555555",
        userProfiles: [
          {
            profileId: manager.id,
          },
        ],
      } as IUser;

      const dataCognito: IUserCreateDefaultCognitoDTO = {
        email: userData.email,
        password: "@Aa12345",
        phone: userData.phone,
      };

      const cognitoId = await this.createDefaultUserCognito(dataCognito);

      if (cognitoId) {
        userData.cognitoId = cognitoId;
        await this.userRepository.create(userData);
        console.log("Default user created successfully");
      }
    }
  }

  async getQuantityNotification(id: string, profile: EProfile): Promise<number> {
    const profileId = this.profilesSingleton[profile].id;

    const result = await this.userRepository.getNotificationQuantity(id, profileId);
    return result;
  }

  async createUserBySocialLogin(data: IUser): Promise<IUserInfoViewModel | null> {
    const userExistsDatabase = await this.userRepository.getByEmail(data.email);

    if (userExistsDatabase) {
      this.notificationManager.add("users.create.errors", "user_alreadyExists");
      return null;
    }

    // const errors = await validate(userDB);
    // if (errors.length > 0) {
    //   this.notificationManager.addFromTypeorm("user", errors);
    //   return null;
    // }

    data.client = await this.clientService.validate({
      evaluation: "",
      status: EProfileStatus.approved,
    });

    const profileId = this.profilesSingleton.client.id;

    data.userProfiles = [{ profileId }];

    // TODO Test creation
    const userCreated = await this.userRepository.create(data);

    // TODO Test without await
    this.notifySocialLoginUser(userCreated.id);

    this.sendConfirmedRegistrationEmail(userCreated);

    return {
      id: userCreated.id,
      profiles: [{ name: "client" }],
    };
  }

  async notifySocialLoginUser(userId: string): Promise<void> {
    await this.messageService.create({
      title: pt.notification.complete_registration_title,
      type: EMessageType.registration,
      sendingType: [EMessageSendingType.notification],
      content: [
        {
          body: pt.notification.complete_registration_body,
          sendingType: EMessageSendingType.notification,
        },
      ],
      userMessage: [{ userId, profileId: this.profilesSingleton.client.id }],
    });
  }

  private async sendConfirmedRegistrationEmail(user: IUser) {
    if (user && this.profilesSingleton.client.id) {
      const emailBody = getUserRegisteredEmail(user);

      await this.sesService.sendEmail(emailBody);

      // TODO Test
      await this.messageService.create({
        title: emailBody.subject,
        type: EMessageType.registration,
        sendingType: [EMessageSendingType.email],
        content: [{ body: emailBody.message, sendingType: EMessageSendingType.email }],
        userMessage: [{ userId: user.id, profileId: this.profilesSingleton.client.id }],
      });
    }
  }

  async resendEmailVerification(token: string): Promise<void> {
    try {
      await this.cognitoService.resendEmailConfirmation({
        AccessToken: token,
        AttributeName: "email",
      });
    } catch (error) {
      console.log(error);
      this.notificationManager.add("generic.errors", "internal_error");
    }
  }

  async verifyEmailCode(verifyEmailProps: IVerifyEmailCodeViewModel): Promise<void> {
    try {
      await this.cognitoService.verifyUserEmail({
        AccessToken: verifyEmailProps.AccessToken,
        Code: verifyEmailProps.Code,
        AttributeName: "email",
      });
    } catch (error) {
      console.log(error);
      this.notificationManager.add("generic.errors", "internal_error");
    }
  }

  async emailStatus(token: string): Promise<IEmailVerificationStatusDTO> {
    const response = await this.cognitoService
      .getEmailStatus({
        AccessToken: token,
      })
      .then((data) => data)
      .catch((err) => console.log(err));

    const emailVerified = response?.UserAttributes?.find((value) => value.Name === "email_verified");

    if (emailVerified?.Value === "true") {
      return { emailVerified: true };
    }

    return { emailVerified: false };
  }

  private checkUserProfile(user: IUser, profileId?: string): boolean {
    const result = user.userProfiles ? user.userProfiles.find((value) => value.profileId === profileId) : undefined;
    if (result) return true;
    return false;
  }

  async getAllPaged(
    currentPage: string,
    pageSize: string,
    filterName?: string,
    filterProfile?: string,
    filterStatus?: string,
    sortDirection?: IOrder,
  ): Promise<PagedResult<IUser>> {
    const users = await this.userRepository.getAllPaged(
      Number(currentPage),
      Number(pageSize),
      filterName,
      filterProfile,
      filterStatus,
      sortDirection,
    );

    return users;
  }

  async updateByUserId(userId: string, data: IUser): Promise<boolean | null> {
    const result = await this.userRepository.updateByUserId(userId, data);

    return result;
  }

  async deleteByUserId(userId: string): Promise<boolean | null> {
    const result = await this.userRepository.deleteByUserId(userId);

    return result;
  }

  async disableUser(data: IDeleteUser): Promise<boolean | null> {
    const result = await this.userRepository.disableUser(data);

    return result;
  }

  async getByMailingFilter(filter: IFilterMailingDTO): Promise<IUserMailingDTO[] | null> {
    const result = await this.userRepository.getByMailingFilter(filter);

    if (!result) return null;

    return result;
  }

  async getPermanentlyDeletedUser(): Promise<boolean | null> {
    const result = await this.userRepository.getPermanentlyDeletedUser();
    return result;
  }

  async createDefaultForDeletedUsers(): Promise<boolean | null> {
    const user = await this.getPermanentlyDeletedUser();
    const { manager, client, deliveryman, shopkeeper } = this.profilesSingleton;

    if (!user) {
      const userData = {
        firstName: "deleted",
        lastName: "user",
        cpf: "00000000000",
        email: "<EMAIL>",
        phone: "00000000000",
        userProfiles: [
          { profileId: manager.id },
          { profileId: client.id },
          { profileId: deliveryman.id },
          { profileId: shopkeeper.id },
        ],
        permanentlyDeleted: true,
        dateOfBirth: new Date("01/01/2000"),
      } as IUser;

      const result = await this.userRepository.create(userData);
      return result && Object.keys(result).length > 0 ? true : null;
    }
    return null;
  }

  async deleteUsersPastRecoverLimit(): Promise<number | null> {
    console.log("Starting delete user routine");
    const configInDb = await this.settingsService.getByName(ESettingsKey.timeToPermanentlyDeleteUser);
    const dateLimit = sub(new Date(), { days: Number(configInDb?.value || 1) });
    const usersToDelete = await this.userRepository.getUsersToDeletePermanently(dateLimit);

    if (usersToDelete) {
      const ids = usersToDelete.map((user) => user.id);
      const result = await this.userRepository.deleteUsersPastRecoverLimit(ids);

      if (result) {
        const cognitoIds = usersToDelete.reduce((list, current) => {
          if (current.cognitoId !== null) list.push(current.cognitoId);
          return list;
        }, [] as string[]);

        await Promise.all(
          cognitoIds.map(async (id) => {
            await this.cognitoService.deleteUser({ Username: id });
          }),
        );
      }

      console.log(result, "Users permanently deleted from database");
      return result;
    }

    console.log(0, "Users to be deleted from database");
    return null;
  }

  async getByTemporallyDeletedStatus(email: string): Promise<boolean | null> {
    const user = await this.userRepository.getByEmail(email);
    if (user) {
      const result = await this.userRepository.getByTemporallyDeletedStatus(user.id);
      return !!result;
    }
    return false;
  }

  async updateDeletedAndReactivateAccount({ email, cpf }: IReactivateAccountViewModel): Promise<boolean | null> {
    const user = await this.userRepository.getByEmailAndCpf({ email, cpf });
    if (!user) {
      this.notificationManager.add("generic.errors", "invalid_cpf");
      return false;
    }

    if (user) {
      const result = await this.userRepository.updateDeletedAndReactivateAccount(user.id);
      return !!result;
    }

    return null;
  }

  async resendEmailConfirmationCode({ email, cpf }: IReactivateAccountViewModel): Promise<boolean> {
    const user = await this.userRepository.getByEmailAndCpf({ email, cpf });

    if (!user) {
      this.notificationManager.add("generic.errors", "invalid_cpf");
      return false;
    }

    try {
      if (user?.reactivationCode) {
        const newCode = this.generateSixDigitsCode();
        await this.userRepository.update(user.id, { reactivationCode: newCode });
        const result = await this.sesService.sendEmail({
          message: getResourceWithParameters(pt.email.reactivate_account_body, newCode),
          sourceEmail: process.env.AWS_SES_EMAIL_SOURCE!,
          toAddresses: [`${user?.firstName} ${user?.lastName} <${email}>`],
          type: ETypeEmail.html,
          subject: pt.email.reactivate_account_title,
        });
        return !!result;
      }
      return false;
    } catch (error) {
      console.log(error);
      this.notificationManager.add("generic.errors", "internal_error");
      return false;
    }
  }

  async confirmSignUpCode(data: IReactivateAccountViewModel): Promise<boolean> {
    const user = await this.userRepository.getByEmailAndCpf({ email: data.email, cpf: data.cpf });
    if (!user) {
      this.notificationManager.add("generic.errors", "user_notFound");
      return false;
    }

    if (user && isEqual(user.reactivationCode, data.code)) {
      const result = await this.updateDeletedAndReactivateAccount({ email: data.email, cpf: data.cpf });
      return !!result;
    }

    return false;
  }

  generateSixDigitsCode() {
    const chars = "**********";
    const randomCodeArray = Array.from({ length: 6 }, () => chars[Math.floor(Math.random() * chars.length)]);

    const randomCode = randomCodeArray.join("");
    return randomCode;
  }

  async getEmailsByUsersIds(usersIds: string[]): Promise<string[]> {
    const response = await this.userRepository.getEmailsByUsersIds(usersIds);

    const emails = response.map((user) => user.email);

    return emails;
  }

  async getByCpf(cpf: string): Promise<IUser | null> {
    const result = await this.userRepository.findFirst({ where: { cpf } });

    return result;
  }

  async getByCpfWithDeliveryman(cpf: string): Promise<IUser | null> {
    const result = await this.userRepository.findFirst({ include: { deliveryman: true }, where: { cpf } });

    return result;
  }

  async getUserDetailsBackOffice(id: string): Promise<IUser | null> {
    const result = await this.userRepository.getUserDetailsBackOffice(id);

    return result;
  }
}
