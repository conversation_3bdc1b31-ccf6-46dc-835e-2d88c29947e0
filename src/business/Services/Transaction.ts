import {
  ECardFlag,
  ECardStatus,
  EMessageType,
  EPaymentMethod,
  ETransactionStatus,
  ETransactionType,
  TransactionCard,
} from "@prisma/client";
import { inject, injectable } from "inversify";
import { io } from "src/api/Server/Socket";
import { ICreateTransactionViewModel } from "src/api/ViewModels/Transaction/ICreate";
import pt from "src/business/Assets/Language/pt.json";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { TransactionMapper } from "src/business/Configs/Automapper/Profile/Transaction";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICreatePaymentResultDTO } from "src/business/DTOs/ICreatePaymentResult";
import { IListTransactionByOrderDTO } from "src/business/DTOs/Transactions/ListTransactionByOrder";
import { EPagSeguroType } from "src/business/Enums/EPagSeguroType";
import { ETransactionNotificationStatus } from "src/business/Enums/ETransactionNotificationStatus";
import { ECardMethod } from "src/business/Enums/Models/ECardMethod";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { ICard } from "src/business/Interfaces/Prisma/ICard";
import { ITransaction } from "src/business/Interfaces/Prisma/ITransaction";
import { IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IOrderStatusRepository } from "src/business/Interfaces/Repository/IOrderStatus";
import { ITransactionRepository } from "src/business/Interfaces/Repository/ITransaction";
import { ITransactionCardRepository } from "src/business/Interfaces/Repository/ITransactionCard";
import { IAddressService } from "src/business/Interfaces/Service/IAddress";
import { ICardService } from "src/business/Interfaces/Service/ICard";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IPayoutService } from "src/business/Interfaces/Service/IPayout";
import { IStoreService } from "src/business/Interfaces/Service/IStore";
import { ITransactionService } from "src/business/Interfaces/Service/ITransaction";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { IFinancialConsolidationResponse } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialConsolidation";
import { INotificationPagSeguro } from "src/business/Interfaces/Service/PagSeguro/INotification";
import { ChargeStatus, ISession } from "src/business/Interfaces/Service/PagSeguro/IObjects";
import { IPagSeguroService } from "src/business/Interfaces/Service/PagSeguro/IPagSeguro";
import { IResponsePayment, isResponseError } from "src/business/Interfaces/Service/PagSeguro/IResponsePayment";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import OrderStatusTypeSingleton from "src/business/Singletons/OrderStatusType";
import { decryptCardFromDB, decryptFromApp, encryptCardToSendApp, encryptToSendDB } from "src/business/Utils/crypto";
import { payoutStatusMap } from "src/business/Utils/PayoutStatusMap";
import { transactionStatusMap } from "src/business/Utils/TransactionStatusMap";
import { ETransactionEvents } from "../Enums/Socket/ETransactionEvents";

@injectable()
export class TransactionService extends BaseService<ITransaction> implements ITransactionService {
  constructor(
    @inject(TOKENS.ITransactionRepository)
    private transactionRepository: ITransactionRepository,
    @inject(TOKENS.IPagSeguroService)
    private pagSeguroService: IPagSeguroService,
    @inject(TOKENS.IUserService)
    private userService: IUserService,
    @inject(TOKENS.IOrderRepository)
    private orderRepository: IOrderRepository,
    @inject(TOKENS.ICardService)
    private cardService: ICardService,
    @inject(TOKENS.IAddressService)
    private addressService: IAddressService,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
    @inject(TOKENS.IOrderStatusRepository)
    private orderStatusRepository: IOrderStatusRepository,
    @inject(TOKENS.OrderStatusTypeSingleton)
    private orderStatusTypeSingleton: OrderStatusTypeSingleton,
    @inject(TOKENS.ITransactionCardRepository)
    private transactionCardRepository: ITransactionCardRepository,
    @inject(TOKENS.IStoreService)
    private storeService: IStoreService,
    @inject(TOKENS.IPayoutService)
    private payoutService: IPayoutService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(transactionRepository, notificationManager);
  }

  async getTransactionByUserId(userId: string): Promise<ITransaction[]> {
    const transactions = await this.transactionRepository.getTransactionByUserId(userId);

    return transactions;
  }

  async getTransactionByOrderId(orderId: string): Promise<IListTransactionByOrderDTO[]> {
    const transactions = await this.transactionRepository.getTransactionByOrderId(orderId);

    return transactions;
  }

  async createPayment(
    userId: string,
    orderId: string,
    transaction: ICreateTransactionViewModel,
  ): Promise<ICreatePaymentResultDTO> {
    const transactionMapped = mapper.map(TransactionMapper.ICreateTransactionToITransaction, transaction);
    transactionMapped.orderId = orderId;
    const transactionCreated = await this.transactionRepository.create(transactionMapped);

    const user = await this.userService.getById(userId);

    if (!user || !transactionCreated) {
      this.notificationManager.add("forms.card.toasts", "payment_failure");
      return { success: false };
    }

    if (!user && transactionCreated) {
      this.transactionRepository.update(transactionCreated.id, {
        status: ETransactionStatus.rejected,
        statusDetail: "User not found.",
      });
      return { success: false };
    }

    const order = await this.orderRepository.getOrderWithItems(orderId);

    if (!order || !order.address) {
      this.notificationManager.add("forms.card.toasts", "payment_failure");
      this.transactionRepository.update(transactionCreated.id, {
        status: ETransactionStatus.rejected,
        statusDetail: "Order not found.",
      });
      return { success: false };
    }

    const defaultAddress = await this.addressService.getDefaultAddress(userId);

    if (transaction.savedCard) {
      const card = await this.cardService.getById(transaction.savedCard.cardId);
      const securityCode = decryptFromApp(transaction.savedCard.securityCode);

      if (!card || !card.idCardPaymentPlatform || !securityCode) {
        this.notificationManager.add("forms.card.toasts", "payment_failure");
        return { success: false };
      }
      transaction.savedCard.idCardPaymentPlatform = card.idCardPaymentPlatform;
      transaction.savedCard.securityCode = securityCode;
    }

    const response = await this.pagSeguroService.paymentOrder(
      transaction,
      user,
      order,
      defaultAddress || order.address,
    );

    if (isResponseError(response) || this.checkChargesFailed(response)) {
      this.notificationManager.add("forms.card.toasts", "payment_failure");
      await this.transactionRepository.update(transactionCreated.id, {
        status: ETransactionStatus.rejected,
        statusDetail: JSON.stringify(response),
        traceId: "traceId" in response ? response.traceId : undefined,
      });

      await this.notificationService.sendNotificationToUser(user.id, {
        title: pt.notification.change_status_title,
        body: pt.notification.payment_failure,
        params: {
          title: order.code,
          order: {
            messageType: EMessageType.order_status,
            orderId: order.id,
          },
        },
        profile: EProfile.client,
        type: EMessageType.order_status,
      });

      return { success: false };
    }

    if (transaction.paymentMethod === EPaymentMethod.pix) {
      if (response.qr_codes && response.qr_codes.length > 0) {
        const pixKey = response.qr_codes[0].text;
        this.transactionRepository.update(transactionCreated.id, {
          status: ETransactionStatus.pending,
          transactionPlatformId: response.id,
          pixKey,
          statusDetail: JSON.stringify(response),
        });
        return { success: true, pixKey };
      }
      this.transactionRepository.update(transactionCreated.id, {
        status: ETransactionStatus.rejected,
        statusDetail: "Pix key generation failed.",
      });
      return { success: false };
    }

    response.charges.forEach(async (charge) => {
      let cardId: string | undefined;
      if (transaction.paymentMethod === EPaymentMethod.credit && !transaction.savedCard) {
        const newCard = await this.cardService.create({
          userId,
          idCardPaymentPlatform: charge.payment_method.card.id,
          method: this.pagSeguroService.pagSeguroMethodToCardMethod(charge.payment_method.type),
          cardHolder: encryptToSendDB(charge.payment_method.card.holder.name) || "",
          cardNumberLastDigits: charge.payment_method.card.last_digits,
          expiration:
            encryptToSendDB(
              String(charge.payment_method.card.exp_month) + String(charge.payment_method.card.exp_year).slice(2, 4),
            ) || "",
          flag: ECardFlag[charge.payment_method.card.brand],
          status: ECardStatus.active,
          saved: transaction.newCard ? transaction.newCard.saveCard : false,
        } as ICard);
        cardId = newCard?.id;
      } else if (transaction.savedCard) {
        cardId = transaction.savedCard.cardId;
      }
      // if (transaction.newCard) {
      //   const newCard = await this.cardService.create({
      //     userId,
      //     idCardPaymentPlatform: charge.payment_method.card.id,
      //     method: this.pagSeguroService.pagSeguroMethodToCardMethod(charge.payment_method.type),
      //     cardHolder: transaction.newCard.saveCard ? transaction.newCard.cardHolder : undefined,
      //     cardNumber: transaction.newCard.saveCard ? transaction.newCard.cardNumber : undefined,
      //     cardNumberLastDigits: transaction.cardNumberLastDigits,
      //     expiration: transaction.newCard.saveCard ? transaction.newCard.expiration : undefined,
      //     flag: ECardFlag[charge.payment_method.card.brand],
      //     status: ECardStatus.active,
      //     saved: transaction.newCard.saveCard,
      //   } as ICard);
      //   cardId = newCard?.id;
      // } else if (transaction.savedCard) {
      //   cardId = transaction.savedCard.cardId;
      // }

      if (transaction.paymentMethod !== EPaymentMethod.pix && cardId) {
        await this.transactionCardRepository.create({
          cardId,
          transactionId: transactionCreated.id,
        } as TransactionCard);
      }

      this.transactionRepository.update(transactionCreated.id, {
        status: this.pagSeguroService.pagSeguroStatusToInternalStatusTransaction(charge.status),
        transactionPlatformId: response.id,
        chargeId: charge.id,
        statusDetail: JSON.stringify(response),
      });
    });

    return { success: true };
  }

  async cancelPayment(transaction: ITransaction): Promise<boolean> {
    const orderLastStatus = await this.orderStatusRepository.getLastByOrderId(transaction.orderId);

    if (
      orderLastStatus?.orderStatusTypeId !== this.orderStatusTypeSingleton.payment_made.id ||
      (transaction.status !== ETransactionStatus.approved && transaction.status !== ETransactionStatus.authorized) ||
      !transaction.transactionPlatformId
    ) {
      this.notificationManager.add("orders.errors", "order_notCanceled");
      return false;
    }

    const transactionPlatform = await this.pagSeguroService.getTransaction(transaction.transactionPlatformId);

    if (!transactionPlatform || transactionPlatform.charges.length < 1) {
      this.notificationManager.add("orders.errors", "payment_notFound");
      return false;
    }

    const charge = transactionPlatform.charges[0];

    if (
      (charge.status === ChargeStatus.AUTHORIZED && charge.payment_method.type === "CREDIT_CARD") ||
      charge.status === ChargeStatus.PAID
    ) {
      const transactionCancel = await this.transactionRepository.create({
        type: ETransactionType.cancellation,
        amount: transaction.amount,
        installments: transaction.installments,
        orderId: transaction.orderId,
        paymentMethod: transaction.paymentMethod,
        status: ETransactionStatus.created,
      } as ITransaction);

      if (!transactionCancel || !transaction.amount) {
        this.notificationManager.add("orders.errors", "payment_notCanceled");
        return false;
      }

      const response = await this.pagSeguroService.cancelPaymentPagSeguro(charge.id, transaction.amount);

      if (isResponseError(response)) {
        await this.transactionRepository.update(transactionCancel.id, {
          status: ETransactionStatus.rejected,
          statusDetail: JSON.stringify(response),
        });

        this.notificationManager.add("orders.errors", "payment_notCanceled");
        return false;
      }

      await this.transactionRepository.update(transactionCancel.id, {
        status: ETransactionStatus.authorized,
        chargeId: response.id,
        statusDetail: JSON.stringify(response),
      });

      return true;
    }

    this.notificationManager.add("orders.errors", "payment_notCanceled");
    return false;
  }

  async getLastTransactionByUser(userId: string, storeId?: string): Promise<ITransaction | null> {
    const transaction = await this.transactionRepository.getLastTransactionByUser(userId);
    if (storeId) {
      const storeData = await this.storeService.getStoreInfoById(storeId);

      const methods: EPaymentMethod[] = [];
      if (storeData?.storeSettings?.credit) {
        methods.push(EPaymentMethod.credit);
      }
      if (storeData?.storeSettings?.debt) {
        methods.push(EPaymentMethod.debt);
      }
      if (storeData?.storeSettings?.pix) {
        methods.push(EPaymentMethod.pix);
      }

      if (transaction && methods.includes(transaction.paymentMethod)) {
        if (
          transaction.transactionCard &&
          transaction.transactionCard.length > 0 &&
          transaction.transactionCard[0].card
        ) {
          const decryptedCard = decryptCardFromDB(transaction.transactionCard[0].card);
          const encryptedCard = encryptCardToSendApp(decryptedCard);
          transaction.transactionCard[0].card = encryptedCard;
        }
        return transaction;
      }
      return null;
    }
    return transaction;
  }

  async getLastTransactionByOrder(orderId: string): Promise<ITransaction | null> {
    const transaction = this.transactionRepository.getLastTransactionByOrder(orderId);

    return transaction;
  }

  async isCanceled(orderId: string): Promise<boolean> {
    const isCanceled = await this.transactionRepository.isCanceled(orderId);

    return isCanceled;
  }

  async createSessionPagSeguro(): Promise<ISession> {
    const session = this.pagSeguroService.createSession();
    return session;
  }

  // async checkTransactionStatus(notification: IObjectNotification): Promise<void> {
  //   const notificationDetails = await this.pagSeguroService.getNotification(notification.notificationCode);

  //   console.log(notificationDetails);
  // }

  async handleWebhook(notification: INotificationPagSeguro): Promise<void> {
    const notificationStatusResponse = await this.getNotificationStatus(notification);

    if (!notificationStatusResponse) return;

    const transaction = await this.findTransaction(notificationStatusResponse.reference);

    if (!transaction || !transaction.amount) return;

    await this.processNotification(notification, notificationStatusResponse.status, transaction);
  }

  private async getNotificationStatus(notification: INotificationPagSeguro) {
    let statusResponse: ETransactionNotificationStatus | undefined;
    let reference: string;

    if ("notificationCode" in notification) {
      const code = notification.notificationCode;

      const pagSeguroNotificationDetails = await this.pagSeguroService.getNotification(code);

      if (!pagSeguroNotificationDetails) return null;

      statusResponse = pagSeguroNotificationDetails.status;
      reference = pagSeguroNotificationDetails.reference;
    } else {
      const { status } = notification.charges[0];

      switch (status) {
        case ChargeStatus.AUTHORIZED:
          statusResponse = ETransactionNotificationStatus.AVAILABLE;
          break;
        case ChargeStatus.PAID:
          statusResponse = ETransactionNotificationStatus.PAID;
          break;
        case ChargeStatus.IN_ANALYSIS:
          statusResponse = ETransactionNotificationStatus.IN_ANALYSIS;
          break;
        /* case ChargeStatus.DECLINED:
          statusResponse = ETransactionNotificationStatus.CANCELED;
          break; */
        case ChargeStatus.CANCELED:
          statusResponse = ETransactionNotificationStatus.CANCELED;
          break;
        default:
          statusResponse = undefined;
      }

      reference = notification.reference_id;
    }

    if (!statusResponse) return null;

    return {
      status: statusResponse,
      reference,
    };
  }

  private async findTransaction(reference: string): Promise<ITransaction | null> {
    return this.transactionRepository.findFirst({
      where: {
        type: ETransactionType.payment,
        orderId: reference,
      },
    });
  }

  private async processNotification(
    notification: INotificationPagSeguro,
    notificationStatus: ETransactionNotificationStatus,
    transaction: ITransaction,
  ): Promise<void> {
    const newTransactionStatus = transactionStatusMap[notificationStatus];

    await this.transactionRepository.update(transaction.id, {
      status: newTransactionStatus,
      statusDetail: JSON.stringify(notification),
    });

    await this.updateOrderStatus(transaction.orderId, newTransactionStatus);

    await this.updatePayoutStatus(notificationStatus, transaction);

    if (transaction.pixKey) {
      await this.sendSocketNotification(transaction.orderId, newTransactionStatus);
    }
  }

  private async updateOrderStatus(orderId: string, transactionStatus: ETransactionStatus) {
    let newOrderStatus;

    if (transactionStatus === "in_process" || transactionStatus === "pending") {
      newOrderStatus = this.orderStatusTypeSingleton.pending_payment.id;
    }

    if (transactionStatus === "cancelled") {
      newOrderStatus = this.orderStatusTypeSingleton.canceled_payment_failure.id;
      io.to(orderId).emit(ETransactionEvents.error, {});
    }

    if (transactionStatus === "approved") {
      newOrderStatus = this.orderStatusTypeSingleton.payment_made.id;
      io.to(orderId).emit(ETransactionEvents.success, {});
    }

    if (newOrderStatus) {
      await this.orderStatusRepository.createOrUpdateOrderStatus(orderId, newOrderStatus);
    }
  }

  private async sendSocketNotification(orderId: string, transactionStatus: ETransactionStatus) {
    if (transactionStatus === "cancelled") {
      io.to(orderId).emit(ETransactionEvents.error, {});
    }

    if (transactionStatus === "approved") {
      io.to(orderId).emit(ETransactionEvents.success, {});
    }
  }

  private async updatePayoutStatus(
    notificationStatus: ETransactionNotificationStatus,
    transaction: ITransaction,
  ): Promise<void> {
    const newStatus = payoutStatusMap[notificationStatus];

    const payoutStatus = await this.payoutService.getPayoutStatusByOrderId(transaction.orderId);

    if (newStatus && payoutStatus && newStatus !== payoutStatus.status) {
      await this.payoutService.updatePayoutStatus(payoutStatus.id, newStatus);
    }
  }

  paymentMethodToCardMethod(paymentMethod: EPaymentMethod): ECardMethod {
    let type: ECardMethod;
    switch (paymentMethod) {
      case EPaymentMethod.credit:
        type = ECardMethod.credit;
        break;
      case EPaymentMethod.debt:
        type = ECardMethod.debt;
        break;
      default:
        type = ECardMethod.credit;
    }
    return type;
  }

  async getFinancialConsolidation(
    date: string,
    page: number,
    pageSize: number,
    type: EPagSeguroType,
  ): Promise<IFinancialConsolidationResponse | null> {
    const result = await this.pagSeguroService.getFinancialConsolidation(date, page, pageSize, type);
    return result;
  }

  private checkChargesFailed(response: IResponsePayment): boolean {
    let isFailed = false;
    response.charges.forEach((charge) => {
      if (
        charge.status === ChargeStatus.CANCELED ||
        charge.status === ChargeStatus.DECLINED ||
        charge.status === ChargeStatus.IN_ANALYSIS
      )
        isFailed = true;
    });
    return isFailed;
  }
}
