import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICreateProductModerationDTO } from "src/business/DTOs/IProductModeration";
import { IProductModeration } from "src/business/Interfaces/Prisma/IProductModeration";
import { IProductModerationRepository } from "src/business/Interfaces/Repository/IProductModerationRepository";
import { IProductModerationService } from "src/business/Interfaces/Service/IProductModerationService";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class ProductModerationService extends BaseService<IProductModeration> implements IProductModerationService {
  constructor(
    @inject(TOKENS.IProductModerationRepository)
    private productModerationRepository: IProductModerationRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(productModerationRepository, notificationManager);
  }

  async create(moderation: ICreateProductModerationDTO): Promise<boolean> {
    const result = await this.productModerationRepository.create(moderation);
    if (result) return true;
    return false;
  }

  async getAllModeration(): Promise<IProductModeration[]> {
    const result = await this.productModerationRepository.getAll();
    return result;
  }

  async getByProductId(productId: string): Promise<IProductModeration[] | null> {
    const result = await this.productModerationRepository.getByProductId(productId);
    return result;
  }

  async getMostRecentProductModeration(productId: string): Promise<IProductModeration | null> {
    const result = await this.productModerationRepository.getMostRecentProductModeration(productId);

    return result;
  }
}
