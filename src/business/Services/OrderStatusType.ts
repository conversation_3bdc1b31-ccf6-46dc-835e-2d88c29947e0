import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { IOrderStatusType } from "src/business/Interfaces/Prisma/IOrderStatusType";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import OrderStatusTypeSingleton from "src/business/Singletons/OrderStatusType";
import { IOrderStatusTypeRepository } from "../Interfaces/Repository/IOrderStatusType";
import { IOrderStatusTypeService } from "../Interfaces/Service/IOrderStatusType";

@injectable()
export class OrderStatusTypeService extends BaseService<IOrderStatusType> implements IOrderStatusTypeService {
  constructor(
    @inject(TOKENS.IOrderStatusTypeRepository)
    private orderStatusTypeRepository: IOrderStatusTypeRepository,
    @inject(TOKENS.OrderStatusTypeSingleton)
    private orderStatusTypeSingleton: OrderStatusTypeSingleton,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(orderStatusTypeRepository, notificationManager);
    this.orderStatusTypeRepository = orderStatusTypeRepository;
    this.orderStatusTypeSingleton = orderStatusTypeSingleton;
  }

  async loadSingleton(): Promise<void> {
    const allOrderStatusType = await this.orderStatusTypeRepository.getAll();

    const orderStatusType = await this.getOrCreate(allOrderStatusType);

    this.orderStatusTypeSingleton.load(orderStatusType);
  }

  private async getOrCreate(orderStatusTypes: IOrderStatusType[]): Promise<IOrderStatusType[]> {
    return new Promise<IOrderStatusType[]>((resolve) => {
      if (orderStatusTypes && orderStatusTypes.length > 0) {
        resolve(orderStatusTypes);
      } else {
        const arrayKey = [
          EOrderStatusValue.placed_order,
          EOrderStatusValue.pending_payment,
          EOrderStatusValue.payment_made,
          EOrderStatusValue.canceled,
          EOrderStatusValue.rejected,
          EOrderStatusValue.preparing,
          EOrderStatusValue.waiting_for_the_delivery_person,
          EOrderStatusValue.on_route_to_store,
          EOrderStatusValue.on_delivery_route,
          EOrderStatusValue.delivered,
          EOrderStatusValue.canceled_delivery,
          EOrderStatusValue.canceled_payment_failure,
        ];

        Promise.all(
          arrayKey.map(async (key) => {
            const createdOrderStatusType = await this.orderStatusTypeRepository.create({
              value: key,
            } as IOrderStatusType);

            orderStatusTypes.push(createdOrderStatusType);
          }),
        ).finally(() => {
          resolve(orderStatusTypes);
        });
      }
    });
  }
}
