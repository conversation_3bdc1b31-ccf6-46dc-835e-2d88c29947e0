/* eslint-disable arrow-body-style */
/* eslint-disable camelcase */
import { format, sub } from "date-fns";
import { Xlsx } from "exceljs";
import { inject, injectable } from "inversify";
import { isEqual, uniqueId } from "lodash";
import { ICreateOrderViewModel } from "src/api/ViewModels/Order/ICreateOrder";
import pt from "src/business/Assets/Language/pt.json";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { OrderMapper } from "src/business/Configs/Automapper/Profile/Order";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICreateOrderResultDTO } from "src/business/DTOs/Order/ICreateOrderResult";
import { IExportOrdersReportDTO } from "src/business/DTOs/Order/IExportOrdersReport";
import { IOrderFinancialConsolidationDTO } from "src/business/DTOs/Order/IFinancialConsolidation";
import { IOrderDetailsDTO } from "src/business/DTOs/Order/IOrderDetails";
import { PagedResult, PagedResultWithCondition } from "src/business/DTOs/PagedResult";
import { EFinancialConsolidation } from "src/business/Enums/EFinancialConsolidation";
import { EPagSeguroType } from "src/business/Enums/EPagSeguroType";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { EPaymentMethod } from "src/business/Enums/Models/EPaymentMethod";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";
import { EMessageType } from "src/business/Enums/Models/Message/EMessageType";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IDeliverymanRepository } from "src/business/Interfaces/Repository/IDeliveryman";
import { IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IOrderStatusRepository } from "src/business/Interfaces/Repository/IOrderStatus";
import { IStoreRepository } from "src/business/Interfaces/Repository/IStore";
import { IUserRepository } from "src/business/Interfaces/Repository/IUser";
import { IMessageService } from "src/business/Interfaces/Service/IMessage";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IOrderService } from "src/business/Interfaces/Service/IOrder";
import { ITransactionService } from "src/business/Interfaces/Service/ITransaction";
import { IFinancialConsolidationResponse } from "src/business/Interfaces/Service/PagSeguro/FinancialConsolidation/IFinancialConsolidation";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import OrderStatusTypeSingleton from "src/business/Singletons/OrderStatusType";
import { exportOrdersToExcel } from "src/business/Utils/ExportOrdersToExcel";
import { IOrder as IOrderSort } from "src/business/DTOs/Order";
import { IOrderByStatusDTO } from "src/business/DTOs/Order/IOrderByStatus";
import { IUserOrdersDTO } from "src/business/DTOs/Order/IUserOrders";
import { IOrderSalesDTO } from "src/business/DTOs/Order/IOrderSales";
import { ISNSService } from "src/business/Interfaces/Service/AWS/ISNS";
import { GenerateOrderTopicName } from "src/business/Utils/Notifications/generateOrderTopicName";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { IOrderDetailsBackOfficeDTO } from "src/business/DTOs/Order/IOrderDetailsBackOffice";
import { IUpdateOrderViewModel } from "src/api/ViewModels/Order/IUpdateOrder";
import { IUpdateOrderResultDTO } from "src/business/DTOs/Order/IUpdateOrderResult";
import { IOrderStatusService } from "src/business/Interfaces/Service/IOrderStatus";
import IFinancialConsolidationService from "src/business/Interfaces/Service/IFinancialConsolidationService";
import { IFinancialConsolidationTransactions } from "src/business/Interfaces/Prisma/IFinancialConsolidationTxn";
import { IFinancialConsolidation } from "src/business/Interfaces/Prisma/IFinancialConsolidation";
import { EFinancialStatus } from "@prisma/client";

@injectable()
export class OrderService extends BaseService<IOrder> implements IOrderService {
  constructor(
    @inject(TOKENS.IOrderRepository)
    private orderRepository: IOrderRepository,
    @inject(TOKENS.OrderStatusTypeSingleton)
    private orderStatusTypeSingleton: OrderStatusTypeSingleton,
    @inject(TOKENS.IUserRepository)
    private userRepository: IUserRepository,
    @inject(TOKENS.IDeliverymanRepository)
    private deliverymanRepository: IDeliverymanRepository,
    @inject(TOKENS.IStoreRepository)
    private storeRepository: IStoreRepository,
    @inject(TOKENS.ITransactionService)
    private transactionService: ITransactionService,
    @inject(TOKENS.IOrderStatusRepository)
    private orderStatusRepository: IOrderStatusRepository,
    @inject(TOKENS.IOrderStatusService)
    private orderStatusService: IOrderStatusService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
    @inject(TOKENS.IMessageService)
    private messageService: IMessageService,
    @inject(TOKENS.ISNSService)
    private snsService: ISNSService,
    @inject(TOKENS.IDeviceService)
    private deviceService: IDeviceService,
    @inject(TOKENS.IFinancialConsolidationService)
    private consolidationService: IFinancialConsolidationService,
  ) {
    super(orderRepository, notificationManager);
    this.orderRepository = orderRepository;
    this.orderStatusTypeSingleton = orderStatusTypeSingleton;
  }

  async create(data: ICreateOrderViewModel): Promise<ICreateOrderResultDTO> {
    const result: ICreateOrderResultDTO = { success: false };
    let attributeIds: string[] = [];
    let attributeOptionsIds: string[] = [];

    const newOrderItems = await Promise.all(
      data.orderItem.map(async (orderIts) => {
        attributeIds = [];
        attributeOptionsIds = [];
        orderIts.attributes?.forEach((attribute) => {
          attributeIds.push(attribute.id);
          attribute.attributeOption?.forEach((attributeOption) => {
            attributeOptionsIds.push(attributeOption.id);
          });
        });

        let orderItemPAOAux: { id: string }[] = [];

        if (attributeIds.length > 0 || attributeOptionsIds.length > 0) {
          orderItemPAOAux = await this.getProductAttributeOptionIds(
            orderIts.productId,
            attributeIds,
            attributeOptionsIds,
          );
          orderIts.productAttributeOptionIds = orderItemPAOAux;

          return orderIts;
        }

        delete orderIts.attributes;

        return orderIts;
      }),
    );

    data.orderItem = newOrderItems;

    const defaultStatus = [
      {
        orderStatusType: { id: this.orderStatusTypeSingleton.placed_order.id },
      },
    ];

    data.orderStatus = defaultStatus;

    const order = mapper.map(OrderMapper.CreateOrderViewModelToIOrder, data);

    const createdOrder = await this.orderRepository.create(order);

    if (order.storeId && createdOrder && createdOrder.userId) {
      result.success = true;

      const orderTopicName = GenerateOrderTopicName(createdOrder.id, createdOrder.createdAt!);
      const { TopicArn } = await this.snsService.createTopic({ Name: orderTopicName });

      if (!TopicArn) {
        return result;
      }

      const orderUserIds: { userId: string; userProfile: EProfile }[] = [];
      await this.deviceService.associateDevicesUserToTopic(TopicArn, data.userId);

      orderUserIds.push({ userId: data.userId, userProfile: EProfile.client });

      const paymentResult = await this.transactionService.createPayment(
        data.userId,
        createdOrder.id,
        data.orderTransactionData,
      );

      if (paymentResult.success) {
        if (data.orderTransactionData.paymentMethod === EPaymentMethod.pix) {
          result.pixKey = paymentResult.pixKey;
          await this.orderStatusService.addOrderStatus(
            createdOrder.id,
            { value: EOrderStatusValue.pending_payment },
            EProfile.manager,
          );
        } else if (this.orderStatusTypeSingleton.payment_made)
          await this.orderStatusService.addOrderStatus(
            createdOrder.id,
            { value: EOrderStatusValue.payment_made },
            EProfile.manager,
          );
      } else if (this.orderStatusTypeSingleton.canceled_payment_failure) {
        await this.orderStatusService.addOrderStatus(
          createdOrder.id,
          { value: EOrderStatusValue.canceled_payment_failure },
          EProfile.manager,
        );
      }
    }
    return result;
  }

  async updateOrderOnFailTransaction(data: IUpdateOrderViewModel): Promise<IUpdateOrderResultDTO> {
    const result: IUpdateOrderResultDTO = { success: false };
    await this.orderRepository.deleteOrderItem(data.id);

    let attributeIds: string[] = [];
    let attributeOptionsIds: string[] = [];

    const newOrderItems = await Promise.all(
      data.orderItem.map(async (orderIts) => {
        attributeIds = [];
        attributeOptionsIds = [];
        orderIts.attributes?.forEach((attribute) => {
          attributeIds.push(attribute.id);
          attribute.attributeOption?.forEach((attributeOption) => {
            attributeOptionsIds.push(attributeOption.id);
          });
        });

        let orderItemPAOAux: { id: string }[] = [];

        if (attributeIds.length > 0 || attributeOptionsIds.length > 0) {
          orderItemPAOAux = await this.getProductAttributeOptionIds(
            orderIts.productId,
            attributeIds,
            attributeOptionsIds,
          );
          orderIts.productAttributeOptionIds = orderItemPAOAux;

          return orderIts;
        }

        delete orderIts.attributes;

        return orderIts;
      }),
    );

    data.orderItem = newOrderItems;

    const order = mapper.map(OrderMapper.UpdateOrderViewModelToIOrder, data);

    await this.orderRepository.createOrderItem(data.id, order);

    delete order.orderItem;
    console.log(data.id, order);
    const updateOrder = await this.orderRepository.update(data.id, order);
    console.log("updateOrder", updateOrder);

    result.success = true;

    return result;
  }

  async updateDeliverymanId(orderId: string, userId: string): Promise<boolean | null> {
    const order = await this.orderRepository.getById(orderId);
    const deliveryman = await this.deliverymanRepository.getByUserId(userId);

    let response: number | null = null;
    if (order && deliveryman) {
      order.deliverymanId = deliveryman.id;

      response = await this.orderRepository.update(order.id, order);
    }

    return !!response;
  }

  async getOrdersByStatus(
    storeId: string,
    page: string,
    pageSize: string,
    statusValue?: EOrderStatusValue,
  ): Promise<PagedResult<IOrderByStatusDTO>> {
    const status = statusValue ? this.orderStatusTypeSingleton[statusValue].value : undefined;

    const { pageSizeInt, currentPageInt } = this.getPageSizeAndCurrentPage(pageSize, page);

    const response = await this.orderRepository.getOrdersByStatus(storeId, currentPageInt, pageSizeInt, status);

    return response;
  }

  async getOrdersByStatusAndDeliverymanId(
    userId: string,
    page: string,
    pageSize: string,
    statusValue?: EOrderStatusValue,
  ): Promise<PagedResult<IOrderByStatusDTO>> {
    const status = statusValue ? this.orderStatusTypeSingleton[statusValue].value : undefined;

    const { pageSizeInt, currentPageInt } = this.getPageSizeAndCurrentPage(pageSize, page);

    const response = await this.orderRepository.getOrdersByStatusAndDeliverymanId(
      userId,
      currentPageInt,
      pageSizeInt,
      status,
    );

    return response;
    // }

    // this.notificationManager.add("order.status.errors", "status_not_found");
    // return [];
  }

  async getOrdersByUserId(userId: string, currentPage: number, pageSize: number): Promise<PagedResult<IUserOrdersDTO>> {
    const response = await this.orderRepository.getOrdersByUserId(userId, currentPage, pageSize);

    return response;
  }

  private async getProductAttributeOptionIds(
    productId: string,
    attributeId: string[],
    attributeOptionId: string[],
  ): Promise<{ id: string }[]> {
    const response = await this.orderRepository.getProductAttributeOptionIds(productId, attributeId, attributeOptionId);

    if (response) {
      return response;
    }

    this.notificationManager.add("generic.errors", "orders_notFound");

    return [];
  }

  async getOrdersPaged(
    currentPage: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    storeFilter?: string,
    // eslint-disable-next-line default-param-last
    getOnlyDelivered: boolean = false,
    orderBy?: string,
    sortDirection?: IOrderSort,
  ): Promise<PagedResult<IOrder>> {
    filterValue = filterValue.toLowerCase();

    const select = {
      id: true,
      code: true,
      createdAt: true,
      totalPrice: true,
      price: true,
      store: {
        select: {
          id: true,
          name: true,
        },
      },
      orderStatus: {
        select: {
          id: true,
          current: true,
          orderStatusType: true,
        },
      },
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
      deliveryman: {
        select: {
          id: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      },
    };

    this.orderRepository.find({
      where: {
        orderStatus: {
          some: {
            AND: [{ current: true }],
          },
        },
      },
    });
    const deliveredStatusId = getOnlyDelivered ? this.orderStatusTypeSingleton.delivered.id : undefined;
    const numberFilter = filterValue?.includes(",") ? Number(filterValue.replace(",", ".")) : Number(filterValue);

    let where: object | undefined = {
      createdAt: {
        lte: endDateFilter,
        gte: startDateFilter,
      },
      orderStatus: {
        some: deliveredStatusId
          ? {
              orderStatusTypeId: deliveredStatusId,
            }
          : undefined,
      },
      store: storeFilter ? { name: storeFilter } : undefined,
    };

    if (filterValue) {
      where = {
        ...where,
        OR: [
          {
            store: {
              name: {
                contains: filterValue,
                mode: "insensitive",
              },
            },
          },
          {
            deliveryman: {
              user: {
                firstName: { equals: filterValue },
              },
            },
          },
          {
            user: {
              firstName: { equals: filterValue },
            },
          },
          !Number.isNaN(numberFilter) ? { code: { equals: numberFilter } } : {},
          !Number.isNaN(numberFilter) ? { price: { equals: numberFilter } } : {},
        ],
      };
    }
    const columnFilter = "";

    const { data } = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      undefined,
      where,
      select,
    );

    return data;
  }

  async getOrderWithItems(orderId: string): Promise<IOrder | null> {
    const response = await this.orderRepository.getOrderWithItems(orderId);

    return response;
  }

  async cancelOrder(orderId: string): Promise<boolean> {
    const order = await this.orderRepository.getById(orderId);

    if (!order) {
      this.notificationManager.add("orders.errors", "order_notFound");
      return false;
    }

    const isCanceled = await this.transactionService.isCanceled(orderId);

    if (isCanceled && this.orderStatusTypeSingleton.canceled) {
      await this.orderStatusRepository.changeOrderStatus(orderId, this.orderStatusTypeSingleton.canceled?.id);
      this.notificationManager.add("orders.errors", "order_already_canceled");
      return false;
    }

    const transaction = await this.transactionService.getLastTransactionByOrder(orderId);

    if (!transaction) {
      this.notificationManager.add("orders.errors", "payment_notFound");
      return false;
    }

    const response = await this.transactionService.cancelPayment(transaction);

    if (!response && order.userId) {
      await this.notificationService.sendNotificationToUser(order.userId, {
        title: pt.notification.change_status_title,
        body: pt.notification.cancellation_failed,
        params: {
          title: order.code,
          order: {
            messageType: EMessageType.order_status,
            orderId: order.id,
          },
        },
        profile: EProfile.client,
        type: EMessageType.order_status,
      });
      return false;
    }

    if (this.orderStatusTypeSingleton.canceled) {
      if (transaction.orderId) {
        await this.orderStatusRepository.changeOrderStatus(
          transaction.orderId,
          this.orderStatusTypeSingleton.canceled.id,
        );
      }
    }

    return true;
  }

  async updateCustomerCode(id: string, customerCode: string): Promise<boolean> {
    const order = await this.orderRepository.getById(id);

    if (!order) {
      this.notificationManager.add("generic.errors", "orders_notFound");
      return false;
    }

    order.customerCode = customerCode;

    const response = await this.orderRepository.update(id, order);

    return !!response;
  }

  async validateCustomerCode(id: string, customerCode: string): Promise<boolean> {
    const order = await this.orderRepository.getById(id);

    if (!order) {
      this.notificationManager.add("generic.errors", "orders_notFound");
      return false;
    }

    if (order?.customerCode === customerCode) return true;

    this.notificationManager.add("orders.errors", "wrong_custom_code");
    return false;
  }

  async saveMessage(title: string, usersId: string[]) {
    await this.messageService.create({
      title,
      type: EMessageType.order_status,
      sendingType: [EMessageSendingType.notification],
      userMessage: usersId.map((userId) => ({
        userId,
      })),
      content: [
        {
          body: pt.notification.status_placed_order,
          sendingType: EMessageSendingType.notification,
        },
      ],
    });
  }

  async createOrderReportFile(): Promise<Xlsx | null> {
    const orders = await this.orderRepository.getOrdersToExport(this.orderStatusTypeSingleton.delivered.id);

    if (orders) {
      const groupedOrders = this.groupOrdersObjects(orders);

      const xlsx = await exportOrdersToExcel(groupedOrders);

      return xlsx;
    }

    return null;
  }

  private groupOrdersObjects(report: IExportOrdersReportDTO[]) {
    const grouped = {} as IExportOrdersReportDTO;

    report.forEach((item) => {
      const { categoryName, ...rest } = item;
      const key = JSON.stringify(rest);

      if (key in grouped) {
        if (!grouped[key].categoryName.includes(categoryName)) {
          grouped[key].categoryName.push(categoryName);
        }
      } else {
        grouped[key] = {
          ...rest,
          categoryName: [categoryName],
        };
      }
    });

    const result = Object.values(grouped);

    result.forEach((item) => {
      item.categoryName = item.categoryName.join(", ");
    });

    return result;
  }

  async updateDeliverymanRouteLength(orderId: string, routeLength: number): Promise<boolean> {
    const response = await this.orderRepository.update(orderId, {
      routeLength,
    });
    return !!response;
  }

  async getFinancialConsolidationPaged(
    currentPage: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue?: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
    statusFilter?: EFinancialConsolidation,
  ): Promise<PagedResult<IOrderFinancialConsolidationDTO>> {
    const includes = {
      store: true,
      orderItem: {
        include: {
          orderItemProductAttributeOption: true,
          product: true,
        },
      },
      deliveryman: {
        include: {
          user: { select: { firstName: true, lastName: true } },
        },
      },
      transaction: true,
      financialConsolidation: true,
    };

    let where: object | undefined = {
      createdAt: {
        lte: endDateFilter,
        gte: startDateFilter,
      },
    };

    const numberFilter = filterValue?.includes(",") ? Number(filterValue.replace(",", ".")) : Number(filterValue);

    if (filterValue) {
      where = {
        ...where,
        OR: [
          {
            deliveryman: {
              user: {
                firstName: { equals: filterValue },
              },
            },
          },
          {
            store: {
              name: { contains: filterValue, mode: "insensitive" },
            },
          },
          !Number.isNaN(numberFilter) ? { price: { equals: numberFilter } } : {},
          !Number.isNaN(numberFilter) ? { totalPrice: { equals: numberFilter } } : {},
          !Number.isNaN(numberFilter) ? { shippingPrice: { equals: numberFilter } } : {},
        ],
      };
    }

    if (
      statusFilter === EFinancialConsolidation.divergentValues ||
      statusFilter === EFinancialConsolidation.onlyFinanceCompany
    ) {
      where = {
        ...where,
        financialConsolidation: {
          status: {
            equals: statusFilter,
          },
        },
      };
    }

    const columnFilter = "";

    const { data, whereCondition } = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      includes,
      where,
    );

    const totalizer = await this.orderRepository.getTotalOrderAmount(whereCondition);

    return {
      result: data.result,
      totalCount: data.totalCount,
      totalPages: data.totalPages,
      totalizer: { ...totalizer },
    };
  }

  async getOneFinancialConsolidationOrder(orderId: string): Promise<IOrder | null> {
    const result = await this.orderRepository.getOneFinancialConsolidationOrder(orderId);
    return result;
  }

  async getOrderDetailsById(orderId: string): Promise<IOrderDetailsDTO | null> {
    const result = await this.orderRepository.getOrderDetailsById(orderId);

    if (!result) {
      this.notificationManager.add("generic.errors", "order_notFound");
      return null;
    }

    return result;
  }

  async getDeliverymanAndShopkeeperSales(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: string,
    pageSize: string,
    profile: EProfile,
    statusValue?: EOrderStatusValue,
  ): Promise<PagedResult<IOrderSalesDTO>> {
    const select = {
      id: true,
      createdAt: true,
      routeLength: profile === EProfile.deliveryman,
      price: true,
      shippingPrice: true,
      totalPrice: true,
      code: true,
      store: {
        select: {
          name: true,
        },
      },
      orderStatus:
        profile === EProfile.shopkeeper
          ? {
              select: {
                current: true,
                orderStatusType: {
                  select: {
                    value: true,
                  },
                },
              },
            }
          : false,
      address:
        profile === EProfile.deliveryman
          ? {
              select: {
                street: true,
                number: true,
                district: true,
              },
            }
          : false,
    };

    const where = {
      orderStatus: {
        some: {
          orderStatusTypeId: statusValue
            ? this.orderStatusTypeSingleton[statusValue].id
            : {
                in: [
                  this.orderStatusTypeSingleton.canceled.id,
                  this.orderStatusTypeSingleton.rejected.id,
                  this.orderStatusTypeSingleton.delivered.id,
                ],
              },
          current: true,
        },
      },
      deliveryman:
        profile === EProfile.deliveryman
          ? {
              user: {
                id: {
                  equals: userId,
                },
              },
            }
          : undefined,
      createdAt: {
        lte: endDate,
        gte: startDate,
      },
      store:
        profile === EProfile.shopkeeper
          ? {
              storeUsers: {
                some: {
                  userId,
                },
              },
            }
          : undefined,
    };

    const { data } = (await this.getPaged(
      "",
      "",
      page,
      pageSize,
      "createdAt",
      "asc",
      undefined,
      where,
      select,
    )) as unknown as PagedResultWithCondition<IOrderSalesDTO>;

    if (profile === EProfile.deliveryman) {
      data.totalizer = await this.orderRepository.getSalesDeliverymanTotalizer(where);
    }

    if (profile === EProfile.shopkeeper) {
      data.totalizer = await this.orderRepository.getSalesShopkeeperTotalizer(where);
    }

    return data;
  }

  async getOrderBackOfficeDetails(orderId: string, isSales?: boolean): Promise<IOrderDetailsBackOfficeDTO | null> {
    const result = await this.orderRepository.getOrderBackOfficeDetails(orderId, isSales);

    if (!result) {
      this.notificationManager.add("generic.errors", "order_notFound");
      return null;
    }

    return result;
  }

  async getLastOrderIdByUserIdAndStoreId(userId: string, storeId: string): Promise<string | null> {
    const result = await this.orderRepository.getLastOrderIdByUserIdAndStoreId(userId, storeId);
    if (!result) {
      this.notificationManager.add("generic.errors", "order_notFound");
      return null;
    }
    return result;
  }

  async createFinancialConsolidation(): Promise<boolean> {
    let currentPage = 1;
    const limit = 1000;
    const yesterdayDate = format(sub(new Date(), { days: 20 }), "yyyy-MM-dd");

    const pagSeguroResult = await this.transactionService.getFinancialConsolidation(
      yesterdayDate,
      currentPage,
      limit,
      EPagSeguroType.financeiro,
    );

    if (!pagSeguroResult) {
      await this.consolidationService.createFinancialConsolidation({
        createdAt: new Date(yesterdayDate),
        status: EFinancialStatus.failed,
      });
      return false;
    }

    if (pagSeguroResult.pagination.totalPages > 1) {
      while (currentPage < pagSeguroResult.pagination.totalPages) {
        currentPage += 1;
        this.transactionService
          .getFinancialConsolidation(
            format(sub(new Date(), { days: 1 }), "yyyy-MM-dd"),
            currentPage,
            limit,
            EPagSeguroType.financeiro,
          )
          .then((data) => {
            if (data) {
              pagSeguroResult.detalhes.concat(data?.detalhes);
              pagSeguroResult.pagination = { ...data.pagination };
            }
          })
          .catch(() => {
            this.consolidationService.createFinancialConsolidation({
              createdAt: new Date(yesterdayDate),
              status: EFinancialStatus.failed,
            });
          });
      }
    }

    await this.saveFinancialConsolidationInconsistency(yesterdayDate, pagSeguroResult);

    return true;
  }

  async saveFinancialConsolidationInconsistency(date: string, pagSeguroResult: IFinancialConsolidationResponse) {
    const currentPage = 1;
    const limit = 1000 * pagSeguroResult.pagination.totalPages;

    const includes = {
      store: true,
      transaction: true,
    };

    const where: object | undefined = {
      createdAt: {
        equals: new Date(date),
      },
    };

    const { data } = await this.getPaged(
      "",
      "",
      currentPage.toString(),
      limit.toString(),
      "createdAt",
      "asc",
      includes,
      where,
    );

    const inconsistencyTransactions: Partial<IFinancialConsolidationTransactions>[] = [];

    const consolidationStatusMap = data.result.map((order) => {
      const promise: Promise<IFinancialConsolidationTransactions> = new Promise((res, rej) => {
        if (pagSeguroResult.detalhes.length > 0) {
          pagSeguroResult.detalhes.forEach((item) => {
            const value = order.transaction?.find((t) =>
              [item.codigo_transacao, item.codigo_venda, item.tx_id].some((value) =>
                [t.pixKey, t.transactionPlatformId, t.chargeId].includes(value),
              ),
            );

            const baseConsolidationTxz = {
              totalPrice: item.valor_original_transacao,
              createdAt: new Date(item.data_inicial_transacao),
              status: EFinancialConsolidation.onlyFinanceCompany,
              storeName: order.store?.name,
              platformId: order.transaction?.[0].transactionPlatformId || item.codigo_transacao,
              chargeId: order.transaction?.[0].chargeId || "",
              administrativeFee: Number(item.taxa_parcela_vendedor) || 0,
              orderId: order.id,
            };

            if (!value) {
              inconsistencyTransactions.push({
                ...baseConsolidationTxz,
                status: EFinancialConsolidation.onlyFinanceCompany,
              });
            }

            if (value && !isEqual(order.totalPrice.toFixed(2), item.valor_original_transacao.toFixed(2))) {
              inconsistencyTransactions.push({
                ...baseConsolidationTxz,
                status: EFinancialConsolidation.divergentValues,
              });
            }
          });
        }
      });

      return promise;
    });

    const result = await Promise.all(consolidationStatusMap);
    await this.consolidationService.createFinancialConsolidation({
      createdAt: new Date(date),
      status: EFinancialStatus.success,
      transactions: result,
    });
  }
}
