/* eslint-disable no-else-return */
import { EBorderoStatus, EPayoutOwner, EPayoutStatus } from "@prisma/client";
import { inject, injectable } from "inversify";
import { IValidateBorderoResultViewModel } from "src/api/ViewModels/Bordero/IValidateBorderoResult";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import IBorderoListDTO from "src/business/DTOs/Bordero/IBordero";
import { ICreateBorderoResultDTO } from "src/business/DTOs/Bordero/ICreateBorderoResult";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IBordero } from "src/business/Interfaces/Prisma/IBordero";
import { IBorderoRepository } from "src/business/Interfaces/Repository/IBordero";
import { IBorderoService } from "src/business/Interfaces/Service/IBordero";
import { ICooperativeService } from "src/business/Interfaces/Service/ICooperative";
import { IPayoutService } from "src/business/Interfaces/Service/IPayout";
import { IStoreService } from "src/business/Interfaces/Service/IStore";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class BorderoService extends BaseService<IBordero> implements IBorderoService {
  constructor(
    @inject(TOKENS.IBorderoRepository)
    private borderoRepository: IBorderoRepository,
    @inject(TOKENS.IPayoutService)
    private payoutService: IPayoutService,
    @inject(TOKENS.IUserService)
    private userService: IUserService,
    @inject(TOKENS.IStoreService)
    private storeService: IStoreService,
    @inject(TOKENS.ICooperativeService)
    private cooperativeService: ICooperativeService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(borderoRepository, notificationManager);
  }

  async create(cpfCnpj: string, payoutOwner: EPayoutOwner): Promise<ICreateBorderoResultDTO> {
    const newBordero = { payoutOwner } as IBordero;
    if (payoutOwner === EPayoutOwner.deliveryman) {
      const user = await this.userService.getByCpfWithDeliveryman(cpfCnpj);
      newBordero.userId = user?.id;
    } else if (payoutOwner === EPayoutOwner.store) {
      const store = await this.storeService.getStoreByCpfCnpj(cpfCnpj);
      newBordero.storeId = store?.id;
    } else if (payoutOwner === EPayoutOwner.cooperative) {
      const cooperative = await this.cooperativeService.getCooperativeByCNPJ(cpfCnpj);
      newBordero.cooperativeId = cooperative?.id;
    }
    const resultCreate = await this.borderoRepository.create(newBordero);

    if (!resultCreate) {
      this.notificationManager.add("bordero.errors", "create_failed");
      return { isCreated: false };
    }

    return {
      isCreated: true,
      borderoId: resultCreate.id,
      userId: resultCreate.userId || undefined,
      cooperativeId: resultCreate.cooperativeId || undefined,
      storeId: resultCreate.storeId || undefined,
    };
  }

  // TODO - implementar rotina numa transação
  async updateBordero(bordero: IBordero, payouts?: string[]): Promise<boolean> {
    const newBordero: IBordero = { ...bordero };
    const totals = await this.payoutService.getPayoutsTotalizerByIds(payouts);
    newBordero.quantityOrders = totals.countOrders;
    newBordero.sumOrderValue = bordero.payoutOwner === EPayoutOwner.store ? totals.sumPrice : totals.sumShipping;
    newBordero.sumAdministrativeFeeValue = totals.sumAdm;
    newBordero.sumTransferValue = totals.sumTransfer;
    const resultUpdate = await this.borderoRepository.update(bordero.id, newBordero);

    if (!resultUpdate) {
      this.notificationManager.add("bordero.errors", "update_failed");
      return false;
    }

    await this.payoutService.updatePayoutsClearBordero(newBordero.id);

    if (payouts && payouts.length > 0) {
      const resultUpdatePayouts = await this.payoutService.updatePayoutsBordero(bordero.id, payouts);

      if (!resultUpdatePayouts) {
        return false;
      }
    }

    return true;
  }

  async getBorderoPaged(
    currentPage: string,
    pageSize: string,
    filterStatus: EBorderoStatus,
    filterCpfCnpj?: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    orderBy?: string,
    sortDirection?: string,
  ): Promise<PagedResult<IBorderoListDTO>> {
    const { currentPageInt, pageSizeInt } = this.getPageSizeAndCurrentPage(pageSize, currentPage);

    const data = await this.borderoRepository.getBorderosPaged(
      currentPageInt,
      pageSizeInt,
      filterStatus,
      filterCpfCnpj,
      startDateFilter,
      endDateFilter,
      orderBy,
      sortDirection,
    );

    return data;
  }

  async validate(cpfCnpj: string, payoutOwner: EPayoutOwner): Promise<IValidateBorderoResultViewModel> {
    if (payoutOwner === EPayoutOwner.deliveryman) {
      const resultBorderoUser = await this.validateBorderoUser(cpfCnpj);
      return resultBorderoUser;
    } else if (payoutOwner === EPayoutOwner.store) {
      const resultBorderoStore = await this.validateBorderoStore(cpfCnpj);
      return resultBorderoStore;
    }
    const resultBorderoCooperative = await this.validateBoderoCooperative(cpfCnpj);
    return resultBorderoCooperative;
  }

  private async validateBoderoCooperative(cnpj: string): Promise<IValidateBorderoResultViewModel> {
    const cooperative = await this.cooperativeService.getCooperativeByCNPJ(cnpj);
    if (!cooperative) {
      return { isValid: false, error: "not_found" };
    }

    const bordero = await this.borderoRepository.findFirst({
      where: { AND: [{ cooperativeId: cooperative.id }, { status: EBorderoStatus.open }] },
    });
    if (bordero) {
      return { isValid: false, name: cooperative.name, borderoId: bordero.id, error: "is_already" };
    }
    const payouts = await this.payoutService.getPayoutsWithoutBorderoByCooperative(cooperative.id);
    if (!payouts) {
      return { isValid: false, name: cooperative.name, error: "no_payouts" };
    }
    return {
      isValid: true,
      name: cooperative.name,
    };
  }

  private async validateBorderoUser(cpfCnpj: string): Promise<IValidateBorderoResultViewModel> {
    const user = await this.userService.getByCpfWithDeliveryman(cpfCnpj);
    if (!user || !user.deliveryman?.id) {
      return { isValid: false, error: "not_found" };
    }
    const bordero = await this.borderoRepository.findFirst({
      where: { AND: [{ userId: user.id }, { status: EBorderoStatus.open }] },
    });
    if (bordero) {
      return { isValid: false, name: `${user.firstName} ${user.lastName}`, borderoId: bordero.id, error: "is_already" };
    }
    const payouts = await this.payoutService.getPayoutsWithoutBorderoByDeliveryman(user.deliveryman.id);
    if (!payouts) {
      return { isValid: false, name: `${user.firstName} ${user.lastName}`, error: "no_payouts" };
    }
    return {
      isValid: true,
      name: `${user.firstName} ${user.lastName}`,
    };
  }

  private async validateBorderoStore(cpfCnpj: string): Promise<IValidateBorderoResultViewModel> {
    const store = await this.storeService.getStoreByCpfCnpj(cpfCnpj);
    if (!store) {
      return { isValid: false, error: "not_found" };
    }
    const bordero = await this.borderoRepository.findFirst({
      where: { AND: [{ storeId: store.id }, { status: EBorderoStatus.open }] },
    });
    if (bordero) {
      return { isValid: false, name: store.name, borderoId: bordero.id, error: "is_already" };
    }
    const payouts = await this.payoutService.getPayoutsWithoutBorderoByStore(store.id);
    if (!payouts) {
      return { isValid: false, name: store.name, error: "no_payouts" };
    }
    return {
      isValid: true,
      name: store.name,
    };
  }

  async delete(id: string): Promise<boolean> {
    const countPayouts = await this.payoutService.getCountPayoutsByBordero(id);
    const payoutsClear = await this.payoutService.updatePayoutsClearBordero(id);
    if (countPayouts !== payoutsClear) {
      this.notificationManager.add("bordero.errors", "delete_failed");
      return false;
    }

    const borderoDelete = await this.borderoRepository.delete(id);
    if (!borderoDelete) {
      this.notificationManager.add("bordero.errors", "delete_failed");
      return false;
    }

    return true;
  }

  async getBorderoDetails(borderoId: string) {
    const data = await this.borderoRepository.getBorderoDetails(borderoId);

    return data;
  }

  async changeBorderoStatus(borderoId: string, status: EBorderoStatus): Promise<boolean> {
    const data = await this.borderoRepository.update(borderoId, { status });

    if (status === EBorderoStatus.paid) {
      await this.payoutService.updatePayoutStatusByBordero(borderoId, EPayoutStatus.executed);
    }

    return !!data;
  }
}
