import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ILog } from "src/business/Interfaces/Prisma/ILog";
import { ILogRepository } from "src/business/Interfaces/Repository/ILog";
import { ILogService } from "src/business/Interfaces/Service/ILog";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { IOrder as IOrderSort } from "src/business/DTOs/Order";
import { ILogDTO } from "src/business/DTOs/Log/ILog";

@injectable()
export class LogService extends BaseService<ILog> implements ILogService {
  constructor(
    @inject(TOKENS.ILogRepository)
    private logRepository: ILogRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(logRepository, notificationManager);

    this.logRepository = logRepository;
  }

  async getLogDetailsById(
    logId: string,
    entity: string,
    entityId: string,
    createdAt: Date,
    action: string,
  ): Promise<ILogDTO | null> {
    const response = await this.logRepository.getLogDetailsById(logId, entity, entityId, createdAt, action);

    if (!response) {
      this.notificationManager.add("generic.errors", "logDetails");
    }

    return response;
  }

  async getPagedLog(
    currentPage: string,
    pageSize: string,
    startDateFilter: Date,
    endDateFilter: Date,
    filterValue: string,
    orderBy?: string,
    sortDirection?: IOrderSort,
  ): Promise<PagedResult<ILog>> {
    let where: object | undefined = {};

    if (endDateFilter && startDateFilter) {
      where = {
        createdAt: {
          lte: endDateFilter,
          gte: startDateFilter,
        },
      };
    }

    const columnFilter = "entity,action,message,level";

    const { data } = await this.getPaged(
      columnFilter,
      filterValue,
      currentPage,
      pageSize,
      orderBy,
      sortDirection,
      undefined,
      where,
    );

    return data;
  }
}
