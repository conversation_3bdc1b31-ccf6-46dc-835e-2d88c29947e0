/* eslint-disable array-callback-return */
/* eslint-disable no-unused-expressions */
import { ECardMethod, EDayOfWeek, EFileType, EMessageType } from "@prisma/client";
import ExcelJS, { Xlsx } from "exceljs";
import { inject, injectable } from "inversify";
import pt from "src/business/Assets/Language/pt.json";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { StoreMapper } from "src/business/Configs/Automapper/Profile/Store";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IPaymentMethodsDTO } from "src/business/DTOs/IPaymentMethods";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import INotificationDTO from "src/business/DTOs/Notification";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ICreateStoreByExportDTO } from "src/business/DTOs/Store/ICreateStore";
import { IListStoresDTO } from "src/business/DTOs/Store/IListStores";
import { IListUserStoreDTO } from "src/business/DTOs/Store/IListUserStore";
import { IStoreNameDTO } from "src/business/DTOs/Store/IStoreName";
import { IStoreShowcaseWithUrlsDTO } from "src/business/DTOs/Store/IStoreShowcaseWithUrls";
import { ICreateStoreModerationDTO } from "src/business/DTOs/StoreModeration/IStoreModeration";
import { EPaymentMethod } from "src/business/Enums/Models/EPaymentMethod";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { ICardRepository } from "src/business/Interfaces/Repository/ICard";
import { ICategoryRepository } from "src/business/Interfaces/Repository/ICategory";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { IStoreRepository } from "src/business/Interfaces/Repository/IStore";
import { IStoreCategoryRepository } from "src/business/Interfaces/Repository/IStoreCategory";
import { IStoreModerationRepository } from "src/business/Interfaces/Repository/IStoreModeration";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IStoreService } from "src/business/Interfaces/Service/IStore";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { decryptCardFromDB, encryptCardToSendApp } from "src/business/Utils/crypto";
import { exportStoresToExcel } from "src/business/Utils/ExportStoresToExcel";
import { handleStoresToExport } from "src/business/Utils/HandleStoresToExport";
import { validateColumn } from "src/business/Utils/ValidateColumn";

@injectable()
export class StoreService extends BaseService<IStore> implements IStoreService {
  constructor(
    @inject(TOKENS.IStoreRepository) private storeRepository: IStoreRepository,
    @inject(TOKENS.IStoreCategoryRepository)
    private storeCategoryRepository: IStoreCategoryRepository,
    @inject(TOKENS.IFileRepository)
    private fileRepository: IFileRepository,
    @inject(TOKENS.ICardRepository)
    private cardRepository: ICardRepository,
    @inject(TOKENS.ICategoryRepository)
    private categoryRepository: ICategoryRepository,
    @inject(TOKENS.IStoreModerationRepository)
    private storeModerationRepository: IStoreModerationRepository,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(storeRepository, notificationManager);
  }

  async getPagedListWithFavoriteStores(
    currentDay: EDayOfWeek,
    currentTime: Date,
    categoryIdFilter: string,
    storeFilter: string,
    favoriteFilter: boolean,
    currentPage: number,
    pageSize: number,
    latitude: number,
    longitude: number,
  ): Promise<PagedResult<IListStoresDTO>> {
    const data = await this.storeRepository.getPagedListWithFavoriteStores(
      currentDay,
      currentTime,
      categoryIdFilter,
      storeFilter,
      favoriteFilter,
      currentPage,
      pageSize,
      latitude,
      longitude,
    );
    return data;
  }

  async create(store: IStore, attachments: { id: string }[]): Promise<boolean> {
    // const errors = await validate(store);
    // if (errors.length > 0) {
    //   this.notificationManager.addFromTypeorm("store", errors);
    //   return null;
    // }

    if (store.slug) {
      const slug = await this.storeRepository.find({
        where: { slug: store.slug },
      });
      if (slug.length > 0) {
        this.notificationManager.add("stores.create.errors", "slug_already_exists");
      }
    }

    const cnpj = await this.storeRepository.find({
      where: { cnpj: store.cnpj },
    });
    if (cnpj.length > 0) {
      this.notificationManager.add("stores.create.errors", "cnpj_required");
    }

    const checkAlreadyExists = await this.storeRepository.find({
      where: { email: store.email },
    });
    if (checkAlreadyExists.length > 0) {
      this.notificationManager.add("stores.create.errors", "email_alreadyExists");
    }

    // const errorStoreSettings = await validate(
    //   store.storeSettings as StoreSettings
    // );

    // if (errorStoreSettings.length > 0) {
    //   this.notificationManager.addFromTypeorm(
    //     "store_settings",
    //     errorStoreSettings
    //   );
    // }

    if (!this.isValid()) return false;

    // const listWorkingHours: StoreHours[] = [];

    // if (store.storeHours) {
    //   store.storeHours.days.forEach((item) => {
    //     item.workingHours.forEach((wh) => {
    //       const obj = StoreHours.create({
    //         dayOfWeek: item.name,
    //         open: wh.open,
    //         close: wh.close,
    //       });
    //       listWorkingHours.push(obj);
    //     });
    //   });

    //   delete store.storeHours;
    // }

    // const storeQ = mapper.map(store, CreateStoreViewModel, Store);
    // storeQ.storeHours = listWorkingHours;
    // if (store.storeSettings)
    //   storeQ.storeSettings = store.storeSettings as StoreSettings;

    const storeDB = await this.storeRepository.create(store);

    if (!storeDB) return false;
    if (this.isValid()) {
      if (attachments && attachments.length > 0) {
        const attachmentsAux = attachments.map((item) => item.id);

        await this.fileRepository.relateEntityFiles(storeDB.id, attachmentsAux);
      }
      console.log("store.attachments", attachments);
    }

    if (!storeDB) return false;

    return true;
  }

  async getBySlug(slug: string): Promise<IStore[]> {
    const store = await this.storeRepository.getBySlug(slug);
    return store;
  }

  async getByCnpj(cnpj: string): Promise<IStore | null> {
    const store = await this.storeRepository.getByCnpj(cnpj);
    return store;
  }

  async getStoreInfoById(storeId: string): Promise<IStore | null> {
    const store = await this.storeRepository.getStoreInfoById(storeId);

    return store;
  }

  async update(storeUpdate: IStore): Promise<boolean> {
    if (storeUpdate.slug) {
      const slug = await this.storeRepository.find({
        where: {
          AND: [{ slug: storeUpdate.slug }, { id: { not: storeUpdate.id } }],
        },
      });
      if (slug.length > 0) {
        this.notificationManager.add("stores.edit.errors", "slug_already_exists");
        return false;
      }
    }

    if (storeUpdate.cnpj) {
      const cnpj = await this.storeRepository.find({
        where: {
          AND: [{ cnpj: storeUpdate.cnpj }, { id: { not: storeUpdate.id } }],
        },
      });
      if (cnpj.length > 0) {
        this.notificationManager.add("stores.edit.errors", "cnpj_already_exists");
        return false;
      }
    }

    if (storeUpdate.email) {
      const cnpj = await this.storeRepository.find({
        where: {
          AND: [{ email: storeUpdate.email }, { id: { not: storeUpdate.id } }],
        },
      });
      if (cnpj.length > 0) {
        this.notificationManager.add("stores.edit.errors", "email_already_exists");
        return false;
      }
    }
    // if (storeUpdate.storeCategory) {
    //   const storesIds = storeUpdate.storeCategory?.map(
    //     (item) =>
    //       ({ storeId: id, categoryId: item.categoryId } as StoreCategory)
    //   );

    //   await this.deleteByStoreId(id);

    //   storeUpdate.storeCategory = undefined;

    //   if (this.isValid()) {
    //     await this.relateStoreCategory(storesIds);
    //   }
    // }

    const erasedPixKey = storeUpdate.pixKey === "";
    if (erasedPixKey) storeUpdate.pixKey = null;
    const result = await this.storeRepository.update(storeUpdate.id, storeUpdate);
    return !!result;
  }

  async relateStoreUser(storeId: string, userId: string): Promise<boolean> {
    const result = await this.storeRepository.relateStoreUser(storeId, userId);

    return result;
  }

  async deleteByStoreId(storeId: string): Promise<number> {
    const result = await this.storeCategoryRepository.deleteByStoreId(storeId);

    return result;
  }

  async getWithUser(idStore: string): Promise<IStore | null> {
    const store = await this.storeRepository.getWithUser(idStore);
    return store;
  }

  async getWithCategory(idStore: string): Promise<IStore | null> {
    const store = await this.storeRepository.getWithCategory(idStore);
    return store;
  }

  async getWithAddress(idStore: string): Promise<IStore | null> {
    const store = await this.storeRepository.getWithAddress(idStore);
    return store;
  }

  async getStoresByUserId(userId: string): Promise<IListUserStoreDTO[]> {
    const stores = await this.storeRepository.getStoresByUserId(userId);

    // if (stores.length < 1) {
    //   this.notificationManager.add("stores.create.errors", "store_notFound");
    // }

    return stores;
  }

  async getStoresNameByUserId(userId: string): Promise<IStoreNameDTO[]> {
    const stores = await this.storeRepository.getStoresNameByUserId(userId);

    // if (stores.length < 1) {
    //   this.notificationManager.add("generic.errors", "generic_message");
    // }

    return stores;
  }

  async getPaymentMethodsByStoreAndUser(storeId: string, userId: string): Promise<IPaymentMethodsDTO | null> {
    const methods: IPaymentMethodsDTO = { types: [], cards: [] };
    const storeData = await this.getStoreInfoById(storeId);

    if (!storeData) {
      this.notificationManager.add("stores.edit.errors", "store_notFound");
      return null;
    }

    const userCards = await this.cardRepository.getCardsByUserId(userId);
    const creditCards = userCards.filter((card) => card.method === ECardMethod.credit);
    // const debtCards = userCards.filter((card) => card.method === ECardMethod.debt);

    if (storeData.storeSettings?.credit) {
      methods.types.push(EPaymentMethod.credit);
      creditCards.forEach((card) => {
        const decryptedCard = decryptCardFromDB(card);
        const encryptedCard = encryptCardToSendApp(decryptedCard);
        methods.cards.push(encryptedCard);
      });
    }
    // if (storeData.storeSettings?.debt) {
    //   methods.types.push(EPaymentMethod.debt);
    //   debtCards.forEach((card) => {
    //     const decryptedCard = decryptCardFromDB(card);
    //     const encryptedCard = encryptCardToSendApp(decryptedCard);
    //     methods.cards.push(encryptedCard);
    //   });
    // }
    if (storeData.storeSettings?.pix) {
      methods.types.push(EPaymentMethod.pix);
    }

    return methods;
  }

  async getPagedListFront(
    currentPage: string,
    pageSize: string,
    filterValue?: string,
    orderBy?: string,
    sortDirection?: IOrder,
  ): Promise<{ result: IStore[]; totalCount: number; totalPages: number }> {
    const columnFilter = "name,cnpj,slug,email,phone,description";

    const { data } = await this.getPaged(columnFilter, filterValue, currentPage, pageSize, orderBy, sortDirection);

    return data;
  }

  async getWithAllDetailsFront(storeId: string): Promise<IStore | null> {
    const result = await this.storeRepository.getWithAllDetailsFront(storeId);

    return result;
  }

  async saveStoreModeration(storeModeration: ICreateStoreModerationDTO): Promise<boolean> {
    const store = await this.storeRepository.getWithUser(storeModeration.moderation.storeId);
    if (store) {
      store.activeByAdmin = storeModeration.activeByAdmin;
      const result = await this.storeRepository.update(storeModeration.moderation.storeId, {
        ...store,
        storeUsers: undefined,
      });

      if (result) {
        const moderationResult = await this.storeModerationRepository.create(storeModeration);
        if (moderationResult) {
          if (store.storeUsers) {
            const notification: INotificationDTO = {
              title: pt.notification.store_moderation_title,
              storeId: store.id,
              body: `${store.cnpj} - ${store.name}: ${storeModeration.moderation.reason}`,
              profile: EProfile.shopkeeper,
              type: EMessageType.moderation,
            };
            await Promise.all(
              store.storeUsers.map(async (storeUser) =>
                this.notificationService.sendNotificationToUser(storeUser.userId!, notification),
              ),
            );
          }

          return true;
        }
      }
    }
    return false;
  }

  async updateStoreCategories(id: string, storeCategory: IStoreCategory[]): Promise<boolean> {
    const response = await this.storeRepository.updateStoreCategories(id, storeCategory);

    return response;
  }

  async checkAvailabilityByStoreId(storeId: string): Promise<boolean> {
    const storeInfo = await this.storeRepository.checkAvailabilityByStoreId(storeId);

    const tolerance = storeInfo?.storeSettings?.toleranceOrder || 0;
    const currentDate = new Date(new Date().setHours(new Date().getHours() - 3));
    const limit = currentDate.setMinutes(currentDate.getMinutes() - tolerance);

    if (storeInfo?.storeHours) {
      return !storeInfo.storeHours.some((storeHour) => {
        const closeHour = new Date();
        // const hours = storeHour.close.split(":");
        limit > closeHour.setHours(storeHour.close.getHours(), storeHour.close.getMinutes());
      });
    }

    return false;
  }

  async deleteWithRelations(id: string): Promise<boolean> {
    const result = await this.storeRepository.deleteWithRelations(id);

    return result;
  }

  async exportStores(): Promise<Xlsx | null> {
    const stores = await this.storeRepository.getAllStoresToExport();

    if (!stores) return null;

    const storeHandled = handleStoresToExport(stores);

    const storesMapped = mapper.map(StoreMapper.ExportStoreDTOToExportStoresData, storeHandled);

    const xlsx = await exportStoresToExcel(storesMapped);

    return xlsx;
  }

  async importStores(xlsx: Buffer): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(xlsx);
    const sheet = workbook.getWorksheet("Data");

    let errors = "";
    if (sheet) {
      const stores: ICreateStoreByExportDTO[] = [];
      const rows = sheet.getRows(1, sheet.rowCount);
      if (!rows) {
        return "Erro: arquivo vazio.";
      }

      // eslint-disable-next-line no-restricted-syntax
      for (const row of rows) {
        // eslint-disable-next-line no-await-in-loop
        errors += await this.handleRowImport(row, stores);
      }

      if (!errors) {
        await this.storeRepository.createManyStores(stores);
      }
    }
    return errors;
  }

  async handleRowImport(row: ExcelJS.Row, stores: ICreateStoreByExportDTO[]): Promise<string> {
    let errors = "";
    if (row.number !== 1) {
      errors += validateColumn(row.number, 1, row.getCell(1).value, 1); // name
      errors += validateColumn(row.number, 2, row.getCell(2).value, 2); // cnpj
      errors += validateColumn(row.number, 3, row.getCell(3).value, 3); // slug
      errors += validateColumn(row.number, 4, row.getCell(4).value, 1); // email
      errors += validateColumn(row.number, 5, row.getCell(5).value, 2); // phone
      errors += validateColumn(row.number, 6, row.getCell(6).value, 3); // description
      errors += validateColumn(row.number, 7, row.getCell(7).value, 1); // district
      errors += validateColumn(row.number, 8, row.getCell(8).value, 1); // street
      errors += validateColumn(row.number, 9, row.getCell(9).value, 4); // number
      errors += validateColumn(row.number, 10, row.getCell(10).value, 3); // complement
      errors += validateColumn(row.number, 11, row.getCell(11).value, 1); // city
      errors += validateColumn(row.number, 12, row.getCell(12).value, 1); // state
      errors += validateColumn(row.number, 13, row.getCell(13).value, 1); // country
      errors += validateColumn(row.number, 14, row.getCell(14).value, 2); // postcode
      errors += validateColumn(row.number, 15, row.getCell(15).value, 3); // nickname
      errors += validateColumn(row.number, 16, row.getCell(16).value, 1); // category_name
      if (!errors) {
        const newStore: ICreateStoreByExportDTO = {
          name: String(row.getCell(1).value).trim(),
          cnpj: String(row.getCell(2).value).trim(),
          slug: String(row.getCell(3).value).trim(),
          email: String(row.getCell(4).value).trim(),
          phone: String(row.getCell(5).value).trim(),
          description: String(row.getCell(6).value).trim(),
          address: {
            street: String(row.getCell(8).value).trim(),
            number: String(row.getCell(9).value).trim(),
            complement: String(row.getCell(10).value).trim(),
            district: String(row.getCell(7).value).trim(),
            country: String(row.getCell(13).value).trim(),
            city: String(row.getCell(11).value).trim(),
            state: String(row.getCell(12).value).trim(),
            postcode: String(row.getCell(14).value).trim(),
            nickname: String(row.getCell(15).value).trim(),
          },
          storeCategory: [],
        };

        const indexStore = stores.findIndex(
          (store) =>
            store.name === newStore.name &&
            store.cnpj === newStore.cnpj &&
            store.slug === newStore.slug &&
            store.email === newStore.email &&
            store.phone === newStore.phone &&
            store.description === newStore.description,
        );

        const categoryName = String(row.getCell(16).value);

        const categoryDescription = String(row.getCell(16).value);

        if (categoryName !== "" && categoryName !== "null") {
          const categoryId = await this.handleCategory(categoryName, categoryDescription);
          if (indexStore === -1 && categoryId) {
            if (newStore.storeCategory?.findIndex((sc) => sc.categoryId === categoryId) === -1) {
              newStore.storeCategory?.push({ categoryId });
            }
          } else if (
            stores[indexStore].storeCategory?.findIndex((sc) => sc.categoryId === categoryId) === -1 &&
            categoryId
          ) {
            stores[indexStore].storeCategory?.push({ categoryId });
          }
        }

        if (indexStore === -1) {
          stores.push(newStore);
        }
      }
    }
    return errors;
  }

  async handleCategory(name: string, description: string): Promise<string | null> {
    const category = await this.categoryRepository.findFirst({
      where: { name, description },
    });

    if (category) {
      return category.id;
    }

    const newCategory = await this.categoryRepository.create({
      name,
      description,
    });

    if (newCategory) return newCategory.id;

    return null;
  }

  async getStoreShowcase(id: string): Promise<IStoreShowcaseWithUrlsDTO | null> {
    const store = await this.storeRepository.getStoreShowcase(id);

    if (!store) return null;

    const iconUrl = store?.files?.find((file) => file.type === EFileType.icon)?.url;
    const bannerUrl = store?.files?.find((file) => file.type === EFileType.bannerMobile)?.url;

    delete store?.files;

    return {
      ...store,
      iconUrl,
      bannerUrl,
    };
  }

  async getSelect(storeName: string, currentPage: number, pageSize: number): Promise<PagedResult<ISelectDTO>> {
    const stores = await this.storeRepository.getSelect(storeName, currentPage, pageSize);

    return stores;
  }

  async filterStoresByUserDistance(latitude: number, longitude: number, distance: number): Promise<string[]> {
    const storeIds = await this.storeRepository.filterStoresByUserDistance(latitude, longitude, distance);
    return storeIds;
  }

  async getStoreByCpfCnpj(cpfCnpj: string): Promise<IStore | null> {
    const store = await this.storeRepository.findFirst({ where: { cnpj: cpfCnpj } });
    return store;
  }

  async getHasStoreMissingPixKey(userId: string) {
    const hasStoresMissingPixKey = await this.storeRepository.getHasStoreMissingPixKey(userId);
    return hasStoresMissingPixKey;
  }
}
