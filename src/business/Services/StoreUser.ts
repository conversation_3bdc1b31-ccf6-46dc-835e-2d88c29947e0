import { EMessageType, EProfileStatus, EStoreModeratorStatus } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IStoreModeratorDTO } from "src/business/DTOs/StoreUser/IStoreModerator";
import { IValidateEmailDTO } from "src/business/DTOs/StoreUser/IValidateEmail";
import { IStoreUser } from "src/business/Interfaces/Prisma/IStoreUser";
import { IStoreUserRepository } from "src/business/Interfaces/Repository/IStoreUser";
import { IStoreUserService } from "src/business/Interfaces/Service/IStoreUser";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import pt from "src/business/Assets/Language/pt.json";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IStoreService } from "src/business/Interfaces/Service/IStore";

@injectable()
export class StoreUserService extends BaseService<IStoreUser> implements IStoreUserService {
  constructor(
    @inject(TOKENS.IStoreUserRepository)
    private storeUserRepository: IStoreUserRepository,
    @inject(TOKENS.IUserService)
    private userService: IUserService,
    @inject(TOKENS.IStoreService)
    private storeService: IStoreService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
  ) {
    super(storeUserRepository, notificationManager);
    this.storeUserRepository = storeUserRepository;
  }

  async create(storeId: string, email: string): Promise<boolean> {
    const userDB = await this.userService.getByEmail(email);

    if (!userDB?.shopkeeper || userDB?.shopkeeper.status !== EProfileStatus.approved) {
      this.notificationManager.add("stores.storeModerators.errors", "not_profile");
      return false;
    }

    const moderators = await this.storeUserRepository.getStoreUsersByStoreId(storeId);

    if (moderators.some((m) => m.userId === userDB.id)) {
      this.notificationManager.add("stores.storeModerators.errors", "is_already");
      return false;
    }

    const result = await this.storeUserRepository.create({
      owner: false,
      status: EStoreModeratorStatus.pending,
      userId: userDB.id,
      storeId,
    } as IStoreUser);

    if (result) {
      const store = await this.storeService.getById(storeId);
      this.notificationService.sendNotificationToUser(userDB.id, {
        title: pt.notification.store_moderator_title,
        body: pt.notification.store_moderator_add,
        params: {
          body: store?.name,
        },
        storeId,
        type: EMessageType.moderator,
        profile: EProfile.shopkeeper,
      });
      return true;
    }

    return false;
  }

  async getStoreUsersByStoreId(idStore: string): Promise<IStoreUser[]> {
    const storeUsers = this.storeUserRepository.getStoreUsersByStoreId(idStore);

    return storeUsers;
  }

  async getModeratorsByStoreId(idStore: string): Promise<IStoreModeratorDTO[]> {
    const storeModerators = this.storeUserRepository.getModeratorByStoreId(idStore);

    return storeModerators;
  }

  async setStatus(storeUserId: string, status: EStoreModeratorStatus): Promise<boolean> {
    const result = await this.storeUserRepository.setStatus(storeUserId, status);

    const moderatorSU = await this.storeUserRepository.findOne({
      where: { id: storeUserId },
      include: { store: true, user: true },
    });

    if (moderatorSU && result) {
      if (moderatorSU.user && moderatorSU.store) {
        switch (status) {
          case EStoreModeratorStatus.active:
            this.sendStatusChangeNotificationToUser(
              moderatorSU.userId,
              pt.notification.store_moderator_unlock,
              moderatorSU.store.name,
              moderatorSU.storeId,
            );
            break;
          case EStoreModeratorStatus.blocked:
            this.sendStatusChangeNotificationToUser(
              moderatorSU.userId,
              pt.notification.store_moderator_block,
              moderatorSU.store.name,
              moderatorSU.storeId,
              true,
            );
            break;
          default:
            break;
        }
      }
    }

    return result;
  }

  sendStatusChangeNotificationToUser(
    userId: string,
    message: string,
    param: string,
    storeId: string,
    blocked?: boolean,
  ) {
    this.notificationService.sendNotificationToUser(userId, {
      title: pt.notification.store_moderator_title,
      body: message,
      params: {
        body: param,
      },
      storeId,
      type: blocked ? EMessageType.moderator_blocked : EMessageType.moderator,
      profile: EProfile.shopkeeper,
    });
  }

  async deleteStoreUser(storeUserId: string, userId: string): Promise<boolean> {
    const userDB = await this.storeUserRepository.findOne({
      where: { id: storeUserId },
      include: { store: true, user: true },
    });
    const result = await this.storeUserRepository.delete(storeUserId);

    if (result && userDB) {
      const isModeratorAction = userDB.userId === userId;
      const ownerSU = await this.storeUserRepository.findFirst({
        where: {
          storeId: userDB.storeId,
          owner: true,
        },
        include: { user: true },
      });
      if (ownerSU) {
        const notificationBody = {
          title: pt.notification.store_moderator_title,
          body: isModeratorAction ? pt.notification.store_moderator_leave : pt.notification.store_moderator_remove,
          params: {
            body: isModeratorAction ? userDB.user?.firstName : userDB.store?.name,
          },
          storeId: userDB.storeId,
          type: EMessageType.moderator,
          profile: EProfile.shopkeeper,
        };

        if (isModeratorAction) {
          this.notificationService.sendNotificationToUser(ownerSU.userId, notificationBody);
        } else {
          this.notificationService.sendNotificationToUser(userDB.userId, notificationBody);
        }
      }
    }

    return result;
  }

  async refuseInvite(storeUserId: string): Promise<boolean> {
    const moderatorSU = await this.storeUserRepository.findOne({
      where: { id: storeUserId },
      include: { store: true, user: true },
    });
    const result = await this.storeUserRepository.delete(storeUserId);

    if (result && moderatorSU) {
      const ownerSU = await this.storeUserRepository.findFirst({
        where: {
          storeId: moderatorSU.storeId,
          owner: true,
        },
        include: { user: true },
      });

      if (ownerSU && ownerSU.user) {
        this.sendStatusChangeNotificationToUser(
          ownerSU.userId,
          pt.notification.store_moderator_refuse,
          ownerSU.user.firstName.toLowerCase(),
          ownerSU.storeId,
        );
      }
    }

    return true;
  }

  async acceptInvite(storeUserId: string): Promise<boolean> {
    const moderatorSU = await this.storeUserRepository.findOne({
      where: { id: storeUserId },
      include: { store: true, user: true },
    });
    const result = await this.storeUserRepository.setStatus(storeUserId, EStoreModeratorStatus.active);

    if (result && moderatorSU) {
      const ownerSU = await this.storeUserRepository.findFirst({
        where: {
          storeId: moderatorSU.storeId,
          owner: true,
        },
        include: { user: true },
      });

      if (ownerSU && ownerSU.user) {
        this.sendStatusChangeNotificationToUser(
          ownerSU.userId,
          pt.notification.store_moderator_accept,
          ownerSU.user.firstName.toUpperCase(),
          ownerSU.storeId,
        );
      }
    }

    return true;
  }

  async validateEmail(email: string, storeId: string): Promise<IValidateEmailDTO> {
    const user = await this.userService.getByEmail(email);

    if (!user) {
      return { isValid: false, error: "not_found" };
    }

    if (!user?.shopkeeper || user?.shopkeeper.status !== EProfileStatus.approved) {
      return { isValid: false, error: "not_profile" };
    }

    const moderators = await this.storeUserRepository.getStoreUsersByStoreId(storeId);

    if (moderators.some((m) => m.userId === user.id)) {
      return { isValid: false, error: "is_already" };
    }

    return { isValid: true };
  }
}
