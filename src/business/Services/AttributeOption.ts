import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import { IAttributeOptionRepository } from "src/business/Interfaces/Repository/IAttributeOption";
import { IAttributeOptionService } from "src/business/Interfaces/Service/IAttributeOption";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class AttributeOptionService extends BaseService<IAttributeOption> implements IAttributeOptionService {
  private attributeOptionRepository: IAttributeOptionRepository;

  constructor(
    @inject(TOKENS.IAttributeOptionRepository)
    attributeOptionRepository: IAttributeOptionRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(attributeOptionRepository, notificationManager);
    this.attributeOptionRepository = attributeOptionRepository;
  }

  async create(option: IAttributeOption): Promise<boolean> {
    const result = await this.attributeOptionRepository.create(option);

    if (result) return true;

    return false;
  }
}
