import { inject, injectable } from "inversify";
import { IProfileService } from "src/business/Interfaces/Service/IProfile";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
// import { Profile } from "src/business/Models/Profile.model";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IDeliverymanRepository } from "src/business/Interfaces/Repository/IDeliveryman";
import { IProfileRepository } from "src/business/Interfaces/Repository/IProfile";
import { IShopkeeperRepository } from "src/business/Interfaces/Repository/IShopkeeper";
import { BaseService } from "src/business/Services/Base";
import ProfilesSingleton from "src/business/Singletons/Profile";
import { IProfile } from "src/business/Interfaces/Prisma/IProfile";

@injectable()
export class ProfileService extends BaseService<IProfile> implements IProfileService {
  private profileRepository: IProfileRepository;

  private profilesSingleton: ProfilesSingleton;

  private deliverymanRepository: IDeliverymanRepository;

  private shopkeeperRepository: IShopkeeperRepository;

  constructor(
    @inject(TOKENS.IProfileRepository) profileRepository: IProfileRepository,
    @inject(TOKENS.ProfilesSingleton)
    profilesSingleton: ProfilesSingleton,
    @inject(TOKENS.IDeliverymanRepository)
    deliverymanRepository: IDeliverymanRepository,
    @inject(TOKENS.IShopkeeperRepository)
    shopkeeperRepository: IShopkeeperRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(profileRepository, notificationManager);
    this.profileRepository = profileRepository;
    this.profilesSingleton = profilesSingleton;
    this.deliverymanRepository = deliverymanRepository;
    this.shopkeeperRepository = shopkeeperRepository;
  }

  async create(data: IProfile): Promise<boolean> {
    await this.profileRepository.create(data);

    return true;
  }

  async getWithPermission(id: string) {
    const profile = await this.profileRepository.findByIdWithPermission(id);

    return profile;
  }

  async loadSingleton() {
    const allProfiles = await this.profileRepository.getAll();

    const profiles = await this.getOrCreate(allProfiles);

    this.profilesSingleton.load(profiles);
  }

  private async getOrCreate(profiles: IProfile[]): Promise<IProfile[]> {
    return new Promise<IProfile[]>((resolve) => {
      if (profiles && profiles.length > 0) {
        resolve(profiles);
      } else {
        const profileEnumKeys = Object.keys(EProfile).filter((p) => Number.isNaN(Number(p)));

        Promise.all(
          profileEnumKeys.map(async (key) => {
            const createdProfile = await this.profileRepository.create({
              name: key,
            } as IProfile);

            profiles.push(createdProfile);
          }),
        ).finally(() => {
          resolve(profiles);
        });
      }
    });
  }

  async getApprovedProfiles(userId: string): Promise<IProfile[]> {
    const profiles: IProfile[] = [];

    if (this.profilesSingleton.client) {
      profiles.push(this.profilesSingleton.client);
    }

    const deliveryman = await this.deliverymanRepository.isDeliverymanApproved(userId);
    if (deliveryman && this.profilesSingleton.deliveryman) {
      profiles.push(this.profilesSingleton.deliveryman);
    }

    const shopkeeper = await this.shopkeeperRepository.isShopkeeperApproved(userId);
    if (shopkeeper && this.profilesSingleton.shopkeeper) {
      profiles.push(this.profilesSingleton.shopkeeper);
    }

    return profiles;
  }
}
