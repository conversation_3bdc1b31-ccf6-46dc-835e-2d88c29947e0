import { GetEndpointAttributesCommandOutput } from "@aws-sdk/client-sns";
import { EDeviceType, ELanguageOptions } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IDevice } from "src/business/Interfaces/Prisma/IDevice";
import { IDeviceRepository } from "src/business/Interfaces/Repository/IDevice";
import { ISNSService } from "src/business/Interfaces/Service/AWS/ISNS";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import ProfilesSingleton from "src/business/Singletons/Profile";

@injectable()
export class DeviceService extends BaseService<IDevice> implements IDeviceService {
  protected notificationManager: INotificationManager;

  constructor(
    @inject(TOKENS.IDeviceRepository)
    private deviceRepository: IDeviceRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.ISNSService)
    private snsService: ISNSService,
    @inject(TOKENS.ProfilesSingleton)
    private profilesSingleton: ProfilesSingleton,
    @inject(TOKENS.LoggerService)
    private loggerService: ILoggerService,
  ) {
    super(deviceRepository, notificationManager);
    this.notificationManager = notificationManager;
  }

  async hasDeviceRegisteredByUserId(userId: string) {
    const devices = await this.find({ where: { userId } });

    const result = devices.some((device) => device.deviceToken && device.endpointArn && device.active);

    return result;
  }

  async getUserActiveDevices(userId: string): Promise<IDevice[]> {
    const devices = await this.find({
      where: { userId, active: true },
    });

    return devices;
  }

  async getUserDevice(userId: string, deviceId: string): Promise<IDevice | null> {
    const device = await this.deviceRepository.findOne({
      where: { deviceId_userId: { deviceId, userId } },
    });

    return device;
  }

  async subscribeMultipleEndpointsToTopic(topicArn: string, devices?: IDevice[]) {
    const selectedDevices = devices || (await this.deviceRepository.getAll());
    let subscriptionArn: string = "";

    if (selectedDevices.length > 0) {
      const requestMap = selectedDevices.map((device) => {
        const promise: Promise<IDevice> = new Promise((res, rej) => {
          this.snsService
            .subscribeDeviceToATopic({
              Protocol: "application",
              TopicArn: topicArn,
              Endpoint: device.endpointArn,
              ReturnSubscriptionArn: true,
            })
            .then(({ SubscriptionArn }) => {
              if (SubscriptionArn) {
                device.subscriptionArn = SubscriptionArn;
                subscriptionArn = SubscriptionArn;
              }
              res(device);
            })
            .catch((error) => {
              console.log("subscription error", error);

              this.loggerService.logError(error);

              rej(error);
            });
        });

        return promise;
      });

      const result = await Promise.all(requestMap);
      console.log("subscribeMultipleEndpointsToTopic", result);

      await this.deviceRepository.updateMany({
        data: {
          subscriptionArn,
        },
        where: {
          id: {
            in: result.map((device) => device.id || ""),
          },
        },
      });
      return result;
    }
    return [];
  }

  async subscribeMultipleDevicesToTopic(topicArn: string, devices?: IDevice[]) {
    const selectedDevices = devices || (await this.deviceRepository.getAll());
    if (selectedDevices.length > 0) {
      const requestMap = selectedDevices.map((device) => {
        const promise = new Promise((res, rej) => {
          this.snsService
            .subscribeDeviceToATopic({
              Protocol: "application",
              TopicArn: topicArn,
              Endpoint: device.endpointArn,
              ReturnSubscriptionArn: true,
            })
            .then((result) => {
              res(result);
            })
            .catch((error) => {
              console.log("subscription error", error);

              this.loggerService.logError(error);

              rej(error);
            });
        });

        return promise;
      });
      const result = await Promise.all(requestMap);
      return result;
    }
    return [];
  }

  async subscribeMultipleDeliverymenToTopic(topicArn: string, devices: IDevice[]) {
    const selectedDevices = devices;
    let deliverymanSubscriptionArn: string = "";

    if (selectedDevices.length > 0) {
      const requestMap = selectedDevices.map((device) => {
        const promise: Promise<IDevice> = new Promise((res, rej) => {
          this.snsService
            .subscribeDeviceToATopic({
              Protocol: "application",
              TopicArn: topicArn,
              Endpoint: device.endpointArn,
              ReturnSubscriptionArn: true,
            })
            .then(({ SubscriptionArn }) => {
              if (SubscriptionArn) {
                device.deliverymanSubscriptionArn = SubscriptionArn;
                deliverymanSubscriptionArn = SubscriptionArn;
              }
              res(device);
            })
            .catch((error) => {
              console.log("subscription error", error);

              this.loggerService.logError(error);

              rej(error);
            });
        });

        return promise;
      });

      const result = await Promise.all(requestMap);
      console.log("subscribeMultipleDeliverymenToTopic", result);

      await this.deviceRepository.updateMany({
        data: {
          deliverymanSubscriptionArn,
        },
        where: {
          id: {
            in: result.map((device) => device.id || ""),
          },
        },
      });
      return result;
    }
    return [];
  }

  async unSubscribeMultipleEndpointsFromTopic(devices?: IDevice[]) {
    const selectedDevices = devices || (await this.deviceRepository.getAll());

    if (selectedDevices.length > 0) {
      const requestMap = selectedDevices.map((device) => {
        const promise: Promise<IDevice> = new Promise((res, rej) => {
          if (device.subscriptionArn) {
            this.snsService
              .unsubscribeDeviceFromTopic({
                SubscriptionArn: device.subscriptionArn,
              })
              .then(() => {
                delete device.subscriptionArn;
                res(device);
              })
              .catch((error) => {
                console.log("unsubscribe device error", error);

                this.loggerService.logError(error);

                rej(error);
              });
          }
        });

        return promise;
      });

      const result = await Promise.all(requestMap);
      await this.deviceRepository.updateMany({
        data: {
          subscriptionArn: null,
        },
        where: {
          id: {
            in: result.map((device) => device.id || ""),
          },
        },
      });
    }
  }

  async getSubscriptionsDevicesFromTopic(topicArn: string) {
    const data = await this.snsService.getSubscriptionsDevicesFromTopic({
      TopicArn: topicArn,
    });
    return data.Subscriptions;
  }

  async getDeliverymanDevices(deliveryManProfileId: string, topicArn?: string): Promise<IDevice[]> {
    const devices = await this.deviceRepository.getDeliverymanDevices(deliveryManProfileId);
    return devices;
  }

  async registerNotificationService(
    userId: string,
    deviceToken: string,
    deviceUniqueId: string,
    deviceManufacturer: string,
  ): Promise<boolean> {
    let device: IDevice = {} as IDevice;
    const deviceType = deviceManufacturer === "Apple" ? EDeviceType.IOS : EDeviceType.ANDROID;

    try {
      const existingDevice = await this.findOne({
        where: { deviceId_userId: { deviceId: deviceUniqueId, userId } },
      });

      if (!existingDevice) {
        device = {
          userId,
          deviceToken,
          deviceId: deviceUniqueId,
          type: deviceType,
          active: true,
          endpointArn: "",
          profileId: this.profilesSingleton[EProfile.client].id,
          language: ELanguageOptions.PT,
        };
      }

      if (existingDevice) {
        device =
          existingDevice.deviceToken !== deviceToken
            ? { ...existingDevice, deviceToken, active: true }
            : { ...existingDevice, active: true };
      }

      const result = await this.registerEndpoint(deviceToken, device, !!existingDevice);
      if (result) {
        await this.deviceRepository.disableOldDevices(
          userId,
          deviceUniqueId,
          existingDevice ? device.profileId : this.profilesSingleton[EProfile.client].id,
        );
      }
      return result;
    } catch (error) {
      console.log(error);
      this.notificationManager.add("", "");
      return false;
    }
  }

  private async registerEndpoint(deviceToken: string, device: IDevice, existingDevice: boolean) {
    const snsResponse = await this.snsService.createEndpoint({
      PlatformApplicationArn: process.env.AWS_SNS_PLATFORM_APPLICATION_ARN_FCM!,
      Token: deviceToken,
    });

    const { EndpointArn } = snsResponse;

    if (EndpointArn) {
      device.endpointArn = EndpointArn;

      if (existingDevice) {
        await this.deviceRepository.updateDevice(device);
        return true;
      }

      await this.deviceRepository.create(device);
      return true;
    }

    this.notificationManager.add("", "");
    return false;
  }

  async getNotificationEndpointAttributes(
    userId: string,
    deviceId: string,
  ): Promise<GetEndpointAttributesCommandOutput["Attributes"] | null> {
    const device = await this.deviceRepository.findOne({
      where: { deviceId_userId: { deviceId, userId } },
    });

    if (device) {
      const details = await this.snsService.getEndpointAttributes({
        EndpointArn: device.endpointArn,
      });

      return details.Attributes;
    }

    // TODO Notificate message
    this.notificationManager.add("", "");
    return null;
  }

  async disableUserDevice(userId: string, deviceId: string): Promise<boolean> {
    const userDevice = await this.getUserDevice(userId, deviceId);

    try {
      if (userDevice) {
        userDevice.active = false;

        await this.deviceRepository.updateDevice(userDevice);
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  async getByDeviceId(deviceId: string): Promise<IDevice[]> {
    const result = await this.deviceRepository.getByDeviceId(deviceId);

    return result;
  }

  async updateDeviceLanguage(deviceId: string, language: ELanguageOptions): Promise<boolean> {
    const devices = await this.deviceRepository.getByDeviceId(deviceId);

    let updateCount = 0;
    if (devices.length > 0) {
      devices.map(async (device) => {
        device.language = language;
        const result = await this.deviceRepository.updateDeviceLanguage(device);

        if (result) {
          updateCount += 1;
        }
      });
    }

    return updateCount > 0;
  }

  async updateDeviceDefaultProfile(deviceId: string, profile: EProfile): Promise<boolean> {
    let response = false;

    const devices = await this.deviceRepository.getByDeviceId(deviceId);

    if (profile && devices.length > 0) {
      const profileId = this.profilesSingleton[profile].id;

      if (profile !== EProfile.deliveryman) {
        this.unSubscribeMultipleEndpointsFromTopic(devices);
      }
      devices.forEach(async (device) => {
        if (device.id) {
          device.profileId = profileId;

          const updateResponse = await this.deviceRepository.update(device.id, device);

          if (updateResponse) {
            response = true;
          }
        }
      });
    }

    return response;
  }

  async getUserActiveDevicesByProfile(userId: string, profile: EProfile): Promise<IDevice[]> {
    if (profile) {
      const profileId = this.profilesSingleton[profile].id;
      const devices = await this.find({
        where: { userId, active: true, profileId },
      });
      return devices;
    }
    return [];
  }

  async getDeviceLanguage(userId: string, deviceId: string): Promise<ELanguageOptions> {
    const device = await this.deviceRepository.findOne({
      where: { deviceId_userId: { deviceId, userId } },
    });

    if (device && device.language) {
      return device.language;
    }

    return ELanguageOptions.PT;
  }

  async getUsersDevicesByUserId(userIds: string[]): Promise<IDevice[] | null> {
    const devices = await this.deviceRepository.getUsersDevicesByUserId(userIds);

    if (devices) return devices;

    return null;
  }

  async getUserDevicesByUserId(userId: string): Promise<IDevice[] | null> {
    const devices = await this.deviceRepository.getUserDevicesByUserId(userId);

    if (devices) return devices;

    return null;
  }

  async associateDevicesUserToTopic(topicArn: string, userId: string): Promise<boolean> {
    const devices = await this.getUserDevicesByUserId(userId);
    if (devices) {
      const devicesSubscribed = await this.subscribeMultipleDevicesToTopic(topicArn, devices);

      if (devicesSubscribed) {
        return true;
      }
    }

    return false;
  }
}
