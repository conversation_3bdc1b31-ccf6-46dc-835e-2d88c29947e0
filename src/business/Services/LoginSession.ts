/* eslint-disable no-underscore-dangle */
import { lastDayOfWeek, startOfWeek } from "date-fns";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ILoginSessionAggregateDTO } from "src/business/DTOs/LoginSession/ILoginSession";
import { ILoginSessionInfoDTO } from "src/business/DTOs/LoginSession/ILoginSessionInfo";
import { ESettingsKey } from "src/business/Enums/ESettingsKey";
import { ILoginSession } from "src/business/Interfaces/Prisma/ILoginSession";
import { ILoginSessionRepository } from "src/business/Interfaces/Repository/ILoginSession";
import { ILoginSessionService } from "src/business/Interfaces/Service/ILoginSession";
import { ISettingsService } from "src/business/Interfaces/Service/ISettings";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class LoginSessionService extends BaseService<ILoginSession> implements ILoginSessionService {
  constructor(
    @inject(TOKENS.ILoginSessionRepository)
    private loginSessionRepository: ILoginSessionRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.ISettingsService)
    private settingsService: ISettingsService,
  ) {
    super(loginSessionRepository, notificationManager);
    this.loginSessionRepository = loginSessionRepository;
  }

  async create(session: ILoginSession): Promise<boolean> {
    const loginActive = await this.loginSessionRepository.getActiveLoginSession(
      session.userId,
      await this.getMaxInactiveDateTime(),
    );

    if (!loginActive) {
      const newSession = await this.loginSessionRepository.create(session);
      return !!newSession;
    }

    return true;
  }

  async updateLastAccess(userId: string): Promise<boolean> {
    const result = await this.loginSessionRepository.updateLastAccess(userId);
    return !!result;
  }

  async getAllLoginSessionsInAWeek(): Promise<ILoginSession[]> {
    const { firstWeekDay, lastWeekDay } = this.getCurrentWeekDateRange();
    const result = await this.loginSessionRepository.getAllLoginSessionsInAWeek(firstWeekDay, lastWeekDay);
    return result;
  }

  async getNumberOfAccessByDay(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]> {
    const result = await this.loginSessionRepository.getNumberOfAccessByDay(startDate, endDate);
    return result;
  }

  async getNumberOfAccessByTime(startDate: Date, endDate: Date): Promise<ILoginSessionAggregateDTO[]> {
    const result = await this.loginSessionRepository.getNumberOfAccessByTime(startDate, endDate);
    return result;
  }

  async getNumberOfAccessAndMaxHour(): Promise<ILoginSessionInfoDTO> {
    const { firstWeekDay, lastWeekDay } = this.getCurrentWeekDateRange();
    const maxHourAccess = await this.loginSessionRepository.getHourWithMostAccess(firstWeekDay, lastWeekDay);
    const totalAccess = await this.loginSessionRepository.getTotalNumberOfAccess(firstWeekDay, lastWeekDay);

    return { totalAccess, maxHourAccess };
  }

  private async getMaxInactiveDateTime() {
    const configInDb = await this.settingsService.getByName(ESettingsKey.maxUserInactiveTime);
    const config = Number(configInDb?.value || 10);
    const currentDate = new Date();
    currentDate.setMinutes(currentDate.getMinutes() - config);
    return currentDate;
  }

  private getCurrentWeekDateRange() {
    const firstWeekDay = startOfWeek(new Date());
    const lastWeekDay = lastDayOfWeek(new Date());
    return { firstWeekDay, lastWeekDay };
  }
}
