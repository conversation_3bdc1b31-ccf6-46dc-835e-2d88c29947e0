import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICustomerProfile } from "src/business/Interfaces/Prisma/ICustomerProfile";
import { ICustomerProfileRepository } from "src/business/Interfaces/Repository/ICustomerProfile";
import { ICustomerProfileService } from "src/business/Interfaces/Service/ICustomerProfile";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class CustomerProfileService extends BaseService<ICustomerProfile> implements ICustomerProfileService {
  constructor(
    @inject(TOKENS.ICustomerProfileRepository)
    private customerProfileRepository: ICustomerProfileRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(customerProfileRepository, notificationManager);
  }
}
