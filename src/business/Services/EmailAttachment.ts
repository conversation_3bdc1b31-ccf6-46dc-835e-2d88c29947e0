import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IEmailAttachment } from "src/business/Interfaces/Prisma/IEmailAttachment";
import { IEmailAttachmentRepository } from "src/business/Interfaces/Repository/IEmailAttachment";
import { IEmailAttachmentService } from "src/business/Interfaces/Service/IEmailAttachment";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class EmailAttachmentService extends BaseService<IEmailAttachment> implements IEmailAttachmentService {
  constructor(
    @inject(TOKENS.IEmailAttachmentRepository)
    private emailAttachmentRepository: IEmailAttachmentRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(emailAttachmentRepository, notificationManager);
    this.emailAttachmentRepository = emailAttachmentRepository;
  }

  async create(data: IEmailAttachment): Promise<boolean> {
    const result = await this.emailAttachmentRepository.create(data);

    return !!result;
  }
}
