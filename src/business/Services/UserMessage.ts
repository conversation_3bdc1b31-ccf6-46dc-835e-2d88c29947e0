import { EMessageStatus } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IUserMessage } from "src/business/Interfaces/Prisma/IUserMessage";
import { IUserMessageRepository } from "src/business/Interfaces/Repository/IUserMessage";
import { IUserMessageService } from "src/business/Interfaces/Service/IUserMessage";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class UserMessageService extends BaseService<IUserMessage> implements IUserMessageService {
  constructor(
    @inject(TOKENS.IUserMessageRepository)
    private userMessageRepository: IUserMessageRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(userMessageRepository, notificationManager);
  }

  async updateStatus(messageId: string, profileId: string, status: EMessageStatus): Promise<number> {
    const result = await this.userMessageRepository.updateStatus(messageId, profileId, status);

    return result;
  }

  async updateMultipleMessages(messageIds: string[], profileId: string, status: EMessageStatus): Promise<number> {
    let count = 0;

    messageIds.forEach(async (item) => {
      const result = await this.userMessageRepository.updateStatus(item, profileId, status);
      count += result;
    });

    return count;
  }
}
