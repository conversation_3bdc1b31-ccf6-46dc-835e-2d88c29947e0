// https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/clients/client-cognito-identity/
import crypto from "crypto";
import {
  AdminConfirmSignUpCommandInput,
  AdminConfirmSignUpCommandOutput,
  AdminCreateUserCommandInput,
  AdminCreateUserCommandOutput,
  AdminDeleteUserCommandInput,
  AdminDeleteUserCommandOutput,
  AdminInitiateAuthCommandInput,
  AdminInitiateAuthCommandOutput,
  AdminRespondToAuthChallengeCommandInput,
  AdminRespondToAuthChallengeCommandOutput,
  AdminUpdateUserAttributesCommandInput,
  AdminUpdateUserAttributesCommandOutput,
  ChangePasswordCommandInput,
  CognitoIdentityProvider,
  ConfirmForgotPasswordCommandInput,
  ForgotPasswordCommandInput,
  GetUserAttributeVerificationCodeCommandInput,
  GetUserAttributeVerificationCodeCommandOutput,
  GetUserCommandInput,
  GetUserCommandOutput,
  RevokeTokenCommandInput,
  RevokeTokenCommandOutput,
  SignUpCommandInput,
  SignUpCommandOutput,
  VerifyUserAttributeCommandInput,
  VerifyUserAttributeCommandOutput,
} from "@aws-sdk/client-cognito-identity-provider";
import axios from "axios";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ExchangeCodeResponse, ICognitoService } from "src/business/Interfaces/Service/AWS/ICognito";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";

@injectable()
export class CognitoService implements ICognitoService {
  private cognitoIdentity: CognitoIdentityProvider;

  private userPoolId: string;

  private clientId: string;

  private clientSecret: string;

  constructor(@inject(TOKENS.LoggerService) private loggerService: ILoggerService) {
    this.cognitoIdentity = new CognitoIdentityProvider({
      region: process.env.AWS_REGION!,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    });

    this.userPoolId = process.env.AWS_COGNITO_USER_POOL_ID!;
    this.clientId = process.env.AWS_COGNITO_CLIENT_ID!;
    this.clientSecret = process.env.AWS_COGNITO_CLIENT_SECRET!;
  }

  async checkIfUserExists(username: string): Promise<boolean> {
    try {
      const response = await this.cognitoIdentity.adminGetUser({
        Username: username,
        UserPoolId: this.userPoolId,
      });

      return !!response;
    } catch (error) {
      console.log("User not found in cognito user pool");

      this.loggerService.logError(error);

      return false;
    }
  }

  async createUser(args: Omit<SignUpCommandInput, "ClientId" | "SecretHash">): Promise<SignUpCommandOutput> {
    if (!args.Username) {
      throw new Error("Username is required");
    }

    const response = await this.cognitoIdentity.signUp({
      ...args,
      ClientId: this.clientId,
      SecretHash: this.hashSecret(this.clientSecret, this.clientId, args.Username),
    });

    const attributes = [
      {
        Name: "email_verified",
        Value: "false",
      },
      {
        Name: "phone_number_verified",
        Value: "false",
      },
    ];

    await this.cognitoIdentity.adminUpdateUserAttributes({
      Username: args.Username,
      UserPoolId: this.userPoolId,
      UserAttributes: attributes,
    });

    return response;
  }

  async adminCreateUser(args: Omit<AdminCreateUserCommandInput, "UserPoolId">): Promise<AdminCreateUserCommandOutput> {
    const response = await this.cognitoIdentity.adminCreateUser({
      ...args,
      UserPoolId: this.userPoolId,
    });

    return response;
  }

  async adminRespondToAuthChallenge(
    args: Omit<AdminRespondToAuthChallengeCommandInput, "UserPoolId" | "ClientId">,
  ): Promise<AdminRespondToAuthChallengeCommandOutput> {
    if (!args.ChallengeResponses?.USERNAME) {
      throw new Error("Username is required");
    }
    Object.assign(args.ChallengeResponses, {
      SECRET_HASH: this.hashSecret(this.clientSecret, this.clientId, args.ChallengeResponses.USERNAME),
    });

    const response = await this.cognitoIdentity.adminRespondToAuthChallenge({
      ...args,
      ClientId: this.clientId,
      UserPoolId: this.userPoolId,
    });

    return response;
  }

  async confirmSignUp(
    args: Omit<AdminConfirmSignUpCommandInput, "UserPoolId">,
  ): Promise<AdminConfirmSignUpCommandOutput> {
    const response = await this.cognitoIdentity.adminConfirmSignUp({
      ...args,
      UserPoolId: this.userPoolId,
    });

    return response;
  }

  async initiateAuth(
    args: Omit<AdminInitiateAuthCommandInput, "UserPoolId" | "ClientId">,
  ): Promise<AdminInitiateAuthCommandOutput> {
    if (!args.AuthParameters?.USERNAME) {
      throw new Error("Username is required");
    }

    Object.assign(args.AuthParameters, {
      SECRET_HASH: this.hashSecret(this.clientSecret, this.clientId, args.AuthParameters.USERNAME),
    });

    const response = await this.cognitoIdentity.adminInitiateAuth({
      ...args,
      ClientId: this.clientId,
      UserPoolId: this.userPoolId,
    });

    return response;
  }

  async updateUser(
    args: Omit<AdminUpdateUserAttributesCommandInput, "UserPoolId">,
  ): Promise<AdminUpdateUserAttributesCommandOutput> {
    const response = await this.cognitoIdentity.adminUpdateUserAttributes({
      ...args,
      UserPoolId: this.userPoolId,
    });

    return response;
  }

  async updatePassword(passwordParams: ChangePasswordCommandInput): Promise<void> {
    await this.cognitoIdentity.changePassword(passwordParams);
  }

  async deleteUser(args: Omit<AdminDeleteUserCommandInput, "UserPoolId">): Promise<AdminDeleteUserCommandOutput> {
    const response = await this.cognitoIdentity.adminDeleteUser({
      ...args,
      UserPoolId: this.userPoolId,
    });

    return response;
  }

  async revokeAccess(
    args: Omit<RevokeTokenCommandInput, "ClientId" | "ClientSecret">,
  ): Promise<RevokeTokenCommandOutput> {
    const response = await this.cognitoIdentity.revokeToken({
      ...args,
      ClientId: this.clientId,
      ClientSecret: this.clientSecret,
    });

    return response;
  }

  async exchangeCode(code: string): Promise<ExchangeCodeResponse | null> {
    try {
      const response = await axios.post<ExchangeCodeResponse>(
        process.env.AWS_COGNITO_CODE_EXCHANGE_URL!,
        {},
        {
          headers: {
            "content-type": "application/x-www-form-urlencoded",
            authorization: `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString("base64")}`,
          },
          params: {
            code,
            redirect_uri: process.env.AWS_COGNITO_REDIRECT_URI!,
          },
        },
      );
      return response.data;
    } catch (error) {
      console.log(error.response);

      this.loggerService.logError(error);

      return null;
    }
  }

  async resetPassword(args: Omit<ForgotPasswordCommandInput, "ClientId" | "SecretHash">) {
    if (!args.Username) {
      throw new Error("Username is required");
    }

    const response = await this.cognitoIdentity.forgotPassword({
      ...args,
      ClientId: this.clientId,
      SecretHash: this.hashSecret(this.clientSecret, this.clientId, args.Username),
    });

    return response;
  }

  async confirmPassword(args: Omit<ConfirmForgotPasswordCommandInput, "ClientId" | "SecretHash">) {
    if (!args.Username) {
      throw new Error("Username is required");
    }

    const response = await this.cognitoIdentity.confirmForgotPassword({
      ...args,
      ClientId: this.clientId,
      SecretHash: this.hashSecret(this.clientSecret, this.clientId, args.Username),
    });

    return response;
  }

  private hashSecret(clientSecret: string, clientId: string, username: string) {
    return crypto
      .createHmac("SHA256", clientSecret)
      .update(username + clientId)
      .digest("base64");
  }

  async resendEmailConfirmation(
    args: Omit<GetUserAttributeVerificationCodeCommandInput, "SecretHash" | "AnalyticsMetadata">,
  ): Promise<GetUserAttributeVerificationCodeCommandOutput> {
    const response = await this.cognitoIdentity.getUserAttributeVerificationCode(args);
    return response;
  }

  async verifyUserEmail(args: VerifyUserAttributeCommandInput): Promise<VerifyUserAttributeCommandOutput> {
    const response = await this.cognitoIdentity.verifyUserAttribute(args);
    return response;
  }

  async getEmailStatus(args: GetUserCommandInput): Promise<GetUserCommandOutput> {
    const response = await this.cognitoIdentity.getUser(args);
    return response;
  }
}
