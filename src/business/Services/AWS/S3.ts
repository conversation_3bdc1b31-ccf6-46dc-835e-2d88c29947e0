// https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/clients/client-s3/classes/s3.html
import {
  DeleteObjectCommandInput,
  DeleteObjectCommandOutput,
  PutObjectCommandInput,
  PutObjectCommandOutput,
  S3,
} from "@aws-sdk/client-s3";
import { injectable } from "inversify";
import { IS3Service } from "src/business/Interfaces/Service/AWS/IS3";

@injectable()
export class S3Service implements IS3Service {
  private s3: S3;

  constructor() {
    if (
      !process.env.AWS_REGION ||
      !process.env.AWS_ACCESS_KEY_ID ||
      !process.env.AWS_SECRET_ACCESS_KEY
    ) {
      throw new Error("AWS environment variables are not set");
    }

    this.s3 = new S3({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
  }

  async putObject(
    args: PutObjectCommandInput
  ): Promise<PutObjectCommandOutput> {
    return this.s3.putObject(args);
  }

  async deleteObject(
    args: Omit<DeleteObjectCommandInput, "Bucket">
  ): Promise<DeleteObjectCommandOutput> {
    return this.s3.deleteObject({
      ...args,
      Bucket: process.env.AWS_BUCKET_NAME,
    });
  }
}
