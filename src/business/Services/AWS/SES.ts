import {
  SendEmailCommandOutput,
  SendEmailRequest,
  SES,
} from "@aws-sdk/client-ses";
import { injectable } from "inversify";
import ISendEmail from "src/business/DTOs/SendEmail";
import { ETypeEmail } from "src/business/Enums/Models/ETypeEmail";
import { ISESService } from "src/business/Interfaces/Service/AWS/ISES";

@injectable()
export class SESService implements ISESService {
  private ses: SES;

  constructor() {
    if (
      !process.env.AWS_REGION ||
      !process.env.AWS_ACCESS_KEY_ID ||
      !process.env.AWS_SECRET_ACCESS_KEY
    ) {
      throw new Error("AWS environment variables are not set");
    }

    this.ses = new SES({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
  }

  async sendEmail(email: ISendEmail): Promise<SendEmailCommandOutput> {
    const params: SendEmailRequest = {
      Destination: {
        ToAddresses: email.toAddresses,
      },
      Message: {
        Body: {},
        Subject: {
          Charset: "UTF-8",
          Data: email.subject,
        },
      },
      Source: email.sourceEmail,
    };

    if (email.type === ETypeEmail.html) {
      if (params.Message) {
        params.Message.Body = {
          Html: {
            Charset: "UTF-8",
            Data: email.message,
          },
        };
      }
    }

    if (email.type === ETypeEmail.text) {
      if (params.Message) {
        params.Message.Body = {
          Text: {
            Charset: "UTF-8",
            Data: email.message,
          },
        };
      }
    }

    return this.ses.sendEmail(params);
  }
}
