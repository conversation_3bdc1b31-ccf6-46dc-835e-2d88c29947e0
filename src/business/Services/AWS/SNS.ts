// https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/clients/client-sns/
import {
  CreatePlatformEndpointCommandInput,
  CreatePlatformEndpointCommandOutput,
  CreateTopicCommandInput,
  CreateTopicCommandOutput,
  DeleteEndpointCommandInput,
  DeleteEndpointCommandOutput,
  DeleteTopicCommandInput,
  DeleteTopicCommandOutput,
  GetEndpointAttributesCommandInput,
  GetEndpointAttributesCommandOutput,
  GetSubscriptionAttributesCommandInput,
  GetSubscriptionAttributesCommandOutput,
  GetTopicAttributesCommandInput,
  GetTopicAttributesCommandOutput,
  ListSubscriptionsByTopicCommandInput,
  ListSubscriptionsByTopicCommandOutput,
  ListSubscriptionsCommandInput,
  ListSubscriptionsCommandOutput,
  ListTopicsCommandInput,
  ListTopicsCommandOutput,
  PublishCommandInput,
  PublishCommandOutput,
  SNS,
  SubscribeCommandInput,
  SubscribeCommandOutput,
  UnsubscribeCommandInput,
  UnsubscribeCommandOutput,
} from "@aws-sdk/client-sns";
import { injectable } from "inversify";
import { ISNSService } from "src/business/Interfaces/Service/AWS/ISNS";

@injectable()
class SNSService implements ISNSService {
  sns: SNS;

  constructor() {
    if (!process.env.AWS_REGION || !process.env.AWS_SNS_ACCESS_KEY || !process.env.AWS_SNS_SECRET_ACCESS_KEY) {
      throw new Error("AWS SNS credentials are not set");
    }

    this.sns = new SNS({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_SNS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SNS_SECRET_ACCESS_KEY,
      },
    });
  }

  async deleteTopic(args: DeleteTopicCommandInput): Promise<DeleteTopicCommandOutput> {
    return this.sns.deleteTopic(args);
  }

  async publish(args: PublishCommandInput): Promise<PublishCommandOutput> {
    return this.sns.publish(args);
  }

  async createEndpoint(args: CreatePlatformEndpointCommandInput): Promise<CreatePlatformEndpointCommandOutput> {
    return this.sns.createPlatformEndpoint(args);
  }

  async deleteEndpoint(args: DeleteEndpointCommandInput): Promise<DeleteEndpointCommandOutput> {
    return this.sns.deleteEndpoint(args);
  }

  async getEndpointAttributes(args: GetEndpointAttributesCommandInput): Promise<GetEndpointAttributesCommandOutput> {
    return this.sns.getEndpointAttributes(args);
  }

  async createTopic(args: CreateTopicCommandInput): Promise<CreateTopicCommandOutput> {
    return this.sns.createTopic(args);
  }

  async getTopicAttributes(args: GetTopicAttributesCommandInput): Promise<GetTopicAttributesCommandOutput> {
    return this.sns.getTopicAttributes(args);
  }

  async getTopicList(args: ListTopicsCommandInput): Promise<ListTopicsCommandOutput> {
    return this.sns.listTopics(args);
  }

  async subscribeDeviceToATopic(args: SubscribeCommandInput): Promise<SubscribeCommandOutput> {
    return this.sns.subscribe(args);
  }

  async unsubscribeDeviceFromTopic(args: UnsubscribeCommandInput): Promise<UnsubscribeCommandOutput> {
    return this.sns.unsubscribe(args);
  }

  async getSubscriptionsDevices(args: ListSubscriptionsCommandInput): Promise<ListSubscriptionsCommandOutput> {
    return this.sns.listSubscriptions(args);
  }

  async getSubscriptionsDevicesFromTopic(
    args: ListSubscriptionsByTopicCommandInput,
  ): Promise<ListSubscriptionsByTopicCommandOutput> {
    return this.sns.listSubscriptionsByTopic(args);
  }

  async getSubscriptionAttributes(
    args: GetSubscriptionAttributesCommandInput,
  ): Promise<GetSubscriptionAttributesCommandOutput> {
    return this.sns.getSubscriptionAttributes(args);
  }

  async listTopic(
    args: ListTopicsCommandInput,
  ): Promise<ListTopicsCommandOutput> {
    return this.sns.listTopics(args);
  }
}

export default SNSService;
