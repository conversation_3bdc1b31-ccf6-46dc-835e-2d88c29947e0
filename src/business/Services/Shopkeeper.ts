import { EMessageType, EProfileStatus } from "@prisma/client";
import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { UnknownObject } from "src/business/DTOs/UnknownObject";
import { IShopkeeper } from "src/business/Interfaces/Prisma/IShopkeeper";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { IShopkeeperRepository } from "src/business/Interfaces/Repository/IShopkeeper";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { IShopkeeperService } from "src/business/Interfaces/Service/IShopkeeper";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import ProfilesSingleton from "src/business/Singletons/Profile";
import pt from "src/business/Assets/Language/pt.json";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { EProfile } from "src/business/Enums/Models/EProfile";

@injectable()
export class ShopkeeperService extends BaseService<IShopkeeper> implements IShopkeeperService {
  protected notificationManager: INotificationManager;

  constructor(
    @inject(TOKENS.IShopkeeperRepository)
    private shopkeeperRepository: IShopkeeperRepository,
    @inject(TOKENS.IUserProfileRepository)
    private userProfileRepository: IUserProfileRepository,
    @inject(TOKENS.ProfilesSingleton)
    private profilesSingleton: ProfilesSingleton,
    @inject(TOKENS.IFileRepository)
    private fileRepository: IFileRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
  ) {
    super(shopkeeperRepository, notificationManager);
    this.notificationManager = notificationManager;
  }

  async updateShopkeeperProfile(userId: string, data: IShopkeeper, attachments?: { id: string }[]): Promise<boolean> {
    // const profile = await this.validate(data);
    // if (!this.isValid() && !profile) {
    //   return null;
    // }

    data.userId = userId;
    data.status = EProfileStatus.pendingDocuments;

    const response = await this.shopkeeperRepository.updateShopkeeperProfile(
      userId,
      data,
      this.profilesSingleton.shopkeeper.id,
    );

    if (response) {
      if (attachments && attachments.length > 0) {
        const shopkeeper = await this.findOne({
          where: {
            userId,
          },
        });
        if (shopkeeper) {
          await this.fileRepository.updateMany({
            where: { entityId: userId },
            data: { entityId: shopkeeper.id },
          });
        }
      }
    }

    return response;
  }

  async update(item: IShopkeeper): Promise<boolean> {
    const { id } = item;

    const shopkeeper = await this.findOne({
      where: {
        id,
      },
    });

    // var aux = {status: EProfileStatus.approved}

    let result = 0;
    if (shopkeeper) {
      const response = await this.repository.update(shopkeeper.id, item);

      result = response;

      this.notificationService.sendNotificationToUser(shopkeeper.userId, {
        title: pt.notification.updated_profile,
        body: pt.notification.updated_profile,
        type: EMessageType.profile_status,
        profile: EProfile.client,
      });
    }

    return !!result;
  }

  // TODO Fix validation
  async validate(data: UnknownObject): Promise<IShopkeeper> {
    // const shopkeeper = Shopkeeper.create(data);
    // const errors = await validate(shopkeeper);
    // if (errors.length > 0) {
    //   this.notificationManager.addFromTypeorm("shopkeeper", errors);
    // }
    // return shopkeeper;

    return {} as IShopkeeper;
  }

  private async updateUserShopkeeper(userId: string, shopkeeper: IShopkeeper) {
    shopkeeper.userId = userId;
    shopkeeper.status = EProfileStatus.review;

    const currentShopkeeperData = await this.shopkeeperRepository.findOne({
      where: { userId },
    });

    if (currentShopkeeperData && currentShopkeeperData.id) {
      await this.shopkeeperRepository.update(currentShopkeeperData.id, shopkeeper);
    } else {
      await this.shopkeeperRepository.create(shopkeeper);
    }
  }

  async getShopkeeperByStatus(
    currentPage: string,
    pageSize: string,
    status: string,
  ): Promise<PagedResult<IShopkeeper>> {
    const shopkeepers = await this.shopkeeperRepository.getUserShopkeeperByStatus(
      Number(currentPage),
      Number(pageSize),
      status as EProfileStatus,
    );

    return shopkeepers;
  }

  async getShopkeeper(userId: string): Promise<IShopkeeper | null> {
    const shopkeeper = await this.shopkeeperRepository.getByUserId(userId);

    return shopkeeper;
  }
}
