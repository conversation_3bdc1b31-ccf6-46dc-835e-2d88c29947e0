import { EMessageType, EProfileStatus } from "@prisma/client";
import { inject, injectable } from "inversify";
import { ILocationViewModel } from "src/api/ViewModels/Location/IViewModel";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { UnknownObject } from "src/business/DTOs/UnknownObject";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IDeliveryman } from "src/business/Interfaces/Prisma/IDeliveryman";
import { IDeliverymanRepository } from "src/business/Interfaces/Repository/IDeliveryman";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { IDeliverymanService } from "src/business/Interfaces/Service/IDeliveryman";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import ProfilesSingleton from "src/business/Singletons/Profile";
import pt from "src/business/Assets/Language/pt.json";

@injectable()
export class DeliverymanService extends BaseService<IDeliveryman> implements IDeliverymanService {
  protected notificationManager: INotificationManager;

  constructor(
    @inject(TOKENS.IDeliverymanRepository)
    private deliverymanRepository: IDeliverymanRepository,
    @inject(TOKENS.IUserProfileRepository)
    private userProfileRepository: IUserProfileRepository,
    @inject(TOKENS.ProfilesSingleton)
    private profilesSingleton: ProfilesSingleton,
    @inject(TOKENS.IFileRepository)
    private fileRepository: IFileRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.INotificationService)
    private notificationService: INotificationService,
  ) {
    super(deliverymanRepository, notificationManager);
    this.notificationManager = notificationManager;
  }

  async updateDeliverymanProfile(userId: string, data: IDeliveryman, attachments?: { id: string }[]) {
    // const profile = await this.validate(data);
    // if (!this.isValid() && !profile) {
    //   return null;
    // }

    data.userId = userId;
    data.status = EProfileStatus.review;

    const response = await this.deliverymanRepository.updateDeliverymanProfile(
      userId,
      data,
      this.profilesSingleton.deliveryman.id,
    );

    if (response) {
      if (attachments && attachments.length > 0) {
        const deliveryman = await this.findOne({
          where: {
            userId,
          },
        });
        if (deliveryman) {
          await this.fileRepository.updateMany({
            where: { entityId: userId },
            data: { entityId: deliveryman.id },
          });
        }
      }
    }

    return response;
  }

  async update(item: IDeliveryman): Promise<boolean> {
    const { id } = item;
    const deliveryman = await this.findOne({
      where: {
        id,
      },
    });

    let result = 0;
    if (deliveryman) {
      const response = await this.repository.update(deliveryman.id, item);

      result = response;

      this.notificationService.sendNotificationToUser(deliveryman.userId, {
        title: pt.notification.updated_profile,
        body: pt.notification.updated_profile,
        type: EMessageType.profile_status,
        profile: EProfile.client,
      });
    }

    return !!result;
  }

  // TODO Fix validation
  async validate(data: UnknownObject): Promise<IDeliveryman> {
    // const deliveryman = Deliveryman.create(data);
    // const errors = await validate(deliveryman);
    // if (errors.length > 0) {
    //   this.notificationManager.addFromTypeorm("deliveryman", errors);
    // }
    // return deliveryman;

    return {} as IDeliveryman;
  }

  async getDeliverymanByStatus(
    currentPage: string,
    pageSize: string,
    status: string,
  ): Promise<PagedResult<IDeliveryman>> {
    // TODO Add validation to enum (EProfileStatus)
    const deliverymen = await this.deliverymanRepository.getUserDeliverymanByStatus(
      Number(currentPage),
      Number(pageSize),
      status as EProfileStatus,
    );

    return deliverymen;
  }

  async getDeliveryman(userId: string): Promise<IDeliveryman | null> {
    const deliveryman = await this.deliverymanRepository.getByUserId(userId);

    return deliveryman;
  }

  async getDeliverymanLocation(cpf: string): Promise<IDeliveryman | null> {
    const deliveryman = await this.deliverymanRepository.getDeliverymanLocationByCPF(cpf);

    if (!deliveryman?.latitude || !deliveryman.longitude) {
      this.notificationManager.add("generic.errors", "location_notFound");
      return null;
    }

    if (deliveryman) {
      return deliveryman;
    }

    this.notificationManager.add("generic.errors", "wrong_cpf");
    return null;
  }

  async updateDeliverymanLocation(userId: string, location: ILocationViewModel): Promise<boolean> {
    const result = await this.deliverymanRepository.updateDeliverymanLocation(userId, location);
    return result;
  }

  async updateDeliverymanPixKey(userId: string, pixKey: string | null) {
    const deliveryman = await this.getDeliveryman(userId);
    if (!deliveryman) return null;

    const erasedPixKey = pixKey === "";
    if (erasedPixKey) pixKey = null;

    const result = await this.update({ id: deliveryman.id, pixKey } as IDeliveryman);
    return result;
  }

  async getCheckPixKey(userId: string): Promise<boolean> {
    const result = await this.deliverymanRepository.getCheckPixKey(userId);
    const { pixKey } = result;
    if (!pixKey) return false;
    return true;
  }
}
