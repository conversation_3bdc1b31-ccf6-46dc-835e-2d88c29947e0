import { inject, injectable } from "inversify";
import { ISettingsViewModel } from "src/api/ViewModels/Settings/ISettings";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ISettings } from "src/business/Interfaces/Prisma/ISettings";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";
import { ISettingsRepository } from "src/business/Interfaces/Repository/ISettings";
import { ISettingsService } from "src/business/Interfaces/Service/ISettings";
import { settingsData } from "src/infrastructure/Data/Settings";

@injectable()
export class SettingsService extends BaseService<ISettings> implements ISettingsService {
  private settingsRepository: ISettingsRepository;

  constructor(
    @inject(TOKENS.ISettingsRepository)
    settingsRepository: ISettingsRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(settingsRepository, notificationManager);
    this.settingsRepository = settingsRepository;
  }

  async getFormatted() {
    const settingsDB = await this.settingsRepository.getAll();
    return settingsDB;
  }

  async loadSettings(): Promise<void> {
    const settingsDB = await this.settingsRepository.getAll();
    const filteredSettings: ISettings[] = [];

    settingsData.forEach((setting) => {
      const savedSetting = settingsDB.find((item) => item.name === setting.name);

      if (!savedSetting) filteredSettings.push(setting as ISettings);
    });

    if (filteredSettings.length > 0) {
      await this.settingsRepository.createMany(filteredSettings);
    }
  }

  async updateByUserId(userId: string, settingUpdate: ISettingsViewModel): Promise<boolean> {
    const settingsDB = await this.settingsRepository.getByName(settingUpdate.name);

    if (settingsDB) {
      settingsDB.value = settingUpdate.value;
      settingsDB.userId = userId;
      const result = await this.settingsRepository.update(settingsDB.id, settingsDB);
      return !!result;
    }

    return false;
  }

  async getByName(name: string): Promise<ISettings | null> {
    const settings = await this.settingsRepository.getByName(name);
    return settings;
  }
}
