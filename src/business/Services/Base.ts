/* eslint-disable default-param-last */
/* eslint-disable dot-notation */
/* eslint-disable prefer-destructuring */
import { injectable } from "inversify";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult, PagedResultWithCondition } from "src/business/DTOs/PagedResult";
import { PagedSearchWhereCondtion } from "src/business/DTOs/PagedWhereCondition";
import { IBaseRepository } from "src/business/Interfaces/Repository/IBase";
import { IBaseService } from "src/business/Interfaces/Service/IBase";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";

@injectable()
export abstract class BaseService<T> implements IBaseService<T> {
  protected repository: IBaseRepository<T, any, any, any, any, any>;

  protected notificationManager: INotificationManager;

  constructor(repository: IBaseRepository<T, any, any, any, any, any>, notificationManager: INotificationManager) {
    this.repository = repository;
    this.notificationManager = notificationManager;
  }

  async update(item: T): Promise<boolean> {
    const id = item["id"];

    const result = await this.repository.update(id, item);

    return !!result;
  }

  async getById(id: string): Promise<T | null> {
    const response = await this.repository.getById(id);
    return response;
  }

  async delete(id: string) {
    const response = this.repository.delete(id);
    return response;
  }

  async getAll() {
    const response = await this.repository.getAll();

    return response;
  }

  async find(options: any) {
    const response = await this.repository.find(options);
    return response;
  }

  async findOne(options: any): Promise<T | null> {
    const response = await this.repository.findOne(options);

    return response;
  }

  private getColumnsFilter(columns: string, filterValue: string, filter: boolean = true) {
    const splittedInput = columns.split(".");

    const result = this.buildNestedObject(splittedInput, 0, filterValue, filter);
    return result;
  }

  private getColumnsOrder(columns: string, filterValue: string) {}

  private buildNestedObject(arr: string[], index: number = 0, filterValue: string, filter: boolean = true): any {
    const obj: any = {};

    if (index < arr.length - 1) {
      obj[arr[index]] = this.buildNestedObject(arr, index + 1, filterValue, filter);
      return obj;
    }

    if (filter === true) {
      obj[arr[index]] = {
        contains: filterValue,
        mode: "insensitive",
      };
    } else {
      obj[arr[index]] = filterValue;
    }

    return obj;
  }

  async getPaged(
    columnFilter: string = "",
    filterValue: string = "",
    currentPage: string = "",
    pageSize: string = "",
    orderBy: string = "",
    sortDirection: IOrder = "asc",
    includes: any = undefined,
    where?: any,
    select?: any,
  ): Promise<PagedResultWithCondition<T>> {
    const { currentPageInt, pageSizeInt } = this.getPageSizeAndCurrentPage(pageSize, currentPage);

    const conditionArray: PagedSearchWhereCondtion = { OR: [], AND: [] };

    if (columnFilter !== "" && filterValue !== "") {
      conditionArray.OR = columnFilter.split(",").map((column) => this.getColumnsFilter(column, filterValue));
    }

    const whereCondition = {
      AND: [where, conditionArray.OR && conditionArray.OR.length > 0 ? conditionArray : undefined],
    };

    let orderByCondition: {} | undefined;

    if (orderBy !== "") {
      orderByCondition = this.getColumnsFilter(orderBy, sortDirection, false);
    } else {
      orderByCondition = undefined;
    }

    const response = await this.repository.getPaged({
      include: includes,
      select,
      where: whereCondition,
      orderBy: orderByCondition,
      skip: (currentPageInt - 1) * pageSizeInt,
      take: pageSizeInt,
    });

    return { data: response, whereCondition };
  }

  isValid() {
    const notificationList = this.notificationManager.getList();
    if (notificationList.length === 0) {
      return true;
    }
    return false;
  }

  protected getPageSizeAndCurrentPage(pageSize: string, currentPage: string) {
    let pageSizeInt = parseInt(pageSize, 10);
    let currentPageInt = parseInt(currentPage, 10) < 1 ? 1 : parseInt(currentPage, 10);

    if (Number.isNaN(pageSizeInt) || pageSizeInt < 1) {
      pageSizeInt = 10;
    }

    if (Number.isNaN(currentPageInt) || currentPageInt < 1) {
      currentPageInt = 1;
    }

    return { pageSizeInt, currentPageInt };
  }
}
