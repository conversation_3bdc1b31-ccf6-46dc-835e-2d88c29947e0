import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";
import { IStoreHoursRepository } from "src/business/Interfaces/Repository/IStoreHours";
import { IStoreHoursService } from "src/business/Interfaces/Service/IStoreHours";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class StoreHoursService extends BaseService<IStoreHours> implements IStoreHoursService {
  private storeHoursRepository: IStoreHoursRepository;

  constructor(
    @inject(TOKENS.IStoreHoursRepository)
    storeHoursRepository: IStoreHoursRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(storeHoursRepository, notificationManager);
    this.storeHoursRepository = storeHoursRepository;
  }

  async create(storeId: string, storeHours: IStoreHours[]): Promise<boolean> {
    await this.storeHoursRepository.deleteByStoreId(storeId);
    const result = await this.storeHoursRepository.createMany(storeId, storeHours);

    return !!result;
  }

  async getStoreHoursByStoreId(storeId: string): Promise<IStoreHours[]> {
    const storeHours = await this.storeHoursRepository.getStoreHoursByStoreId(storeId);
    if (!storeHours) {
      this.notificationManager.add("stores.create.errors", "store_notFound");
    }
    return storeHours;
  }

  async updateByStore(storeId: string, storeHours: IStoreHours[]): Promise<boolean> {
    const result = await this.storeHoursRepository.updateByStore(storeId, storeHours);

    return result;
  }

  async deleteByStoreId(storeId: string): Promise<number> {
    const result = await this.storeHoursRepository.deleteByStoreId(storeId);
    return result;
  }
}
