import { inject, injectable } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import ITrackingDeliverymanDTO from "src/business/DTOs/TrackingOrder/ITrackingDeliveryman";
import { ITrackingOrder } from "src/business/Interfaces/Prisma/ITrackingOrder";
import { ITrackingOrderRepository } from "src/business/Interfaces/Repository/ITrackingOrder";
import { ITrackingOrderService } from "src/business/Interfaces/Service/ITrackingOrder";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { BaseService } from "src/business/Services/Base";

@injectable()
export class TrackingOrderService extends BaseService<ITrackingOrder> implements ITrackingOrderService {
  constructor(
    @inject(TOKENS.ITrackingOrderRepository)
    private trackingOrderRepository: ITrackingOrderRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(trackingOrderRepository, notificationManager);
    this.trackingOrderRepository = trackingOrderRepository;
  }

  async create(trackingOrder: ITrackingOrder): Promise<boolean> {
    const result = await this.trackingOrderRepository.create(trackingOrder);

    if (!result) return false;

    return true;
  }

  async trackOrderDeliveryById(orderId: string): Promise<ITrackingDeliverymanDTO> {
    const response = await this.trackingOrderRepository.trackOrderDeliveryById(orderId);

    return {
      deliverymanCoords: response.map((item) => ({ latitude: item.latitude, longitude: item.longitude })),
      order: response?.[0].order,
    };
  }

  async deleteByOrderId(orderId: string): Promise<boolean> {
    const result = await this.trackingOrderRepository.deleteByOrder(orderId);
    return result > 0;
  }
}
