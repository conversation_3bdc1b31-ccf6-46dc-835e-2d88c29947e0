import { Container, interfaces } from "inversify";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IDefaultPrismaClient } from "src/business/Interfaces/Database/IDefault";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";
import { IAddressRepository } from "src/business/Interfaces/Repository/IAddress";
import { IAttributeRepository } from "src/business/Interfaces/Repository/IAttribute";
import { IAttributeOptionRepository } from "src/business/Interfaces/Repository/IAttributeOption";
import { ICardRepository } from "src/business/Interfaces/Repository/ICard";
import { ICategoryRepository } from "src/business/Interfaces/Repository/ICategory";
import { ICategorySubcategoryRepository } from "src/business/Interfaces/Repository/ICategorySubcategory";
import { IChatRepository } from "src/business/Interfaces/Repository/IChat";
import { IChatMessagesRepository } from "src/business/Interfaces/Repository/IChatMessages";
import { IClientRepository } from "src/business/Interfaces/Repository/IClient";
import { IContentManagementRepository } from "src/business/Interfaces/Repository/IContentManagement";
import { ICustomerProfileRepository } from "src/business/Interfaces/Repository/ICustomerProfile";
import { IDeliverymanRepository } from "src/business/Interfaces/Repository/IDeliveryman";
import { IDeviceRepository } from "src/business/Interfaces/Repository/IDevice";
import { IEmailAttachmentRepository } from "src/business/Interfaces/Repository/IEmailAttachment";
import { IFileRepository } from "src/business/Interfaces/Repository/IFile";
import { ILogRepository } from "src/business/Interfaces/Repository/ILog";
import { ILoginSessionRepository } from "src/business/Interfaces/Repository/ILoginSession";
import { IMailingRepository } from "src/business/Interfaces/Repository/IMailing";
import { IMessageRepository } from "src/business/Interfaces/Repository/IMessage";
import { IMessageContentRepository } from "src/business/Interfaces/Repository/IMessageContent";
import { IOrderRepository } from "src/business/Interfaces/Repository/IOrder";
import { IOrderStatusRepository } from "src/business/Interfaces/Repository/IOrderStatus";
import { IOrderStatusTypeRepository } from "src/business/Interfaces/Repository/IOrderStatusType";
import { IPayoutRepository } from "src/business/Interfaces/Repository/IPayout";
import { IProductRepository } from "src/business/Interfaces/Repository/IProduct";
import { IProductAttributeRepository } from "src/business/Interfaces/Repository/IProductAttribute";
import { IProductAttributeOptionRepository } from "src/business/Interfaces/Repository/IProductAttributeOption";
import { IProductCategoryRepository } from "src/business/Interfaces/Repository/IProductCategory";
import { IProductModerationRepository } from "src/business/Interfaces/Repository/IProductModerationRepository";
import { IProductSubcategoryRepository } from "src/business/Interfaces/Repository/IProductSubcategory";
import { IProfileRepository } from "src/business/Interfaces/Repository/IProfile";
import { IResultMailingRepository } from "src/business/Interfaces/Repository/IResultMailing";
import { IReviewRepository } from "src/business/Interfaces/Repository/IReview";
import { ISettingsRepository } from "src/business/Interfaces/Repository/ISettings";
import { IShopkeeperRepository } from "src/business/Interfaces/Repository/IShopkeeper";
import { IStoreRepository } from "src/business/Interfaces/Repository/IStore";
import { IStoreCategoryRepository } from "src/business/Interfaces/Repository/IStoreCategory";
import { IStoreHoursRepository } from "src/business/Interfaces/Repository/IStoreHours";
import { IStoreModerationRepository } from "src/business/Interfaces/Repository/IStoreModeration";
import { IStoreSettingsRepository } from "src/business/Interfaces/Repository/IStoreSettings";
import { IStoreUserRepository } from "src/business/Interfaces/Repository/IStoreUser";
import { ISubcategoryRepository } from "src/business/Interfaces/Repository/ISubcategory";
import { ITrackingOrderRepository } from "src/business/Interfaces/Repository/ITrackingOrder";
import { ITransactionRepository } from "src/business/Interfaces/Repository/ITransaction";
import { ITransactionCardRepository } from "src/business/Interfaces/Repository/ITransactionCard";
import { IUserRepository } from "src/business/Interfaces/Repository/IUser";
import { IUserAddressRepository } from "src/business/Interfaces/Repository/IUserAddress";
import { IUserFavoriteProductsRepository } from "src/business/Interfaces/Repository/IUserFavoriteProducts";
import { IUserFavoriteStoresRepository } from "src/business/Interfaces/Repository/IUserFavoriteStores";
import { IUserMessageRepository } from "src/business/Interfaces/Repository/IUserMessage";
import { IUserProfileRepository } from "src/business/Interfaces/Repository/IUserProfile";
import { ICognitoService } from "src/business/Interfaces/Service/AWS/ICognito";
import { IS3Service } from "src/business/Interfaces/Service/AWS/IS3";
import { ISESService } from "src/business/Interfaces/Service/AWS/ISES";
import { ISNSService } from "src/business/Interfaces/Service/AWS/ISNS";
import { IIntegrationHubService } from "src/business/Interfaces/Service/IntegrationHub/IntegrationHub";
import { ICronService } from "src/business/Interfaces/Service/Cron/ICronService";
import { IAddressService } from "src/business/Interfaces/Service/IAddress";
import { IAttributeService } from "src/business/Interfaces/Service/IAttribute";
import { IAttributeOptionService } from "src/business/Interfaces/Service/IAttributeOption";
import { ICardService } from "src/business/Interfaces/Service/ICard";
import { ICategoryService } from "src/business/Interfaces/Service/ICategory";
import { IChatService } from "src/business/Interfaces/Service/IChat";
import { IChatMessagesService } from "src/business/Interfaces/Service/IChatMessages";
import { IClientService } from "src/business/Interfaces/Service/IClient";
import { IContentManagementService } from "src/business/Interfaces/Service/IContentManagement";
import { ICustomerProfileService } from "src/business/Interfaces/Service/ICustomerProfile";
import { IDeliverymanService } from "src/business/Interfaces/Service/IDeliveryman";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { IEmailAttachmentService } from "src/business/Interfaces/Service/IEmailAttachment";
import { IFileService } from "src/business/Interfaces/Service/IFile";
import { ILogService } from "src/business/Interfaces/Service/ILog";
import { ILoginSessionService } from "src/business/Interfaces/Service/ILoginSession";
import { IMailingService } from "src/business/Interfaces/Service/IMailing";
import { IMessageService } from "src/business/Interfaces/Service/IMessage";
import { IMessageContentService } from "src/business/Interfaces/Service/IMessageContent";
import { INotificationService } from "src/business/Interfaces/Service/INotification";
import { IOrderService } from "src/business/Interfaces/Service/IOrder";
import { IOrderStatusService } from "src/business/Interfaces/Service/IOrderStatus";
import { IOrderStatusTypeService } from "src/business/Interfaces/Service/IOrderStatusType";
import { IPayoutService } from "src/business/Interfaces/Service/IPayout";
import { IProductService } from "src/business/Interfaces/Service/IProduct";
import { IProductModerationService } from "src/business/Interfaces/Service/IProductModerationService";
import { IProfileService } from "src/business/Interfaces/Service/IProfile";
import { IResourcesService } from "src/business/Interfaces/Service/IResources";
import { IResultMailingService } from "src/business/Interfaces/Service/IResultMailing";
import { IReviewService } from "src/business/Interfaces/Service/IReview";
import { ISettingsService } from "src/business/Interfaces/Service/ISettings";
import { IShopkeeperService } from "src/business/Interfaces/Service/IShopkeeper";
import { IStoreService } from "src/business/Interfaces/Service/IStore";
import { IStoreHoursService } from "src/business/Interfaces/Service/IStoreHours";
import { IStoreModerationService } from "src/business/Interfaces/Service/IStoreModeration";
import { IStoreSettingsService } from "src/business/Interfaces/Service/IStoreSettings";
import { ISubcategoryService } from "src/business/Interfaces/Service/ISubcategory";
import { ITokenService } from "src/business/Interfaces/Service/IToken";
import { ITrackingOrderService } from "src/business/Interfaces/Service/ITrackingOrder";
import { ITransactionService } from "src/business/Interfaces/Service/ITransaction";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { IUserMessageService } from "src/business/Interfaces/Service/IUserMessage";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";
import { IPagSeguroService } from "src/business/Interfaces/Service/PagSeguro/IPagSeguro";
import { IAuthenticateRequest } from "src/business/Interfaces/Tools/IAuthenticateRequest";
import { ILogBuilder } from "src/business/Interfaces/Tools/ILogBuilder";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { IUserContext } from "src/business/Interfaces/Tools/IUserContext";
import { AddressService } from "src/business/Services/Address";
import { AttributeService } from "src/business/Services/Attribute";
import { AttributeOptionService } from "src/business/Services/AttributeOption";
import { CognitoService } from "src/business/Services/AWS/Cognito";
import { S3Service } from "src/business/Services/AWS/S3";
import { SESService } from "src/business/Services/AWS/SES";
import SNSService from "src/business/Services/AWS/SNS";
import IntegrationHubService from "src/business/Services/IntegrationHub/IntegrationHub";
import { CardService } from "src/business/Services/Card";
import { CategoryService } from "src/business/Services/Category";
import { ChatService } from "src/business/Services/Chat";
import { ChatMessagesService } from "src/business/Services/ChatMessages";
import { ClientService } from "src/business/Services/Client";
import { ContentManagementService } from "src/business/Services/ContentManagement";
import { CronService } from "src/business/Services/Cron/CronService";
import { CustomerProfileService } from "src/business/Services/CustomerProfile";
import { DeliverymanService } from "src/business/Services/Deliveryman";
import { DeviceService } from "src/business/Services/Device";
import { EmailAttachmentService } from "src/business/Services/EmailAttachment";
import { FileService } from "src/business/Services/File";
import { LogService } from "src/business/Services/Log";
import { LoggerService } from "src/business/Services/Logger/LoggerService";
import { LoginSessionService } from "src/business/Services/LoginSession";
import { MailingService } from "src/business/Services/Mailing";
import { MessageService } from "src/business/Services/Message";
import { MessageContentService } from "src/business/Services/MessageContent";
import { NotificationService } from "src/business/Services/Notification";
import { OrderService } from "src/business/Services/Order";
import { OrderStatusService } from "src/business/Services/OrderStatus";
import { OrderStatusTypeService } from "src/business/Services/OrderStatusType";
import { PagSeguroService } from "src/business/Services/PagSeguro";
import { PayoutService } from "src/business/Services/Payout";
import { ProductService } from "src/business/Services/Product";
import { ProductModerationService } from "src/business/Services/ProductModeration";
import { ProfileService } from "src/business/Services/Profile";
import { ResourcesService } from "src/business/Services/Resources";
import { ResultMailingService } from "src/business/Services/ResultMailing";
import { ReviewService } from "src/business/Services/Review";
import { SettingsService } from "src/business/Services/Settings";
import { ShopkeeperService } from "src/business/Services/Shopkeeper";
import { StoreService } from "src/business/Services/Store";
import { StoreHoursService } from "src/business/Services/StoreHours";
import { StoreModerationService } from "src/business/Services/StoreModeration";
import { StoreSettingsService } from "src/business/Services/StoreSettings";
import { SubcategoryService } from "src/business/Services/Subcategory";
import { TokenService } from "src/business/Services/Token";
import { TrackingOrderService } from "src/business/Services/TrackingOrder";
import { TransactionService } from "src/business/Services/Transaction";
import { UserService } from "src/business/Services/User";
import { UserMessageService } from "src/business/Services/UserMessage";
import OrderStatusTypeSingleton from "src/business/Singletons/OrderStatusType";
import ProfilesSingleton from "src/business/Singletons/Profile";
import { AuthenticateRequest } from "src/business/Tools/AuthenticateRequest";
import { LogBuilder } from "src/business/Tools/LogBuilder";
import NotificationManager from "src/business/Tools/NotificationManager";
import { UserContext } from "src/business/Tools/UserContext";
import Cookies from "src/business/Utils/Cookies";
import { AddressRepository } from "src/infrastructure/Data/Repositories/Address";
import { AttributeRepository } from "src/infrastructure/Data/Repositories/Attribute";
import { AttributeOptionRepository } from "src/infrastructure/Data/Repositories/AttributeOption";
import { CardRepository } from "src/infrastructure/Data/Repositories/Card";
import { CategoryRepository } from "src/infrastructure/Data/Repositories/Category";
import { CategorySubcategoryRepository } from "src/infrastructure/Data/Repositories/CategorySubcategory";
import { ChatRepository } from "src/infrastructure/Data/Repositories/Chat";
import { ChatMessagesRepository } from "src/infrastructure/Data/Repositories/ChatMessages";
import { ClientRepository } from "src/infrastructure/Data/Repositories/Client";
import { ContentManagementRepository } from "src/infrastructure/Data/Repositories/ContentManagement";
import { CustomerProfileRepository } from "src/infrastructure/Data/Repositories/CustomerProfile";
import { DeliverymanRepository } from "src/infrastructure/Data/Repositories/Deliveryman";
import { DeviceRepository } from "src/infrastructure/Data/Repositories/Device";
import { EmailAttachmentRepository } from "src/infrastructure/Data/Repositories/EmailAttachment";
import { FileRepository } from "src/infrastructure/Data/Repositories/File";
import { LogRepository } from "src/infrastructure/Data/Repositories/Log";
import { LoginSessionRepository } from "src/infrastructure/Data/Repositories/LoginSession";
import { MailingRepository } from "src/infrastructure/Data/Repositories/Mailing";
import { MessageRepository } from "src/infrastructure/Data/Repositories/Message";
import { MessageContentRepository } from "src/infrastructure/Data/Repositories/MessageContent";
import { OrderRepository } from "src/infrastructure/Data/Repositories/Order";
import { OrderStatusRepository } from "src/infrastructure/Data/Repositories/OrderStatus";
import { OrderStatusTypeRepository } from "src/infrastructure/Data/Repositories/OrderStatusType";
import { PayoutRepository } from "src/infrastructure/Data/Repositories/Payout";
import { ProductRepository } from "src/infrastructure/Data/Repositories/Product";
import { ProductAttributeRepository } from "src/infrastructure/Data/Repositories/ProductAttribute";
import { ProductAttributeOptionRepository } from "src/infrastructure/Data/Repositories/ProductAttributeOption";
import { ProductCategoryRepository } from "src/infrastructure/Data/Repositories/ProductCategory";
import { ProductModerationRepository } from "src/infrastructure/Data/Repositories/ProductModeration";
import { ProductSubcategoryRepository } from "src/infrastructure/Data/Repositories/ProductSubcategory";
import { ProfileRepository } from "src/infrastructure/Data/Repositories/Profile";
import { ResultMailingRepository } from "src/infrastructure/Data/Repositories/ResultMailing";
import { ReviewRepository } from "src/infrastructure/Data/Repositories/Review";
import { SettingsRepository } from "src/infrastructure/Data/Repositories/Settings";
import { ShopkeeperRepository } from "src/infrastructure/Data/Repositories/Shopkeeper";
import { StoreRepository } from "src/infrastructure/Data/Repositories/Store";
import { StoreCategoryRepository } from "src/infrastructure/Data/Repositories/StoreCategory";
import { StoreHoursRepository } from "src/infrastructure/Data/Repositories/StoreHours";
import { StoreModerationRepository } from "src/infrastructure/Data/Repositories/StoreModeration";
import { StoreSettingsRepository } from "src/infrastructure/Data/Repositories/StoreSettings";
import { StoreUserRepository } from "src/infrastructure/Data/Repositories/StoreUser";
import { SubcategoryRepository } from "src/infrastructure/Data/Repositories/Subcategory";
import { TrackingOrderRepository } from "src/infrastructure/Data/Repositories/TrackingOrder";
import { TransactionRepository } from "src/infrastructure/Data/Repositories/Transaction";
import { TransactionCardRepository } from "src/infrastructure/Data/Repositories/TransactionCard";
import { UsersRepository } from "src/infrastructure/Data/Repositories/User";
import { UserAddressRepository } from "src/infrastructure/Data/Repositories/UserAddress";
import { UserFavoriteProductsRepository } from "src/infrastructure/Data/Repositories/UserFavoriteProducts";
import { UserFavoriteStoresRepository } from "src/infrastructure/Data/Repositories/UserFavoriteStores";
import { UserMessageRepository } from "src/infrastructure/Data/Repositories/UserMessage";
import { UserProfileRepository } from "src/infrastructure/Data/Repositories/UserProfile";
import { DefaultPrismaClient } from "src/infrastructure/DefaultPrismaClient";
import { ExtendedPrismaClient } from "src/infrastructure/ExtendedPrismaClient";
import { IStoreUserService } from "src/business/Interfaces/Service/IStoreUser";
import { StoreUserService } from "src/business/Services/StoreUser";
import { IBorderoService } from "src/business/Interfaces/Service/IBordero";
import { BorderoService } from "src/business/Services/Bordero";
import { IBorderoRepository } from "src/business/Interfaces/Repository/IBordero";
import { BorderoRepository } from "src/infrastructure/Data/Repositories/Bordero";
import { ICooperativeService } from "src/business/Interfaces/Service/ICooperative";
import { CooperativeService } from "src/business/Services/Cooperative";
import { ICooperativeRepository } from "src/business/Interfaces/Repository/ICooperative";
import { CooperativeRepository } from "src/infrastructure/Data/Repositories/Cooperative";
import { FinancialConsolidationRepository } from "src/infrastructure/Data/Repositories/FinancialConsolidation";
import { IFinancialConsolidationRepository } from "src/business/Interfaces/Repository/IFinancialConsolidation";
import IFinancialConsolidationService from "src/business/Interfaces/Service/IFinancialConsolidationService";
import { FinancialConsolidationService } from "src/business/Services/FinancialConsolidation";

const container: Container = new Container();

// Services
container.bind<IAddressService>(TOKENS.IAddressService).to(AddressService);
container.bind<ILogService>(TOKENS.ILogService).to(LogService);
container.bind<IUserService>(TOKENS.IUserService).to(UserService);
container.bind<IAttributeService>(TOKENS.IAttributeService).to(AttributeService);
container.bind<IAttributeOptionService>(TOKENS.IAttributeOptionService).to(AttributeOptionService);
container.bind<IDeliverymanService>(TOKENS.IDeliverymanService).to(DeliverymanService);
container.bind<IShopkeeperService>(TOKENS.IShopkeeperService).to(ShopkeeperService);
container.bind<IProfileService>(TOKENS.IProfileService).to(ProfileService);
container.bind<ICognitoService>(TOKENS.ICognitoService).to(CognitoService);
container.bind<IClientService>(TOKENS.IClientService).to(ClientService);
container.bind<IStoreService>(TOKENS.IStoreService).to(StoreService);
container.bind<IDeviceService>(TOKENS.IDeviceService).to(DeviceService);
container.bind<ISNSService>(TOKENS.ISNSService).to(SNSService);
container.bind<IFileService>(TOKENS.IFileService).to(FileService);
container.bind<IS3Service>(TOKENS.IS3Service).to(S3Service);
container.bind<IStoreSettingsService>(TOKENS.IStoreSettingsService).to(StoreSettingsService);
container.bind<IProductService>(TOKENS.IProductService).to(ProductService);
container.bind<ICategoryService>(TOKENS.ICategoryService).to(CategoryService);
container.bind<IStoreHoursService>(TOKENS.IStoreHoursService).to(StoreHoursService);
container.bind<IOrderService>(TOKENS.IOrderService).to(OrderService);
container.bind<IOrderStatusTypeService>(TOKENS.IOrderStatusTypeService).to(OrderStatusTypeService);
container.bind<INotificationService>(TOKENS.INotificationService).to(NotificationService);
container.bind<IReviewService>(TOKENS.IReviewService).to(ReviewService);
container.bind<ITrackingOrderService>(TOKENS.ITrackingOrderService).to(TrackingOrderService);
container.bind<IOrderStatusService>(TOKENS.IOrderStatusService).to(OrderStatusService);
container.bind<ISubcategoryService>(TOKENS.ISubcategoryService).to(SubcategoryService);
container.bind<ITokenService>(TOKENS.ITokenService).to(TokenService);
container.bind<ICardService>(TOKENS.ICardService).to(CardService);
container.bind<ITransactionService>(TOKENS.ITransactionService).to(TransactionService);
container.bind<IPagSeguroService>(TOKENS.IPagSeguroService).to(PagSeguroService);
container.bind<IResourcesService>(TOKENS.IResourcesService).to(ResourcesService);
container.bind<ICustomerProfileService>(TOKENS.ICustomerProfileService).to(CustomerProfileService);
container.bind<IMessageService>(TOKENS.IMessageService).to(MessageService);
container.bind<IMessageContentService>(TOKENS.IMessageContentService).to(MessageContentService);
container.bind<IUserMessageService>(TOKENS.IUserMessageService).to(UserMessageService);
container.bind<IEmailAttachmentService>(TOKENS.IEmailAttachmentService).to(EmailAttachmentService);
container.bind<IStoreModerationService>(TOKENS.IStoreModerationService).to(StoreModerationService);
container.bind<IProductModerationService>(TOKENS.IProductModerationService).to(ProductModerationService);
container.bind<ISESService>(TOKENS.ISESService).to(SESService);
container.bind<IChatService>(TOKENS.IChatService).to(ChatService);
container.bind<IChatMessagesService>(TOKENS.IChatMessagesService).to(ChatMessagesService);
container.bind<ILoginSessionService>(TOKENS.ILoginSessionService).to(LoginSessionService);
container.bind<ILoggerService>(TOKENS.LoggerService).to(LoggerService).inSingletonScope();
container.bind<IMailingService>(TOKENS.IMailingService).to(MailingService);
container.bind<IResultMailingService>(TOKENS.IResultMailingService).to(ResultMailingService);
container.bind<ICronService>(TOKENS.ICronService).to(CronService);
container.bind<IIntegrationHubService>(TOKENS.IntegrationHubService).to(IntegrationHubService);
container.bind<IContentManagementService>(TOKENS.IContentManagementService).to(ContentManagementService);
container.bind<IPayoutService>(TOKENS.IPayoutService).to(PayoutService);
container.bind<IStoreUserService>(TOKENS.IStoreUserService).to(StoreUserService);
container.bind<IBorderoService>(TOKENS.IBorderoService).to(BorderoService);
// Repositories
container.bind<IMailingRepository>(TOKENS.IMailingRepository).to(MailingRepository);
container.bind<IResultMailingRepository>(TOKENS.IResultMailingRepository).to(ResultMailingRepository);
container.bind<IAddressRepository>(TOKENS.IAddressRepository).to(AddressRepository);
container.bind<IAttributeOptionRepository>(TOKENS.IAttributeOptionRepository).to(AttributeOptionRepository);
container.bind<IUserRepository>(TOKENS.IUserRepository).to(UsersRepository);
container.bind<IAttributeRepository>(TOKENS.IAttributeRepository).to(AttributeRepository);
container.bind<IClientRepository>(TOKENS.IClientRepository).to(ClientRepository);
container.bind<IShopkeeperRepository>(TOKENS.IShopkeeperRepository).to(ShopkeeperRepository);
container.bind<IDeliverymanRepository>(TOKENS.IDeliverymanRepository).to(DeliverymanRepository);
container.bind<IProfileRepository>(TOKENS.IProfileRepository).to(ProfileRepository);
// container.bind<IPermissionRepository>(TOKENS.IPermissionRepository).to(PermissionRepository);
container.bind<IStoreRepository>(TOKENS.IStoreRepository).to(StoreRepository);
container.bind<ILogRepository>(TOKENS.ILogRepository).to(LogRepository);
container.bind<IDeviceRepository>(TOKENS.IDeviceRepository).to(DeviceRepository);
container.bind<IUserProfileRepository>(TOKENS.IUserProfileRepository).to(UserProfileRepository);
container.bind<IFileRepository>(TOKENS.IFileRepository).to(FileRepository);
container.bind<IStoreSettingsRepository>(TOKENS.IStoreSettingsRepository).to(StoreSettingsRepository);
container.bind<IProductRepository>(TOKENS.IProductRepository).to(ProductRepository);
container.bind<ICategoryRepository>(TOKENS.ICategoryRepository).to(CategoryRepository);
container.bind<ICategorySubcategoryRepository>(TOKENS.ICategorySubcategoryRepository).to(CategorySubcategoryRepository);
container.bind<IStoreHoursRepository>(TOKENS.IStoreHoursRepository).to(StoreHoursRepository);
container.bind<IOrderRepository>(TOKENS.IOrderRepository).to(OrderRepository);
container.bind<IOrderStatusTypeRepository>(TOKENS.IOrderStatusTypeRepository).to(OrderStatusTypeRepository);
container.bind<IReviewRepository>(TOKENS.IReviewRepository).to(ReviewRepository);
container.bind<ITrackingOrderRepository>(TOKENS.ITrackingOrderRepository).to(TrackingOrderRepository);
container.bind<IOrderStatusRepository>(TOKENS.IOrderStatusRepository).to(OrderStatusRepository);
container.bind<IProductAttributeRepository>(TOKENS.IProductAttributeRepository).to(ProductAttributeRepository);
container
  .bind<IProductAttributeOptionRepository>(TOKENS.IProductAttributeOptionRepository)
  .to(ProductAttributeOptionRepository);
container.bind<IProductCategoryRepository>(TOKENS.IProductCategoryRepository).to(ProductCategoryRepository);
container.bind<IProductSubcategoryRepository>(TOKENS.IProductSubcategoryRepository).to(ProductSubcategoryRepository);
container.bind<IStoreCategoryRepository>(TOKENS.IStoreCategoryRepository).to(StoreCategoryRepository);
container.bind<ISubcategoryRepository>(TOKENS.ISubcategoryRepository).to(SubcategoryRepository);
// container.bind<ITokenRepository>(TOKENS.ITokenRepository).to(TokenRepository);
container.bind<ICardRepository>(TOKENS.ICardRepository).to(CardRepository);
container.bind<ITransactionRepository>(TOKENS.ITransactionRepository).to(TransactionRepository);
container.bind<ICustomerProfileRepository>(TOKENS.ICustomerProfileRepository).to(CustomerProfileRepository);
container.bind<IMessageRepository>(TOKENS.IMessageRepository).to(MessageRepository);
container.bind<IUserMessageRepository>(TOKENS.IUserMessageRepository).to(UserMessageRepository);
container.bind<IMessageContentRepository>(TOKENS.IMessageContentRepository).to(MessageContentRepository);
container.bind<IEmailAttachmentRepository>(TOKENS.IEmailAttachmentRepository).to(EmailAttachmentRepository);
container.bind<IStoreModerationRepository>(TOKENS.IStoreModerationRepository).to(StoreModerationRepository);
container.bind<IProductModerationRepository>(TOKENS.IProductModerationRepository).to(ProductModerationRepository);
container.bind<ITransactionCardRepository>(TOKENS.ITransactionCardRepository).to(TransactionCardRepository);
container.bind<IChatRepository>(TOKENS.IChatRepository).to(ChatRepository);
container.bind<IChatMessagesRepository>(TOKENS.IChatMessagesRepository).to(ChatMessagesRepository);
container.bind<ILoginSessionRepository>(TOKENS.ILoginSessionRepository).to(LoginSessionRepository);
container.bind<IStoreUserRepository>(TOKENS.IStoreUserRepository).to(StoreUserRepository);
container.bind<IUserAddressRepository>(TOKENS.IUserAddressRepository).to(UserAddressRepository);
container
  .bind<IUserFavoriteProductsRepository>(TOKENS.IUserFavoriteProductsRepository)
  .to(UserFavoriteProductsRepository);
container.bind<IUserFavoriteStoresRepository>(TOKENS.IUserFavoriteStoresRepository).to(UserFavoriteStoresRepository);
container.bind<IContentManagementRepository>(TOKENS.IContentManagementRepository).to(ContentManagementRepository);
container.bind<IPayoutRepository>(TOKENS.IPayoutRepository).to(PayoutRepository);
container.bind<IBorderoRepository>(TOKENS.IBorderoRepository).to(BorderoRepository);
// Singletons
container.bind<ProfilesSingleton>(TOKENS.ProfilesSingleton).to(ProfilesSingleton).inSingletonScope();
container
  .bind<OrderStatusTypeSingleton>(TOKENS.OrderStatusTypeSingleton)
  .to(OrderStatusTypeSingleton)
  .inSingletonScope();

// Others*
container.bind<INotificationManager>(TOKENS.NotificationManager).to(NotificationManager).inRequestScope();
container
  .bind<interfaces.Factory<Cookies>>(TOKENS.CookiesFactory)
  .toFactory<Cookies, void[], unknown[]>(() => () => new Cookies());
container.bind<IAuthenticateRequest>(TOKENS.AuthenticateRequest).to(AuthenticateRequest).inRequestScope();
container.bind<IUserContext>(TOKENS.UserContext).to(UserContext).inRequestScope();
container.bind<ILogBuilder>(TOKENS.LogBuilder).to(LogBuilder).inSingletonScope();
container.bind<IDefaultPrismaClient>(TOKENS.DefaultPrismaClient).to(DefaultPrismaClient).inSingletonScope();
container.bind<IExtendedPrismaClient>(TOKENS.ExtendedPrismaClient).to(ExtendedPrismaClient).inSingletonScope();
// container
//   .bind<interfaces.Factory<void>>(TOKENS.ConnectionFactory)
//   .toFactory<void, [connection: DataSource], unknown[]>(
//     (context: interfaces.Context) => (connection: DataSource) => {
//       context.container.bind(TOKENS.Connection).toConstantValue(connection);
//     }
//   );

// container
//   .bind<IAttributeOptionService>(TOKENS.IAttributeOptionService)
//   .to(AttributeOptionService);

container.bind<ISettingsService>(TOKENS.ISettingsService).to(SettingsService);

container.bind<ISettingsRepository>(TOKENS.ISettingsRepository).to(SettingsRepository);

container.bind<ICooperativeService>(TOKENS.ICooperativeService).to(CooperativeService);

container.bind<ICooperativeRepository>(TOKENS.ICooperativeRepository).to(CooperativeRepository);

container.bind<IFinancialConsolidationService>(TOKENS.IFinancialConsolidationService).to(FinancialConsolidationService);
container
  .bind<IFinancialConsolidationRepository>(TOKENS.IFinancialConsolidationRepository)
  .to(FinancialConsolidationRepository);

export default container;
