const TOKENS = {
  IBaseRepository: Symbol.for("IBaseRepository"),
  IUserRepository: Symbol.for("IUserRepository"),
  IPostRepository: Symbol.for("IPostRepository"),
  ICommentRepository: Symbol.for("ICommentRepository"),
  ITokenRepository: Symbol.for("ITokenRepository"),
  IBaseService: Symbol.for("IBaseService"),
  IUserService: Symbol.for("IUserService"),
  IPostService: Symbol.for("IPostService"),
  ICommentService: Symbol.for("ICommentService"),
  ITokenService: Symbol.for("ITokenService"),
  IProfileService: Symbol.for("IProfileService"),
  IProfileRepository: Symbol.for("IProfileRepository"),
  NotificationManager: Symbol.for("NotificationManager"),
  IAddressService: Symbol.for("IAddressService"),
  IAddressRepository: Symbol.for("IAddressRepository"),
  IStoreService: Symbol.for("IStoreService"),
  IStoreRepository: Symbol.for("IStoreRepository"),
  IStoreSettingsService: Symbol.for("IStoreSettingsService"),
  IStoreSettingsRepository: Symbol.for("IStoreSettingsRepository"),
  IStoreCategoryRepository: Symbol.for("IStoreCategoryRepository"),
  IProductService: Symbol.for("IProductService"),
  IProductRepository: Symbol.for("IProductRepository"),
  IProductCategoryRepository: Symbol.for("IProductCategoryRepository"),
  IProductSubcategoryRepository: Symbol.for("IProductSubcategoryRepository"),
  ICategorySubcategoryRepository: Symbol.for("ICategorySubcategoryRepository"),
  ICategoryService: Symbol.for("ICategoryService"),
  ICategoryRepository: Symbol.for("ICategoryRepository"),
  ISubcategoryService: Symbol.for("ISubcategoryService"),
  ISubcategoryRepository: Symbol.for("ISubcategoryRepository"),
  ConnectionFactory: Symbol.for("ConnectionFactory"),
  Connection: Symbol.for("Connection"),
  IFileService: Symbol.for("IFileService"),
  IFileRepository: Symbol.for("IFileRepository"),
  IStoreHoursService: Symbol.for("IStoreHoursService"),
  IStoreHoursRepository: Symbol.for("IStoreHoursRepository"),
  IReviewRepository: Symbol.for("IReviewRepository"),
  IReviewService: Symbol.for("IReviewService"),
  ICognitoService: Symbol.for("ICognitoService"),
  IUserProfileRepository: Symbol.for("IUserProfileRepository"),
  IAttributeService: Symbol.for("IAttributeService"),
  IAttributeRepository: Symbol.for("IAttributeRepository"),
  IAttributeOptionService: Symbol.for("IAttributeOptionService"),
  IAttributeOptionRepository: Symbol.for("IAttributeOptionRepository"),
  IProductAttributeRepository: Symbol.for("IProductAttributeRepository"),
  IProductAttributeOptionRepository: Symbol.for("IProductAttributeOptionRepository"),
  CookiesFactory: Symbol.for("CookiesFactory"),
  IClientService: Symbol.for("IClientService"),
  IClientRepository: Symbol.for("IClientRepository"),
  IDeliverymanService: Symbol.for("IDeliverymanService"),
  IDeliverymanRepository: Symbol.for("IDeliverymanRepository"),
  IShopkeeperService: Symbol.for("IShopkeeperService"),
  IShopkeeperRepository: Symbol.for("IShopkeeperRepository"),
  ProfilesSingleton: Symbol.for("ProfilesSingleton"),
  OrderStatusTypeSingleton: Symbol.for("OrderStatusTypeSingleton"),
  IOrderStatusTypeService: Symbol.for("IOrderStatusTypeService"),
  IOrderStatusTypeRepository: Symbol.for("IOrderStatusTypeRepository"),
  IOrderRepository: Symbol.for("IOrderRepository"),
  IOrderService: Symbol.for("IOrderService"),
  IOrderStatusRepository: Symbol.for("IOrderStatusRepository"),
  IOrderStatusService: Symbol.for("IOrderStatusService"),
  ISNSService: Symbol.for("ISNSService"),
  IDeviceService: Symbol.for("IDeviceService"),
  IDeviceRepository: Symbol.for("IDeviceRepository"),
  INotificationService: Symbol.for("INotificationService"),
  IS3Service: Symbol.for("IS3Service"),
  ISESService: Symbol.for("ISESService"),
  ITrackingOrderService: Symbol.for("ITrackingOrderService"),
  ITrackingOrderRepository: Symbol.for("ITrackingOrderRepository"),
  ICardService: Symbol.for("ICardService"),
  ICardRepository: Symbol.for("ICardRepository"),
  ITransactionService: Symbol.for("ITransactionService"),
  ITransactionRepository: Symbol.for("ITransactionRepository"),
  IPagSeguroService: Symbol.for("IPagSeguroService"),
  IResourcesService: Symbol.for("IResourcesService"),
  ICustomerProfileService: Symbol.for("ICustomerProfileService"),
  ICustomerProfileRepository: Symbol.for("ICustomerProfileRepository"),
  IMessageRepository: Symbol.for("IMessageRepository"),
  IMessageService: Symbol.for("IMessageService"),
  IMessageContentRepository: Symbol.for("IMessageContentRepository"),
  IMessageContentService: Symbol.for("IMessageContentService"),
  IUserMessageRepository: Symbol.for("IUserMessageRepository"),
  IUserMessageService: Symbol.for("IUserMessageService"),
  IEmailAttachmentRepository: Symbol.for("IEmailAttachmentRepository"),
  IEmailAttachmentService: Symbol.for("IEmailAttachmentService"),
  IStoreModerationService: Symbol.for("IStoreModerationService"),
  IStoreModerationRepository: Symbol.for("IStoreModerationRepository"),
  IProductModerationService: Symbol.for("IProductModerationService"),
  IProductModerationRepository: Symbol.for("IProductModerationRepository"),
  IChatMessagesService: Symbol.for("IChatMessagesService"),
  IChatMessagesRepository: Symbol.for("IChatMessagesRepository"),
  IChatService: Symbol.for("IChatService"),
  IChatRepository: Symbol.for("IChatRepository"),
  ITransactionCardRepository: Symbol.for("ITransactionCardRepository"),
  ILoginSessionService: Symbol.for("ILoginSessionService"),
  ILoginSessionRepository: Symbol.for("ILoginSessionRepository"),
  AuthenticateRequest: Symbol.for("AuthenticateRequest"),
  UserContext: Symbol.for("UserContext"),
  LoggerService: Symbol.for("LoggerService"),
  LogBuilder: Symbol.for("LogBuilder"),
  DefaultPrismaClient: Symbol.for("DefaultPrismaClient"),
  ExtendedPrismaClient: Symbol.for("ExtendedPrismaClient"),
  IMailingRepository: Symbol.for("IMailingRepository"),
  IMailingService: Symbol.for("IMailingService"),
  IStoreUserRepository: Symbol.for("IStoreUserRepository"),
  IStoreUserService: Symbol.for("IStoreUserService"),
  IUserAddressRepository: Symbol.for("IUserAddressRepository"),
  IUserFavoriteProductsRepository: Symbol.for("IUserFavoriteProductsRepository"),
  IUserFavoriteStoresRepository: Symbol.for("IUserFavoriteStoresRepository"),
  IResultMailingRepository: Symbol.for("IResultMailingRepository"),
  IResultMailingService: Symbol.for("IResultMailingService"),
  ICronService: Symbol.for("ICronService"),
  IntegrationHubService: Symbol.for("IntegrationHubService"),
  ISettingsService: Symbol.for("ISettingsService"),
  ISettingsRepository: Symbol.for("ISettingsRepository"),
  ILogRepository: Symbol.for("ILogRepository"),
  ILogService: Symbol.for("ILogService"),
  IContentManagementService: Symbol.for("IContentManagementService"),
  IContentManagementRepository: Symbol.for("IContentManagementRepository"),
  IPayoutRepository: Symbol.for("IPayoutRepository"),
  IPayoutService: Symbol.for("IPayoutService"),
  IBorderoRepository: Symbol.for("IBorderoRepository"),
  IBorderoService: Symbol.for("IBorderoService"),
  ICooperativeRepository: Symbol.for("ICooperativeRepository"),
  ICooperativeService: Symbol.for("ICooperativeService"),
  IFinancialConsolidationService: Symbol.for("IFinancialConsolidationService"),
  IFinancialConsolidationRepository: Symbol.for("IFinancialConsolidationRepository"),
};

export default TOKENS;
