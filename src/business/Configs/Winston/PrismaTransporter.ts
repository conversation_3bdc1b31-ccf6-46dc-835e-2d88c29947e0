import { PrismaClient } from "@prisma/client";
import { ILog } from "src/business/Interfaces/Prisma/ILog";
import Transport, { type TransportStreamOptions } from "winston-transport";

export interface PrismaTransporterOptions extends TransportStreamOptions {
  prisma: any;
  tableName?: string;
}

export class PrismaWinstonTransporter extends Transport {
  private prismaClient: PrismaClient;

  private tableName: string;

  constructor(options: PrismaTransporterOptions) {
    super(options);
    this.prismaClient = options.prisma;
    this.tableName = options.tableName ?? "log";
  }

  log(info: ILog, callback?: (error?: Error, value?: unknown) => void): void {
    process.nextTick(() => {
      if (!callback) {
        callback = () => {};
      }

      const log: ILog = {
        level: info?.level,
        message: info?.message,
        details: info?.details,
        userId: info?.userId,
        entityId: info?.entityId,
        entity: info?.entity,
        action: info?.action,
        currentData: info?.currentData,
      };

      this.prismaClient[this.tableName]
        .create({
          data: log,
        })
        .then(() => {
          setImmediate(() => {
            this.emit("logged", info);
          });

          return callback && callback(undefined, true);
        })
        .catch((err: Error) => {
          setImmediate(() => {
            this.emit("error", err);
          });

          return callback && callback(err, null);
        });
    });
  }
}
