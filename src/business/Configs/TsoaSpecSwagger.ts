import path from "path";
import { generateSpec, ExtendedSpecConfig } from "tsoa";
// eslint-disable-next-line import/no-extraneous-dependencies
import { CompilerOptions } from "typescript";
import dotenv from "dotenv";

dotenv.config({
  path: path.resolve(__dirname, "../../api", `${process.env.NODE_ENV}.env`),
});

(async () => {
  const specOptions: ExtendedSpecConfig = {
    name: "API:AppDeliveryBahia",
    description: "Swager gerado a partir da API AppDeliveryBahia.",
    basePath: process.env.ROOT_PATH || "/",
    entryFile: "./src/api/app.ts",
    specVersion: 3,
    outputDirectory: "./src/business/Configs/Swagger",
    controllerPathGlobs: ["./src/api/Controllers/*.ts"],
    noImplicitAdditionalProperties: "throw-on-extras",
    securityDefinitions: {
      api_key: {
        type: "apiKey",
        name: "authorization",
        in: "header",
      },
    },
    /*     spec: {
      securityDefinitions: {
        api_key: {
          type: "apiKey",
          authorizationUrl: "http://swagger.io/api/oauth/dialog",
          name: "access_token",
          in: "query",
        },
      },
    }, */
  };

  const compilerOptions: CompilerOptions = {
    paths: {
      "custome-module": ["src/business/Models/*.ts"],
      authenticationModule: ["src/business/Middlewares/Passport/PassportJWTConfig"],
    },
    baseUrl: ".",
  };

  await generateSpec(specOptions, compilerOptions);
})();
