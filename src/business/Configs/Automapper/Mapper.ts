/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable no-sequences */
/* eslint-disable no-unused-expressions */
import { MapperConfiguration } from "@dynamic-mapper/mapper";
import { AddressMapper } from "src/business/Configs/Automapper/Profile/Address";
import { AttributeMapper } from "src/business/Configs/Automapper/Profile/Attribute";
import { BorderoMapper } from "src/business/Configs/Automapper/Profile/Bordero";
import { CardMapper } from "src/business/Configs/Automapper/Profile/Card";
import { CategoryMapper } from "src/business/Configs/Automapper/Profile/Category";
import { ChatMapper } from "src/business/Configs/Automapper/Profile/Chat";
import { ClientMapper } from "src/business/Configs/Automapper/Profile/Client";
import { ContentManagementMapper } from "src/business/Configs/Automapper/Profile/ContentManagement";
import { CooperativeMapper } from "src/business/Configs/Automapper/Profile/Cooperative";
import { DeliverymanMapper } from "src/business/Configs/Automapper/Profile/Deliveryman";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import { LogMapper } from "src/business/Configs/Automapper/Profile/Log";
import { LoginSessionMapper } from "src/business/Configs/Automapper/Profile/LoginSession";
import { MailingMapper } from "src/business/Configs/Automapper/Profile/Mailing";
import { MessageMapper } from "src/business/Configs/Automapper/Profile/Message";
import { OrderMapper } from "src/business/Configs/Automapper/Profile/Order";
import { OrderStatusMapper } from "src/business/Configs/Automapper/Profile/OrderStatus";
import { ProductMapper } from "src/business/Configs/Automapper/Profile/Product";
import { ProductModerationMapper } from "src/business/Configs/Automapper/Profile/ProductModeration";
import { ProfileMapper } from "src/business/Configs/Automapper/Profile/Profile";
import { ReviewMapper } from "src/business/Configs/Automapper/Profile/Review";
import { SettingsMapper } from "src/business/Configs/Automapper/Profile/Settings";
import { ShopkeeperMapper } from "src/business/Configs/Automapper/Profile/Shopkeeper";
import { StoreMapper } from "src/business/Configs/Automapper/Profile/Store";
import { StoreHoursMapper } from "src/business/Configs/Automapper/Profile/StoreHours";
import { StoreModerationMapper } from "src/business/Configs/Automapper/Profile/StoreModeration";
import { StoreSettingsMapper } from "src/business/Configs/Automapper/Profile/StoreSettings";
import { SubcategoryMapper } from "src/business/Configs/Automapper/Profile/Subcategory";
import { TrackingOrderMapper } from "src/business/Configs/Automapper/Profile/TrackingOrder";
import { TransactionMapper } from "src/business/Configs/Automapper/Profile/Transaction";
import { UserMapper } from "src/business/Configs/Automapper/Profile/User";
import { PayoutMapper } from "./Profile/Payout";

const config = new MapperConfiguration((cfg) => {
  cfg.addProfile(new AddressMapper()),
    cfg.addProfile(new UserMapper()),
    cfg.addProfile(new StoreMapper()),
    cfg.addProfile(new StoreSettingsMapper()),
    cfg.addProfile(new StoreHoursMapper()),
    cfg.addProfile(new StoreModerationMapper()),
    cfg.addProfile(new ShopkeeperMapper()),
    cfg.addProfile(new DeliverymanMapper()),
    cfg.addProfile(new ProfileMapper()),
    cfg.addProfile(new ClientMapper()),
    cfg.addProfile(new AttributeMapper()),
    cfg.addProfile(new FilesMapper()),
    cfg.addProfile(new OrderMapper()),
    cfg.addProfile(new ProductMapper()),
    cfg.addProfile(new ReviewMapper()),
    cfg.addProfile(new OrderStatusMapper()),
    cfg.addProfile(new TrackingOrderMapper()),
    cfg.addProfile(new CategoryMapper()),
    cfg.addProfile(new SubcategoryMapper()),
    cfg.addProfile(new MessageMapper()),
    cfg.addProfile(new TransactionMapper()),
    cfg.addProfile(new CardMapper()),
    cfg.addProfile(new ChatMapper()),
    cfg.addProfile(new LoginSessionMapper()),
    cfg.addProfile(new MailingMapper()),
    cfg.addProfile(new SettingsMapper()),
    cfg.addProfile(new LogMapper()),
    cfg.addProfile(new PayoutMapper()),
    cfg.addProfile(new ContentManagementMapper()),
    cfg.addProfile(new ProductModerationMapper());
  cfg.addProfile(new BorderoMapper());
  cfg.addProfile(new CooperativeMapper());
});

export const mapper = config.createMapper();
