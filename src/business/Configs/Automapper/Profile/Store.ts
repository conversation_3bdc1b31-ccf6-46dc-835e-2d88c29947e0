import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { storeHoursToStoreHoursViewModel, storeHoursViewModelToStoreHours } from "src/api/Utils/StoreHoursMap";
import { ICategoryViewModel } from "src/api/ViewModels/Category/ICategory";
import { ICreateStoreViewModel } from "src/api/ViewModels/Store/ICreate";
import { IListStoreViewModel } from "src/api/ViewModels/Store/IList";
import { IPaymentMethodsViewModel } from "src/api/ViewModels/Store/IPaymentMethods";
import { IStoreShowcaseViewModel } from "src/api/ViewModels/Store/IStoreShowcase";
import { IUpdateStoreViewModel } from "src/api/ViewModels/Store/IUpdate";
import { IStoreViewModel } from "src/api/ViewModels/Store/IViewModel";
import { AddressMapper } from "src/business/Configs/Automapper/Profile/Address";
import { CategoryMapper } from "src/business/Configs/Automapper/Profile/Category";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import { StoreModerationMapper } from "src/business/Configs/Automapper/Profile/StoreModeration";
import { StoreSettingsMapper } from "src/business/Configs/Automapper/Profile/StoreSettings";
import { ExportStoresData } from "src/business/DTOs/ExportedStoresData";
import { IPaymentMethodsDTO } from "src/business/DTOs/IPaymentMethods";
import { IListStoresDTO } from "src/business/DTOs/Store/IListStores";
import { ExportStoreDTO } from "src/business/DTOs/Store/ExportStore";
import { IStore } from "src/business/Interfaces/Prisma/IStore";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IStoreUser } from "src/business/Interfaces/Prisma/IStoreUser";
import { IStoreNameDTO } from "src/business/DTOs/Store/IStoreName";
import { IStoreNameViewModel } from "src/api/ViewModels/Store/IStoreName";
import { IStoreUserViewModel } from "src/api/ViewModels/StoreUser/IViewModel";
import { IStoreShowcaseDTO } from "src/business/DTOs/Store/IStoreShowcase";
import { IListUserStoreDTO } from "src/business/DTOs/Store/IListUserStore";
import { IListUserStoreViewModel } from "src/api/ViewModels/Store/IListUserStore";
import { IStoreShowcaseWithUrlsDTO } from "src/business/DTOs/Store/IStoreShowcaseWithUrls";

export class StoreMapper extends MappingProfile {
  static readonly IStoreToIStoreViewModel = new MappingPair<IStore, IStoreViewModel>();

  static readonly IListUserStoreDTOToIListUserStoreViewModel = new MappingPair<
    IListUserStoreDTO,
    IListUserStoreViewModel
  >();

  static readonly IStoreNameDTOToIStoreNameViewModel = new MappingPair<IStoreNameDTO, IStoreNameViewModel>();

  static readonly IStoreCategoryToICategoryViewModel = new MappingPair<IStoreCategory, ICategoryViewModel>();

  static readonly ICreateStoreViewModelToIStore = new MappingPair<ICreateStoreViewModel, IStore>();

  static readonly IUpdateStoreViewModelToIStore = new MappingPair<IUpdateStoreViewModel, IStore>();

  static readonly IStoreUserViewModelToIStoreUser = new MappingPair<IStoreUserViewModel, IStoreUser>();

  static readonly IStoreUserToIStoreUserViewModel = new MappingPair<IStoreUser, IStoreUserViewModel>();

  static readonly IPaymentMethodsDTOToIPaymentMethodsViewModel = new MappingPair<
    IPaymentMethodsDTO,
    IPaymentMethodsViewModel
  >();

  static readonly IListStoresDTOToIListStoreViewModel = new MappingPair<IListStoresDTO, IListStoreViewModel>();

  static readonly IStoreShowcaseWithUrlsDTOToIStoreShowcaseViewModel = new MappingPair<
    IStoreShowcaseWithUrlsDTO,
    IStoreShowcaseViewModel
  >();

  static readonly ExportStoreDTOToExportStoresData = new MappingPair<ExportStoreDTO, ExportStoresData>();

  constructor() {
    super();

    this.createAutoMap(StoreMapper.IStoreUserViewModelToIStoreUser, {
      id: (opt) => opt.ignore(),
      storeId: (opt) => opt.ignore(),
      userId: (opt) => opt.mapFrom((src) => src.userId),
      owner: (opt) => opt.mapFrom((src) => src.owner),
      status: (opt) => opt.mapFrom((src) => src.status),
      store: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
    });

    this.createAutoMap(StoreMapper.IStoreUserToIStoreUserViewModel, {
      id: (opt) => opt.ignore(),
      storeId: (opt) => opt.ignore(),
      userId: (opt) => opt.mapFrom((src) => src.userId),
      owner: (opt) => opt.mapFrom((src) => src.owner),
      status: (opt) => opt.mapFrom((src) => src.status),
    });

    this.createAutoMap(StoreMapper.ExportStoreDTOToExportStoresData, {
      city: (opt) => {
        opt.preCondition((src) => src.address?.city !== undefined);
        opt.mapFrom((src) => src.address?.city!);
      },
      street: (opt) => {
        opt.preCondition((src) => src.address?.street !== undefined);
        opt.mapFrom((src) => src.address?.street!);
      },
      country: (opt) => {
        opt.preCondition((src) => src.address?.country !== undefined);
        opt.mapFrom((src) => src.address?.country!);
      },
      state: (opt) => {
        opt.preCondition((src) => src.address?.state !== undefined);
        opt.mapFrom((src) => src.address?.state!);
      },
      district: (opt) => {
        opt.preCondition((src) => src.address?.district !== undefined);
        opt.mapFrom((src) => src.address?.district!);
      },
      number: (opt) => {
        opt.preCondition((src) => src.address?.number !== undefined);
        opt.mapFrom((src) => src.address?.number!);
      },
      nickname: (opt) => {
        opt.preCondition((src) => src.address?.nickname !== undefined);
        opt.mapFrom((src) => src.address?.nickname!);
      },
      complement: (opt) => {
        opt.preCondition((src) => src.address?.complement !== undefined);
        opt.mapFrom((src) => src.address?.complement!);
      },
      postcode: (opt) => {
        opt.preCondition((src) => src.address?.postcode !== undefined);
        opt.mapFrom((src) => src.address?.postcode!);
      },
      categoryName: (opt) =>
        opt.mapFrom((src) => {
          const categorysName = src.storeCategory.map((category) => category.category.name);

          return categorysName[0];
        }),
      cnpj: (opt) => opt.mapFrom((src) => src.cnpj),
      email: (opt) => opt.mapFrom((src) => src.email),
      name: (opt) => opt.mapFrom((src) => src.name),
      phone: (opt) => opt.mapFrom((src) => src.phone),
      slug: (opt) => {
        opt.preCondition((src) => src.slug !== undefined);
        opt.mapFrom((src) => src.slug!);
      },
      description: (opt) => opt.mapFrom((src) => src.description),
    });

    this.createAutoMap(StoreMapper.IStoreToIStoreViewModel, {
      storeCategory: (opt) =>
        opt.mapFromUsing((src) => src.storeCategory, StoreMapper.IStoreCategoryToICategoryViewModel),
      address: (opt) => opt.mapFromUsing((src) => src.address, AddressMapper.IAddressToIListAddressViewModel),
      storeUsers: (opt) => {
        opt.mapFromUsing((src) => src.storeUsers, StoreMapper.IStoreUserToIStoreUserViewModel);
      },
      storeHours: (opt) =>
        opt.mapFrom((src) => {
          const listWorkingHours = src.storeHours ? storeHoursToStoreHoursViewModel(src.storeHours) : undefined;
          return listWorkingHours;
        }),
      storeSettings: (opt) =>
        opt.mapFromUsing((src) => src.storeSettings, StoreSettingsMapper.IStoreSettingsToStoreSettingsViewModel),
      attachments: (opt) => {
        opt.ignore();
      },
      storeReviewAverage(opt) {
        opt.mapFrom((src) => {
          const ratingSum = src.reviews?.reduce((soma, rate) => soma + rate.rate, 0);
          if (ratingSum && src.reviews) {
            const average = ratingSum / src.reviews.length;
            return average.toFixed(2);
          }

          return undefined;
        });
      },
      // storeModeration: (opt) => {
      //   opt.preCondition((src) => (src.storeModeration != null && src.storeModeration != undefined && src.storeModeration.length > 0));
      //   opt.mapFromUsing(
      //     (src) => src.storeModeration?.[0],
      //     StoreModerationMapper.IStoreModerationToIStoreModerationViewModel,
      //   );
      // },
      storeModeration: (opt) =>
        opt.mapFromUsing(
          (src) => src?.storeModeration?.[0]!,
          StoreModerationMapper.IStoreModerationToIStoreModerationViewModel,
        ),
      userFavoriteStoreId: (opt) => opt.mapFrom((src) => src.userFavoriteStoreId),
    }).forSourceMember("updatedAt", (opt) => opt.ignore());

    this.createAutoMap(StoreMapper.IListUserStoreDTOToIListUserStoreViewModel, {
      storeHours: (opt) =>
        opt.mapFrom((src) => {
          const listWorkingHours = src.storeHours ? storeHoursToStoreHoursViewModel(src.storeHours) : undefined;
          return listWorkingHours;
        }),
      addressId: (opt) => opt.mapFrom((src) => src.address?.id),
      owner: (opt) => opt.mapFrom((src) => src.storeUsers.some((storeUser) => storeUser.owner)),
      status: (opt) => opt.mapFrom((src) => src.storeUsers[0].status),
      storeUserId: (opt) => opt.mapFrom((src) => src.storeUsers[0].id),
    }).forSourceMember("storeHours", (opt) => opt.ignore());

    this.createAutoMap(StoreMapper.IStoreCategoryToICategoryViewModel, {
      name: (opt) => opt.mapFrom((src) => src.category?.name || ""),
      checked: (opt) => opt.nullSubstitute(false),
      icon: (opt) => opt.mapFromUsing((src) => src.category?.icon, FilesMapper.IFileToIListFileViewModel),
    })
      .forSourceMember("category", (opt) => opt.ignore())
      .forSourceMember("categoryId", (opt) => opt.ignore())
      .forSourceMember("store", (opt) => opt.ignore())
      .forSourceMember("storeId", (opt) => opt.ignore());

    this.createAutoMap(StoreMapper.ICreateStoreViewModelToIStore, {
      id: (opt) => opt.ignore(),
      pixKey: (opt) => opt.mapFrom((src) => src.pixKey),
      addressId: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      products: (opt) => opt.ignore(),
      reviews: (opt) => opt.ignore(),
      storeHours: (opt) =>
        opt.mapFrom((src) => {
          const storeHours = src.storeHours ? storeHoursViewModelToStoreHours(src.storeHours) : undefined;
          return storeHours;
        }),
      userFavoriteStores: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      storeSettings: (opt) =>
        opt.mapFromUsing((src) => src.storeSettings, StoreSettingsMapper.IStoreSettingsViewModelToIStoreSettings),
      address: (opt) => opt.mapFromUsing((src) => src.address, AddressMapper.ICreateAddressViewModelToIAddress),
      storeCategory: (opt) =>
        opt.mapFromUsing((src) => src.storeCategory, CategoryMapper.ICategoryViewModelToIStoreCategory),
      storeUsers: (opt) => opt.mapFromUsing((src) => src.storeUsers, StoreMapper.IStoreUserViewModelToIStoreUser),
      storeModeration: (opt) =>
        opt.mapFromUsing(
          (src) => src.storeModeration,
          StoreModerationMapper.IStoreModerationViewModelToIStoreModeration,
        ),
      userFavoriteStoreId: (opt) => opt.ignore(),
    }).forSourceMember("attachments", (opt) => opt.ignore());

    this.createAutoMap(StoreMapper.IUpdateStoreViewModelToIStore, {
      pixKey: (opt) => opt.mapFrom((src) => src.pixKey),
      addressId: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      products: (opt) => opt.ignore(),
      reviews: (opt) => opt.ignore(),
      storeHours: (opt) => opt.ignore(),
      userFavoriteStores: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      storeSettings: (opt) => opt.ignore(),
      address: (opt) => opt.ignore(),
      storeUsers: (opt) => opt.ignore(),
      storeCategory: (opt) => opt.ignore(),
      storeModeration: (opt) => opt.ignore(),
      userFavoriteStoreId: (opt) => opt.ignore(),
    });

    this.createAutoMap(StoreMapper.IPaymentMethodsDTOToIPaymentMethodsViewModel, {});

    this.createAutoMap(StoreMapper.IListStoresDTOToIListStoreViewModel, {
      icon: (opt) => opt.mapFrom((src) => src.fileUrl),
      open: (opt) => opt.mapFrom((src) => src.opened),
      // addressId: (opt) => opt.ignore(),
      // createdAt: (opt) => opt.ignore(),
      // updatedAt: (opt) => opt.ignore(),
    })
      .forSourceMember("fileUrl", (opt) => opt.ignore())
      .forSourceMember("opened", (opt) => opt.ignore());

    this.createAutoMap(StoreMapper.IStoreShowcaseWithUrlsDTOToIStoreShowcaseViewModel, {
      storeReviewAverage: (opt) =>
        opt.mapFrom((src) => {
          let quantity = 0;
          const reviewSum = src.reviews?.reduce((sum, review) => {
            quantity += 1;
            return sum + review.rate;
          }, 0);

          const average = reviewSum ? (reviewSum / quantity).toFixed(1) : undefined;

          return {
            average,
            quantity,
          };
        }),
    }).forSourceMember("reviews", (opt) => opt.ignore());

    this.createAutoMap(StoreMapper.IStoreNameDTOToIStoreNameViewModel, {});
  }
}
