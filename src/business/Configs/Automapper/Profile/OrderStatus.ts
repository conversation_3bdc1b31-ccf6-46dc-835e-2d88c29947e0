/* eslint-disable @typescript-eslint/no-unused-vars */
import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IListOrderStatusTypeViewModel } from "src/api/ViewModels/OrderStatus/IListOrderStatusType";
import { IOrderStatus } from "src/business/Interfaces/Prisma/IOrderStatus";

export class OrderStatusMapper extends MappingProfile {
  static readonly IOrderStatusToOrderStatusTypeListViewModel = new MappingPair<
    IOrderStatus,
    IListOrderStatusTypeViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(OrderStatusMapper.IOrderStatusToOrderStatusTypeListViewModel, {
      value: (opt) => {
        opt.preCondition((src) => src.orderStatusType !== null && src.orderStatusType !== undefined);
        opt.condition((src) => src.orderStatusType !== undefined);
        opt.mapFrom((src) => src.orderStatusType?.value!);
      },
    })
      .forSourceMember("id", (opt) => opt.ignore())
      .forSourceMember("observation", (opt) => opt.ignore())
      .forSourceMember("order", (opt) => opt.ignore())
      .forSourceMember("orderId", (opt) => opt.ignore())
      .forSourceMember("orderStatusType", (opt) => opt.ignore())
      .forSourceMember("orderStatusTypeId", (opt) => opt.ignore());
  }
}

// const orderStatusMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     OrderStatus,
//     OrderStatusTypeListViewModel,
//     forMember(
//       (destination) => destination.value,
//       mapFrom((start) => start.orderStatusType.value)
//     ),
//     forMember(
//       (destination) => destination.createdAt,
//       mapFrom((start) => start.createdAt)
//     )
//   );
// }

// export { orderStatusMapper };
