import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IPayoutAlertListViewModel } from "src/api/ViewModels/Payout/IALertList";
import { IPayoutListViewModel } from "src/api/ViewModels/Payout/IList";
import { IPayoutListDTO } from "src/business/DTOs/Payout/IPayoutList";
import { IPayout } from "src/business/Interfaces/Prisma/IPayout";

export class PayoutMapper extends MappingProfile {
  static readonly IPayoutToIPayoutListViewModel = new MappingPair<IPayout, IPayoutListViewModel>();

  static readonly IPayoutToIPayoutAlertListViewModel = new MappingPair<IPayout, IPayoutAlertListViewModel>();

  static readonly IPayoutListDTOToIPayoutListViewModel = new MappingPair<IPayoutListDTO, IPayoutListViewModel>();

  constructor() {
    super();

    this.createAutoMap(PayoutMapper.IPayoutToIPayoutListViewModel, {
      id: (opt) => opt.mapFrom((src) => src.id),
      orderId: (opt) => opt.mapFrom((src) => src.orderId),
      administrativeFeePercent: (opt) => opt.mapFrom((src) => src.administrativeFeePercent),
      administrativeFeeValue: (opt) => opt.mapFrom((src) => src.administrativeFeeValue),
      createdAt: (opt) => opt.mapFrom((src) => src.createdAt),
      transferValue: (opt) => opt.mapFrom((src) => src.transferValue),
      statusDate: (opt) => opt.mapFrom((src) => src.statusDate),
      status: (opt) => opt.mapFrom((src) => src.status),
      payoutOwner: (opt) => opt.mapFrom((src) => src.payoutOwner),
      price: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.mapFrom((src) => src.order!.price);
      },
      shippingPrice: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.mapFrom((src) => src.order!.shippingPrice);
      },
      totalPrice: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.mapFrom((src) => src.order!.totalPrice);
      },
      storeName: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.store !== undefined);
        opt.mapFrom((src) => src.order!.store!.name);
      },
      storeCnpj: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.store !== undefined);
        opt.mapFrom((src) => src.order!.store!.cnpj);
      },
      deliverymanFirstName: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman?.user !== undefined);
        opt.mapFrom((src) => src.order!.deliveryman!.user!.firstName);
      },
      deliverymanLastName: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman?.user !== undefined);
        opt.mapFrom((src) => src.order!.deliveryman!.user!.lastName);
      },
      deliverymanCpf: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman?.user !== undefined);
        opt.mapFrom((src) => src.order!.deliveryman!.user!.cpf);
      },
    }).forSourceMember("order", (opt) => opt.ignore());

    this.createAutoMap(PayoutMapper.IPayoutListDTOToIPayoutListViewModel, {
      id: (opt) => opt.mapFrom((src) => src.id),
      orderId: (opt) => opt.mapFrom((src) => src.orderId),
      administrativeFeePercent: (opt) => opt.mapFrom((src) => src.administrativeFeePercent),
      administrativeFeeValue: (opt) => opt.mapFrom((src) => src.administrativeFeeValue),
      createdAt: (opt) => opt.mapFrom((src) => src.createdAt),
      transferValue: (opt) => opt.mapFrom((src) => src.transferValue),
      statusDate: (opt) => opt.mapFrom((src) => src.statusDate),
      status: (opt) => opt.mapFrom((src) => src.status),
      payoutOwner: (opt) => opt.mapFrom((src) => src.payoutOwner),
      price: (opt) => opt.mapFrom((src) => src.price),
      shippingPrice: (opt) => opt.mapFrom((src) => src.shippingPrice),
      totalPrice: (opt) => opt.mapFrom((src) => src.totalPrice),
      deliverymanCpf: (opt) => opt.ignore(),
      deliverymanFirstName: (opt) => opt.ignore(),
      deliverymanLastName: (opt) => opt.ignore(),
      storeCnpj: (opt) => opt.ignore(),
      storeName: (opt) => opt.ignore(),
    });

    this.createAutoMap(PayoutMapper.IPayoutToIPayoutAlertListViewModel, {
      id: (opt) => opt.mapFrom((src) => src.id),
      period: (opt) => opt.ignore(),
      status: (opt) => opt.mapFrom((src) => src.status),
      payoutOwner: (opt) => opt.mapFrom((src) => src.payoutOwner),
      transferValue: (opt) => opt.mapFrom((src) => src.transferValue),
      storeId: (opt) => opt.mapFrom((src) => src?.order?.store?.id),
      userId: (opt) => opt.mapFrom((src) => src?.order?.deliveryman?.user?.id),
      cooperativeName: (opt) => {
        opt.preCondition((opt) => opt.cooperative !== undefined);
        opt.preCondition((opt) => opt.cooperative?.name !== undefined);
        opt.mapFrom((src) => src.cooperative!.name);
      },
      cooperativeCnpj: (opt) => {
        opt.preCondition((opt) => opt.cooperative !== undefined);
        opt.preCondition((opt) => opt.cooperative?.cnpj !== undefined);
        opt.mapFrom((src) => src.cooperative!.cnpj);
      },
      storeName: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.store !== undefined);
        opt.mapFrom((src) => src.order!.store!.name);
      },
      storeCnpj: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.store !== undefined);
        opt.mapFrom((src) => src.order!.store!.cnpj);
      },
      deliverymanFirstName: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman?.user !== undefined);
        opt.mapFrom((src) => src.order!.deliveryman!.user!.firstName);
      },
      deliverymanLastName: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman?.user !== undefined);
        opt.mapFrom((src) => src.order!.deliveryman!.user!.lastName);
      },
      deliverymanCpf: (opt) => {
        opt.preCondition((opt) => opt.order !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman !== undefined);
        opt.preCondition((opt) => opt.order?.deliveryman?.user !== undefined);
        opt.mapFrom((src) => src.order!.deliveryman!.user!.cpf);
      },
    }).forSourceMember("order", (opt) => opt.ignore());
  }
}
