import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IListCategoryViewModel } from "src/api/ViewModels/Category/IList";
import { ICreateSubcategoryViewModel } from "src/api/ViewModels/Subcategory/ICreate";
import { IListSubcategoryViewModel } from "src/api/ViewModels/Subcategory/IList";
import { ISubcategoryViewModel } from "src/api/ViewModels/Subcategory/ISubcategory";
import { ISubcategoryFrontViewModel } from "src/api/ViewModels/Subcategory/ISubcategoryFront";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { ISubcategory } from "src/business/Interfaces/Prisma/ISubcategory";

export class SubcategoryMapper extends MappingProfile {
  static readonly ISubcategoryToCategoryListViewModel = new MappingPair<ISubcategory, IListCategoryViewModel>();

  static readonly ISubcategoryToSubcategoryFrontViewModel = new MappingPair<ISubcategory, ISubcategoryFrontViewModel>();

  // static readonly ISubcategoryToSubCategoryViewModel = new MappingPair<
  //   ISubcategory,
  //   ISubcategoryViewModel
  // >();

  static readonly ISubcategoryToIListSubcategoryViewModel = new MappingPair<ISubcategory, IListSubcategoryViewModel>();

  static readonly IProductSubcategoryToISubcategoryViewModel = new MappingPair<
    IProductSubcategory,
    ISubcategoryViewModel
  >();

  static readonly ICreateSubcategoryViewModelToISubcategory = new MappingPair<ICreateSubcategoryViewModel, ISubcategory>();

  constructor() {
    super();

    this.createAutoMap(SubcategoryMapper.ISubcategoryToCategoryListViewModel, {
      subcategory: (opt) => opt.nullSubstitute(undefined),
      checked: (opt) => opt.mapFrom(() => false),
    });

    this.createAutoMap(SubcategoryMapper.ISubcategoryToSubcategoryFrontViewModel, {
    })
      .forSourceMember("categorySubcategory", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("productSubcategory", (opt) => opt.ignore())
      ;

    // this.createAutoMap(
    //   SubcategoryMapper.ISubcategoryToSubCategoryViewModel,
    //   {}
    // );

    this.createAutoMap(SubcategoryMapper.ISubcategoryToIListSubcategoryViewModel, {
      checked: (opt) => opt.ignore(),
    })
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore());

    this.createAutoMap(SubcategoryMapper.IProductSubcategoryToISubcategoryViewModel, {
      name: (opt) => opt.mapFrom((src) => src.subcategory?.name!),
      checked: (opt) => opt.ignore(),
      description: (opt) => opt.mapFrom((src) => src.subcategory?.description || ""),
    });

    this.createAutoMap(SubcategoryMapper.ICreateSubcategoryViewModelToISubcategory, {
      id: (opt) => opt.ignore(),
      name: (opt) => opt.mapFrom((src) => src.name),
      description: (opt) => opt.mapFrom((src) => src.description),
      categorySubcategory: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      productSubcategory: (opt) => opt.ignore()
    });
  }
}

// const subcategoryMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     Subcategory,
//     CategoryListViewModel,
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     ),
//     forMember(
//       (destination) => destination.name,
//       mapFrom((start) => start?.name)
//     ),
//     forMember(
//       (destination) => destination.checked,
//       mapWithArguments(
//         (start: Category, { checkedItem }) => checkedItem || false
//       )
//     )
//   );

//   createMap(mapper, Subcategory, SubCategoryViewModel);
// };

// export { subcategoryMapper };
