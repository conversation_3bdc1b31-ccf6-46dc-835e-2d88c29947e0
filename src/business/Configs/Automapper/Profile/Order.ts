/* eslint-disable no-plusplus */
/* eslint-disable no-const-assign */
/* eslint-disable arrow-body-style */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IAttributeViewModel } from "src/api/ViewModels/Attribute/IAttribute";
import { IAttributeOptionViewModel } from "src/api/ViewModels/Attribute/IAttributeOption";
import { ICreateOrderViewModel } from "src/api/ViewModels/Order/ICreateOrder";
import { ICreateOrderItemViewModel } from "src/api/ViewModels/Order/ICreateOrderItem";
import { ICreateOrderResultViewModel } from "src/api/ViewModels/Order/ICreateOrderResult";
import { ICreateProductAttributeOption } from "src/api/ViewModels/Order/ICreateProductAttributeOption";
import { IDeliverymanSalesViewModel } from "src/api/ViewModels/Order/IDeliverymanSales";
import { IOrderFinancialConsolidation } from "src/api/ViewModels/Order/IFinancialConsolidation";
import { IFinancialDetailsViewModel } from "src/api/ViewModels/Order/IFinancialConsolidationDetails";
import { IListOrderViewModel } from "src/api/ViewModels/Order/IListOrder";
import { IListOrderByStatusViewModel } from "src/api/ViewModels/Order/IListOrderByStatus";
import { IListOrderFrontViewModel } from "src/api/ViewModels/Order/IListOrderFront";
import { IListOrderItemViewModel } from "src/api/ViewModels/Order/IListOrderItem";
import { IListOrderStatusViewModel } from "src/api/ViewModels/Order/IListOrderStatus";
import { IListUserOrdersViewModel } from "src/api/ViewModels/Order/IListUserOrders";
import { IOrderDetailsViewModel } from "src/api/ViewModels/Order/IOrderDetails";
import { IOrderDetailsBackOfficeViewModel } from "src/api/ViewModels/Order/IOrderDetailsBackOffice";
import { IOrderItemProductViewModel, ItemProduct } from "src/api/ViewModels/Order/IOrderItemProductViewModel";
import { ISalesTotalizerViewModel } from "src/api/ViewModels/Order/ISalesTotalizer";
import { IShopkeeperSalesViewModel } from "src/api/ViewModels/Sales/IShopkeeperSales";
import { DeliverymanMapper } from "src/business/Configs/Automapper/Profile/Deliveryman";
import { ProductMapper } from "src/business/Configs/Automapper/Profile/Product";
import { StoreMapper } from "src/business/Configs/Automapper/Profile/Store";
import { ICreateOrderResultDTO } from "src/business/DTOs/Order/ICreateOrderResult";
import { IOrderFinancialConsolidationDTO } from "src/business/DTOs/Order/IFinancialConsolidation";
import { IOrderByStatusDTO } from "src/business/DTOs/Order/IOrderByStatus";
import { IOrderDetailsDTO, IOrderStatusDetailsDTO } from "src/business/DTOs/Order/IOrderDetails";
import { IOrderDetailsBackOfficeDTO } from "src/business/DTOs/Order/IOrderDetailsBackOffice";
import { IOrderSalesDTO } from "src/business/DTOs/Order/IOrderSales";
import { IUserOrdersDTO } from "src/business/DTOs/Order/IUserOrders";
import { Totalizer } from "src/business/DTOs/PagedResult";
import { EFinancialConsolidation } from "src/business/Enums/EFinancialConsolidation";
import ESalesTotalizer from "src/business/Enums/ESalesTotalizer";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { IOrder } from "src/business/Interfaces/Prisma/IOrder";
import { IOrderItem } from "src/business/Interfaces/Prisma/IOrderItem";
import { IOrderItemProductAttributeOption } from "src/business/Interfaces/Prisma/IOrderItemProductAttributeOption";
import { IOrderStatus } from "src/business/Interfaces/Prisma/IOrderStatus";
import { IUpdateOrderViewModel } from "src/api/ViewModels/Order/IUpdateOrder";
import { IMemberConfigurationExpression } from "@dynamic-mapper/mapper/lib/interface";
import { IFinancialConsolidationTransactions } from "src/business/Interfaces/Prisma/IFinancialConsolidationTxn";
import { AddressMapper } from "./Address";
import { UserMapper } from "./User";

export class OrderMapper extends MappingProfile {
  static readonly CreateOrderViewModelToIOrder = new MappingPair<ICreateOrderViewModel, IOrder>();

  static readonly UpdateOrderViewModelToIOrder = new MappingPair<IUpdateOrderViewModel, IOrder>();

  static readonly ICreateOrderItemViewModelToIOrderItem = new MappingPair<ICreateOrderItemViewModel, IOrderItem>();

  static readonly ICreateProductAttributeOptionToIOrderItemProductAttributeOption = new MappingPair<
    ICreateProductAttributeOption,
    IOrderItemProductAttributeOption
  >();

  static readonly IOrderToIListOrderViewModel = new MappingPair<IOrder, IListOrderViewModel>();

  static readonly IUserOrdersDTOToIListOrderViewModel = new MappingPair<IUserOrdersDTO, IListUserOrdersViewModel>();

  static readonly IOrderByStatusDTOToIListOrderViewModel = new MappingPair<
    IOrderByStatusDTO,
    IListOrderByStatusViewModel
  >();

  static readonly IOrderSalesDTOToIDeliverymanSalesViewModel = new MappingPair<
    IOrderSalesDTO,
    IDeliverymanSalesViewModel
  >();

  static readonly IOrderSalesDTOToIShopkeeperSalesViewModel = new MappingPair<
    IOrderSalesDTO,
    IShopkeeperSalesViewModel
  >();

  static readonly ITotalizerToISalesTotalizerViewModel = new MappingPair<Totalizer, ISalesTotalizerViewModel>();

  static readonly IOrderDetailsDTOToIOrderDetailsViewModel = new MappingPair<
    IOrderDetailsDTO,
    IOrderDetailsViewModel
  >();

  static readonly IOrderToIOrderFinancialConsolidation = new MappingPair<
    IOrderFinancialConsolidationDTO,
    IOrderFinancialConsolidation
  >();

  static readonly IOrderToIFinancialDetailsViewModel = new MappingPair<IOrder, IFinancialDetailsViewModel>();

  static readonly IOrderItemToIListOrderItemViewModel = new MappingPair<IOrderItem, IListOrderItemViewModel>();

  static readonly IOrderStatusToIListOrderStatusViewModel = new MappingPair<IOrderStatus, IListOrderStatusViewModel>();

  static readonly IOrderStatusDetailsDTOToIListOrderStatusViewModel = new MappingPair<
    IOrderStatusDetailsDTO,
    IListOrderStatusViewModel
  >();

  // static readonly IOrderArrayToOrderListViewModelArray = new MappingPair<
  //   IOrder[],
  //   OrderListViewModel[]
  // >();

  static readonly IOrderToIListOrderFrontViewModel = new MappingPair<IOrder, IListOrderFrontViewModel>();

  // static readonly IOrderArrayToOrderListFrontViewModelArray = new MappingPair<
  //   IOrder[],
  //   OrderListFrontViewModel[]
  // >();

  static readonly IOrderToIOrdertemProductVewModel = new MappingPair<IOrder, IOrderItemProductViewModel>();

  static readonly IOrderItemToItemProduct = new MappingPair<IOrderItem, ItemProduct>();

  static readonly ICreateOrderResultDTOToICreateOrderResultViewModel = new MappingPair<
    ICreateOrderResultDTO,
    ICreateOrderResultViewModel
  >();

  static readonly IOrderDetailsBackOfficeDTOToIOrderDetailsBackOfficeViewModel = new MappingPair<
    IOrderDetailsBackOfficeDTO,
    IOrderDetailsBackOfficeViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(OrderMapper.ICreateProductAttributeOptionToIOrderItemProductAttributeOption, {
      id: (opt) => opt.ignore(),
      orderItem: (opt) => opt.ignore(),
      productAttributeOption: (opt) => opt.ignore(),
      orderItemId: (opt) => opt.ignore(),
      productAttributeOptionId: (opt) => opt.mapFrom((src) => src.id),
    });

    this.createAutoMap(OrderMapper.ICreateOrderItemViewModelToIOrderItem, {
      id: (opt) => opt.ignore(),
      product: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      orderItemProductAttributeOption: (opt) => {
        opt.preCondition(
          (src) => src.productAttributeOptionIds !== undefined && Array.isArray(src.productAttributeOptionIds),
        );
        opt.condition(
          (src) => src.productAttributeOptionIds !== undefined && Array.isArray(src.productAttributeOptionIds),
        );
        opt.mapFromUsing(
          (src) => src.productAttributeOptionIds,
          OrderMapper.ICreateProductAttributeOptionToIOrderItemProductAttributeOption,
        );
      },
    })
      .forSourceMember("productAttributeOptionIds", (opt) => opt.ignore())
      .forSourceMember("attributes", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.CreateOrderViewModelToIOrder, {
      orderItem: (opt) =>
        opt.mapFromUsing((src) => src.orderItem.map((p) => p), OrderMapper.ICreateOrderItemViewModelToIOrderItem),
      orderStatus: (opt) =>
        opt.mapFrom((src) =>
          src.orderStatus.map((p) => ({
            orderStatusTypeId: p.orderStatusType.id,
          })),
        ),
      id: (opt) => opt.ignore(),
      code: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      address: (opt) => opt.ignore(),
      deliveryman: (opt) => opt.ignore(),
      review: (opt) => opt.ignore(),
      customerCode: (opt) => opt.ignore(),
      transaction: (opt) => opt.ignore(),
      routeLength: (opt) => opt.ignore(),
      financialConsolidation: (opt) => opt.ignore(),
    }).forSourceMember("orderTransactionData", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.UpdateOrderViewModelToIOrder, {
      orderItem: (opt) =>
        opt.mapFromUsing((src) => src.orderItem.map((p) => p), OrderMapper.ICreateOrderItemViewModelToIOrderItem),
      orderStatus: (opt) => opt.ignore(),
      id: (opt) => opt.ignore(),
      code: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      address: (opt) => opt.ignore(),
      deliveryman: (opt) => opt.ignore(),
      review: (opt) => opt.ignore(),
      customerCode: (opt) => opt.ignore(),
      transaction: (opt) => opt.ignore(),
      routeLength: (opt) => opt.ignore(),
      financialConsolidation: (opt) => opt.ignore(),
    }).forSourceMember("orderTransactionData", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.IOrderStatusToIListOrderStatusViewModel, {
      id: (opt) => {
        opt.preCondition((src) => src.id !== undefined);
        opt.mapFrom((src) => src.id!);
      },
      value: (opt) => {
        opt.preCondition((src) => src.orderStatusType !== undefined);
        opt.mapFrom((src) => src.orderStatusType!.value);
      },
    })
      .forSourceMember("orderStatusTypeId", (opt) => opt.ignore())
      .forSourceMember("orderId", (opt) => opt.ignore())
      .forSourceMember("orderStatusType", (opt) => opt.ignore());

    // TODO unitPrice and totalPrice are being converted to string
    this.createAutoMap(OrderMapper.IOrderItemToIListOrderItemViewModel, {
      productName: (opt) => {
        opt.preCondition((src) => src.product !== undefined);
        opt.mapFrom((src) => src.product!.name);
      },
      storeId: (opt) => {
        opt.preCondition((src) => src.product !== undefined);
        opt.mapFrom((src) => src.product!.storeId);
      },
      attributes: (opt) => {
        opt.preCondition((src) => src.orderItemProductAttributeOption !== undefined);
        opt.condition((src) => src.orderItemProductAttributeOption !== undefined);
        opt.mapFrom((src) => {
          const attributes: IAttributeViewModel[] = [];

          src.orderItemProductAttributeOption?.forEach((orderItemProductAttributeOption) => {
            const attribute = orderItemProductAttributeOption.productAttributeOption?.productAttribute?.attribute;
            if (!attribute) return;

            const attributeViewModel: IAttributeViewModel = {
              id: attribute.id,
              name: attribute.name,
              required: attribute.required,
              shortDescription: attribute.shortDescription,
              type: attribute.type,
            };

            const attributeOption = orderItemProductAttributeOption.productAttributeOption?.attributeOption;
            if (attributeOption) {
              const option: IAttributeOptionViewModel = {
                id: attributeOption.id,
                value: attributeOption.value,
              };

              attributeViewModel.attributeOption = [option];

              const index = attributes.findIndex((a) => a.id === attributeViewModel?.id);
              if (index === -1) {
                attributes.push(attributeViewModel);
              } else {
                attributes[index].attributeOption?.push(option);
              }
            }
          });

          return attributes;
        });
      },
      product: (opt) => opt.mapFromUsing((src) => src.product, ProductMapper.IProductToIProductViewModel),
    })
      .forSourceMember("id", (opt) => opt.ignore())
      .forSourceMember("orderId", (opt) => opt.ignore())
      // .forSourceMember("observation", (opt) => opt.ignore())
      .forSourceMember("orderItemProductAttributeOption", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.IOrderToIListOrderViewModel, {
      orderStatus: (opt) =>
        opt.mapFromUsing(
          (src) => src.orderStatus?.filter((os) => os.current),
          OrderMapper.IOrderStatusToIListOrderStatusViewModel,
        ),
      store: (opt) => {
        opt.preCondition((src) => src.store !== undefined && src.store !== null);
        opt.mapFrom((src) => ({ id: src.store!.id, name: src.store!.name }));
      },
      deliveryman: (opt) => {
        opt.preCondition((src) => src.deliveryman !== undefined && src.deliveryman !== null);
        opt.mapFromUsing((src) => src.deliveryman, DeliverymanMapper.IDeliverymanToIListDeliverymanViewModel);
      },
      user: (opt) => {
        opt.mapFromUsing((src) => src.user, UserMapper.IUserToIUserViewModel);
      },
    }).forSourceMember("review", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.IUserOrdersDTOToIListOrderViewModel, {
      orderItem: (opt) =>
        opt.mapFrom((src) => {
          return src.orderItem.map((item) => ({
            id: item.id,
            productName: item.product.name,
            quantity: item.quantity,
            observation: item.observation || "",
          }));
        }),
      lastStatus: (opt) => opt.mapFrom((src) => src.orderStatus[0].orderStatusType.value),
      rating: (opt) =>
        opt.mapFrom((src) => {
          if (src.review === undefined) return undefined;

          let aux = 0;

          const reviewSum = src.review.reduce((acc, curr) => {
            if (curr.rate === undefined) return acc;

            if (curr.deliverymanId !== null) return acc;

            aux++;

            return acc + curr.rate;
          }, 0);

          if (reviewSum === 0 || aux === 0) return 0;

          return reviewSum / aux;
        }),
      store: (opt) => {
        opt.mapFrom((src) => ({
          id: src.store?.id || "",
          name: src.store?.name || "",
        }));
      },
      ratingDelivery: (opt) =>
        opt.mapFrom((src) => {
          if (src.review === undefined) return undefined;
          let aux = 0;

          const reviewSum = src.review.reduce((acc, curr) => {
            if (curr.rate === undefined) return acc;

            if (curr.deliverymanId !== null) {
              aux++;

              return acc + curr.rate;
            }

            return acc;
          }, 0);

          if (reviewSum === 0) return 0;

          return reviewSum / aux;
        }),
      deliverymanId: (opt) => opt.mapFrom((src) => src.deliveryman?.id || ""),
    })
      .forSourceMember("orderItem", (opt) => opt.ignore())
      .forSourceMember("orderStatus", (opt) => opt.ignore())
      .forSourceMember("review", (opt) => opt.ignore())
      .forSourceMember("deliveryman", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.IOrderByStatusDTOToIListOrderViewModel, {
      orderItem: (opt) =>
        opt.mapFrom((src) => {
          return src.orderItem.map((item) => ({
            productName: item.product.name,
            quantity: item.quantity,
          }));
        }),
      lastStatus: (opt) =>
        opt.mapFrom((src) => {
          const lastStatus = src.orderStatus[0];

          return {
            createdAt: lastStatus.createdAt,
            value: lastStatus.orderStatusType.value,
          };
        }),
    })
      .forSourceMember("orderItem", (opt) => opt.ignore())
      .forSourceMember("orderStatus", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.IOrderStatusDetailsDTOToIListOrderStatusViewModel, {
      current: (opt) => opt.mapFrom((src) => src.current),
      value: (opt) => opt.mapFrom((src) => src.orderStatusType.value),
      id: (opt) => opt.mapFrom((src) => src.id),
      observation: (opt) => {
        opt.preCondition((src) => src.observation !== undefined && src.observation !== null);
        opt.mapFrom((src) => src?.observation!);
      },
    }).forSourceMember("orderStatusType", (opt) => opt.ignore());

    this.createAutoMap(OrderMapper.IOrderDetailsDTOToIOrderDetailsViewModel, {
      orderItem: (opt) => opt.mapFromUsing((src) => src.orderItem, OrderMapper.IOrderItemToIListOrderItemViewModel),
      deliveryman: (opt) => {
        opt.preCondition(
          (src) => src.deliveryman !== undefined && src.deliveryman !== null && src.deliveryman.user !== null,
        );
        opt.mapFrom((src) => {
          const deliveryman = src?.deliveryman;
          const firstName = deliveryman?.user.firstName!;
          const lastName = deliveryman?.user.lastName!;

          let average = 0;
          if (deliveryman?.review === undefined) {
            average = 0;
          } else {
            const reviewSum = deliveryman.review.reduce((acc, curr) => {
              if (curr.rate === undefined) return acc;

              return acc + curr.rate;
            }, 0);

            if (reviewSum === 0) {
              average = 0;
            } else {
              average = reviewSum / deliveryman.review.length;
            }
          }

          return {
            firstName,
            lastName,
            rating: average.toFixed(2) || "",
          };
        });
      },
      orderStatus: (opt) =>
        opt.mapFromUsing((src) => src.orderStatus, OrderMapper.IOrderStatusDetailsDTOToIListOrderStatusViewModel),
    });

    this.createAutoMap(OrderMapper.IOrderToIListOrderFrontViewModel, {
      // TODO Check orderStatus result and if it is ordered
      orderStatus: (opt) =>
        opt.mapFrom(
          (src) => (src.orderStatus?.[0].orderStatusType?.value as EOrderStatusValue) || EOrderStatusValue.placed_order,
        ),
      address: (opt) => opt.mapFromUsing((src) => src.address, AddressMapper.IAddressToIListAddressViewModel),
      store: (opt) => {
        opt.preCondition((src) => src.store !== undefined && src.store !== null);
        opt.mapFromUsing((src) => src.store!, StoreMapper.IStoreToIStoreViewModel);
      },
      deliveryman: (opt) => {
        opt.preCondition((src) => src.deliveryman !== undefined);
        opt.mapFromUsing((src) => src.deliveryman!, DeliverymanMapper.IDeliverymanToIListDeliverymanViewModel);
      },
      user: (opt) => opt.mapFromUsing((src) => src.user, UserMapper.IUserToIUserViewModel),
      orderItem: (opt) => opt.ignore(),
    });

    this.createMap(OrderMapper.IOrderToIOrdertemProductVewModel, {
      // address: (opt) => opt.mapFrom((src) => {
      //   if (src.address) return src.address;
      //   return {} as IAddress;
      // }),
      // code: (opt) => opt.mapFrom((src) => src.code),
      // id: (opt) => opt.mapFrom((src) => src.id),
      orderItems: (opt) => opt.mapFromUsing((src) => src.orderItem, OrderMapper.IOrderItemToItemProduct),
      // price: (opt) => opt.mapFrom((src) => src.price),
      // quantityItems: (opt) => opt.mapFrom((src) => src.quantityItems),
      // quantityProducts: (opt) => opt.mapFrom((src) => src.quantityProducts),
      // shippingPrice: (opt) => opt.mapFrom((src) => src.shippingPrice),
      // storeId: (opt) => opt.mapFrom((src) => src.storeId),
      storeName: (opt) => opt.mapFrom((src) => src.store?.name || ""),
    });

    this.createMap(OrderMapper.IOrderItemToItemProduct, {
      id: (opt) => opt.mapFrom((src) => src.id),
      product: (opt) =>
        opt.mapFrom((src) => {
          return { id: src.product.id, name: src.product.name };
        }),
    });

    this.createAutoMap(OrderMapper.ICreateOrderResultDTOToICreateOrderResultViewModel, {});

    this.createMap(OrderMapper.IOrderToIOrderFinancialConsolidation, {
      id: (opt) => opt.mapFrom((src) => src.id),
      status: (opt) =>
        opt.mapFrom((src) =>
          src.financialConsolidation?.status
            ? src.financialConsolidation?.status
            : EFinancialConsolidation.correctValues,
        ),
      cooperativeRate: (opt) => opt.mapFrom(() => 0),
      firstName: (opt) =>
        opt.mapFrom((src) =>
          src.status === EFinancialConsolidation.onlyFinanceCompany ? undefined : src.deliveryman?.user?.firstName,
        ),
      totalPrice: (opt) => opt.mapFrom((src) => src.totalPrice),
      paymentRate: (opt) => opt.mapFrom((src) => 0),
      paymentType: (opt) => opt.mapFrom((src) => src.transaction?.[0]?.paymentMethod || ""),
      storeName: (opt) =>
        opt.mapFrom((src) => (src.status === EFinancialConsolidation.onlyFinanceCompany ? undefined : src.store?.name)),
      price: (opt) =>
        opt.mapFrom((src) => (src.status === EFinancialConsolidation.onlyFinanceCompany ? undefined : src.price)),
      shippingPrice: (opt) =>
        opt.mapFrom((src) =>
          src.status === EFinancialConsolidation.onlyFinanceCompany ? undefined : src.shippingPrice,
        ),
      createdAt: (opt) => opt.mapFrom((src) => src.createdAt || new Date()),
      financeCompanyPayment: (opt) => opt.mapFrom((src) => src.financeCompanyPayment),
      financeCompanyId: (opt) => opt.mapFrom((src) => src.financeCompanyId),
      financeCompanyStore: (opt) => opt.mapFrom((src) => src.financeCompanyStore),
    });

    this.createMap(OrderMapper.IOrderToIFinancialDetailsViewModel, {
      financial: (opt) => opt.mapFromUsing((src) => src, OrderMapper.IOrderToIOrderFinancialConsolidation),
      deliveryman: (opt) => {
        opt.preCondition(
          (src) => src.deliveryman !== undefined && src.deliveryman !== null && src.deliveryman.user !== null,
        );
        opt.mapFromUsing((src) => src.deliveryman, DeliverymanMapper.IDeliverymanToIUserDeliverymanViewModel);
      },
      store: (opt) => opt.mapFromUsing((src) => src.store, StoreMapper.IStoreToIStoreViewModel),
    });

    this.createMap(OrderMapper.IOrderSalesDTOToIDeliverymanSalesViewModel, {
      id: (opt) => opt.mapFrom((src) => src.id),
      createdAt: (opt) => opt.mapFrom((src) => src.createdAt || new Date()),
      distance: (opt) => opt.mapFrom((src) => src.routeLength!),
      orderCode: (opt) => opt.mapFrom((src) => src.code),
      price: (opt) => opt.mapFrom((src) => src.price),
      shippingPrice: (opt) => opt.mapFrom((src) => src.shippingPrice),
      store: (opt) => opt.mapFrom((src) => src.store?.name || ""),
      totalPrice: (opt) => opt.mapFrom((src) => src.totalPrice),
      address: (opt) => opt.mapFromUsing((src) => src.address as any, AddressMapper.IAddressToIListAddressViewModel),
    });

    this.createMap(OrderMapper.ITotalizerToISalesTotalizerViewModel, {
      key: (opt) => opt.mapFrom((src) => Object.keys(src).map((key) => key)[0]),
      value: (opt) => opt.mapFrom((src) => Object.values(src).map((value) => (value !== null ? value : 0))[0]),
      type: (opt) =>
        opt.mapFrom(
          (src) =>
            Object.keys(src).map((key) =>
              // eslint-disable-next-line no-nested-ternary
              key === "routeLength"
                ? ESalesTotalizer.distance
                : key.toLowerCase().includes("price") || key.toLowerCase().includes("payout")
                ? ESalesTotalizer.currency
                : ESalesTotalizer.quantity,
            )[0],
        ),
    });

    this.createMap(OrderMapper.IOrderSalesDTOToIShopkeeperSalesViewModel, {
      id: (opt) => opt.mapFrom((src) => src.id),
      createdAt: (opt) => opt.mapFrom((src) => src.createdAt || new Date()),
      orderCode: (opt) => opt.mapFrom((src) => src.code),
      price: (opt) => opt.mapFrom((src) => src.price),
      shippingPrice: (opt) => opt.mapFrom((src) => src.shippingPrice),
      store: (opt) => opt.mapFrom((src) => src.store?.name || ""),
      totalPrice: (opt) => opt.mapFrom((src) => src.totalPrice),
      orderStatus: (opt) =>
        opt.mapFrom((src) => {
          return src.orderStatus
            ? (src.orderStatus.filter((status) => status.current === true)[0].orderStatusType
                ?.value as EOrderStatusValue)
            : undefined;
        }),
    });

    this.createAutoMap(OrderMapper.IOrderDetailsBackOfficeDTOToIOrderDetailsBackOfficeViewModel, {
      deliveryman: (opt) => {
        opt.preCondition(
          (src) => src.deliveryman !== undefined && src.deliveryman !== null && src.deliveryman.user !== null,
        );
        opt.mapFrom((src) => {
          return {
            id: src.deliveryman?.id!,
            firstName: src.deliveryman?.user.firstName!,
            lastName: src.deliveryman?.user.lastName!,
            cpf: src.deliveryman?.user.cpf!,
          };
        });
      },
      orderStatus: (opt) =>
        opt.mapFromUsing(
          (src) => src.orderStatus?.filter((os) => os.current),
          OrderMapper.IOrderStatusDetailsDTOToIListOrderStatusViewModel,
        ),
    });
  }
}

// const orderMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     CreateOrderViewModel,
//     Order,
//     forMember(
//       (destination) => destination.orderItem,
//       mapFrom((start) => {
//         const orderItemDB: OrderItem[] = [];
//         start.orderItem.map((item) =>
//           orderItemDB.push({
//             quantity: item.quantity,
//             totalPrice: item.totalPrice,
//             unityPrice: item.unityPrice,
//             observation: item.observation,
//             productId: item.productId,
//             orderItemProductAttributeOption: item.productAttributeOptionIds,
//           } as OrderItem)
//         );
//         return orderItemDB;
//       })
//     ),
//     forMember(
//       (destination) => destination.orderStatus,
//       mapFrom((start) => start.orderStatus)
//     )
//   );
//   createMap(
//     mapper,
//     Order,
//     OrderListViewModel,
//     forMember(
//       (destination) => destination.orderItem,
//       mapFrom(
//         (start) => {
//           const orderItemDB: OrderItemListViewModel[] = [];
//           start.orderItem?.map((item) =>
//             orderItemDB.push({
//               orderId: item.orderId,
//               product: item.product as unknown as ProductViewModel,
//               quantity: item.quantity,
//               totalPrice: item.totalPrice,
//               unityPrice: item.unityPrice,
//               attribute: item.orderItemProductAttributeOption?.reduce(
//                 (attributeList, attribute) => {
//                   const dataAttribute = {
//                     id: attribute.productAttributeOption!.productAttribute
//                       .attribute.id,
//                     name: attribute.productAttributeOption!.productAttribute
//                       .attribute.name,
//                     short_description:
//                       attribute.productAttributeOption!.productAttribute
//                         .attribute.short_description,
//                     required:
//                       attribute.productAttributeOption!.productAttribute
//                         .attribute.required,
//                     type: attribute.productAttributeOption!.productAttribute
//                       .attribute.type,
//                     checked: true,
//                     attributeOptions: [] as AttributeOptionClass[],
//                   };

//                   const dataAttOpt = {
//                     id: attribute.productAttributeOption!.attributeOption.id,
//                     value:
//                       attribute.productAttributeOption!.attributeOption.value,
//                     checked: true,
//                   };

//                   const indice = attributeList.findIndex(
//                     (att) => att.id === dataAttribute.id
//                   );

//                   if (indice > -1) {
//                     attributeList[indice].attributeOption?.push(dataAttOpt);
//                   } else {
//                     dataAttribute.attributeOptions.push(dataAttOpt);
//                     attributeList.push(dataAttribute);
//                   }

//                   return attributeList;
//                 },
//                 [] as AttributeViewModel[]
//               ),
//               observation: item.observation,
//             })
//           );
//           return orderItemDB;
//         }
//       )
//     ),
//     forMember(
//       (destination) => destination.store,
//       mapFrom((start) => {
//         const storeFront: StoreViewModel = {
//           name: start.store?.name,
//           cnpj: start.store?.cnpj,
//           email: start.store?.email,
//           phone: start.store?.phone,
//           slug: start.store?.slug,
//           description: start.store?.description,
//           active: start.store?.active,
//           open: start.store?.open,
//         };
//         return storeFront;
//       })
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start.id)
//     ),
//     forMember(
//       (destination) => destination.price,
//       mapFrom((start) => start.price)
//     ),
//     forMember(
//       (destination) => destination.createdAt,
//       mapFrom((start) => start.createdAt)
//     ),
//     forMember(
//       (destination) => destination.address,
//       mapFrom((start) => ({ ...start.address }))
//     ),
//     forMember(
//       (destination) => destination.rating,
//       mapFrom((start) => start.review?.rate)
//     ),
//     forMember(
//       (destination) => destination.orderStatus,
//       mapFrom((start) => {
//         const orderStatus: OrderStatusListViewModel[] = [];
//         const statusType = container.get<OrderStatusTypeDB>(
//           TOKENS.OrderStatusTypeDB
//         );
//         start.orderStatus?.forEach((status) => {
//           const statusKey = Object.keys(EOrderStatusType)
//             .filter((value) => Number.isNaN(Number(value)))
//             .find(
//               (key) => statusType[key].value === status.orderStatusType.value
//             );
//           orderStatus.push({
//             id: status.id,
//             type: EOrderStatusType[statusKey || 0],
//             value: status.orderStatusType.value,
//             observation: status.observation,
//             createdAt: status.createdAt,
//           });
//         });
//         return orderStatus.sort((a, b) => a.type - b.type);
//       })
//     )
//   );
//   createMap(
//     mapper,
//     Order,
//     OrderListFrontViewModel,
//     forMember(
//       (destination) => destination.user,
//       mapFrom((start) => {
//         const userFront: UserViewModel = {
//           firstName: start.user?.firstName,
//           lastName: start.user?.lastName,
//           email: start.user?.email,
//           phone: start.user?.phone,
//         };
//         return userFront;
//       })
//     ),

//     forMember(
//       (destination) => destination.store,
//       mapFrom((start) => {
//         const storeFront: StoreViewModel = {
//           name: start.store?.name,
//           cnpj: start.store?.cnpj,
//           email: start.store?.email,
//           phone: start.store?.phone,
//           slug: start.store?.slug,
//           description: start.store?.description,
//           active: start.store?.active,
//           open: start.store?.open,
//         };
//         return storeFront;
//       })
//     ),

//     forMember(
//       (destination) => destination.address,
//       mapFrom((start) => {
//         const addressFront: AddressViewModel = {
//           id: start.address?.id,
//           street: start.address?.street,
//           number: start.address?.number,
//           complement: start.address?.complement,
//           district: start.address?.district,
//           country: start.address?.country,
//           city: start.address?.city,
//           state: start.address?.state,
//           latitude: start.address?.latitude,
//           longitude: start.address?.longitude,
//           postcode: start.address?.postcode,
//           nickname: start.address?.nickname,
//           type: start.address?.type,
//         };
//         return addressFront;
//       })
//     ),

//     forMember(
//       (destination) => destination.deliveryman,
//       mapFrom((start) => {
//         const deliverymanFront: DeliverymanViewModel = {
//           firstName: start.deliveryman?.firstName,
//           lastName: start.deliveryman?.lastName,
//           region: start.deliveryman?.deliveryman?.region,
//         };
//         return deliverymanFront;
//       })
//     ),

//     forMember(
//       (destination) => destination.orderStatus,
//       mapFrom((start) => {
//         const mostRecentStatus = start.orderStatus?.reduce((mostRecent, item) =>
//           item.createdAt > mostRecent.createdAt ? item : mostRecent
//         );

//         return mostRecentStatus?.orderStatusType.value;
//       })
//     ),

//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start.id)
//     ),

//     forMember(
//       (destination) => destination.createdAt,
//       mapFrom((start) => start.createdAt)
//     ),

//     forMember(
//       (destination) => destination.price,
//       mapFrom((start) => start.price)
//     ),

//     forMember(
//       (destination) => destination.shippingPrice,
//       mapFrom((start) => start.shippingPrice)
//     ),

//     forMember(
//       (destination) => destination.totalPrice,
//       mapFrom((start) => start.totalPrice)
//     )
//   );
// };

// export { orderMapper };
