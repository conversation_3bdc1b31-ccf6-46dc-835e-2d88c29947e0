/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable arrow-body-style */
import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateTrackingOrderViewModel } from "src/api/ViewModels/TrackingOrder/ICreate";
import { ITrackingOrderViewModel } from "src/api/ViewModels/TrackingOrder/IViewModel";
import { AddressMapper } from "src/business/Configs/Automapper/Profile/Address";
import ITrackingDeliverymanDTO from "src/business/DTOs/TrackingOrder/ITrackingDeliveryman";
import { ITrackingOrder } from "src/business/Interfaces/Prisma/ITrackingOrder";

export class TrackingOrderMapper extends MappingProfile {
  static readonly ITrackingOrderToITrackingOrderViewModel = new MappingPair<
    ITrackingDeliverymanDTO,
    ITrackingOrderViewModel
  >();

  // static readonly ICreateTrackingOrderToITrackingOrderDTO = new MappingPair<
  //   ICreateTrackingOrderViewModel,
  //   ITrackingOrderDTO
  // >();

  static readonly ICreateTrackingOrderToITrackingOrder = new MappingPair<
    ICreateTrackingOrderViewModel,
    ITrackingOrder
  >();

  constructor() {
    super();

    this.createAutoMap(TrackingOrderMapper.ITrackingOrderToITrackingOrderViewModel, {
      orderId: (opt) => {
        opt.preCondition((src) => src.order?.id !== undefined);
        opt.condition((src) => src !== undefined);
        opt.mapFrom((src) => src.order!.id);
      },
      client: (opt) =>
        opt.mapFrom((src) => ({
          latitude: src.order?.address?.latitude || 0,
          longitude: src.order?.address?.longitude || 0,
        })),
      deliveryman: (opt) => opt.mapFrom((src) => src.deliverymanCoords),
      store: (opt) =>
        opt.mapFrom((src) => ({
          latitude: src.order?.store?.address?.latitude || 0,
          longitude: src.order?.store?.address?.longitude || 0,
        })),
    })
      .forSourceMember("order", (opt) => opt.ignore())
      .forSourceMember("deliverymanCoords", (opt) => opt.ignore());

    // this.createAutoMap(TrackingOrderMapper.ICreateTrackingOrderToITrackingOrderDTO, {});
    this.createAutoMap(TrackingOrderMapper.ICreateTrackingOrderToITrackingOrder, {
      id: (opt) => opt.ignore(),
      orderId: (opt) => opt.mapFrom((src) => src.orderId),
      latitude: (opt) => opt.mapFrom((src) => src.latitude),
      longitude: (opt) => opt.mapFrom((src) => src.longitude),
      createdAt: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
    });
  }
}

// const trackingOrderMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     TrackingOrder,
//     TrackingOrderListViewModel,
//     forMember(
//       (viewModel) => viewModel.userCoords,
//       mapFrom((model) => {
//         const { address } = model.order;
//         return {
//           latitude: address.latitude,
//           longitude: address.longitude,
//         };
//       })
//     ),
//     forMember(
//       (viewModel) => viewModel.storeCoords,
//       mapFrom((model) => {
//         const { store } = model.order;
//         return {
//           latitude: store.address?.latitude,
//           longitude: store.address?.longitude,
//         };
//       })
//     ),
//     forMember(
//       (viewModel) => viewModel.id,
//       mapFrom((model) => model.id)
//     )
//   );
// };

// export { trackingOrderMapper };
