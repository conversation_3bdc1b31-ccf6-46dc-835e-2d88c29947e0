import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IProductModerationViewModel } from "src/api/ViewModels/ProductModeration/IViewModel";
import { IProductModeration } from "src/business/Interfaces/Prisma/IProductModeration";

export class ProductModerationMapper extends MappingProfile {
  static readonly IProductModerationToIProductModerationViewModel = new MappingPair<
    IProductModeration,
    IProductModerationViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(ProductModerationMapper.IProductModerationToIProductModerationViewModel, {});
  }
}
