import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateFiltersMailingViewModel } from "src/api/ViewModels/FiltersMailing/ICreateFiltersMailing";
import { IFiltersMailingViewModel } from "src/api/ViewModels/FiltersMailing/IViewModel";
import { ICreateMailingViewModel } from "src/api/ViewModels/Mailing/ICreateMailing";
import { IMailingViewModel } from "src/api/ViewModels/Mailing/IViewModel";
import { IFiltersMailing } from "src/business/Interfaces/Prisma/IFiltersMailing";
import { IMailing } from "src/business/Interfaces/Prisma/IMailing";

export class MailingMapper extends MappingProfile {
  static readonly ICreateMailingViewModelToIMailing = new MappingPair<ICreateMailingViewModel, IMailing>();

  static readonly ICreateFiltersMalingToIFiltersMailing = new MappingPair<
    ICreateFiltersMailingViewModel,
    IFiltersMailing
  >();

  static readonly IMailingToIMailingViewModel = new MappingPair<IMailing, IMailingViewModel>();

  static readonly IFiltersMailingToIFiltersMailingViewModel = new MappingPair<
    IFiltersMailing,
    IFiltersMailingViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(MailingMapper.IMailingToIMailingViewModel, {
      filtersMailing: (opt) =>
        opt.mapFromUsing(
          (src) => src.filtersMailing,
          MailingMapper.IFiltersMailingToIFiltersMailingViewModel,
        ),
    })
      .forSourceMember("topicArn", (opt) => opt.ignore())
      .forSourceMember("resultMailing", (opt) => opt.ignore());

    this.createAutoMap(MailingMapper.IFiltersMailingToIFiltersMailingViewModel, {
      id: (opt) => opt.ignore(),
      state: (opt) => {
        opt.condition((src) => src.state !== "" && src.state !== null);
        opt.mapFrom((src) => src.state);
      },
      city: (opt) => {
        opt.condition((src) => src.city !== "" && src.city !== null);
        opt.mapFrom((src) => src.city);
      },
      district: (opt) => {
        opt.condition((src) => src.district !== "" && src.district !== null);
        opt.mapFrom((src) => src.district);
      },
      orderPriceComparison: (opt) => {
        opt.condition((src) => src.orderPriceComparison !== null);
        opt.mapFrom((src) => src.orderPriceComparison);
      },
      paymentMethod: (opt) => {
        opt.condition((src) => src.paymentMethod !== null);
        opt.mapFrom((src) => src.paymentMethod);
      },
      orderPrice: (opt) => {
        opt.condition((src) => src.orderPrice !== null);
        opt.mapFrom((src) => src.orderPrice);
      },
      category: (opt) => {
        opt.condition((src) => src.category !== undefined);
        opt.mapFrom((src) => src.category);
      },
    })
      .forSourceMember("Mailing", (opt) => opt.ignore())
      .forSourceMember("MailingId", (opt) => opt.ignore());

    this.createAutoMap(MailingMapper.ICreateFiltersMalingToIFiltersMailing, {
      id: (opt) => opt.ignore(),
      Mailing: (opt) => opt.ignore(),
      orderPrice: (opt) => {
        opt.mapFrom((src) => {
          if (src.orderPrice?.valueOf()) {
            return Number(src.orderPrice);
          }

          return undefined;
        });
      },
      orderPriceComparison: (opt) =>
        opt.mapFrom((src) => {
          if (!src.orderPrice) return undefined;

          return src.orderPriceComparison;
        }),
      MailingId: (opt) => opt.ignore(),
    });

    this.createAutoMap(MailingMapper.ICreateMailingViewModelToIMailing, {
      id: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      resultMailing: (opt) => opt.ignore(),
      filtersMailing: (opt) =>
        opt.mapFromUsing((src) => src.filtersMailing, MailingMapper.ICreateFiltersMalingToIFiltersMailing),
      topicArn: (opt) => opt.ignore(),
    });
  }
}
