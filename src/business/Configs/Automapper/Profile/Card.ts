import { ITransactionCard } from "src/business/Interfaces/Prisma/ITransactionCard";
import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICardViewModel } from "src/api/ViewModels/Card/ICard";
import { ICard } from "src/business/Interfaces/Prisma/ICard";
import { IUpdateCardViewModel } from "src/api/ViewModels/Card/IUpdate";

export class CardMapper extends MappingProfile {
  static readonly ICreateCardViewModelToICard = new MappingPair<ICardViewModel, ICard>();

  static readonly IUpdateCardViewModelToICard = new MappingPair<IUpdateCardViewModel, ICard>();

  static readonly ITransactionCardToICardViewModel = new MappingPair<ITransactionCard, ICardViewModel>();

  constructor() {
    super();

    this.createAutoMap(CardMapper.ICreateCardViewModelToICard, {
      id: (opt) => opt.ignore(),
      userId: (opt) => opt.mapFrom((src) => src.userId),
      method: (opt) => opt.mapFrom((src) => src.method),
      flag: (opt) => opt.mapFrom((src) => src.flag),
      status: (opt) => opt.mapFrom((src) => src.status),
      saved: (opt) => opt.mapFrom((src) => src.saved),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      cardHolder: (opt) => opt.mapFrom((src) => src.cardHolder),
      cardNumber: (opt) => opt.mapFrom((src) => src.cardNumber),
      cardNumberLastDigits: (opt) => opt.mapFrom((src) => src.cardNumberLastDigits),
      expiration: (opt) => opt.mapFrom((src) => src.expiration),
      idCardPaymentPlatform: (opt) => opt.mapFrom((src) => src.idCardPaymentPlatform),
      user: (opt) => opt.ignore(),
      transactionCard: (opt) => opt.ignore(),
    });
    this.createAutoMap(CardMapper.IUpdateCardViewModelToICard, {
      id: (opt) => opt.mapFrom((src) => src.id),
      userId: (opt) => opt.mapFrom((src) => src.userId),
      method: (opt) => opt.mapFrom((src) => src.method),
      flag: (opt) => opt.mapFrom((src) => src.flag),
      status: (opt) => opt.mapFrom((src) => src.status),
      saved: (opt) => opt.mapFrom((src) => src.saved),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      cardHolder: (opt) => opt.mapFrom((src) => src.cardHolder),
      cardNumber: (opt) => opt.mapFrom((src) => src.cardNumber),
      cardNumberLastDigits: (opt) => opt.mapFrom((src) => src.cardNumberLastDigits),
      expiration: (opt) => opt.mapFrom((src) => src.expiration),
      idCardPaymentPlatform: (opt) => opt.mapFrom((src) => src.idCardPaymentPlatform),
      user: (opt) => opt.ignore(),
      transactionCard: (opt) => opt.ignore(),
    });

    this.createAutoMap(CardMapper.ITransactionCardToICardViewModel, {
      id: (opt) => opt.mapFrom((src) => src.cardId),
      userId: (opt) => opt.mapFrom((src) => src.card!.userId),
      method: (opt) => {
        opt.preCondition((src) => src.card !== undefined);
        opt.mapFrom((src) => src.card!.method);
      },
      flag: (opt) => {
        opt.preCondition((src) => src.card !== undefined);
        opt.mapFrom((src) => src.card!.flag);
      },
      status: (opt) => {
        opt.preCondition((src) => src.card !== undefined);
        opt.mapFrom((src) => src.card!.status);
      },
      saved: (opt) => {
        opt.preCondition((src) => src.card !== undefined);
        opt.mapFrom((src) => src.card!.saved);
      },
      cardHolder: (opt) => opt.mapFrom((src) => src.card?.cardHolder),
      cardNumber: (opt) => opt.mapFrom((src) => src.card?.cardNumber),
      cardNumberLastDigits: (opt) => {
        opt.preCondition((src) => src.card !== undefined);
        opt.mapFrom((src) => src.card!.cardNumberLastDigits);
      },
      expiration: (opt) => opt.mapFrom((src) => src.card?.expiration),
      idCardPaymentPlatform: (opt) => opt.mapFrom((src) => src.card?.idCardPaymentPlatform),
    })
      .forSourceMember("card", (opt) => opt.ignore())
      .forSourceMember("cardId", (opt) => opt.ignore())
      .forSourceMember("transaction", (opt) => opt.ignore())
      .forSourceMember("transactionId", (opt) => opt.ignore())
      .forSourceMember("id", (opt) => opt.ignore());
  }
}
