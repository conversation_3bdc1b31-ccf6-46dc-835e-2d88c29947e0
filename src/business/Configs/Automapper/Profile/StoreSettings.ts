import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateStoreSettingsViewModel } from "src/api/ViewModels/StoreSettings/ICreate";
import { IStoreSettingsViewModel } from "src/api/ViewModels/StoreSettings/IViewModel";
import { IStoreSettings } from "src/business/Interfaces/Prisma/IStoreSettings";

export class StoreSettingsMapper extends MappingProfile {
  static readonly IStoreSettingsToStoreSettingsViewModel = new MappingPair<
    IStoreSettings,
    IStoreSettingsViewModel
  >();

  static readonly IStoreSettingsViewModelToIStoreSettings = new MappingPair<
    IStoreSettingsViewModel,
    IStoreSettings
  >();

  static readonly ICreateStoreSettingsViewModelToIStoreSettings =
    new MappingPair<ICreateStoreSettingsViewModel, IStoreSettings>();

  static readonly IStoreSettingsArrayToStoreSettingsViewModelArray =
    new MappingPair<IStoreSettings[], IStoreSettingsViewModel[]>();

  constructor() {
    super();

    this.createAutoMap(
      StoreSettingsMapper.IStoreSettingsToStoreSettingsViewModel,
      {}
    );
    this.createAutoMap(
      StoreSettingsMapper.IStoreSettingsViewModelToIStoreSettings,
      {
        id: (opt) => opt.ignore(),
        cash: (opt) => opt.mapFrom((src) => Boolean(src.cash)),
        debt: (opt) => opt.mapFrom((src) => Boolean(src.debt)),
        credit: (opt) => opt.mapFrom((src) => Boolean(src.credit)),
        ticket: (opt) => opt.mapFrom((src) => Boolean(src.ticket)),
        store: (opt) => opt.ignore(),
        createdAt: (opt) => opt.ignore(),
        updatedAt: (opt) => opt.ignore(),
        storeId: (opt) => opt.ignore(),
      }
    );

    this.createAutoMap(
      StoreSettingsMapper.ICreateStoreSettingsViewModelToIStoreSettings,
      {
        id: (opt) => opt.ignore(),
        cash: (opt) => opt.mapFrom((src) => Boolean(src.cash)),
        debt: (opt) => opt.mapFrom((src) => Boolean(src.debt)),
        credit: (opt) => opt.mapFrom((src) => Boolean(src.credit)),
        ticket: (opt) => opt.mapFrom((src) => Boolean(src.ticket)),
        store: (opt) => opt.ignore(),
        createdAt: (opt) => opt.ignore(),
        updatedAt: (opt) => opt.ignore(),
      }
    );

    this.createAutoMap(
      StoreSettingsMapper.IStoreSettingsArrayToStoreSettingsViewModelArray,
      {}
    );
  }
}

// const storeSettingsMapper: MappingProfile = (mapper) => {
//     createMap(mapper, StoreSettings, StoreSettingsViewModel);
//     createMap(mapper, StoreSettingsViewModel, StoreSettings);
//   };

// export { storeSettingsMapper };
