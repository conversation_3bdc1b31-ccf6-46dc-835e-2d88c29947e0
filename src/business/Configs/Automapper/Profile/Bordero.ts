/* eslint-disable no-nested-ternary */
/* eslint-disable arrow-body-style */
import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { EBorderoStatus } from "@prisma/client";
import { ICreateBorderoViewModel } from "src/api/ViewModels/Bordero/ICreate";
import { ICreateBorderoResultViewModel } from "src/api/ViewModels/Bordero/ICreateBorderoResult";
import { IListBorderoViewModel } from "src/api/ViewModels/Bordero/IList";
import IBorderoPaymentViewModel from "src/api/ViewModels/Bordero/IPayment";
import { IUpdateBorderoViewModel } from "src/api/ViewModels/Bordero/IUpdate";
import { IValidateBorderoResultViewModel } from "src/api/ViewModels/Bordero/IValidateBorderoResult";
import { IBorderoViewModel } from "src/api/ViewModels/Bordero/IViewModel";
import IBorderoListDTO from "src/business/DTOs/Bordero/IBordero";
import IBorderoPaymentDTO from "src/business/DTOs/Bordero/IBorderoPayment";
import { ICreateBorderoResultDTO } from "src/business/DTOs/Bordero/ICreateBorderoResult";
import { IValidateBorderoResultDTO } from "src/business/DTOs/Bordero/IValidateBorderoResult";
import { IBordero } from "src/business/Interfaces/Prisma/IBordero";

export class BorderoMapper extends MappingProfile {
  static readonly IBorderoToIBorderoViewModel = new MappingPair<IBordero, IBorderoViewModel>();

  static readonly ICreateBorderoViewModelToIBordero = new MappingPair<ICreateBorderoViewModel, IBordero>();

  static readonly IUpdateBorderoViewModelToIBordero = new MappingPair<IUpdateBorderoViewModel, IBordero>();

  static readonly IBorderoListDTOToIListBorderoViewModel = new MappingPair<IBorderoListDTO, IListBorderoViewModel>();

  static readonly IBorderoPaymentDTOToIBorderoPaymentViewModel = new MappingPair<
    IBorderoPaymentDTO,
    IBorderoPaymentViewModel
  >();

  static readonly IValidateBorderoResultDTOToIValidateBorderoResultViewModel = new MappingPair<
    IValidateBorderoResultDTO,
    IValidateBorderoResultViewModel
  >();

  static readonly ICreateBorderoResultDTOToICreateBorderoResultViewModel = new MappingPair<
    ICreateBorderoResultViewModel,
    ICreateBorderoResultDTO
  >();

  constructor() {
    super();

    this.createAutoMap(BorderoMapper.IBorderoToIBorderoViewModel, {
      user: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      userAdmin: (opt) => opt.ignore(),
      payout: (opt) => opt.ignore(),
    });

    this.createAutoMap(BorderoMapper.ICreateBorderoViewModelToIBordero, {
      id: (opt) => opt.ignore(),
      status: (opt) => opt.nullSubstitute(EBorderoStatus.open),
      createdAt: (opt) => opt.ignore(),
      statusDate: (opt) => opt.nullSubstitute(new Date()),
      user: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      cooperative: (opt) => opt.ignore(),
      userAdmin: (opt) => opt.ignore(),
      payout: (opt) => opt.ignore(),
      userIdAdmin: (opt) => opt.ignore(),
      quantityOrders: (opt) => opt.nullSubstitute(0),
      sumAdministrativeFeeValue: (opt) => opt.nullSubstitute(0),
      sumOrderValue: (opt) => opt.nullSubstitute(0),
      sumTransferValue: (opt) => opt.nullSubstitute(0),
    }).forSourceMember("payouts", (src) => src.ignore());

    this.createAutoMap(BorderoMapper.IUpdateBorderoViewModelToIBordero, {
      createdAt: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      cooperative: (opt) => opt.ignore(),
      userAdmin: (opt) => opt.ignore(),
      payout: (opt) => opt.ignore(),
      userIdAdmin: (opt) => opt.ignore(),
      statusDate: (opt) => opt.nullSubstitute(new Date()),
      quantityOrders: (opt) => opt.nullSubstitute(0),
      sumAdministrativeFeeValue: (opt) => opt.nullSubstitute(0),
      sumOrderValue: (opt) => opt.nullSubstitute(0),
      sumTransferValue: (opt) => opt.nullSubstitute(0),
    }).forSourceMember("payouts", (src) => src.ignore());

    this.createAutoMap(BorderoMapper.IBorderoListDTOToIListBorderoViewModel, {
      userIdAdmin: (opt) => opt.ignore(),
      statusDate: (opt) => opt.mapFrom((src) => src.statusDate),
      name: (opt) =>
        opt.mapFrom((src) =>
          src.user ? `${src.user.firstName} ${src.user.lastName}` : src.store ? src.store.name : src.cooperative?.name,
        ),
      cpfcnpj: (opt) =>
        opt.mapFrom((src) => (src.user ? src.user.cpf : src.store ? src.store.cnpj : src.cooperative?.cnpj)),
      createdBy: (opt) => opt.mapFrom((src) => `${src.userAdmin?.firstName} ${src.userAdmin?.lastName}`),
    });

    this.createAutoMap(BorderoMapper.IValidateBorderoResultDTOToIValidateBorderoResultViewModel, {});

    this.createAutoMap(BorderoMapper.ICreateBorderoResultDTOToICreateBorderoResultViewModel, {});

    this.createAutoMap(BorderoMapper.IBorderoPaymentDTOToIBorderoPaymentViewModel, {
      user: (opt) => {
        opt.preCondition((src) => src.user !== undefined);
        opt.mapFrom((src) => {
          return {
            firstName: src.user?.firstName!,
            lastName: src.user?.firstName!,
            cpf: src.user?.cpf!,
            pixKey: src.user?.deliveryman?.pixKey,
          };
        });
      },
    });
  }
}
