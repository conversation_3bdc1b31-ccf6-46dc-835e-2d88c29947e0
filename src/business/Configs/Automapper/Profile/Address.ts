/* eslint-disable import/no-extraneous-dependencies */
import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IAddressOptionViewModel } from "src/api/ViewModels/Address/IAddressOption";
import { ICreateAddressViewModel } from "src/api/ViewModels/Address/ICreate";
import { ICreateAddressWithoutRelationViewModel } from "src/api/ViewModels/Address/ICreateWithoutRelation";
import { IListAddressViewModel } from "src/api/ViewModels/Address/IList";
import { IUpdateAddressViewModel } from "src/api/ViewModels/Address/IUpdate";
import { ILocationViewModel } from "src/api/ViewModels/Location/IViewModel";
import { IAddressOptionDTO } from "src/business/DTOs/Address/IAddressOption";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IUserAddress } from "src/business/Interfaces/Prisma/IUserAddress";

export class AddressMapper extends MappingProfile {
  static readonly IAddressToIListAddressViewModel = new MappingPair<IAddress, IListAddressViewModel>();

  static readonly IAddressOptionToIAddressOptionViewModel = new MappingPair<
    IAddressOptionDTO,
    IAddressOptionViewModel
  >();

  static readonly IAddressToLocationListViewModel = new MappingPair<IAddress, ILocationViewModel>();

  static readonly ICreateAddressViewModelToIAddress = new MappingPair<ICreateAddressViewModel, IAddress>();

  static readonly ICreateAddressWithoutRelationViewModelToIAddress = new MappingPair<
    ICreateAddressWithoutRelationViewModel,
    IAddress
  >();

  static readonly ICreateAddressViewModelToIUserAddress = new MappingPair<ICreateAddressViewModel, IUserAddress>();

  static readonly ICreateAddressViewModelArrayToIUserAddressArray = new MappingPair<
    ICreateAddressViewModel[],
    IUserAddress[]
  >();

  static readonly ICreateAddressViewModelArrayToIAddressArray = new MappingPair<
    ICreateAddressViewModel[],
    IAddress[]
  >();

  static readonly IUpdateAddressViewModelToIAddress = new MappingPair<IUpdateAddressViewModel, IAddress>();

  constructor() {
    super();

    this.createAutoMap(AddressMapper.IAddressToIListAddressViewModel, {})
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("cityHash", (opt) => opt.ignore())
      .forSourceMember("complementHash", (opt) => opt.ignore())
      .forSourceMember("countryHash", (opt) => opt.ignore())
      .forSourceMember("districtHash", (opt) => opt.ignore())
      .forSourceMember("nicknameHash", (opt) => opt.ignore())
      .forSourceMember("numberHash", (opt) => opt.ignore())
      .forSourceMember("postcodeHash", (opt) => opt.ignore())
      .forSourceMember("stateHash", (opt) => opt.ignore())
      .forSourceMember("streetHash", (opt) => opt.ignore());

    this.createAutoMap(AddressMapper.IAddressOptionToIAddressOptionViewModel, {});

    this.createAutoMap(AddressMapper.IAddressToLocationListViewModel, {})
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("city", (opt) => opt.ignore())
      .forSourceMember("complement", (opt) => opt.ignore())
      .forSourceMember("country", (opt) => opt.ignore())
      .forSourceMember("district", (opt) => opt.ignore())
      .forSourceMember("id", (opt) => opt.ignore())
      .forSourceMember("isDefault", (opt) => opt.ignore())
      .forSourceMember("nickname", (opt) => opt.ignore())
      .forSourceMember("number", (opt) => opt.ignore())
      .forSourceMember("postcode", (opt) => opt.ignore())
      .forSourceMember("state", (opt) => opt.ignore())
      .forSourceMember("street", (opt) => opt.ignore())
      .forSourceMember("type", (opt) => opt.ignore());

    this.createAutoMap(AddressMapper.ICreateAddressViewModelToIAddress, {
      id: (opt) => opt.ignore(),
      orderAddress: (opt) => opt.ignore(),
      stores: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      cityHash: (opt) => opt.ignore(),
      complementHash: (opt) => opt.ignore(),
      countryHash: (opt) => opt.ignore(),
      districtHash: (opt) => opt.ignore(),
      nicknameHash: (opt) => opt.ignore(),
      numberHash: (opt) => opt.ignore(),
      postcodeHash: (opt) => opt.ignore(),
      stateHash: (opt) => opt.ignore(),
      streetHash: (opt) => opt.ignore(),
    });

    this.createAutoMap(AddressMapper.ICreateAddressWithoutRelationViewModelToIAddress, {
      id: (opt) => opt.ignore(),
      number: (opt) => opt.ignore(),
      userAddress: (opt) => opt.ignore(),
      complement: (opt) => opt.ignore(),
      type: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      orderAddress: (opt) => opt.ignore(),
      stores: (opt) => opt.ignore(),
      isDefault: (opt) => opt.ignore(),
      cityHash: (opt) => opt.ignore(),
      complementHash: (opt) => opt.ignore(),
      countryHash: (opt) => opt.ignore(),
      districtHash: (opt) => opt.ignore(),
      nicknameHash: (opt) => opt.ignore(),
      numberHash: (opt) => opt.ignore(),
      postcodeHash: (opt) => opt.ignore(),
      stateHash: (opt) => opt.ignore(),
      streetHash: (opt) => opt.ignore(),
    });

    this.createAutoMap(AddressMapper.IUpdateAddressViewModelToIAddress, {
      orderAddress: (opt) => opt.ignore(),
      userAddress: (opt) => opt.ignore(),
      stores: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      cityHash: (opt) => opt.ignore(),
      complementHash: (opt) => opt.ignore(),
      countryHash: (opt) => opt.ignore(),
      districtHash: (opt) => opt.ignore(),
      nicknameHash: (opt) => opt.ignore(),
      numberHash: (opt) => opt.ignore(),
      postcodeHash: (opt) => opt.ignore(),
      stateHash: (opt) => opt.ignore(),
      streetHash: (opt) => opt.ignore(),
    });

    this.createAutoMap(AddressMapper.ICreateAddressViewModelArrayToIAddressArray, {});

    this.createAutoMap(AddressMapper.ICreateAddressViewModelToIUserAddress, {
      addressId: (opt) => opt.ignore(),
      address: (opt) => opt.mapFromUsing((src) => src, AddressMapper.ICreateAddressViewModelToIAddress),
      id: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
    })
      .forSourceMember("country", (opt) => opt.ignore())
      .forSourceMember("city", (opt) => opt.ignore())
      .forSourceMember("complement", (opt) => opt.ignore())
      .forSourceMember("district", (opt) => opt.ignore())
      .forSourceMember("isDefault", (opt) => opt.ignore())
      .forSourceMember("latitude", (opt) => opt.ignore())
      .forSourceMember("longitude", (opt) => opt.ignore())
      .forSourceMember("nickname", (opt) => opt.ignore())
      .forSourceMember("number", (opt) => opt.ignore())
      .forSourceMember("postcode", (opt) => opt.ignore())
      .forSourceMember("state", (opt) => opt.ignore())
      .forSourceMember("street", (opt) => opt.ignore());

    this.createAutoMap(AddressMapper.ICreateAddressViewModelArrayToIUserAddressArray, {});
  }
}
