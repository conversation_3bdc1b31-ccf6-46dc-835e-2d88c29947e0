import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICategoryViewModel } from "src/api/ViewModels/Category/ICategory";
import { ICategoryDetailsViewModel } from "src/api/ViewModels/Category/ICategoryDetails";
import { ICategorySubcategoryViewModel } from "src/api/ViewModels/Category/ICategorySubcategory";
import { ICategoryWithIconViewModel } from "src/api/ViewModels/Category/ICategoryWithIcon";
import { ICreateCategoryViewModel } from "src/api/ViewModels/Category/ICreate";
import { IListCategoryViewModel } from "src/api/ViewModels/Category/IList";
import { IUpdateCategoryViewModel } from "src/api/ViewModels/Category/IUpdate";
import { TreeViewListViewModel } from "src/api/ViewModels/List/TreeViewList.vm";
import { IListSubcategoryViewModel } from "src/api/ViewModels/Subcategory/IList";
import { ISubcategoryViewModel } from "src/api/ViewModels/Subcategory/ISubcategory";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import { ICategory } from "src/business/Interfaces/Prisma/ICategory";
import { ICategorySubcategory } from "src/business/Interfaces/Prisma/ICategorySubcategory";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";

export class CategoryMapper extends MappingProfile {
  static readonly ICreateCategoryViewModelToICategory = new MappingPair<ICreateCategoryViewModel, ICategory>();

  static readonly IUpdateCategoryViewModelToICategory = new MappingPair<IUpdateCategoryViewModel, ICategory>();

  static readonly ICategoryToICategoryViewModel = new MappingPair<ICategory, ICategoryViewModel>();

  static readonly ICategoryToCategoryListViewModel = new MappingPair<ICategory, IListCategoryViewModel>();

  static readonly ICategoryToICategorySubcategoryViewModel = new MappingPair<
    ICategory,
    ICategorySubcategoryViewModel
  >();

  static readonly ICategoryToICategoryWithIconViewModel = new MappingPair<ICategory, ICategoryWithIconViewModel>();

  static readonly ICategoryToTreeViewListViewModel = new MappingPair<ICategory, TreeViewListViewModel>();

  static readonly IListCategoryViewModelToIProductCategory = new MappingPair<
    IListCategoryViewModel,
    IProductCategory
  >();

  static readonly IListSubcategoryViewModelToIProductSubcategory = new MappingPair<
    IListSubcategoryViewModel,
    IProductSubcategory
  >();

  static readonly SubcategoryIdToICategorySubcategory = new MappingPair<{ id: string }, ICategorySubcategory>();

  static readonly ICategorySubcategoryToIListSubcategoryViewModel = new MappingPair<
    ICategorySubcategory,
    IListSubcategoryViewModel
  >();

  static readonly ICategorySubcategoryToISubCategoryViewModel = new MappingPair<
    ICategorySubcategory,
    ISubcategoryViewModel
  >();

  static readonly IProductCategoryToICategoryViewModel = new MappingPair<IProductCategory, ICategoryViewModel>();

  static readonly IProductSubcategoryToICategoryViewModel = new MappingPair<IProductSubcategory, ICategoryViewModel>();

  static readonly ICreateCategoryViewModelToCategoryListViewModel = new MappingPair<
    ICreateCategoryViewModel,
    IListCategoryViewModel
  >();

  static readonly ICategoryViewModelToIStoreCategory = new MappingPair<ICategoryViewModel, IStoreCategory>();

  static readonly ICategoryToICategoryDetailsViewModel = new MappingPair<ICategory, ICategoryDetailsViewModel>();

  constructor() {
    super();

    this.createAutoMap(CategoryMapper.IListCategoryViewModelToIProductCategory, {
      productId: (opt) => opt.ignore(),
      categoryId: (opt) => opt.mapFrom((src) => src.id),
      category: (opt) => opt.nullSubstitute(undefined),
      product: (opt) => opt.nullSubstitute(undefined),
    });

    this.createAutoMap(CategoryMapper.IListSubcategoryViewModelToIProductSubcategory, {
      productId: (opt) => opt.ignore(),
      subcategoryId: (opt) => opt.mapFrom((src) => src.id),
      product: (opt) => opt.nullSubstitute(undefined),
      subcategory: (opt) => opt.nullSubstitute(undefined),
    });

    this.createAutoMap(CategoryMapper.SubcategoryIdToICategorySubcategory, {
      id: (opt) => opt.ignore(),
      subcategoryId: (opt) => opt.mapFrom((src) => src.id),
      categoryId: (opt) => opt.ignore(),
      category: (opt) => opt.ignore(),
      subcategory: (opt) => opt.ignore(),
    });

    this.createAutoMap(CategoryMapper.IProductCategoryToICategoryViewModel, {
      name: (opt) => opt.mapFrom((src) => src.category?.name!),
      checked: (opt) => opt.nullSubstitute(false),
      icon: (opt) => opt.nullSubstitute(undefined),
    })
      .forSourceMember("productId", (opt) => opt.ignore())
      .forSourceMember("category", (opt) => opt.ignore())
      .forSourceMember("categoryId", (opt) => opt.ignore())
      .forSourceMember("product", (opt) => opt.ignore())
      .forSourceMember("productId", (opt) => opt.ignore());

    this.createAutoMap(CategoryMapper.IProductSubcategoryToICategoryViewModel, {
      name: (opt) => opt.mapFrom((src) => src.subcategory?.name!),
      checked: (opt) => opt.nullSubstitute(false),
      icon: (opt) => opt.nullSubstitute(undefined),
    });

    this.createAutoMap(CategoryMapper.ICreateCategoryViewModelToICategory, {
      categorySubcategory: (opt) => {
        opt.preCondition((src) => src.subcategory !== undefined && Array.isArray(src.subcategory));
        opt.condition((src) => src.subcategory !== undefined && Array.isArray(src.subcategory));
        opt.mapFromUsing((src) => src.subcategory, CategoryMapper.SubcategoryIdToICategorySubcategory);
      },
      id: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      productCategories: (opt) => opt.ignore(),
      storeCategory: (opt) => opt.ignore(),
      icon: (opt) => opt.ignore(),
    })
      .forSourceMember("attachments", (opt) => opt.ignore())
      .forSourceMember("subcategory", (opt) => opt.ignore());

    this.createAutoMap(CategoryMapper.IUpdateCategoryViewModelToICategory, {
      // categorySubcategory: (opt) => {
      //   opt.preCondition(
      //     (src) =>
      //       src.subcategory !== undefined && Array.isArray(src.subcategory)
      //   );
      //   opt.condition(
      //     (src) =>
      //       src.subcategory !== undefined && Array.isArray(src.subcategory)
      //   );
      //   opt.mapFrom((src) => {
      //     const categorySubcategory: ICategorySubcategory[] = [];
      //     src.subcategory.forEach((item) => {
      //       categorySubcategory.push({
      //         categoryId: src.id,
      //         subcategoryId: item.id,
      //       } as ICategorySubcategory);
      //     });
      //     return categorySubcategory;
      //   });
      // },
      categorySubcategory: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      // updatedAt: (opt) => opt.ignore(),
      productCategories: (opt) => opt.ignore(),
      storeCategory: (opt) => opt.ignore(),
      icon: (opt) => opt.ignore(),
    }).forSourceMember("subcategory", (opt) => opt.ignore());

    this.createAutoMap(CategoryMapper.ICreateCategoryViewModelToCategoryListViewModel, {
      id: (opt) => opt.nullSubstitute(""),
      checked: (opt) => opt.mapFrom(() => true),
      subcategory: (opt) => opt.ignore(),
    });

    this.createAutoMap(CategoryMapper.ICategorySubcategoryToIListSubcategoryViewModel, {
      name: (opt) => opt.mapFrom((src) => src.subcategory?.name!),
      checked: (opt) => opt.mapFrom(() => false),
    });

    this.createAutoMap(CategoryMapper.ICategoryToICategoryViewModel, {
      checked: (opt) => opt.mapFrom(() => false),
      icon: (opt) => opt.nullSubstitute(undefined),
    });

    this.createAutoMap(CategoryMapper.ICategoryToCategoryListViewModel, {
      subcategory: (opt) =>
        opt.mapFromUsing(
          (src) => src.categorySubcategory,
          CategoryMapper.ICategorySubcategoryToIListSubcategoryViewModel,
        ),
      checked: (opt) => opt.mapFrom(() => false),
    });

    this.createAutoMap(CategoryMapper.ICategorySubcategoryToISubCategoryViewModel, {
      id: (opt) => opt.mapFrom((src) => src.subcategory?.id!),
      name: (opt) => opt.mapFrom((src) => src.subcategory?.name!),
      description: (opt) => opt.mapFrom((src) => src.subcategory?.description!),
      checked: (opt) => opt.mapFrom(() => false),
    })
      .forSourceMember("categoryId", (opt) => opt.ignore())
      .forSourceMember("subcategory", (opt) => opt.ignore())
      .forSourceMember("subcategoryId", (opt) => opt.ignore());

    this.createAutoMap(CategoryMapper.ICategoryToICategorySubcategoryViewModel, {
      subcategory: (opt) => {
        opt.preCondition((src) => src.categorySubcategory !== undefined && src.categorySubcategory !== null);
        opt.condition((src) => src.categorySubcategory !== undefined && src.categorySubcategory !== null);
        opt.mapFromUsing(
          (src) => src.categorySubcategory?.map((item) => item),
          CategoryMapper.ICategorySubcategoryToISubCategoryViewModel,
        );
      },
    })
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("categorySubcategory", (opt) => opt.ignore())
      .forSourceMember("productCategories", (opt) => opt.ignore())
      .forSourceMember("storeCategory", (opt) => opt.ignore());

    this.createAutoMap(CategoryMapper.ICategoryToICategoryWithIconViewModel, {
      icon: (opt) => {
        opt.preCondition((src) => src.icon !== undefined && src.icon !== null);
        opt.condition((src) => src.icon !== undefined && src.icon !== null);
        opt.mapFromUsing((src) => src.icon, FilesMapper.IFileToIListFileViewModel);
      },
    })
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("categorySubcategory", (opt) => opt.ignore())
      .forSourceMember("productCategories", (opt) => opt.ignore())
      .forSourceMember("storeCategory", (opt) => opt.ignore());

    this.createAutoMap(CategoryMapper.ICategoryToTreeViewListViewModel, {
      options: (opt) =>
        opt.mapFrom((src) =>
          src?.categorySubcategory!.map((item) => ({
            id: item.subcategory!.id,
            name: item.subcategory!.name,
            checked: false,
          })),
        ),
      isOpen: (opt) => opt.mapFrom(() => false),
      checked: (opt) => opt.mapFrom(() => false),
    });

    this.createAutoMap(CategoryMapper.ICategoryViewModelToIStoreCategory, {
      id: (opt) => opt.ignore(),
      storeId: (opt) => opt.ignore(),
      categoryId: (opt) => opt.mapFrom((src) => src.id),
      category: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
    });

    this.createAutoMap(CategoryMapper.ICategoryToICategoryDetailsViewModel, {
      subcategory: (opt) =>
        opt.mapFromUsing(
          (src) => src.categorySubcategory?.map((item) => item),
          CategoryMapper.ICategorySubcategoryToISubCategoryViewModel,
        ),
      icon: (opt) => {
        opt.preCondition((src) => src.icon !== undefined && src.icon !== null);
        opt.condition((src) => src.icon !== undefined && src.icon !== null);
        opt.mapFromUsing((src) => src.icon, FilesMapper.IFileToIListFileViewModel);
      },
    });
  }
}

// const categoryMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     CreateCategoryViewModel,
//     Category,
//     forMember(
//       (destination) => destination.categorySubcategory,
//       mapFrom((start) => {
//         const subcategoryDB: CategorySubcategory[] =
//           [] as CategorySubcategory[];
//         start?.subcategory?.map((item) =>
//           subcategoryDB.push({ subcategoryId: item.id } as CategorySubcategory)
//         );
//         return subcategoryDB;
//       })
//     )
//   );

//   createMap(
//     mapper,
//     UpdateCategoryViewModel,
//     Category,
//     forMember(
//       (destination) => destination.categorySubcategory,
//       mapFrom((start) => {
//         const subcategoryDB: CategorySubcategory[] =
//           [] as CategorySubcategory[];
//         start?.subcategory?.map((item) =>
//           subcategoryDB.push({ categoryId: start.id, subcategoryId: item.id } as CategorySubcategory)
//         );
//         return subcategoryDB;
//       }),

//     ),
//     forMember(
//       (destination) => destination.name,
//       mapFrom((start) => start?.name)
//     ),
//     forMember(
//       (destination) => destination.description,
//       mapFrom((start) => start?.description)
//     ),
//   );

//   createMap(
//     mapper,
//     CreateCategoryViewModel,
//     CategoryListViewModel,
//     forMember(
//       (destination) => destination.checked,
//       mapFrom(() => true)
//     )
//   );

//   createMap(
//     mapper,
//     Category,
//     CategoryListViewModel,
//     forMember(
//       (destination) => destination.subcategory,
//       mapWithArguments((start: Category, { checkedItem }) =>
//         start?.categorySubcategory?.map((item) => ({
//           id: item.subcategory.id,
//           name: item.subcategory.name,
//           checked: checkedItem || false,
//         }))
//       )
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     ),
//     forMember(
//       (destination) => destination.name,
//       mapFrom((start) => start?.name)
//     ),
//     forMember(
//       (destination) => destination.checked,
//       mapWithArguments(
//         (start: Category, { checkedItem }) => checkedItem || false
//       )
//     )
//   );

//   createMap(
//     mapper,
//     Category,
//     CategorySubcategoryViewModel,
//     forMember(
//       (destination) => destination.subcategory,
//       mapWithArguments((start: Category) =>
//         start?.categorySubcategory?.map((item) => ({
//           id: item.subcategory.id,
//           name: item.subcategory.name,
//           description: item.subcategory.description
//         }))
//       )
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     ),
//     forMember(
//       (destination) => destination.name,
//       mapFrom((start) => start?.name)
//     ),
//     forMember(
//       (destination) => destination.updatedAt,
//       mapFrom((start) => start?.updatedAt)
//     ),
//   );

//   createMap(
//     mapper,
//     CategoryPagedResult,
//     FilteredCategoryViewModel,
//     forMember(
//       (destination) => destination.result,
//       mapFrom((start) =>
//         start.result.map((item) => ({
//           id: item.id,
//           name: item.name,
//           checked: "0",
//         }))
//       )
//     ),
//     forMember(
//       (destination) => destination.totalCount,
//       mapFrom((start) => start.totalCount)
//     ),
//     forMember(
//       (destination) => destination.totalPages,
//       mapFrom((start) => start.totalPages)
//     )
//   );

//   createMap(
//     mapper,
//     Category,
//     TreeViewListViewModel,
//     forMember(
//       (destination) => destination.options,
//       mapFrom((start) => start?.categorySubcategory?.map((item) => ({
//         id: item.subcategory.id,
//         name: item.subcategory.name,
//         checked: false,
//       })))
//     ),
//     forMember(
//       (destination) => destination.isOpen,
//       mapFrom(() => false)
//     ),
//     forMember(
//       (destination) => destination.checked,
//       mapFrom(() => false)
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start.id)
//     ),
//   );
// };

// export { categoryMapper };
