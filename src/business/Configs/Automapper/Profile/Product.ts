import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateProductViewModel } from "src/api/ViewModels/Product/ICreate";
import { IFavoriteProductViewModel } from "src/api/ViewModels/Product/IFavorite";
import { IProductByStoreViewModel } from "src/api/ViewModels/Product/IProductByStore";
import { IUpdateProductViewModel } from "src/api/ViewModels/Product/IUpdate";
import { IProductViewModel } from "src/api/ViewModels/Product/IViewModel";
import { AttributeMapper } from "src/business/Configs/Automapper/Profile/Attribute";
import { CategoryMapper } from "src/business/Configs/Automapper/Profile/Category";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import { StoreMapper } from "src/business/Configs/Automapper/Profile/Store";
import { SubcategoryMapper } from "src/business/Configs/Automapper/Profile/Subcategory";
import { IProductByStoreWithFavoriteDTO } from "src/business/DTOs/Product/IProductByStoreWithFavorite";
import { IProduct } from "src/business/Interfaces/Prisma/IProduct";
import { IProductSubcategory } from "src/business/Interfaces/Prisma/IProductSubcategory";
import { IUpdateCategorySubcategoryViewModel } from "../../../../api/ViewModels/Product/IUpdateCategorySubcategory";
import { IUpdateCategorySubcategoryDTO } from "../../../DTOs/Product/IUpdateCategorySubCategories";

export class ProductMapper extends MappingProfile {
  static readonly ICreateProductViewModelToIProduct = new MappingPair<ICreateProductViewModel, IProduct>();

  static readonly IProductToIProductViewModel = new MappingPair<IProduct, IProductViewModel>();

  static readonly IProductByStoreWithFavoriteDTOToIProductViewModel = new MappingPair<
    IProductByStoreWithFavoriteDTO,
    IProductByStoreViewModel
  >();

  static readonly IProductToIFavoriteProductViewModel = new MappingPair<IProduct, IFavoriteProductViewModel>();

  static readonly IUpdateProductViewModelToIProduct = new MappingPair<IUpdateProductViewModel, IProduct>();

  static readonly IUpdateCategorySubCategoriesViewModelToIUpdateCategorySubCategoryDTO = new MappingPair<
    IUpdateCategorySubcategoryViewModel,
    IUpdateCategorySubcategoryDTO
  >();

  constructor() {
    super();

    this.createAutoMap(ProductMapper.ICreateProductViewModelToIProduct, {
      id: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      orderItem: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      userFavoriteProducts: (opt) => opt.ignore(),
      productModeration: (opt) => opt.ignore(),
      productCategory: (opt) => {
        opt.preCondition((src) => src.category?.length > 0);
        opt.mapFromUsing((src) => src.category.map((p) => p), CategoryMapper.IListCategoryViewModelToIProductCategory);
      },
      productSubcategory: (opt) =>
        opt.mapFrom((src) => {
          const subcategoryDB: IProductSubcategory[] = [];
          src.category?.map((item) =>
            item.subcategory?.forEach((sb) => {
              subcategoryDB.push({
                subcategoryId: sb.id,
              } as IProductSubcategory);
            }),
          );
          return subcategoryDB;
        }),
      productAttribute: (opt) => {
        opt.preCondition((src) => !!src.attribute && src.attribute.length > 0);
        opt.mapFromUsing((src) => src.attribute, AttributeMapper.IAttributeViewModelToIProductAttribute);
      },
    })
      .forSourceMember("attribute", (src) => src.ignore())
      .forSourceMember("attachments", (src) => src.ignore())
      .forSourceMember("category", (src) => src.ignore());

    this.createAutoMap(ProductMapper.IProductToIProductViewModel, {
      storeName: (opt) => opt.mapFrom((src) => src.store?.name || ""),
      category: (opt) => {
        opt.preCondition((src) => !!src.productCategory && src.productCategory?.length > 0);
        opt.mapFromUsing((src) => src.productCategory, CategoryMapper.IProductCategoryToICategoryViewModel);
      },
      subcategory: (opt) => {
        opt.preCondition((src) => !!src.productSubcategory && src.productSubcategory?.length > 0);
        opt.mapFromUsing((src) => src.productSubcategory, SubcategoryMapper.IProductSubcategoryToISubcategoryViewModel);
      },
      attribute: (opt) => {
        opt.preCondition((src) => !!src.productAttribute && src.productAttribute?.length > 0);
        opt.mapFromUsing((src) => src.productAttribute, AttributeMapper.IProductAttributeToIAttributeViewModel);
      },
      files: (opt) => opt.mapFromUsing((src) => src.files, FilesMapper.IFileToIListFileViewModel),
      activeByAdmin: (opt) => opt.mapFrom((src) => src.activeByAdmin),
      moderation: (opt) => opt.mapFrom((src) => src.productModeration?.[0]),
      store: (opt) => opt.mapFromUsing((src) => src?.store, StoreMapper.IStoreToIStoreViewModel),
    })
      .forSourceMember("productAttribute", (src) => src.ignore())
      .forSourceMember("productCategory", (src) => src.ignore())
      .forSourceMember("productSubcategory", (src) => src.ignore());

    this.createAutoMap(ProductMapper.IProductByStoreWithFavoriteDTOToIProductViewModel, {
      storeName: (opt) => opt.mapFrom((src) => src.store?.name || ""),
      category: (opt) => {
        opt.preCondition((src) => !!src.productCategory && src.productCategory?.length > 0);
        opt.mapFrom((src) => src.productCategory.map((p) => p.category));
      },
      files: (opt) => opt.mapFrom((src) => src.files || []),
      isFavorite: (opt) => opt.mapFrom((src) => src.isFavorite),
    }).forSourceMember("productCategory", (src) => src.ignore());

    this.createAutoMap(ProductMapper.IProductToIFavoriteProductViewModel, {
      category: (opt) =>
        opt.mapFromUsing((src) => src.productCategory, CategoryMapper.IProductCategoryToICategoryViewModel),
      subcategory: (opt) =>
        opt.mapFromUsing((src) => src.productSubcategory, SubcategoryMapper.IProductSubcategoryToISubcategoryViewModel),
      store: (opt) => opt.mapFromUsing((src) => src.store, StoreMapper.IStoreToIStoreViewModel),
      icon: (opt) => opt.mapFromUsing((src) => src.files, FilesMapper.IFileToIListFileViewModel),
    }).forSourceMember("productAttribute", (src) => src.ignore());

    this.createAutoMap(ProductMapper.IUpdateProductViewModelToIProduct, {
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      orderItem: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      userFavoriteProducts: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      productCategory: (opt) => opt.ignore(),
      productSubcategory: (opt) => opt.ignore(),
      productAttribute: (opt) => opt.ignore(),
      productModeration: (opt) => opt.ignore(),
    });

    this.createMap(ProductMapper.IUpdateCategorySubCategoriesViewModelToIUpdateCategorySubCategoryDTO, {
      productId: (opt) => opt.mapFrom((src) => src.productId),
      categories: (opt) => opt.mapFrom((src) => src.categories),
    });
  }
}

// const productMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     CreateProductViewModel,
//     Product,
//     forMember(
//       (destination) => destination.productCategory,
//       mapFrom((start) => {
//         const categoryDB: ProductCategory[] = [] as ProductCategory[];
//         start?.category?.forEach((item) => {
//           if (item.checked) {
//             categoryDB.push({ categoryId: item.id } as ProductCategory);
//           }
//         });
//         return categoryDB;
//       })
//     ),
//     forMember(
//       (destination) => destination.productSubcategory,
//       mapFrom((start) => {
//         const subcategoryDB: ProductSubcategory[] = [] as ProductSubcategory[];
//         start?.category?.map((item) =>
//           item.options.forEach((option) => {
//             if (option.checked) {
//               subcategoryDB.push({
//                 subcategoryId: option.id,
//               } as ProductSubcategory);
//             }
//           })
//         );
//         return subcategoryDB;
//       })
//     ),
//     forMember(
//       (destination) => destination.productAttribute,
//       mapFrom((start) => {
//         const productAttributes = start.attribute?.map((attribute) => {
//           const newPa = ProductAttribute.create({
//             attribute,
//             productAttributeOption: attribute.attributeOption?.map((option) => {
//               const newPao = ProductAttributeOption.create({
//                 attributeOption: option,
//               });
//               return newPao;
//             }),
//           });
//           return newPa;
//         });
//         return productAttributes;
//       })
//     )
//   );

//   createMap(
//     mapper,
//     Product,
//     ProductViewModel,
//     forMember(
//       (destination) => destination.category,
//       mapWithArguments((source: Product, { categories }) => {
//         if (categories) return categories;
//         return source?.productCategory?.map((item) => item.category);
//       })
//     ),
//     forMember(
//       (destination) => destination.subcategory,
//       mapWithArguments((source: Product, { subcategories }) => {
//         if (subcategories) return subcategories;
//         return source?.productSubcategory?.map((item) => item.subcategory);
//       })
//     ),
//     forMember(
//       (destination) => destination.sku,
//       mapFrom((start) => (start.sku.length > 8 ? "" : start.sku))
//     ),
//     forMember(
//       (destination) => destination.icon,
//       mapFrom((start) => start.icon as FileViewModel)
//     ),
//     forMember(
//       (destination) => destination.attribute,
//       mapFrom((start) =>
//         start.productAttribute?.map((item) => {
//           const attributes: AttributeViewModel = {
//             id: item.attribute.id,
//             name: item.attribute.name,
//             short_description: item.attribute.short_description,
//             required: item.attribute.required,
//             type: item.attribute.type,
//             checked: true,
//             attributeOption: item.attribute
//               .attributeOption as unknown as IAttributeOption[],
//           };
//           return attributes;
//         })
//       )
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     ),
//     forMember(
//       (destination) => destination.userFavoriteProducts,
//       mapFrom((start) => {
//         const ufp = start.userFavoriteProducts?.map((ufp) => {
//           const value = { id: ufp.id };
//           return value;
//         });
//         return ufp;
//       })
//     ),
//     forMember(
//       (destination) => destination.storeName,
//       mapFrom((start) => start.store?.name)
//     )
//   );
//   createMap(
//     mapper,
//     Product,
//     FavoriteProductViewModel,
//     forMember(
//       (destination) => destination.category,
//       mapWithArguments((source: Product, { categories }) => {
//         if (categories) return categories;
//         return source?.productCategory?.map((item) => item.category);
//       })
//     ),
//     forMember(
//       (destination) => destination.subcategory,
//       mapWithArguments((source: Product, { subcategories }) => {
//         if (subcategories) return subcategories;
//         return source?.productSubcategory?.map((item) => item.subcategory);
//       })
//     ),
//     forMember(
//       (destination) => destination.sku,
//       mapFrom((start) => (start.sku.length > 8 ? "" : start.sku))
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     ),
//     forMember(
//       (destination) => destination.userFavoriteProducts,
//       mapFrom((start) => {
//         const ufp = start.userFavoriteProducts?.map((ufp) => {
//           const value = { id: ufp.id };
//           return value;
//         });
//         return ufp;
//       })
//     ),
//     forMember(
//       (destination) => destination.store,
//       mapFrom((start) => mapper.map(start.store, Store, StoreViewModel))
//     ),
//     forMember(
//       (destination) => destination.icon,
//       mapFrom((start) => start.icon as FileViewModel)
//     )
//   );

//   createMap(
//     mapper,
//     UpdateProductViewModel,
//     Product,
//     forMember(
//       (destination) => destination.productCategory,
//       mapFrom((start) => {
//         const categoryDB: ProductCategory[] = [] as ProductCategory[];
//         start?.category?.forEach((item) => {
//           if (item.checked) {
//             categoryDB.push({ categoryId: item.id } as ProductCategory);
//           }
//         });
//         return categoryDB;
//       })
//     ),
//     forMember(
//       (destination) => destination.productSubcategory,
//       mapFrom((start) => {
//         const subcategoryDB: ProductSubcategory[] = [] as ProductSubcategory[];
//         start?.category?.map((item) =>
//           item.options.forEach((option) => {
//             if (option.checked) {
//               subcategoryDB.push({
//                 subcategoryId: option.id,
//               } as ProductSubcategory);
//             }
//           })
//         );
//         return subcategoryDB;
//       })
//     ),
//     forMember(
//       (destination) => destination.productAttribute,
//       mapFrom((start) => {
//         const productAttributes = start.attribute.map((attribute) => {
//           const newPa = ProductAttribute.create({
//             productId: start.id,
//             attribute,
//             productAttributeOption: attribute.attributeOption?.map((option) => {
//               const newPao = ProductAttributeOption.create({
//                 attributeOption: option,
//               });
//               return newPao;
//             }),
//           });
//           return newPa;
//         });
//         return productAttributes;
//       })
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     )
//   );
// };

// export { productMapper };
