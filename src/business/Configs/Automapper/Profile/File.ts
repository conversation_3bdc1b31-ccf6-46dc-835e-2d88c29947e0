import { Profile as MappingProfile, MappingPair } from "@dynamic-mapper/mapper";
import { IListFileViewModel } from "src/api/ViewModels/File/IList";
import { IUpdateFileViewModel } from "src/api/ViewModels/File/IUpdateFile";
import { IFile } from "src/business/Interfaces/Prisma/IFile";

export class FilesMapper extends MappingProfile {
  static readonly IFileToIListFileViewModel = new MappingPair<IFile, IListFileViewModel>();

  static readonly IUpdateFileViewModelToIFile = new MappingPair<IUpdateFileViewModel, IFile>();

  constructor() {
    super();

    this.createAutoMap(FilesMapper.IFileToIListFileViewModel, {})
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore());

    this.createAutoMap(FilesMapper.IUpdateFileViewModelToIFile, {
      url: (opt) => opt.ignore(),
      key: (opt) => opt.ignore(),
      bucket: (opt) => opt.ignore(),
      originalName: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      extension: (opt) => opt.ignore(),
    });
  }
}
