import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IClientViewModel } from "src/api/ViewModels/Client/IClient";
import { CreateClientViewModel } from "src/api/ViewModels/Client/ICreateClient";
import { IClient } from "src/business/Interfaces/Prisma/IClient";

export class ClientMapper extends MappingProfile {
  static readonly IClientToIClientViewModel = new MappingPair<
    IClient,
    IClientViewModel
  >();

  static readonly IClientViewModelToIClient = new MappingPair<
    IClientViewModel,
    IClient
  >();

  static readonly CreateClientViewModelToIClient = new MappingPair<
    CreateClientViewModel,
    IClient
  >();

  constructor() {
    super();

    this.createAutoMap(ClientMapper.CreateClientViewModelToIClient, {
      id: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      status: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
    });

    this.createAutoMap(ClientMapper.IClientViewModelToIClient, {
      user: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      status: (opt) => opt.ignore(),
    });

    this.createAutoMap(ClientMapper.IClientToIClientViewModel, {})
      .forSourceMember("userId", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore());
  }
}
