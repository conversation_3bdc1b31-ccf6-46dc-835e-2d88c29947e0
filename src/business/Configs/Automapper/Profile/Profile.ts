import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateProfileViewModel } from "src/api/ViewModels/Profile/ICreateProfile";
import { IProfileViewModel } from "src/api/ViewModels/Profile/IProfile";
import { IUpdateProfileViewModel } from "src/api/ViewModels/Profile/IUpdateProfile";
import { IProfile } from "src/business/Interfaces/Prisma/IProfile";
import { IUserProfile } from "src/business/Interfaces/Prisma/IUserProfile";

export class ProfileMapper extends MappingProfile {
  static readonly IProfileToIProfileViewModel = new MappingPair<IProfile, IProfileViewModel>();

  static readonly IUserProfileToIProfileViewModel = new MappingPair<IUserProfile, IProfileViewModel>();

  static readonly ICreateProfileViewModelToIProfile = new MappingPair<ICreateProfileViewModel, IProfile>();

  static readonly IUpdateProfileViewModelToIProfile = new MappingPair<IUpdateProfileViewModel, IProfile>();

  constructor() {
    super();

    this.createAutoMap(ProfileMapper.IProfileToIProfileViewModel, {
      name: (opt) => opt.mapFrom((src) => src.name),
    })
      .forSourceMember("profilePermissions", (opt) => opt.ignore())
      .forSourceMember("userProfiles", (opt) => opt.ignore());

    this.createAutoMap(ProfileMapper.ICreateProfileViewModelToIProfile, {
      id: (opt) => opt.ignore(),
      profilePermissions: (opt) => opt.ignore(),
      userProfiles: (opt) => opt.ignore(),
    });

    this.createAutoMap(ProfileMapper.IUserProfileToIProfileViewModel, {
      name: (opt) => {
        opt.preCondition((src) => src.profile?.name !== undefined);
        opt.mapFrom((src) => src.profile!.name);
      },
    })
      .forSourceMember("id", (opt) => opt.ignore())
      .forSourceMember("userId", (opt) => opt.ignore())
      .forSourceMember("profileId", (opt) => opt.ignore())
      .forSourceMember("user", (opt) => opt.ignore())
      .forSourceMember("profile", (opt) => opt.ignore());

    this.createAutoMap(ProfileMapper.IUpdateProfileViewModelToIProfile, {
      userProfiles: (opt) => opt.ignore(),
      profilePermissions: (opt) => opt.ignore(),
    });
  }
}
