import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateReviewViewModel } from "src/api/ViewModels/Review/ICreate";
import { IUpdateReviewViewModel } from "src/api/ViewModels/Review/IUpdate";
import { IReviewViewModel } from "src/api/ViewModels/Review/IViewModel";
import { IReview } from "src/business/Interfaces/Prisma/IReview";

export class ReviewMapper extends MappingProfile {
  static readonly IReviewToIReviewViewModel = new MappingPair<IReview, IReviewViewModel>();

  static readonly IReviewArrayToIReviewViewModelArray = new MappingPair<IReview[], IReviewViewModel[]>();

  static readonly ICreateReviewViewModelToIReview = new MappingPair<ICreateReviewViewModel, IReview>();

  static readonly IUpdateReviewViewModelToIReview = new MappingPair<IUpdateReviewViewModel, IReview>();

  constructor() {
    super();

    this.createAutoMap(ReviewMapper.IReviewToIReviewViewModel, {
      user: (opt) =>
        opt.mapFrom((src) => ({
          id: src.user?.id || src.userId,
          firstName: src.user?.firstName || "",
        })),
      store: (opt) =>
        opt.mapFrom((src) => ({
          id: src.store?.id || src.storeId || undefined,
          name: src.store?.name || "",
        })),
      deliveryman: (opt) =>
        opt.mapFrom((src) => ({
          id: src.order?.deliverymanId,
        })),
    });

    this.createAutoMap(ReviewMapper.IReviewArrayToIReviewViewModelArray, {});

    this.createAutoMap(ReviewMapper.ICreateReviewViewModelToIReview, {
      id: (opt) => opt.ignore(),
      userId: (opt) => opt.mapFrom((src) => src.user.id),
      orderId: (opt) => {
        opt.preCondition((src) => src.order?.id !== undefined);
        opt.mapFrom((src) => src.order!.id);
      },
      storeId: (opt) => opt.mapFrom((src) => src.store.id),
      deliverymanId: (opt) => {
        opt.preCondition((src) => src.deliveryman?.id !== undefined);
        opt.mapFrom((src) => src.deliveryman!.id);
      },
      user: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      deliveryman: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
    });

    this.createAutoMap(ReviewMapper.IUpdateReviewViewModelToIReview, {
      user: (opt) => opt.ignore(),
      store: (opt) => opt.ignore(),
      deliveryman: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      storeId: (opt) => opt.ignore(),
      orderId: (opt) => opt.ignore(),
      deliverymanId: (opt) => opt.ignore(),
    });
  }
}

// const reviewMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     Review,
//     ReviewViewModel,
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start.id)
//     ),
//     forMember(
//       (destination) => destination.rate,
//       mapFrom((start) => start.rate)
//     ),
//     forMember(
//       (destination) => destination.customerReview,
//       mapFrom((start) => start.customerReview)
//     ),
//     forMember(
//       (destination) => destination.storeResponse,
//       mapFrom((start) => start?.storeResponse)
//     ),
//     forMember(
//       (destination) => destination.createdAt,
//       mapFrom((start) => start.createdAt)
//     ),
//     forMember(
//       (destination) => destination.user,
//       mapFrom((start) => ({
//         id: start.user.id,
//         firstName: start.user.firstName,
//       }))
//     ),
//     forMember(
//       (destination) => destination.store,
//       mapFrom((start) => ({
//         id: start.store.id,
//         name: start.store.name,
//       }))
//     )
//   );
// };

// export { reviewMapper };
