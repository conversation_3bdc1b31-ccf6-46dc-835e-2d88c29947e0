import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateOrUpdateCooperativeViewModel } from "src/api/ViewModels/Cooperative/ICreate";
import { IUpdateCooperativeViewModel } from "src/api/ViewModels/Cooperative/IUpdate";
import { ICooperativeViewModel } from "src/api/ViewModels/Cooperative/IViewModel";
import { AddressMapper } from "src/business/Configs/Automapper/Profile/Address";
import { ICooperative } from "src/business/Interfaces/Prisma/ICooperative";

export class CooperativeMapper extends MappingProfile {
  static readonly ICreateOrUpdateCooperativeViewModelToICooperative = new MappingPair<
    ICreateOrUpdateCooperativeViewModel,
    ICooperative
  >();

  static readonly IUpdateCooperativeViewModelToICooperative = new MappingPair<
    IUpdateCooperativeViewModel,
    ICooperative
  >();

  static readonly ICooperativeToICooperativeViewModel = new MappingPair<ICooperative, ICooperativeViewModel>();

  constructor() {
    super();

    this.createAutoMap(CooperativeMapper.ICreateOrUpdateCooperativeViewModelToICooperative, {
      id: (opt) => opt.ignore(),
      pixKey: (opt) => opt.mapFrom((src) => src.pixKey),
      addressId: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      address: (opt) => opt.mapFromUsing((src) => src.address, AddressMapper.ICreateAddressViewModelToIAddress),
    });

    this.createAutoMap(CooperativeMapper.IUpdateCooperativeViewModelToICooperative, {
      addressId: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      address: (opt) => opt.ignore(),
    });

    this.createAutoMap(CooperativeMapper.ICooperativeToICooperativeViewModel, {
      address: (opt) => opt.mapFromUsing((src) => src.address, AddressMapper.IAddressToIListAddressViewModel),
    }).forSourceMember("updatedAt", (opt) => opt.ignore());
  }
}
