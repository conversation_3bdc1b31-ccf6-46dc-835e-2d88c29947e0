import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ISettingsViewModel } from "src/api/ViewModels/Settings/ISettings";
import { ISettings } from "src/business/Interfaces/Prisma/ISettings";

export class SettingsMapper extends MappingProfile {
  static readonly ISettingViewModelToISettings = new MappingPair<ISettingsViewModel, ISettings>();

  static readonly ISettingsToISettingViewModel = new MappingPair<ISettings, ISettingsViewModel>();

  constructor() {
    super();

    this.createAutoMap(SettingsMapper.ISettingViewModelToISettings, {
      id: (opt) => opt.ignore(),
      name: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      value: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      type: (opt) => opt.ignore(),
    });

    this.createAutoMap(SettingsMapper.ISettingsToISettingViewModel, {});
  }
}
