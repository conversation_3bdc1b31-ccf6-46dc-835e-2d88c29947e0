import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/ICreateDeliveryman";
import { IDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IDeliveryman";
import { IDeliverymanLocationViewModel } from "src/api/ViewModels/Deliveryman/IDeliverymanLocation";
import { IListDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IListDeliveryman";
import { IUpdateDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IUpdateDeliveryman";
import { IUserDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IUserDeliveryman";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import { IDeliveryman } from "src/business/Interfaces/Prisma/IDeliveryman";

export class DeliverymanMapper extends MappingProfile {
  static readonly IDeliverymanToIDeliverymanViewModel = new MappingPair<IDeliveryman, IDeliverymanViewModel>();

  static readonly IDeliverymanToIListDeliverymanViewModel = new MappingPair<IDeliveryman, IListDeliverymanViewModel>();

  static readonly IDeliverymanViewModelToIDeliveryman = new MappingPair<IDeliverymanViewModel, IDeliveryman>();

  static readonly ICreateDeliverymanViewModelToIDeliveryman = new MappingPair<
    ICreateDeliverymanViewModel,
    IDeliveryman
  >();

  static readonly IDeliverymanToIUserDeliverymanViewModel = new MappingPair<IDeliveryman, IUserDeliverymanViewModel>();

  static readonly IDeliverymanUpdateViewModelToIShopkeeper = new MappingPair<
    IUpdateDeliverymanViewModel,
    IDeliveryman
  >();

  static readonly IDeliverymanToIDeliverymanLocationViewModel = new MappingPair<
    IDeliveryman,
    IDeliverymanLocationViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(DeliverymanMapper.IDeliverymanToIDeliverymanViewModel, {
      id: (opt) => opt.mapFrom((src) => src.id),
      attachments: (opt) => opt.ignore(),
      files: (opt) => opt.mapFromUsing((src) => src.files, FilesMapper.IFileToIListFileViewModel),
    })
      .forSourceMember("user", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("userId", (opt) => opt.ignore());

    this.createAutoMap(DeliverymanMapper.IDeliverymanToIListDeliverymanViewModel, {
      firstName: (opt) => {
        opt.preCondition((src) => src.user?.firstName !== undefined);
        opt.mapFrom((src) => src.user?.firstName!);
      },
      lastName: (opt) => {
        opt.preCondition((src) => src.user?.lastName !== undefined);
        opt.mapFrom((src) => src.user?.lastName!);
      },
      cpf: (opt) => {
        opt.preCondition((src) => src.user?.cpf !== undefined);
        opt.mapFrom((src) => src.user?.cpf!);
      },
      rating: (opt) =>
        opt.mapFrom((src) => {
          if (src.review === undefined) return undefined;

          const reviewSum = src.review.reduce((acc, curr) => {
            if (curr.rate === undefined) return acc;

            if (curr.deliverymanId !== null) return acc + curr.rate;

            return acc;
          }, 0);

          if (reviewSum === 0) return "0";

          const average = reviewSum / src.review.length;

          return average.toFixed(2);
        }),
    })
      .forSourceMember("user", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("userId", (opt) => opt.ignore());

    this.createAutoMap(DeliverymanMapper.IDeliverymanViewModelToIDeliveryman, {
      // pixKey: (opt) => opt.mapFrom((src) => src.)
      user: (opt) => opt.ignore(),
      orders: (opt) => opt.ignore(),
      review: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      latitude: (opt) => opt.ignore(),
      longitude: (opt) => opt.ignore(),
    }).forSourceMember("attachments", (opt) => opt.ignore());

    this.createAutoMap(DeliverymanMapper.IDeliverymanUpdateViewModelToIShopkeeper, {
      id: (opt) => opt.mapFrom((src) => src.id),
      review: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      modality: (opt) => opt.ignore(),
      orders: (opt) => opt.ignore(),
      type: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      latitude: (opt) => opt.ignore(),
      longitude: (opt) => opt.ignore(),
      pixKey: (opt) => opt.ignore(),
    });

    this.createAutoMap(DeliverymanMapper.ICreateDeliverymanViewModelToIDeliveryman, {
      id: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      status: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      orders: (opt) => opt.ignore(),
      review: (opt) => opt.ignore(),
      annotation: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      latitude: (opt) => opt.ignore(),
      longitude: (opt) => opt.ignore(),
      pixKey: (opt) => opt.ignore(),
    });

    this.createAutoMap(DeliverymanMapper.IDeliverymanToIUserDeliverymanViewModel, {
      id: (opt) => opt.mapFrom((src) => src.user?.id!),
      name: (opt) => opt.mapFrom((src) => src.user?.firstName!),
      email: (opt) => opt.mapFrom((src) => src.user?.email!),
      phone: (opt) => opt.mapFrom((src) => src.user?.phone!),
      cpf: (opt) => opt.mapFrom((src) => src.user?.cpf!),
    })
      .forSourceMember("user", (opt) => opt.ignore())
      .forSourceMember("orders", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("userId", (opt) => opt.ignore());

    this.createAutoMap(DeliverymanMapper.IDeliverymanToIDeliverymanLocationViewModel, {
      name: (opt) => opt.mapFrom((src) => `${src.user?.firstName!} ${src.user?.lastName}`),
      email: (opt) => opt.mapFrom((src) => src.user?.email!),
      phone: (opt) => opt.mapFrom((src) => src.user?.phone!),
      cpf: (opt) => opt.mapFrom((src) => src.user?.cpf!),
      modality: (opt) => opt.mapFrom((src) => src.modality),
      latitude: (opt) => {
        opt.preCondition((src) => src?.latitude !== undefined);
        opt.mapFrom((src) => src.latitude!);
      },
      longitude: (opt) => {
        opt.preCondition((src) => src?.longitude !== undefined);
        opt.mapFrom((src) => src.longitude!);
      },
    });
  }
}

// const deliverymanMapper: MappingProfile = (mapper) => {
//   createMap(mapper, Deliveryman, DeliverymanViewModel);
//   createMap(mapper, UpdateDeliverymanViewModel, Deliveryman);
// };

// export { deliverymanMapper };
