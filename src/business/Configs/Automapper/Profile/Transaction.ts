import { CardMapper } from "src/business/Configs/Automapper/Profile/Card";
import { MappingPair, Profile as MappingProfile2 } from "@dynamic-mapper/mapper";
import { ICardViewModel } from "src/api/ViewModels/Card/ICard";
import { ITransaction } from "src/business/Interfaces/Prisma/ITransaction";
import { ICreateTransactionViewModel } from "src/api/ViewModels/Transaction/ICreate";
import { ITransactionCard } from "src/business/Interfaces/Prisma/ITransactionCard";
import { ITransactionViewModel } from "src/api/ViewModels/Transaction/IViewModel";
import { ETransactionStatus, ETransactionType } from "@prisma/client";
import { IListTransactionByOrderViewModel } from "src/api/ViewModels/Transaction/IListTransactionByOrder";
import { IListTransactionByOrderDTO } from "src/business/DTOs/Transactions/ListTransactionByOrder";

export class TransactionMapper extends MappingProfile2 {
  static readonly ICreateTransactionToITransaction = new MappingPair<ICreateTransactionViewModel, ITransaction>();

  static readonly ITransactionToITransactionViewModel = new MappingPair<ITransaction, ITransactionViewModel>();

  static readonly IListTransactionByOrderDTOToIListTransactionByOrderViewModel = new MappingPair<
    IListTransactionByOrderDTO,
    IListTransactionByOrderViewModel
  >();

  static readonly ITransactionCardToICardViewModel = new MappingPair<ITransactionCard, ICardViewModel>();

  constructor() {
    super();

    this.createAutoMap(TransactionMapper.ICreateTransactionToITransaction, {
      authorizationCode: (opt) => opt.ignore(),
      chargeId: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      customerId: (opt) => opt.ignore(),
      id: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      orderId: (opt) => opt.ignore(),
      status: (opt) => opt.nullSubstitute(ETransactionStatus.created),
      statusDetail: (opt) => opt.ignore(),
      traceId: (opt) => opt.ignore(),
      transactionCard: (opt) =>
        opt.mapFrom((src) => {
          const cardsDB = [] as ITransactionCard[];
          if (src?.savedCard?.cardId) {
            cardsDB.push({
              cardId: src?.savedCard?.cardId,
            } as ITransactionCard);
          }
          return cardsDB;
        }),
      transactionPlatformId: (opt) => opt.ignore(),
      type: (opt) => opt.nullSubstitute(ETransactionType.payment),
      updatedAt: (opt) => opt.ignore(),
      pixKey: (opt) => opt.ignore(),
      amount: (opt) => opt.mapFrom((src) => src.amount),
      installments: (opt) => opt.mapFrom((src) => src.installments),
      paymentMethod: (opt) => opt.mapFrom((src) => src.paymentMethod),
    })
      .forSourceMember("cardHolder", (opt) => opt.ignore())
      .forSourceMember("cardNumberLastDigits", (opt) => opt.ignore())
      .forSourceMember("flag", (opt) => opt.ignore())
      .forSourceMember("hashCardPaymentPlatform", (opt) => opt.ignore())
      .forSourceMember("newCard", (opt) => opt.ignore())
      .forSourceMember("savedCard", (opt) => opt.ignore());

    this.createAutoMap(TransactionMapper.ITransactionToITransactionViewModel, {
      cards: (opt) => opt.mapFromUsing((src) => src.transactionCard, CardMapper.ITransactionCardToICardViewModel),

      // cards: (opt) =>
      //   opt.mapFrom((src) => {
      //     const cards = this.mapCards(src?.transactionCard);

      //     return cards;
      //   }),
      // amount: (opt) => {
      //   opt.preCondition((src) => src.amount !== undefined);
      //   opt.mapFrom((src) => src.amount!);
      // },
      // amount: (opt) => opt.mapFrom((src) => src.amount!),
      // authorizationCode: (opt) => {
      //   opt.preCondition((src) => src?.authorizationCode !== undefined );
      //   opt.mapFrom((src) => src.authorizationCode!);
      // },
      // chargeId: (opt) => opt.mapFrom((src) => src.chargeId!),
      // createdAt: (opt) => {
      //   opt.preCondition((src) => src.createdAt !== undefined);
      //   opt.mapFrom((src) => src.createdAt!);
      // },
      // customerId: (opt) => {
      //   opt.preCondition((src) => src.customerId !== undefined);
      //   opt.mapFrom((src) => src.customerId!);
      // },
      // id: (opt) => opt.mapFrom((src) => src.id),
      // installments: (opt) => {
      //   opt.preCondition((src) => src.installments !== null);
      //   opt.mapFrom((src) => src.installments!);
      // },
      // orderId: (opt) => {
      //   opt.preCondition((src) => src.orderId !== undefined);
      //   opt.mapFrom((src) => src.orderId!);
      // },
      // paymentMethod: (opt) => opt.mapFrom((src) => src.paymentMethod),
      // status: (opt) => opt.mapFrom((src) => src.status),
      // statusDetail: (opt) => {
      //   opt.preCondition((src) => src.statusDetail !== undefined);
      //   opt.mapFrom((src) => src.statusDetail!);
      // },
      // transactionPlatformId: (opt) => opt.mapFrom((src) => src.transactionPlatformId!),
      // type: (opt) => opt.mapFrom((src) => src.type),
      // updatedAt: (opt) => {
      //   opt.preCondition((src) => src.updatedAt !== undefined);
      //   opt.mapFrom((src) => src.updatedAt!);
      // },
    });

    this.createAutoMap(TransactionMapper.IListTransactionByOrderDTOToIListTransactionByOrderViewModel, {
      cards: (opt) => opt.mapFromUsing((src) => src.transactionCard, CardMapper.ITransactionCardToICardViewModel),
    }).forSourceMember("transactionCard", (opt) => opt.ignore());
  }
}
