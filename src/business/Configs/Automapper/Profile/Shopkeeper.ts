import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { CreateShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/ICreateShopkeeper";
import { IShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/IShopkeeper";
import { IUpdateShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/IUpdateShopkeeper";
import { IUserShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/IUserShopkeeper";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import { IShopkeeper } from "src/business/Interfaces/Prisma/IShopkeeper";

export class ShopkeeperMapper extends MappingProfile {
  static readonly IShopkeeperToIShopkeeperViewModel = new MappingPair<IShopkeeper, IShopkeeperViewModel>();

  static readonly IShopkeeperViewModelToIShopkeeper = new MappingPair<IShopkeeperViewModel, IShopkeeper>();

  static readonly IShopkeeperToIUserShopkeeperViewModel = new MappingPair<IShopkeeper, IUserShopkeeperViewModel>();

  static readonly CreateShopkeeperViewModelToIShopkeeper = new MappingPair<CreateShopkeeperViewModel, IShopkeeper>();

  static readonly IShopkeeperUpdateViewModelToIShopkeeper = new MappingPair<IUpdateShopkeeperViewModel, IShopkeeper>();

  constructor() {
    super();

    this.createAutoMap(ShopkeeperMapper.IShopkeeperToIShopkeeperViewModel, {
      id: (opt) => opt.mapFrom((src) => src.id),
      attachments: (opt) => opt.ignore(),
      files: (opt) => opt.mapFromUsing((src) => src.files, FilesMapper.IFileToIListFileViewModel),
    })
      .forSourceMember("user", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("userId", (opt) => opt.ignore());

    this.createAutoMap(ShopkeeperMapper.IShopkeeperUpdateViewModelToIShopkeeper, {
      id: (opt) => opt.mapFrom((src) => src.id),
      user: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
    });

    this.createAutoMap(ShopkeeperMapper.IShopkeeperViewModelToIShopkeeper, {
      user: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
    }).forSourceMember("attachments", (opt) => opt.ignore());

    this.createAutoMap(ShopkeeperMapper.IShopkeeperToIUserShopkeeperViewModel, {
      id: (opt) => opt.mapFrom((src) => src.user?.id!),
      name: (opt) => opt.mapFrom((src) => src.user?.firstName!),
      email: (opt) => opt.mapFrom((src) => src.user?.email!),
      phone: (opt) => opt.mapFrom((src) => src.user?.phone!),
      cpf: (opt) => opt.mapFrom((src) => src.user?.cpf!),
      shopkeeper: (opt) => opt.mapFromUsing((src) => src, ShopkeeperMapper.IShopkeeperToIShopkeeperViewModel),
    })
      .forSourceMember("user", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("status", (opt) => opt.ignore())
      .forSourceMember("userId", (opt) => opt.ignore());

    this.createAutoMap(ShopkeeperMapper.CreateShopkeeperViewModelToIShopkeeper, {
      id: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      status: (opt) => opt.ignore(),
      userId: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
    });
  }
}

// const shopkeeperMapper: MappingProfile = (mapper) => {
//   createMap(mapper, Shopkeeper, ShopkeeperViewModel);
//   createMap(mapper, UpdateShopkeeperViewModel, Shopkeeper);
// };

// export { shopkeeperMapper };
