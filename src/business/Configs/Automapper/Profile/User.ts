import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateUserViewModel } from "src/api/ViewModels/User/ICreateUser";
import { ICreateUserSocialLoginViewModel } from "src/api/ViewModels/User/ICreateUserSocialLogin";
import { ICreateUserWebViewModel } from "src/api/ViewModels/User/ICreateUserWeb";
import { IUpdateUserViewModel } from "src/api/ViewModels/User/IUpdateUser";
import { IUpdateUserFrontViewModel } from "src/api/ViewModels/User/IUpdateUserFront";
import { IUserViewModel } from "src/api/ViewModels/User/IUser";
import { IUserMailingViewModel } from "src/api/ViewModels/User/IUserMailing";
import { IUserProfileViewModel } from "src/api/ViewModels/User/IUserProfile";
import { IUserProfileAnalyzeViewModel } from "src/api/ViewModels/User/IUserProfileAnalyze";
import { IUserWithAddressViewModel } from "src/api/ViewModels/User/IUserWithAddress";
import { IUserWithProfileDataViewModel } from "src/api/ViewModels/User/IUserWithProfileData";
import { IUserWithProfilePictureViewModel } from "src/api/ViewModels/User/IUserWithProfilePicture";
import { AddressMapper } from "src/business/Configs/Automapper/Profile/Address";
import { ClientMapper } from "src/business/Configs/Automapper/Profile/Client";
import { DeliverymanMapper } from "src/business/Configs/Automapper/Profile/Deliveryman";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import { ProfileMapper } from "src/business/Configs/Automapper/Profile/Profile";
import { ShopkeeperMapper } from "src/business/Configs/Automapper/Profile/Shopkeeper";
import { IUserMailingDTO } from "src/business/DTOs/User/IUserMailing";
import { IUserRegisterDTO } from "src/business/DTOs/User/IUserRegister";
import { IUserWithProfilePictureDTO } from "src/business/DTOs/User/IUserWithProfilePicture";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

export class UserMapper extends MappingProfile {
  static readonly IUserToIUserViewModel = new MappingPair<IUser, IUserViewModel>();

  static readonly IUserToIUserWithAddressViewModel = new MappingPair<IUser, IUserWithAddressViewModel>();

  // static readonly IUserToUserViewModelArray = new MappingPair<
  //   IUser[],
  //   UserViewModel[]
  // >();

  static readonly IUserToIUserProfileViewModel = new MappingPair<IUser, IUserProfileViewModel>();

  // static readonly CreateUserViewModelToIUser = new MappingPair<
  //   CreateUserViewModel,
  //   IUser
  // >();

  static readonly IUserToIUserWithProfileDataViewModel = new MappingPair<IUser, IUserWithProfileDataViewModel>();

  static readonly IUserWithProfilePictureDTOToIUserWithProfilePictureViewModel = new MappingPair<
    IUserWithProfilePictureDTO,
    IUserWithProfilePictureViewModel
  >();

  static readonly IUserRegisterDTOToIUser = new MappingPair<IUserRegisterDTO, IUser>();

  static readonly ICreateUserViewModelToIUserRegisterDTO = new MappingPair<ICreateUserViewModel, IUserRegisterDTO>();

  static readonly ICreateUserWebViewModelToIUserRegisterDTO = new MappingPair<
    ICreateUserWebViewModel,
    IUserRegisterDTO
  >();

  static readonly IUpdateUserViewModelToIUser = new MappingPair<IUpdateUserViewModel, IUser>();

  static readonly ICreateUserSocialLoginViewModelToIUser = new MappingPair<ICreateUserSocialLoginViewModel, IUser>();

  static readonly IUserToIUserProfileAnalyzeViewModel = new MappingPair<IUser, IUserProfileAnalyzeViewModel>();

  static readonly IUpdateUserFrontViewModelToIUser = new MappingPair<IUpdateUserFrontViewModel, IUser>();

  static readonly IUserMailingDTOToIUserMailingViewModel = new MappingPair<IUserMailingDTO, IUserMailingViewModel>();

  constructor() {
    super();

    this.createAutoMap(UserMapper.IUserToIUserViewModel, {})
      .forSourceMember("cognitoId", (opt) => opt.ignore())
      .forSourceMember("deletedAt", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("deleted", (opt) => opt.ignore())
      .forSourceMember("client", (opt) => opt.ignore())
      .forSourceMember("deliveryman", (opt) => opt.ignore())
      .forSourceMember("order", (opt) => opt.ignore())
      .forSourceMember("reviewUser", (opt) => opt.ignore())
      .forSourceMember("shopkeeper", (opt) => opt.ignore())
      .forSourceMember("storeUsers", (opt) => opt.ignore())
      .forSourceMember("userAddress", (opt) => opt.ignore())
      .forSourceMember("userProfiles", (opt) => opt.ignore())
      .forSourceMember("userPermissions", (opt) => opt.ignore())
      .forSourceMember("userFavoriteProducts", (opt) => opt.ignore())
      .forSourceMember("userFavoriteStores", (opt) => opt.ignore())
      .forSourceMember("devices", (opt) => opt.ignore());

    this.createAutoMap(UserMapper.IUserToIUserWithAddressViewModel, {
      address: (opt) =>
        opt.mapFromUsing(
          (src) => src.userAddress?.map((p) => p.address),
          AddressMapper.IAddressToIListAddressViewModel,
        ),
    })
      .forSourceMember("cognitoId", (opt) => opt.ignore())
      .forSourceMember("deletedAt", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("userAddress", (opt) => opt.ignore())
      .forSourceMember("deleted", (opt) => opt.ignore())
      .forSourceMember("deleted", (opt) => opt.ignore());

    // this.createAutoMap(UserMapper.IUserToUserViewModelArray, {});

    this.createAutoMap(UserMapper.IUserToIUserProfileViewModel, {
      profiles: (opt) => {
        opt.mapFromUsing((src) => src.userProfiles, ProfileMapper.IUserProfileToIProfileViewModel);
      },
    })
      .forSourceMember("userProfiles", (opt) => opt.ignore())
      .forSourceMember("cognitoId", (opt) => opt.ignore())
      .forSourceMember("deletedAt", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("deleted", (opt) => opt.ignore());

    // this.createAutoMap(UserMapper.CreateAddressViewModelToIUserAddress, {
    //   id: opt => opt.nullSubstitute(undefined),
    //   userId: opt => opt.nullSubstitute(undefined),
    //   address: opt => opt.mapFrom(src => src),
    //   addressId: opt => opt.nullSubstitute(undefined),
    //   user: opt => opt.nullSubstitute(undefined),
    // });

    // this.createAutoMap(UserMapper.CreateUserViewModelToIUser, {
    //   userAddress: (opt) =>
    //     opt.mapFrom((src) =>
    //       src.userAddress?.map((p) => {
    //         const result: IUserAddress = {
    //           address: p.address as any,
    //         };
    //         return result;
    //       })
    //     ),
    //   id: (opt) => opt.ignore(),
    //   cognitoId: (opt) => opt.ignore(),
    //   deletedAt: (opt) => opt.nullSubstitute(undefined),
    //   createdAt: (opt) => opt.nullSubstitute(undefined),
    //   updatedAt: (opt) => opt.nullSubstitute(undefined),
    //   deleted: (opt) => opt.nullSubstitute(undefined),
    //   token: (opt) => opt.nullSubstitute(undefined),
    //   userProfiles: (opt) => opt.mapFrom((src) => src.userProfiles),
    //   userPermissions: (opt) => opt.mapFrom((src) => src.userPermissions),
    //   client: (opt) => opt.mapFrom((src) => src.client),
    //   deliveryman: (opt) => opt.mapFrom((src) => src.deliveryman),
    //   order: (opt) => opt.nullSubstitute(undefined),
    //   reviewUser: (opt) => opt.nullSubstitute(undefined),
    //   storeUsers: (opt) => opt.nullSubstitute(undefined),
    //   userFavoriteProducts: (opt) => opt.nullSubstitute(undefined),
    //   userFavoriteStores: (opt) => opt.nullSubstitute(undefined),
    //   devices: (opt) => opt.nullSubstitute(undefined),
    // });

    this.createAutoMap(UserMapper.IUserMailingDTOToIUserMailingViewModel, {});

    this.createAutoMap(UserMapper.IUserToIUserWithProfileDataViewModel, {
      profiles: (opt) => {
        opt.preCondition((src) => src.userProfiles !== undefined);
        opt.mapFromUsing((src) => src.userProfiles!, ProfileMapper.IUserProfileToIProfileViewModel);
      },
      client: (opt) => {
        opt.preCondition((src) => src.client !== undefined);
        opt.mapFromUsing((src) => src.client!, ClientMapper.IClientToIClientViewModel);
      },
      deliveryman: (opt) => {
        opt.preCondition((src) => src.deliveryman !== undefined);
        opt.mapFromUsing((src) => src.deliveryman!, DeliverymanMapper.IDeliverymanToIDeliverymanViewModel);
      },
      shopkeeper: (opt) => {
        opt.preCondition((src) => src.shopkeeper !== undefined);
        opt.mapFromUsing((src) => src.shopkeeper!, ShopkeeperMapper.IShopkeeperToIShopkeeperViewModel);
      },
      files: (opt) => {
        opt.preCondition((src) => src.files !== undefined);
        opt.mapFromUsing((src) => src.files!, FilesMapper.IFileToIListFileViewModel);
      },
    })
      .forSourceMember("userProfiles", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("deleted", (opt) => opt.ignore())
      .forSourceMember("deletedAt", (opt) => opt.ignore())
      .forSourceMember("cognitoId", (opt) => opt.ignore());

    this.createAutoMap(UserMapper.IUserRegisterDTOToIUser, {
      id: (opt) => opt.ignore(),
      cognitoId: (opt) => opt.ignore(),
      cognitoIdGoogle: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      deletedAt: (opt) => opt.ignore(),
      deleted: (opt) => opt.ignore(),
      userAddress: (opt) => {
        opt.preCondition((src) => src.userAddress !== undefined);
        opt.condition((src) => src.userAddress !== undefined);
        opt.mapFromUsing((src) => [src.userAddress], AddressMapper.ICreateAddressViewModelToIUserAddress);
      },
      userProfiles: (opt) => opt.ignore(),
      userPermissions: (opt) => opt.ignore(),
      client: (opt) => {
        opt.preCondition((src) => src.client !== undefined);
        opt.condition((src) => src.client !== undefined);
        opt.mapFromUsing((src) => src.client, ClientMapper.CreateClientViewModelToIClient);
      },
      deliveryman: (opt) => {
        opt.preCondition((src) => src.deliveryman !== undefined);
        opt.condition((src) => src.deliveryman !== undefined);
        opt.mapFromUsing((src) => src.deliveryman, DeliverymanMapper.ICreateDeliverymanViewModelToIDeliveryman);
      },
      shopkeeper: (opt) => {
        opt.preCondition((src) => src.shopkeeper !== undefined);
        opt.condition((src) => src.shopkeeper !== undefined);
        opt.mapFromUsing((src) => src.shopkeeper, ShopkeeperMapper.CreateShopkeeperViewModelToIShopkeeper);
      },
      order: (opt) => opt.ignore(),
      reviewUser: (opt) => opt.ignore(),
      storeUsers: (opt) => opt.ignore(),
      userFavoriteProducts: (opt) => opt.ignore(),
      userFavoriteStores: (opt) => opt.ignore(),
      devices: (opt) => opt.ignore(),
      messages: (opt) => opt.ignore(),
      userMessage: (opt) => opt.ignore(),
      loginSession: (opt) => opt.ignore(),
      disabled: (opt) => opt.ignore(),
      permanentlyDeleted: (opt) => opt.ignore(),
      dateOfBirth: (opt) => opt.mapFrom((src) => src.dateOfBirth),
      reactivationCode: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
    })
      .forSourceMember("password", (opt) => opt.ignore())
      .forSourceMember("authChallengeData", (opt) => opt.ignore());

    this.createAutoMap(UserMapper.ICreateUserViewModelToIUserRegisterDTO, {
      authChallengeData: (opt) => opt.ignore(),
      dateOfBirth: (opt) => opt.mapFrom((src) => src.dateOfBirth),
    });

    this.createAutoMap(UserMapper.ICreateUserWebViewModelToIUserRegisterDTO, {
      userAddress: (opt) => opt.ignore(),
      contactByEmail: (opt) => opt.ignore(),
      client: (opt) => opt.ignore(),
      deliveryman: (opt) => opt.ignore(),
      contactBySms: (opt) => opt.ignore(),
      contactByWhatsapp: (opt) => opt.ignore(),
      shopkeeper: (opt) => opt.ignore(),
      userPermissions: (opt) => opt.ignore(),
      userProfiles: (opt) => opt.ignore(),
      dateOfBirth: (opt) => opt.mapFrom((src) => new Date(src.dateOfBirth)),
    });

    this.createAutoMap(UserMapper.IUserWithProfilePictureDTOToIUserWithProfilePictureViewModel, {
      deliverymanPixKey: (opt) => opt.mapFrom((src) => src.deliveryman?.pixKey || " "),
      deliverymanId: (opt) => opt.mapFrom((src) => src.deliveryman?.id),
      shopkeeperId: (opt) => opt.mapFrom((src) => src.shopkeeper?.id),
    }).forSourceMember("deliveryman", (opt) => opt.ignore());

    this.createAutoMap(UserMapper.IUpdateUserViewModelToIUser, {
      client: (opt) => opt.ignore(),
      deliveryman: (opt) => opt.ignore(),
      cognitoId: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      deleted: (opt) => opt.ignore(),
      deletedAt: (opt) => opt.ignore(),
      devices: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      reviewUser: (opt) => opt.ignore(),
      shopkeeper: (opt) => opt.ignore(),
      storeUsers: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      userAddress: (opt) => opt.ignore(),
      userFavoriteProducts: (opt) => opt.ignore(),
      userFavoriteStores: (opt) => opt.ignore(),
      userPermissions: (opt) => opt.ignore(),
      userProfiles: (opt) => opt.ignore(),
      cognitoIdGoogle: (opt) => opt.ignore(),
      messages: (opt) => opt.ignore(),
      userMessage: (opt) => opt.ignore(),
      loginSession: (opt) => opt.ignore(),
      disabled: (opt) => opt.ignore(),
      permanentlyDeleted: (opt) => opt.ignore(),
      reactivationCode: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
      dateOfBirth: (opt) => opt.mapFrom((src) => src.dateOfBirth),
    }).forSourceMember("pixKey", (opt) => opt.ignore());

    this.createAutoMap(UserMapper.ICreateUserSocialLoginViewModelToIUser, {
      id: (opt) => opt.ignore(),
      cognitoId: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      deletedAt: (opt) => opt.ignore(),
      deleted: (opt) => opt.ignore(),
      userAddress: (opt) => opt.ignore(),
      userProfiles: (opt) => opt.ignore(),
      userPermissions: (opt) => opt.ignore(),
      client: (opt) => opt.ignore(),
      deliveryman: (opt) => opt.ignore(),
      shopkeeper: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
      reviewUser: (opt) => opt.ignore(),
      storeUsers: (opt) => opt.ignore(),
      userFavoriteProducts: (opt) => opt.ignore(),
      userFavoriteStores: (opt) => opt.ignore(),
      devices: (opt) => opt.ignore(),
      messages: (opt) => opt.ignore(),
      userMessage: (opt) => opt.ignore(),
      loginSession: (opt) => opt.ignore(),
      disabled: (opt) => opt.ignore(),
      permanentlyDeleted: (opt) => opt.ignore(),
      dateOfBirth: (opt) => opt.mapFrom((src) => src.dateOfBirth),
      reactivationCode: (opt) => opt.ignore(),
      files: (opt) => opt.ignore(),
    });

    this.createAutoMap(UserMapper.IUserToIUserProfileAnalyzeViewModel, {
      profiles: (opt) => {
        opt.mapFromUsing((src) => src.userProfiles, ProfileMapper.IUserProfileToIProfileViewModel);
      },
      status: (opt) => {
        opt.mapFrom((src) => ({
          deliveryman: src.deliveryman?.status || undefined,
          shopkeeper: src.shopkeeper?.status || undefined,
        }));
      },
      isDefaultAdmin: (opt) => opt.mapFrom((src) => src.email === "<EMAIL>"),
    })
      .forSourceMember("cognitoId", (opt) => opt.ignore())
      .forSourceMember("cognitoIdGoogle", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("deletedAt", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("client", (opt) => opt.ignore())
      .forSourceMember("order", (opt) => opt.ignore())
      .forSourceMember("reviewUser", (opt) => opt.ignore())
      .forSourceMember("storeUsers", (opt) => opt.ignore())
      .forSourceMember("userAddress", (opt) => opt.ignore())
      .forSourceMember("userProfiles", (opt) => opt.ignore())
      .forSourceMember("userPermissions", (opt) => opt.ignore())
      .forSourceMember("userFavoriteProducts", (opt) => opt.ignore())
      .forSourceMember("userFavoriteStores", (opt) => opt.ignore())
      .forSourceMember("devices", (opt) => opt.ignore())
      .forSourceMember("deliveryman", (opt) => opt.ignore())
      .forSourceMember("shopkeeper", (opt) => opt.ignore())
      .forSourceMember("shopkeeper", (opt) => opt.ignore())
      .forSourceMember("loginSession", (opt) => opt.ignore())
      .forSourceMember("messages", (opt) => opt.ignore());
  }
}

// const userMapper: MappingProfile = (mapper) => {

//   createMap(
//     mapper,
//     User,
//     UserViewModel,
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     ),
//     forMember(
//       (destination) => destination.userAddress,
//       mapFrom(
//         (start) =>
//           start?.userAddress?.map(
//             (p) => p.address
//           ) as unknown as AddressViewModel[]
//       )
//     )
//   );

//   createMap(mapper, UserProfile, User);
//   createMap(mapper, User, UserProfile);

//   createMap(
//     mapper,
//     User,
//     UserProfileViewModel,
//     forMember(
//       (destination) => destination.profiles,
//       mapWithArguments((source: User, { profile }) => {
//         if (profile) return profile;
//         return source?.userProfiles?.map((p) => p.profile);
//       })
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     )
//   );

//   createMap(
//     mapper,
//     CreateUserViewModel,
//     User,
//     forMember(
//       (destination) => destination.userAddress,
//       mapFrom((start) => start?.userAddress as unknown as UserAddress[])
//     ),
//     forMember(
//       (destination) => destination.client,
//       mapFrom((viewModel) => viewModel.client)
//     ),
//     forMember(
//       (destination) => destination.deliveryman,
//       mapFrom((viewModel) => viewModel.deliveryman)
//     ),
//     forMember(
//       (destination) => destination.shopkeeper,
//       mapFrom((viewModel) => viewModel.shopkeeper)
//     )
//   );

//   createMap(
//     mapper,
//     User,
//     UserProfileDataViewModel,
//     forMember(
//       (destination) => destination.profiles,
//       mapWithArguments((source: User, { profile }) => {
//         if (profile) return profile;
//         return source?.userProfiles?.map((p) => p.profile);
//       })
//     ),
//     forMember(
//       (destination) => destination.client,
//       mapFrom((viewModel) => viewModel.client)
//     ),
//     forMember(
//       (destination) => destination.deliveryman,
//       mapFrom((viewModel) => viewModel.deliveryman as unknown)
//     ),
//     forMember(
//       (destination) => destination.shopkeeper,
//       mapFrom((viewModel) => viewModel.shopkeeper as unknown)
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start?.id)
//     )
//   );
// };

// export { userMapper };
