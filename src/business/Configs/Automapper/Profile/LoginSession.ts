/* eslint-disable no-underscore-dangle */
import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { EDayOfWeek } from "@prisma/client";
import { ICreateLoginSessionViewModel } from "src/api/ViewModels/LoginSession/ICreate";
import { ILoginSessionInfoViewModel } from "src/api/ViewModels/LoginSession/ILoginSessionInfo";
import { ILoginSessionDashboardViewModel } from "src/api/ViewModels/LoginSession/IListDashboard";
import { ILoginSessionAggregateDTO } from "src/business/DTOs/LoginSession/ILoginSession";
import { ILoginSessionInfoDTO } from "src/business/DTOs/LoginSession/ILoginSessionInfo";
import { ILoginSession } from "src/business/Interfaces/Prisma/ILoginSession";

export class LoginSessionMapper extends MappingProfile {
  static readonly ILoginSessionViewModelToILoginSession = new MappingPair<
    ICreateLoginSessionViewModel,
    ILoginSession
  >();

  static readonly ILoginSessionAggregateDTOToILoginSessionDashboardViewModel = new MappingPair<
    ILoginSessionAggregateDTO,
    ILoginSessionDashboardViewModel
  >();

  static readonly ILoginSessionInfoDTOToILoginSessionInfoViewModel = new MappingPair<
    ILoginSessionInfoDTO,
    ILoginSessionInfoViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(LoginSessionMapper.ILoginSessionViewModelToILoginSession, {
      id: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      startAccessDate: (opt) => opt.ignore(),
      startAccessTime: (opt) => opt.ignore(),
      startAccessDay: (opt) =>
        opt.mapFrom(() => {
          const currentDay = new Date().toLocaleString("en", { weekday: "long" }).toLowerCase() as EDayOfWeek;
          return currentDay;
        }),
      startAccessHour: (opt) =>
        opt.mapFrom(() => {
          const currentHour = new Date().getUTCHours();
          return currentHour;
        }),
      endAccess: (opt) => opt.ignore(),
    });

    this.createAutoMap(LoginSessionMapper.ILoginSessionAggregateDTOToILoginSessionDashboardViewModel, {
      accessHour: (opt) => {
        opt.preCondition((src) => src.startAccessHour !== null);
        opt.condition((src) => src !== null);
        opt.mapFrom((src) => Number(src.startAccessHour));
      },
      totalAccess: (opt) => {
        opt.preCondition((src) => src._count !== undefined && src._count?.startAccessDate !== undefined);
        opt.condition((src) => src !== undefined);
        opt.mapFrom((src) => src._count!.startAccessDate!);
      },
      weekDay: (opt) => {
        opt.preCondition((src) => src.startAccessDay !== undefined);
        opt.condition((src) => src !== undefined);
        opt.mapFrom((src) => src.startAccessDay!);
      },
    })
      .forSourceMember("startAccessDate", (opt) => opt.ignore())
      .forSourceMember("startAccessDay", (opt) => opt.ignore())
      .forSourceMember("startAccessHour", (opt) => opt.ignore());

    this.createAutoMap(LoginSessionMapper.ILoginSessionInfoDTOToILoginSessionInfoViewModel, {
      maxHourAccess: (opt) => {
        opt.mapFrom((src) => {
          const data: { max: number; count: number } = src.maxHourAccess.reduce(
            (result, dto) => {
              if (dto.startAccessHour && dto._count?.startAccessHour && dto._count?.startAccessHour > result.count) {
                result.count = dto._count?.startAccessHour;
                result.max = dto.startAccessHour;
              }
              return result;
            },
            { max: 0, count: 0 },
          );
          return data.max;
        });
      },
      totalAccess: (opt) => {
        opt.preCondition((src) => src.totalAccess._count?.startAccessDate !== undefined);
        opt.condition((src) => src !== undefined);
        opt.mapFrom((src) => src.totalAccess._count?.startAccessDate!);
      },
    });
  }
}
