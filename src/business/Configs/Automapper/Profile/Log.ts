import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ILogListViewModel } from "src/api/ViewModels/Log/IList";
import { ILogDetailsViewModel } from "src/api/ViewModels/Log/ILogDetails";
import { ILogDTO } from "src/business/DTOs/Log/ILog";
import { ILog } from "src/business/Interfaces/Prisma/ILog";

export class LogMapper extends MappingProfile {
  static readonly ILogToILogListViewModel = new MappingPair<ILog, ILogListViewModel>();

  static readonly ILogDTOToILogDetailsViewModel = new MappingPair<ILogDTO, ILogDetailsViewModel>();

  constructor() {
    super();

    this.createAutoMap(LogMapper.ILogToILogListViewModel, {})
      .forSourceMember("currentData", (opt) => opt.ignore())
      .forSourceMember("details", (opt) => opt.ignore())
      .forSourceMember("userId", (opt) => opt.ignore())
      .forSourceMember("detailsHash", (opt) => opt.ignore())
      .forSourceMember("currentDataHash", (opt) => opt.ignore());

    this.createAutoMap(LogMapper.ILogDTOToILogDetailsViewModel, {
      userName: (opt) => {
        opt.mapFrom((src) => (src.user ? `${src.user!.firstName} ${src.user!.lastName}` : ""));
      },
      entityId: (opt) => opt.ignore(),
    })
      .forSourceMember("detailsHash", (opt) => opt.ignore())
      .forSourceMember("currentDataHash", (opt) => opt.ignore())
      .forSourceMember("user", (opt) => opt.ignore());
  }
}
