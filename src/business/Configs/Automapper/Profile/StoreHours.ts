/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ArrayToObjectMappingPair,
  MappingPair,
  Profile as MappingProfile,
} from "@dynamic-mapper/mapper";
import {
  storeHoursToStoreHoursViewModel,
  storeHoursViewModelToStoreHours,
} from "src/api/Utils/StoreHoursMap";
import { IStoreHoursViewModel } from "src/api/ViewModels/StoreHours/IViewModel";
import { IStoreHoursList } from "src/business/DTOs/StoreHours";
import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";

export class StoreHoursMapper extends MappingProfile {
  static readonly IStoreHoursViewModelToIStoreHoursList = new MappingPair<
    IStoreHoursViewModel,
    IStoreHoursList
  >();

  static readonly IStoreHoursToIStoreHoursViewModel =
    new ArrayToObjectMappingPair<IStoreHours[], IStoreHoursViewModel>();

  constructor() {
    super();

    this.createAutoMap(StoreHoursMapper.IStoreHoursViewModelToIStoreHoursList, {
      list: (opt) => {
        opt.mapFrom((src) => {
          const storeHoursList = storeHoursViewModelToStoreHours(src);
          return storeHoursList;
        });
      },
    });

    this.createAutoMap(StoreHoursMapper.IStoreHoursToIStoreHoursViewModel, {
      days: (opt) => opt.ignore(),
      storeId: (opt) => opt.ignore(),
    }).convertUsing((src, dest) => ({
      days: storeHoursToStoreHoursViewModel(src).days,
    }));
  }
}
