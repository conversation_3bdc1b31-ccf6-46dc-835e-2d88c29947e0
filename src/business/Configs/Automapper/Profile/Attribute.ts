import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IAttributeViewModel } from "src/api/ViewModels/Attribute/IAttribute";
import { IAttributeOptionViewModel } from "src/api/ViewModels/Attribute/IAttributeOption";
import { IAttributeWithoutOptionViewModel } from "src/api/ViewModels/Attribute/IAttributeWithoutOption";
import { IAttributeListViewModel } from "src/api/ViewModels/Attribute/IList";
import { IAttributeOptionDTO } from "src/business/DTOs/Attribute/IAttributeOption";
import { IAttribute } from "src/business/Interfaces/Prisma/IAttribute";
import { IAttributeOption } from "src/business/Interfaces/Prisma/IAttributeOption";
import { IProductAttribute } from "src/business/Interfaces/Prisma/IProductAttribute";
import { IProductAttributeOption } from "src/business/Interfaces/Prisma/IProductAttributeOption";

export class AttributeMapper extends MappingProfile {
  static readonly IAttributeToIAttributeListViewModel = new MappingPair<IAttribute, IAttributeListViewModel>();

  static readonly IAttributeToIAttributeViewModel = new MappingPair<IAttribute, IAttributeViewModel>();

  static readonly IAttributeToIAttributeWithoutOptionViewModel = new MappingPair<
    IAttribute,
    IAttributeWithoutOptionViewModel
  >();

  static readonly IAttributeViewModelToIAttribute = new MappingPair<IAttributeViewModel, IAttribute>();

  static readonly IAttributeOptionToIAttributeOptionDto = new MappingPair<IAttributeOption, IAttributeOptionDTO>();

  static readonly IAttributeViewModelToIProductAttribute = new MappingPair<IAttributeViewModel, IProductAttribute>();

  static readonly IProductAttributeToIAttributeViewModel = new MappingPair<IProductAttribute, IAttributeViewModel>();

  static readonly IAttributeOptionViewModelToIProductAttributeOption = new MappingPair<
    IAttributeOptionViewModel,
    IProductAttributeOption
  >();

  static readonly IAttributeOptionViewModelToIAttributeOption = new MappingPair<
    IAttributeOptionViewModel,
    IAttributeOption
  >();

  static readonly IAttributeOptionToIAttributeOptionViewModel = new MappingPair<
    IAttributeOption,
    IAttributeOptionViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(AttributeMapper.IAttributeOptionViewModelToIAttributeOption, {
      attributeId: (opt) => opt.nullSubstitute(""),
      attribute: (opt) => opt.nullSubstitute(undefined),
      productAttributeOption: (opt) => opt.nullSubstitute(undefined),
    });

    this.createAutoMap(AttributeMapper.IAttributeOptionToIAttributeOptionViewModel, {
      checked: (opt) => opt.nullSubstitute(undefined),
    }).forSourceMember("attributeId", (opt) => opt.ignore());

    this.createAutoMap(AttributeMapper.IAttributeToIAttributeWithoutOptionViewModel, {
      shortDescription: (opt) => opt.mapFrom((src) => src.shortDescription!),
    }).forSourceMember("attributeOption", (opt) => opt.ignore());

    this.createAutoMap(AttributeMapper.IAttributeToIAttributeViewModel, {
      checked: (opt) => opt.nullSubstitute(undefined),
      shortDescription: (opt) => opt.mapFrom((src) => src.shortDescription!),
      attributeOption: (opt) =>
        opt.mapFromUsing((src) => src.attributeOption, AttributeMapper.IAttributeOptionToIAttributeOptionViewModel),
    });

    this.createAutoMap(AttributeMapper.IAttributeViewModelToIAttribute, {
      shortDescription: (opt) => opt.mapFrom((src) => src.shortDescription),
      createdAt: (opt) => opt.nullSubstitute(undefined),
      updatedAt: (opt) => opt.nullSubstitute(undefined),
      productAttribute: (opt) => opt.ignore(),
      attributeOption: (opt) =>
        opt.mapFromUsing((src) => src.attributeOption, AttributeMapper.IAttributeOptionViewModelToIAttributeOption),
    });

    this.createAutoMap(AttributeMapper.IAttributeOptionToIAttributeOptionDto, {
      checked: (opt) => opt.nullSubstitute(false),
    }).forSourceMember("attributeId", (opt) => opt.ignore());

    this.createAutoMap(AttributeMapper.IAttributeToIAttributeListViewModel, {
      attribute: (opt) => opt.mapFromUsing((src) => src, AttributeMapper.IAttributeToIAttributeWithoutOptionViewModel),
      data: (opt) =>
        opt.mapFromUsing((src) => src.attributeOption, AttributeMapper.IAttributeOptionToIAttributeOptionDto),
    })
      .forSourceMember("id", (opt) => opt.ignore())
      .forSourceMember("name", (opt) => opt.ignore())
      .forSourceMember("shortDescription", (opt) => opt.ignore())
      .forSourceMember("required", (opt) => opt.ignore())
      .forSourceMember("type", (opt) => opt.ignore())
      .forSourceMember("createdAt", (opt) => opt.ignore())
      .forSourceMember("updatedAt", (opt) => opt.ignore())
      .forSourceMember("attributeOption", (opt) => opt.ignore());

    this.createAutoMap(AttributeMapper.IAttributeOptionViewModelToIProductAttributeOption, {
      id: (opt) => opt.ignore(),
      attributeOptionId: (opt) => opt.mapFrom((src) => src.id),
      productAttributeId: (opt) => opt.ignore(),
      attributeOption: (opt) => opt.ignore(),
      productAttribute: (opt) => opt.ignore(),
      orderItemProductAttributeOption: (opt) => opt.ignore(),
      active: (opt) => opt.ignore(),
    })
      .forSourceMember("id", (src) => src.ignore())
      .forSourceMember("value", (src) => src.ignore())
      .forSourceMember("checked", (src) => src.ignore());

    this.createAutoMap(AttributeMapper.IAttributeViewModelToIProductAttribute, {
      id: (opt) => opt.ignore(),
      productId: (opt) => opt.ignore(),
      attributeId: (opt) => opt.mapFrom((src) => src.id),
      attribute: (opt) => opt.ignore(),
      // opt.mapFromUsing(
      //   (src) => src,
      //   AttributeMapper.IAttributeViewModelToIAttribute
      // ),
      active: (opt) => opt.ignore(),
      product: (opt) => opt.ignore(),
      productAttributeOption: (opt) =>
        opt.mapFromUsing(
          (src) => src.attributeOption,
          AttributeMapper.IAttributeOptionViewModelToIProductAttributeOption,
        ),
    })
      .forSourceMember("id", (src) => src.ignore())
      .forSourceMember("name", (src) => src.ignore())
      .forSourceMember("shortDescription", (src) => src.ignore())
      .forSourceMember("type", (src) => src.ignore())
      .forSourceMember("required", (src) => src.ignore())
      .forSourceMember("checked", (src) => src.ignore())
      .forSourceMember("attributeOption", (src) => src.ignore());

    this.createAutoMap(AttributeMapper.IProductAttributeToIAttributeViewModel, {
      name: (opt) => opt.mapFrom((src) => src.attribute?.name!),
      shortDescription: (opt) => opt.mapFrom((src) => src.attribute?.shortDescription!),
      required: (opt) => opt.mapFrom((src) => src.attribute?.required!),
      type: (opt) => opt.mapFrom((src) => src.attribute?.type!),
      checked: (opt) => opt.nullSubstitute(undefined),
      attributeOption: (opt) =>
        opt.mapFromUsing(
          (src) => src.attribute?.attributeOption,
          AttributeMapper.IAttributeOptionToIAttributeOptionViewModel,
        ),
    })
      .forSourceMember("id", (src) => src.ignore())
      .forSourceMember("attribute", (src) => src.ignore())
      .forSourceMember("attributeId", (src) => src.ignore())
      .forSourceMember("product", (src) => src.ignore())
      .forSourceMember("productId", (src) => src.ignore())
      .forSourceMember("productAttributeOption", (src) => src.ignore());
  }
}

// const attributeMapper: MappingProfile = (mapper) => {
//   createMap(
//     mapper,
//     Attribute,
//     AttributeSectionListViewModel,
//     forMember(
//       (destination) => destination.data,
//       mapFrom((start) => start.attributeOption as unknown as IAttributeOption[])
//     ),
//     forMember(
//       (destination) => destination.attribute,
//       mapFrom((start) => {
//         delete start.attributeOption
//         return start as unknown as IAttributeViewModel
//       })
//     ),

//   );
// };

// export { attributeMapper };
