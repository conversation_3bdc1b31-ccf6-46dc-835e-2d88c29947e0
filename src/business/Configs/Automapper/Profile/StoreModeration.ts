import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { IStoreModerationViewModel } from "src/api/ViewModels/StoreModeration/IViewModel";
import { IStoreModeration } from "src/business/Interfaces/Prisma/IStoreModeration";

export class StoreModerationMapper extends MappingProfile {
  static readonly IStoreModerationToIStoreModerationViewModel = new MappingPair<
    IStoreModeration,
    IStoreModerationViewModel
  >();

  static readonly IStoreModerationViewModelToIStoreModeration = new MappingPair<
    IStoreModerationViewModel,
    IStoreModeration
  >();

  constructor() {
    super();

    this.createAutoMap(StoreModerationMapper.IStoreModerationToIStoreModerationViewModel, {});
    this.createAutoMap(StoreModerationMapper.IStoreModerationViewModelToIStoreModeration, {
      store: (opt) => opt.ignore(),
    });
  }
}
