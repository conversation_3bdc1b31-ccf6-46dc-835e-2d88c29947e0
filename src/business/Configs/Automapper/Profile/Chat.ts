// import { Chat } from "src/business/Models/Chat.model";

import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateChatViewModel } from "src/api/ViewModels/Chat/ICreate";
import { ICreateChatMessageViewModel } from "src/api/ViewModels/ChatMessages/ICreate";
import { IListChatMessagesViewModel } from "src/api/ViewModels/ChatMessages/IList";
import { IListChatMessageUserViewModel } from "src/api/ViewModels/ChatMessages/IListUser";
import { IChat } from "src/business/Interfaces/Prisma/IChat";
import { IChatMessage } from "src/business/Interfaces/Prisma/IChatMessage";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

export class ChatMapper extends MappingProfile {
  static readonly ICreateChatViewModelToIChat = new MappingPair<ICreateChatViewModel, IChat>();

  static readonly ICreateChatMessageViewModelToIChatMessage = new MappingPair<
    ICreateChatMessageViewModel,
    IChatMessage
  >();

  static readonly IListChatMessagesViewModelToIChatMessage = new MappingPair<
    IListChatMessagesViewModel,
    IChatMessage
  >();

  static readonly IListChatMessageUserViewModelToIUser = new MappingPair<IListChatMessageUserViewModel, IUser>();

  constructor() {
    super();

    this.createAutoMap(ChatMapper.ICreateChatViewModelToIChat, {
      id: (opt) => opt.ignore(),
      chatMessage: (opt) =>
        opt.mapFromUsing((src) => src.chatMessages, ChatMapper.ICreateChatMessageViewModelToIChatMessage),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      order: (opt) => opt.ignore(),
    });

    this.createAutoMap(ChatMapper.ICreateChatMessageViewModelToIChatMessage, {
      chat: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      id: (opt) => opt.ignore(),
      profile: (opt) => {
        opt.preCondition((src) => src.profile !== undefined);
        opt.mapFrom((src) => ({ id: "", name: src.profile! }));
      },
      profileId: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
    });

    this.createAutoMap(ChatMapper.IListChatMessagesViewModelToIChatMessage, {
      message: (opt) => opt.mapFrom((src) => src.message),
      userId: (opt) => opt.mapFrom((src) => src.user.id),
      chatId: (opt) => opt.mapFrom((src) => src.id),
      profile: (opt) => opt.mapFrom((src) => ({ id: "", name: src.user.profile })),
      user: (opt) => opt.ignore(),
      profileId: (opt) => opt.ignore(),
      chat: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
    });
  }
}

// const chatMapper: MappingProfile = (mapper) => {
// createMap(mapper, CreateChatViewModel, Chat);
// createMap(
//   mapper,
//   CreateChatMessagesViewModel,
//   ChatMessages,
//   forMember(
//     (destination) => destination.profileId,
//     mapFrom((start) => {
//       const profilesDB = container.get<ProfilesDB>(TOKENS.ProfilesDB);
//       return start.profile ? profilesDB[EProfile[start.profile]]?.id : undefined;
//     }),
//   ),
// );
// createMap(
//   mapper,
//   ChatMessages,
//   ChatMessagesListViewModel,
//   forMember(
//     (destination) => destination.user,
//     mapFrom((start) => {
//       const profilesDB = container.get<ProfilesDB>(TOKENS.ProfilesSingle);
//       const data = {
//         id: start?.user?.id,
//         firstName: start?.user?.firstName,
//         lastName: start?.user?.lastName,
//         pictureUrl: start?.user?.profilePicture?.url,
//       };
//       if (profilesDB.deliveryman?.id === start.profileId) {
//         return { ...data, profile: EProfile.deliveryman };
//       }
//       if (profilesDB.shopkeeper?.id === start.profileId) {
//         return { ...data, profile: EProfile.shopkeeper };
//       }
//       return { ...data, profile: EProfile.client };
//     }),
//   ),
//   forMember(
//     (destination) => destination.id,
//     mapFrom((start) => start.id),
//   ),
// );
// };

// export { chatMapper };
