import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { EMessageSendingType, EMessageStatus } from "@prisma/client";
import { ICreateMessageViewModel } from "src/api/ViewModels/Message/ICreate";
import { IListNotificationMessagesViewModel } from "src/api/ViewModels/Message/IListNotification";
import { IListNotificationMessage } from "src/business/DTOs/Message/IListNotificationMessage";
import { IMessage } from "src/business/Interfaces/Prisma/IMessage";

export class MessageMapper extends MappingProfile {
  static readonly IListNotificationMessageToIListNotificationMessagesViewModel = new MappingPair<
    IListNotificationMessage,
    IListNotificationMessagesViewModel
  >();

  static readonly ICreateMessageViewModelToIMessage = new MappingPair<ICreateMessageViewModel, IMessage>();

  constructor() {
    super();

    this.createAutoMap(MessageMapper.IListNotificationMessageToIListNotificationMessagesViewModel, {
      body: (opt) => {
        opt.preCondition((src) => src.content !== undefined);
        opt.condition((src) => src !== undefined);
        opt.mapFrom((src) => {
          const content = src.content!.find((content) => content.sendingType === EMessageSendingType.notification);

          return content?.body ?? "";
        });
      },
      status: (opt) => {
        opt.preCondition((src) => src.userMessage !== undefined);
        opt.condition((src) => src !== undefined);
        opt.mapFrom((src) => {
          const { status } = src.userMessage![0];

          return status ?? EMessageStatus.created;
        });
      },
    })
      .forSourceMember("content", (opt) => opt.ignore())
      .forSourceMember("userMessage", (opt) => opt.ignore());

    this.createAutoMap(MessageMapper.ICreateMessageViewModelToIMessage, {
      createdAt: (opt) => opt.ignore(),
      id: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      sender: (opt) => opt.mapFrom((src) => ({ id: src.senderId })),
      userMessage: (opt) => opt.mapFrom((src) => src.usersId.map((userId) => ({ userId, profileId: src.profileId }))),
    })
      .forSourceMember("usersId", (opt) => opt.ignore())
      .forSourceMember("profileId", (opt) => opt.ignore())
      .forSourceMember("senderId", (opt) => opt.ignore());

    // this.createAutoMap(FilesMapper.IFileToIListFileViewModel, {
    //   entity: (opt) => opt.ignore(),
    // })
    //   .forSourceMember("createdAt", (opt) => opt.ignore())
    //   .forSourceMember("updatedAt", (opt) => opt.ignore());

    // this.createAutoMap(FilesMapper.IUpdateFileViewModelToIFile, {
    //   url: (opt) => opt.ignore(),
    //   key: (opt) => opt.ignore(),
    //   bucket: (opt) => opt.ignore(),
    //   originalName: (opt) => opt.ignore(),
    //   type: (opt) => opt.ignore(),
    //   createdAt: (opt) => opt.ignore(),
    //   updatedAt: (opt) => opt.ignore(),
    // });
  }
}

// const messageMapper: MappingProfile = (mapper) => {
//   createMap(mapper, CreateMessageContentViewModel, MessageContent);
//   createMap(
//     mapper,
//     CreateMessageViewModel,
//     Message,
//     forMember(
//       (destination) => destination.userMessage,
//       mapFrom((start) =>
//         start.usersId.map((item) => ({
//           userId: item,
//           status: EMessageStatus.sended,
//           profileId: start.profileId,
//         }))
//       )
//     ),
//     forMember(
//       (destination) => destination.sendingType,
//       mapFrom((start) => start.content.map((item) => item.sendingType))
//     )
//   );
//   createMap(
//     mapper,
//     Message,
//     CreateMessageViewModel,
//     forMember(
//       (destination) => destination.usersId,
//       mapFrom((start) => start.userMessage.map((item) => item.userId))
//     )
//   );
//   createMap(
//     mapper,
//     Message,
//     MessageListViewModel,
//     forMember(
//       (destination) => destination.receivers,
//       mapFrom((start) =>
//         start.userMessage.map((item) => ({
//           firstName: item.user.firstName,
//           lastName: item.user.lastName,
//           email: item.user.email,
//           phone: item.user.phone,
//         }))
//       )
//     ),
//     forMember(
//       (destination) => destination.sender,
//       mapFrom((start) =>
//         start.sender
//           ? {
//               firstName: start.sender?.firstName,
//               lastName: start.sender?.lastName,
//               email: start.sender?.email,
//               phone: start.sender?.phone,
//             }
//           : undefined
//       )
//     ),
//     forMember(
//       (destination) => destination.content,
//       mapFrom((start) =>
//         start.content.map((item) => ({
//           body: item.body,
//           sendingType: item.sendingType,
//           emailAttachment: item.emailAttachment?.map((file) => file.url),
//         }))
//       )
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start.id)
//     )
//   );
//   createMap(
//     mapper,
//     Message,
//     NotificationMessagesListViewModel,
//     forMember(
//       (destination) => destination.body,
//       mapFrom(
//         (start) =>
//           start.content.find(
//             (item) => item.sendingType === EMessageSendingType.notification
//           )?.body
//       )
//     ),
//     forMember(
//       (destination) => destination.status,
//       mapFrom((start) => start.userMessage[0].status)
//     ),
//     forMember(
//       (destination) => destination.id,
//       mapFrom((start) => start.id)
//     )
//   );
// };

// export { messageMapper };
