import { MappingPair, Profile as MappingProfile } from "@dynamic-mapper/mapper";
import { ICreateContentManagementViewModel } from "src/api/ViewModels/ContentManagement/ICreate";
import { IUpdateContentManagementViewModel } from "src/api/ViewModels/ContentManagement/IUpdate";
import { IContentManagementViewModel } from "src/api/ViewModels/ContentManagement/IViewModel";
import { IContentManagement } from "src/business/Interfaces/Prisma/IContentManagement";

export class ContentManagementMapper extends MappingProfile {
  static readonly ICreateContentManagementViewModelToIContentManagement = new MappingPair<
    ICreateContentManagementViewModel,
    IContentManagement
  >();

  static readonly IUpdateContentManagementViewModelToIContentManagement = new MappingPair<
    IUpdateContentManagementViewModel,
    IContentManagement
  >();

  static readonly IContentManagementToIContentManagementViewModel = new MappingPair<
    IContentManagement,
    IContentManagementViewModel
  >();

  constructor() {
    super();

    this.createAutoMap(ContentManagementMapper.ICreateContentManagementViewModelToIContentManagement, {
      id: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
    });

    this.createAutoMap(ContentManagementMapper.IUpdateContentManagementViewModelToIContentManagement, {
      createdAt: (opt) => opt.ignore(),
      updatedAt: (opt) => opt.ignore(),
      user: (opt) => opt.ignore(),
    });
    this.createAutoMap(ContentManagementMapper.IContentManagementToIContentManagementViewModel, {});
  }
}
