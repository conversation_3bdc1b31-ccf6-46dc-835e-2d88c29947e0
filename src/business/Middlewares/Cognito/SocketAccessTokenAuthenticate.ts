import { CognitoJwtVerifier } from "aws-jwt-verify";
import cookie from "cookie";
import socketio from "socket.io";
import { ExtendedError } from "socket.io/dist/namespace";
import { DefaultEventsMap } from "socket.io/dist/typed-events";
import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IUserService } from "src/business/Interfaces/Service/IUser";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";

const socketCognitoAccessTokenAuthenticate = async (
  socket: socketio.Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>,
  next: (err?: ExtendedError | undefined) => void,
) => {
  if (!socket.handshake.headers.cookie && !socket.handshake.auth.token) {
    return next(new Error("TokenNotFound"));
  }

  const rawToken: string = socket.handshake.headers.cookie
    ? cookie.parse(socket.handshake.headers.cookie).authorization
    : socket.handshake.auth.token;

  const token = rawToken?.replace("Bearer ", "");

  const verifier = CognitoJwtVerifier.create({
    clientId: process.env.AWS_COGNITO_CLIENT_ID!,
    userPoolId: process.env.AWS_COGNITO_USER_POOL_ID!,
    tokenUse: "access",
  });

  if (token) {
    try {
      const payload = await verifier.verify(token, { tokenUse: "access" });

      const userService = container.get<IUserService>(TOKENS.IUserService);

      const user = payload.username.includes("google")
        ? await userService.getByCognitoIdGoogle(payload.sub)
        : await userService.getByCognitoId(payload.sub);

      if (!user) {
        return next(new Error("TokenValidationError"));
      }

      return next();
    } catch (error) {
      const loggerService = container.get<ILoggerService>(TOKENS.LoggerService);

      loggerService.logError(error);

      return next(new Error("TokenValidationError"));
    }
  }

  return next(new Error("TokenNotFound"));
};

export default socketCognitoAccessTokenAuthenticate;
