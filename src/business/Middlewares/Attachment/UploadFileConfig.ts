import multer from "multer";
import mime from "mime-types";
import { Express, Request } from "express";

class UploadFileConfig {
  private fileFilter(filterTypes: string[]) {
    return (
      req: Request,
      file: Express.Multer.File,
      cb: multer.FileFilterCallback
    ) => {
      const type = mime.extension(file.mimetype);

      if (filterTypes.includes(`${type}`)) {
        cb(null, true);
      }

      cb(null, false);
    };
  }

  getConfig(filterTypes: string[] = ["png", "jpg", "jpeg"]): multer.Options {
    return {
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 1024 * 1024 * 20,
      },
    };
  }
}

export const uploadFileConfig = new UploadFileConfig();
