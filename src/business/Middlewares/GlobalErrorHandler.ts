import { NextFunction, Request, Response } from "express";
import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ILoggerService } from "src/business/Interfaces/Service/Logger/ILoggerService";

const globalErrorHandler = (
  err: Error,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction,
) => {
  const loggerService = container.get<ILoggerService>(TOKENS.LoggerService);

  loggerService.logError(err);

  console.log(err);

  return res.status(500).json({ status: 500, data: {}, error: "Internal Server Error" });
};

export default globalErrorHandler;
