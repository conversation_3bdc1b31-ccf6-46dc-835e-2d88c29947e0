import ISendEmail from "src/business/DTOs/SendEmail";
import { ETypeEmail } from "src/business/Enums/Models/ETypeEmail";
import { IUser } from "src/business/Interfaces/Prisma/IUser";

const getUserRegisteredEmail = (user: IUser) => {
  const subject = "Boas vindas ao aplicativo Coop Delivery";
  const message = "<p>Cadastro realizado com sucesso no <strong>Coop Delivery</strong></p>";

  const email: ISendEmail = {
    sourceEmail: process.env.AWS_SES_EMAIL_SOURCE!,
    toAddresses: [`${user.firstName} ${user.lastName} <${user.email}>`],
    type: ETypeEmail.html,
    message,
    subject,
  };

  return email;
};

export default getUserRegisteredEmail;
