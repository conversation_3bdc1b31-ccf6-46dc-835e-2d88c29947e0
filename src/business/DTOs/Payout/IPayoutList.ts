import { EPayoutOwner, EPayoutStatus } from "@prisma/client";

export interface IPayoutListDTO {
  id: string;
  orderId: string;
  borderoId?: string | null;
  payoutOwner: EPayoutOwner;
  administrativeFeePercent: number;
  administrativeFeeValue: number;
  createdAt: Date;
  transferValue: number;
  statusDate: Date;
  status: EPayoutStatus;
  price: number;
  shippingPrice: number;
  totalPrice: number;
}
