import { EPayoutStatus } from "@prisma/client";

export interface IPayoutDetailsDTO {
  id: string;
  orderId: string;
  administrativeFeePercent: number;
  administrativeFeeValue: number;
  createdAt: Date;
  status: EPayoutStatus;
  statusDate: Date;
  transferValue: number;
  cooperative: {
    name: string;
    cnpj: string;
  };
  order?: {
    createdAt: Date;
    shippingPrice: number;
    price: number;
    totalPrice: number;
    deliveryman?: {
      user: {
        firstName: string;
        cpf: string;
        email: string;
        phone: string;
      };
    };
    store: {
      id: string;
      name: string;
      description: string;
      cnpj: string;
      email: string;
      phone: string;
      slug?: string;
    };
    transaction: {
      paymentMethod: string;
    };
  };
}
