import { EPayoutOwner, EPayoutStatus } from "@prisma/client";

export interface IPayoutExportReportDTO {
  administrativeFeePercent: number;
  administrativeFeeValue: number;
  transferValue: number;
  createdAt: Date;
  status: EPayoutStatus;
  statusDate?: Date;
  payoutOwner: EPayoutOwner;
  cooperative: {
    name: string;
    cnpj: string;
  } | null;
  order: {
    totalPrice: number;
    price: number;
    shippingPrice: number;
    store: {
      name: string;
      cnpj: string;
    } | null;
    deliveryman: {
      user: {
        firstName: string;
        cpf: string;
      } | null;
    } | null;
  } | null;
}
