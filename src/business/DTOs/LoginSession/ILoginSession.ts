import { IBaseLoginSessionDTO } from "src/business/DTOs/LoginSession/IBaseLoginSession";
import { ILoginSessionAvgSumDTO } from "src/business/DTOs/LoginSession/ILoginSessionAvgSum";
import { ILoginSessionCountDTO } from "src/business/DTOs/LoginSession/ILoginSessionCount";

export interface ILoginSessionAggregateDTO extends IBaseLoginSessionDTO {
  _count?: ILoginSessionCountDTO | null;
  _avg?: ILoginSessionAvgSumDTO | null;
  _sum?: ILoginSessionAvgSumDTO | null;
  _min?: IBaseLoginSessionDTO | null;
  _max?: IBaseLoginSessionDTO | null;
}
