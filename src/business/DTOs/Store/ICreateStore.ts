import { ICreateAddressDTO } from "src/business/DTOs/Address/ICreateAddress";
import { ICreateStoreCategoryDTO } from "src/business/DTOs/StoreCategory/StoreCategory";

export interface ICreateStoreByExportDTO {
  name: string;
  cnpj: string;
  slug?: string | null;
  email: string;
  phone: string;
  pixKey?: string;
  description: string;
  address: ICreateAddressDTO;
  storeCategory?: ICreateStoreCategoryDTO[];
}
