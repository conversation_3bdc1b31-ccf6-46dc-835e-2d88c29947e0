export interface ExportStoreDTO {
  name: string;
  description: string;
  cnpj: string;
  slug?: string | null;
  email: string;
  phone: string;
  address?: {
    street: string;
    city: string;
    country: string;
    number?: string | null;
    district: string;
    nickname?: string | null;
    state: string;
    complement?: string | null;
    postcode?: string | null;
  } | null;
  storeCategory: {
    category: {
      name: string;
    };
  }[];
}
