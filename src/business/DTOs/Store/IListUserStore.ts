import { EStoreModeratorStatus } from "@prisma/client";
import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";

export interface IListUserStoreDTO {
  id: string;
  name: string;
  phone: string;
  cnpj: string;
  email: string;
  active: boolean;
  activeByAdmin: boolean;
  open: boolean;
  iconUrl?: string;
  pixKey?: string | null
  storeUsers: {
    id: string;
    owner: boolean;
    status: EStoreModeratorStatus;
  }[];
  address?: {
    id: string;
  } | null;
  storeHours?: IStoreHours[];
}
