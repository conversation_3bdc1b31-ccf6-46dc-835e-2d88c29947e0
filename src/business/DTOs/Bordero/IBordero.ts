import { EBorderoStatus, EPayoutOwner } from "@prisma/client";

interface IBorderoListDTO {
  id: string;
  userId?: string | null;
  storeId?: string | null;
  cooperativeId?: string | null;
  status: EBorderoStatus;
  payoutOwner: EPayoutOwner;
  createdAt: Date;
  statusDate: Date;
  quantityOrders: number;
  sumOrderValue: number;
  sumAdministrativeFeeValue: number;
  sumTransferValue: number;
  cooperative?: {
    name: string;
    cnpj: string;
    pixKey: string | null;
  } | null;
  user?: {
    firstName: string;
    lastName: string;
    cpf: string;
    deliveryman: {
      pixKey: string | null;
    } | null;
  } | null;
  store?: {
    name: string;
    cnpj: string;
    pixKey: string | null;
  } | null;
  userAdmin?: {
    firstName: string;
    lastName: string;
  };
}

export default IBorderoListDTO;
