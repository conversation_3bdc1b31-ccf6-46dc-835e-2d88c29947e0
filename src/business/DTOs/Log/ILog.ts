import { IUser } from "src/business/Interfaces/Prisma/IUser";

export interface ILogDTO {
  id: string;
  level: string;
  message: string;
  details: string;
  userId?: string | null;
  entity?: string | null;
  action?: string | null;
  createdAt?: Date;
  currentData?: string | null;
  previousData?: string | null;
  detailsHash?: string | null;
  currentDataHash?: string | null;
  user?: IUser | null;
}
