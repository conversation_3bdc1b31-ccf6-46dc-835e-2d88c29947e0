import { IReview } from "src/business/Interfaces/Prisma/IReview";
import { IUserFavoriteStores } from "src/business/Interfaces/Prisma/IUserFavoriteStores";

interface IProductSearchStoreAddressDTO {
  latitude: string;
  longitude: string;
}

export interface IProductSearchStoreDTO {
  id: string;
  name: string;
  userFavoriteStores?: IUserFavoriteStores[];
  address: IProductSearchStoreAddressDTO;
  iconUrl?: string;
  reviews?: IReview[];
}

export interface IProductSearchDTO {
  id: string;
  name: string;
  description: string;
  price: string;
  salePrice: string;
  preparationTime: string;
  store: IProductSearchStoreDTO;
  iconUrl?: string;
}
