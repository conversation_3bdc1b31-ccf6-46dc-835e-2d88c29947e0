import { IFileDTO } from "src/business/DTOs/Files/IFile";

export interface IProductByStoreWithFavoriteDTO {
  id: string;
  name: string;
  shortDescription: string;
  price: number;
  salePrice: number;
  storeId: string;
  preparationTime: number;
  sku: string;
  active: boolean;
  activeByAdmin: boolean;
  files?: IFileDTO[] | null;
  store: {
    name: string;
  };
  productCategory: {
    category: {
      name: string;
    };
  }[];
  isFavorite: boolean;
}
