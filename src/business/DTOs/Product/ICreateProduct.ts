import { ICreateProductAttributeDTO } from "src/business/DTOs/ProductAttribute/ICreate";
import { ICreateProductCategoryDTO } from "src/business/DTOs/ProductCategory/ICreate";
import { ICreateProductSubcategoryDTO } from "src/business/DTOs/ProductSubcategory/ICreate";

export interface ICreateProductByImportDTO {
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  salePrice: number;
  sku: string;
  active: boolean;
  storeId: string;
  preparationTime: number;
  productCategory?: ICreateProductCategoryDTO[];
  productSubcategory?: ICreateProductSubcategoryDTO[];
  productAttribute?: ICreateProductAttributeDTO[];
}
