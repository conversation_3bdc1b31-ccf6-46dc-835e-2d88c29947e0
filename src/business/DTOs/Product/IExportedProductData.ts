export interface IExportedProductData {
  id: string;
  name: string;
  short_description: string;
  description: string;
  price: number;
  sale_price: number;
  sku: string;
  createdAt: Date;
  updatedAt: Date;
  active: number;
  preparation_time: number;
  category_id?: string;
  category_name?: string;
  category_description?: string;
  category_subcategory_id?: string;
  category_subcategory_name?: string;
  category_subcategory_description?: string;
  attribute_id?: string;
  attribute_name?: string;
  attribute_shortdescription?: string;
  attribute_required?: boolean;
  attribute_type?: number;
  attribute_option_id?: string;
  attribute_option_value?: string;
}
