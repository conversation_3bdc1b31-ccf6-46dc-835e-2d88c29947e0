export interface IOrderByStatusOrderItem {
  product: {
    name: string;
  };
  quantity: number;
}

export interface IOrderByStatusOrderStatus {
  createdAt: Date;
  orderStatusType: {
    value: string;
  };
}

export interface IOrderByStatusDTO {
  id: string;
  code: number;
  totalPrice: number;
  shippingPrice: number;
  createdAt: Date;
  orderItem: IOrderByStatusOrderItem[];
  orderStatus: IOrderByStatusOrderStatus[];
}
