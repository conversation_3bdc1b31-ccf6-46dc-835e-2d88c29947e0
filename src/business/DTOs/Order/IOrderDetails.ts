import { IOrderItem } from "src/business/Interfaces/Prisma/IOrderItem";

export interface IOrderStatusTypeDetailsDTO {
  id: string;
  value: string;
}
export interface IOrderStatusDetailsDTO {
  id: string;
  createdAt: Date;
  orderStatusType: IOrderStatusTypeDetailsDTO;
  current: boolean;
  observation: string | null;
}

export interface IOrderDeliverymanDetailsDTO {
  id: string;
  deliveryman: {
    id: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
    };
  } | null;
}

export interface IOrderDetailsDTO {
  id: string;
  code: number;
  totalPrice: number;
  shippingPrice: number;
  customerCode: string | null;
  estimatedDeliveryTime: number;
  orderStatus: IOrderStatusDetailsDTO[];
  user: {
    firstName: string;
    lastName: string;
  };
  deliveryman: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
    };
    review: {
      rate: number;
    }[];
  } | null;
  address: {
    id: string;
    nickname: string | null;
    street: string;
    number: string | null;
    district: string;
    city: string;
    state: string;
    complement: string | null;
  } | null;
  store: {
    id: string;
    name: string;
  };
  orderItem: IOrderItem[];
}
