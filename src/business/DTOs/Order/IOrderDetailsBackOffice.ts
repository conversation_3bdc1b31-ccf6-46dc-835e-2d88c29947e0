import { IOrderItem } from "src/business/Interfaces/Prisma/IOrderItem";

interface IOrderStatusDetailsDTO {
  id: string;
  createdAt: Date;
  orderStatusType: {
    id: string;
    value: string;
  };
  current: boolean;
  observation: string | null;
}

export interface IOrderDetailsBackOfficeDTO {
  id: string;
  code: number;
  totalPrice: number;
  price: number;
  shippingPrice: number;
  orderStatus: IOrderStatusDetailsDTO[];
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  deliveryman: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
      cpf: string;
    };
  } | null;
  address: {
    id: string;
    street: string;
    number: string | null;
    district: string;
    city: string;
    state: string;
    complement: string | null;
    postcode: string;
    country: string;
  } | null;
  store: {
    id: string;
    name: string;
    cnpj: string;
    phone: string;
    email: string;
  };
  orderItem?: Partial<IOrderItem>[];
}
