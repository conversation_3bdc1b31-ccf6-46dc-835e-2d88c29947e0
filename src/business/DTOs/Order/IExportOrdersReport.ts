export interface IExportOrdersReportDTO {
  orderCode: number;
  orderCreatedAt: Date;
  orderPrice: number;
  orderShippingPrice: number;
  orderTotalPrice: number;
  orderQuantityItems: number;
  orderQuantityProducts: number;
  orderItemQuantity: number;
  orderItemUnity_price: number;
  orderItemTotalPrice: number;
  orderItemObservation: string;
  attributeOptionValue?: string;
  paymentMethod: string;
  attributeName?: string;
  productName: string;
  categoryName: string;
  storeName: string;
  storeCnpj: string;
  addressDistrict: string;
}
