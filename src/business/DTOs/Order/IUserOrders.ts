export interface IUserOrdersOrderItem {
  id: string;
  quantity: number;
  observation: string | null;
  product: {
    name: string;
  };
}

export interface IUserOrdersOrderStatus {
  orderStatusType: {
    value: string;
  };
}

export interface IUserOrdersDTO {
  id: string;
  code: number;
  totalPrice: number;
  createdAt: Date;
  store: {
    id: string;
    name: string;
  } | null;
  deliveryman: {
    id: string;
  } | null;
  review: {
    rate: number;
    storeId: string | null;
    deliverymanId: string | null;
  }[];
  orderItem: IUserOrdersOrderItem[];
  orderStatus: IUserOrdersOrderStatus[];
}
