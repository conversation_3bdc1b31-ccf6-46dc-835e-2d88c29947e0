import { ChallengeNameType } from "@aws-sdk/client-cognito-identity-provider";
import { ICreateAddressViewModel } from "src/api/ViewModels/Address/ICreate";
import { CreateClientViewModel } from "src/api/ViewModels/Client/ICreateClient";
import { ICreateDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/ICreateDeliveryman";
import { CreateShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/ICreateShopkeeper";

export interface IUserRegisterDTO {
  firstName: string;
  lastName: string;
  cpf: string;
  email: string;
  password: string;
  phone: string;
  contactByEmail: boolean;
  contactBySms: boolean;
  dateOfBirth: Date;
  contactByWhatsapp: boolean;
  userProfiles?: { profileId: string }[];
  userPermissions?: { permissionId: string }[];
  client?: CreateClientViewModel;
  shopkeeper?: CreateShopkeeperViewModel;
  deliveryman?: ICreateDeliverymanViewModel;
  userAddress?: ICreateAddressViewModel;
  authChallengeData?: {
    ChallengeName: ChallengeNameType;
    ChallengeParameters: { [key: string]: string };
    ChallengeResponses?: { [key: string]: string };
    Session: string;
  };
}
