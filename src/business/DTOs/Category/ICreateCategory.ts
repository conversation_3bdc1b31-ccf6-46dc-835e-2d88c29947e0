import { ICategorySubcategory } from "src/business/Interfaces/Prisma/ICategorySubcategory";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IProductCategory } from "src/business/Interfaces/Prisma/IProductCategory";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";

export interface ICreateCategory {
  // id: string;
  name: string;
  description: string;
  createdAt?: Date;
  updatedAt?: Date;
  categorySubcategory?: ICategorySubcategory[];
  productCategories?: IProductCategory[];
  storeCategory?: IStoreCategory[];
  icon?: IFile | null;
}