export enum ETransactionNotificationStatus {
  AWAITING_PAYMENT = 1,
  IN_ANALYSIS = 2,
  PAID = 3,
  AVAILABLE = 4,
  IN_DISPUTE = 5,
  REFUNDED = 6,
  CANCELED = 7,
  DEBITED = 8,
  TEMPORARILY_WITHHELD = 9,
}

// https://dev.pagbank.uol.com.br/v1/docs/api-notificacao-v1#a-namestatus-da-transacaoastatus-da-transa%C3%A7%C3%A3o
// 1 - Aguardando pagamento
// 2 - Em análise
// 3 - Paga
// 4 - Disponível
// 5 - Em disputa
// 6 - Devolvida
// 7 - Cancelada
// 8 - Debitado
// 9 - Retenção temporária
