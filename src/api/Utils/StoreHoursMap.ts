import { EDayOfWeek } from "@prisma/client";
import { format, parse } from "date-fns";
import { IStoreHoursViewModel } from "src/api/ViewModels/StoreHours/IViewModel";
import { IStoreHours } from "src/business/Interfaces/Prisma/IStoreHours";

const storeHoursViewModelToStoreHours = (storeHoursViewModel: IStoreHoursViewModel) => {
  const storeHours: IStoreHours[] = [];
  storeHoursViewModel.days.forEach((day) => {
    day.workingHours.forEach((wh) => {
      storeHours.push({
        id: undefined,
        storeId: storeHoursViewModel.storeId || undefined,
        store: undefined,
        dayOfWeek: day.name,
        open: parse(wh.open.toString(), "HH:mm", new Date()),
        close:  parse(wh.close.toString(), "HH:mm", new Date()),
      });
    });
  });
  return storeHours;
};

const storeHoursToStoreHoursViewModel = (storeHours: IStoreHours[]) => {
  const listWorkingHours: IStoreHoursViewModel = {
    days: [],
    storeId: undefined,
  };

  for (let i = 0; i <= 6; i += 1) {
    listWorkingHours.days.push({
      name: Object.values(EDayOfWeek)[i],
      workingHours: [],
    });
  }

  storeHours.forEach((item) => {
    listWorkingHours.storeId = item.storeId;
    const dayEnabled = listWorkingHours.days.find((day) => day.name === item.dayOfWeek);

    if (dayEnabled) {
      listWorkingHours.days.forEach((day) => {
        if (day.name === dayEnabled.name) {
          day.workingHours.push({
            id: day.workingHours.length.toString(),
            open: format(item.open, "HH:mm"),
            close: format(item.close, "HH:mm"),
          });
        }
      });
    }
  });

  // listWorkingHours.days.sort((a, b) => a.name.localeCompare(b.name));

  return listWorkingHours;
};

export { storeHoursToStoreHoursViewModel, storeHoursViewModelToStoreHours };
