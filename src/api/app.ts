/* eslint-disable import/no-extraneous-dependencies */
import "reflect-metadata";
import "express-async-errors";

import "src/api/@types/fetch-polyfill";

import "src/api/Controllers/User";
import "src/api/Controllers/Address";
import "src/api/Controllers/Attribute";
import "src/api/Controllers/Store";
import "src/api/Controllers/Device";
import "src/api/Controllers/AttributeOption";
import "src/api/Controllers/Category";
import "src/api/Controllers/Deliveryman";
import "src/api/Controllers/File";
import "src/api/Controllers/Order";
import "src/api/Controllers/OrderStatus";
import "src/api/Controllers/Product";
import "src/api/Controllers/Review";
import "src/api/Controllers/Shopkeeper";
import "src/api/Controllers/StoreHours";
import "src/api/Controllers/StoreSettings";
import "src/api/Controllers/Subcategory";
import "src/api/Controllers/Token";
import "src/api/Controllers/Mailing";
import "src/api/Controllers/ResultMailing";
import "src/api/Controllers/IntegrationHub";
import "src/api/Controllers/Card";
import "src/api/Controllers/Transactions";
import "src/api/Controllers/Utils";
import "src/api/Controllers/CustomerProfile";
import "src/api/Controllers/Message";
import "src/api/Controllers/TrackingOrder";
import "src/api/Controllers/LoginSession";
import "src/api/Controllers/Settings";
import "src/api/Controllers/Log";
import "src/api/Controllers/ContentManagement";
import "src/api/Controllers/StoreUser";
import "src/api/Controllers/Bordero";
import "src/api/Controllers/Payout";
import "src/api/Controllers/Cooperative";

import server from "src/api/Server";
import loadProfiles from "src/api/Server/Config/LoadProfiles";
import loadOrderStatusType from "src/api/Server/Config/LoadOrderStatusType";
import validateResourcesFiles from "src/business/Utils/Resources/ValidateResourcesFiles";
import socketServer from "src/api/Server/Socket";
import createDefaultCategories from "src/api/Server/Config/LoadCategory";
import loadUserDefault from "src/api/Server/Config/CreateUserDefault";
import createPermanentlyDeletedUser from "src/api/Server/Config/CreatePermanentlyDeletedUser";
import deleteUsersAfterDateLimit from "src/api/Server/Config/DeleteUsersAfterDateLimit";
import deleteUntiedFiles from "src/api/Server/Config/DeleteUntiedFiles";
import deleteUnusedDBConnections from "src/api/Server/Config/DeleteUnusedDBConnections";
import handleCreateProfileView from "src/api/Server/Config/handleCreateProfileView";
import startFinancialConsolidation from "src/api/Server/Config/StartFinancialConsolidation";
import loadSettings from "./Server/Config/LoadSettings";

const init = async () => {
  await deleteUnusedDBConnections();
  await handleCreateProfileView();
  await loadProfiles();
  await loadOrderStatusType();
  await loadUserDefault();
  await createDefaultCategories();
  await createPermanentlyDeletedUser();
  loadSettings();

  validateResourcesFiles();

  const app = server.build();

  const serverHttp = app.listen(4000, () => console.log("Server is running on port 4000"));

  socketServer(serverHttp);
  deleteUntiedFiles();
  deleteUsersAfterDateLimit();
  startFinancialConsolidation();
};

init();
