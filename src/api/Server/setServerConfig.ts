import { InversifyExpressServer } from "inversify-express-utils";
import applyCookieParser from "src/api/Server/Middlewares/CookieParser";
import applyExpressMiddlewares from "src/api/Server/Middlewares/Express";
import applyInitPassportMiddleware from "src/api/Server/Middlewares/InitPassport";
import applyMorganMiddleware from "src/api/Server/Middlewares/Morgan";
import registerSwaggerMiddleware from "src/api/Server/Middlewares/RegisterSwagger";
import callAllWith from "src/business/Utils/callAllWith";

const setServerConfig = (server: InversifyExpressServer) => {
  server.setConfig((app) => {
    callAllWith(
      [
        applyExpressMiddlewares,
        applyMorganMiddleware,
        applyInitPassportMiddleware,
        registerSwaggerMiddleware,
        // setHealthChecks,
        applyCookieParser,
      ],
      app,
    );
  });
};

export default setServerConfig;
