import { IncomingMessage, Server, ServerResponse } from "http";
import socketio from "socket.io";
import chatListener from "src/api/Server/Socket/Listeners/ChatListener";
import ImportProductsListener from "src/api/Server/Socket/Listeners/ImportProductsListener";
import ImportStoresListener from "src/api/Server/Socket/Listeners/ImportStoresListener";
import socketCognitoAccessTokenAuthenticate from "src/business/Middlewares/Cognito/SocketAccessTokenAuthenticate";
import transactionListener from "src/api/Server/Socket/Listeners/TransactionListener";

let io: socketio.Server;

const socketServer = (server: Server<typeof IncomingMessage, typeof ServerResponse>) => {
  io = new socketio.Server(server, {
    cors: {
      origin: process.env.CORS_ORIGIN,
      methods: ["GET", "POST"],
      allowedHeaders: ["Origin, X-Requested-With", "Content-Type", "Accept"],
      credentials: true,
    },
    path: process.env.SOCKET_PATH,
  });

  io.use(socketCognitoAccessTokenAuthenticate);

  io.on("connection", (socket) => {
    chatListener(socket, io);
    ImportProductsListener(socket);
    ImportStoresListener(socket);
    transactionListener(socket, io);
  });
  return io;
};

export { io };

export default socketServer;
