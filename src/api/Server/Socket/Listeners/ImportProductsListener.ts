import { File } from "buffer";
import socketio from "socket.io";
import { DefaultEventsMap } from "socket.io/dist/typed-events";
import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IProductService } from "src/business/Interfaces/Service/IProduct";

const importProductsListener = (socket: socketio.Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>) => {
  socket.on("import_products", (storeId: string) => {
    console.log("storeId: ", storeId);
    socket.on("import_products_file", async (file: File) => {
      const productService = container.get<IProductService>(TOKENS.IProductService);
      const result = await productService.importByStore(storeId, file);
      socket.emit("import_products_result", result);
      socket.emit("close", "close");
      socket.disconnect();
    });
  });
};

export default importProductsListener;
