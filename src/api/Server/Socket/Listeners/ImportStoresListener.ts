import { File } from "buffer";
import socketio from "socket.io";
import { DefaultEventsMap } from "socket.io/dist/typed-events";
import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IStoreService } from "src/business/Interfaces/Service/IStore";

const ImportStoresListener = (socket: socketio.Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>) => {
  socket.on("import_stores_file", async (file: File) => {
    const storeService = container.get<IStoreService>(TOKENS.IStoreService);
    const result = await storeService.importStores(file);
    socket.emit("import_stores_result", result);
    socket.emit("close", "close");
    socket.disconnect();
  });
};

export default ImportStoresListener;