import socketio from "socket.io";
import { DefaultEventsMap } from "socket.io/dist/typed-events";
import { IListChatMessagesViewModel } from "src/api/ViewModels/ChatMessages/IList";
import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { EChatEvents } from "src/business/Enums/Socket/EChatEvents";
import { IChatService } from "src/business/Interfaces/Service/IChat";
import { IChatMessagesService } from "src/business/Interfaces/Service/IChatMessages";
import ProfilesSingleton from "src/business/Singletons/Profile";

const chatListener = (socket: socketio.Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>, io: any) => {
  const chatService = container.get<IChatService>(TOKENS.IChatService);
  const chatMessagesService = container.get<IChatMessagesService>(TOKENS.IChatMessagesService);
  const profilesSingleton = container.get<ProfilesSingleton>(TOKENS.ProfilesSingleton);

  socket.on(EChatEvents.enter_chat, async (orderId: string, userId: string) => {
    socket.join(orderId);
    socket.data.userId = userId;
    const chat = await chatService.getByOrderId(orderId);

    if (!chat) {
      await chatService.create({ orderId });
    }

    console.log("User joined Chat");
  });

  socket.on(EChatEvents.send_message, async (data: IListChatMessagesViewModel, orderId: string) => {
    const chat = await chatService.getByOrderId(orderId);

    if (chat && chat.id) {
      const chatMessage = {
        chatId: chat.id,
        message: data.message,
        profileId: profilesSingleton[data.user.profile].id,
        userId: data.user.id,
      };

      const result = await chatMessagesService.create(chatMessage);

      if (result && result.createdAt) {
        data.createdAt = result.createdAt;
        socket.to(orderId).emit(EChatEvents.get_messages, data);
      }
    }

    const connectedSockets = await io.in(orderId).fetchSockets();
    await chatService.sendChatNotifications(
      orderId,
      connectedSockets.map((item: { data: { userId: string } }) => item.data.userId),
    );

    console.log("Message sent to the group");
  });

  socket.on(EChatEvents.typing_message, (orderId: string, profile: EProfile, event: string) => {
    socket.to(orderId).emit(event, { profile });
  });
};

export default chatListener;
