import socketio from "socket.io";
import { DefaultEventsMap } from "socket.io/dist/typed-events";
import { ETransactionEvents } from "src/business/Enums/Socket/ETransactionEvents";

const transactionListener = (socket: socketio.Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>, io: any) => {

  socket.on(ETransactionEvents.init, async (orderId: string, userId: string) => {
    socket.join(orderId);
    socket.data.userId = userId;

    console.log("User joined transaction payment. Order id:", orderId);
  });

};

export default transactionListener;
