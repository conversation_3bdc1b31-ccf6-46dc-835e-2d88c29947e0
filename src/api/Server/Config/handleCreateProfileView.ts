import { createCustomerProfileView } from "prisma/views/CustomerProfile";
import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";

const handleCreateProfileView = async () => {
  const databaseClient = container.get<IExtendedPrismaClient>(TOKENS.ExtendedPrismaClient).client;

  await databaseClient.$executeRaw(createCustomerProfileView).then(() => console.log("Customer profile view created"));
};

export default handleCreateProfileView;
