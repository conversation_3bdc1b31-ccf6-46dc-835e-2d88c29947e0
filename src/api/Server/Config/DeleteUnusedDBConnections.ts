import { Prisma } from "@prisma/client";
import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IExtendedPrismaClient } from "src/business/Interfaces/Database/IExtended";

const deleteUnusedDBConnections = async () => {
  const databaseClient = container.get<IExtendedPrismaClient>(TOKENS.ExtendedPrismaClient).client;

  const result = await databaseClient.$queryRaw<{ pg_terminate_backend: boolean }[]>(
    Prisma.raw(
      `
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE pid <> pg_backend_pid()
        AND datname = '${process.env.DATABASE_NAME}'
        AND usename = '${process.env.DATABASE_USERNAME}'
        AND now() - state_change > INTERVAL '1 hour';

      `,
    ),
  );

  console.log("Connections cleared: ", result.length);
};

export default deleteUnusedDBConnections;
