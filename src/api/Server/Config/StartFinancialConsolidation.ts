import container from "src/business/Configs/Inversify/Container";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICronService } from "src/business/Interfaces/Service/Cron/ICronService";
import { IOrderService } from "src/business/Interfaces/Service/IOrder";

const startFinancialConsolidation = async () => {
  container.get<ICronService>(TOKENS.ICronService).execute({ hour: 1, minute: 0, tz: "America/Sao_Paulo" }, () => {
    const orderService = container.get<IOrderService>(TOKENS.IOrderService);
    orderService.createFinancialConsolidation();
  });
};

export default startFinancialConsolidation;
