import { InversifyExpressServer } from "inversify-express-utils";
import applyCookieParser from "src/api/Server/Middlewares/CookieParser";
import applyErrorHandlerMiddleware from "src/api/Server/Middlewares/ErrorHandler";
import applyExpressMiddlewares from "src/api/Server/Middlewares/Express";
import applyInitPassportMiddleware from "src/api/Server/Middlewares/InitPassport";
import applyMorganMiddleware from "src/api/Server/Middlewares/Morgan";
import registerSwaggerMiddleware from "src/api/Server/Middlewares/RegisterSwagger";
import setServerConfig from "src/api/Server/setServerConfig";
import setServerErrorConfig from "src/api/Server/setServerErrorConfig";
import container from "src/business/Configs/Inversify/Container";

const server: InversifyExpressServer = new InversifyExpressServer(container, null, {
  rootPath: process.env.ROOT_PATH || "/",
});

setServerConfig(server);
setServerErrorConfig(server);

export default server;
