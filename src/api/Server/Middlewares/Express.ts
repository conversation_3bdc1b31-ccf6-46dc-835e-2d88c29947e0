import express from "express";
import cors from "cors";

const applyExpressMiddlewares = (app: express.Application) => {
  app.use(
    cors({
      origin: [process.env.CORS_ORIGIN!, process.env.PAGSEGURO_CORS_URL!],
      allowedHeaders: ["Origin", "X-Requested-With", "Content-Type", "Accept"],
      credentials: true,
    }),
  );
  app.use(
    express.urlencoded({
      extended: true,
    }),
  );
  app.use(express.json());
};

export default applyExpressMiddlewares;
