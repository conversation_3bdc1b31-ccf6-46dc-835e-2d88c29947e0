import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import swaggerUi from 'swagger-ui-express';
import * as swagger from 'src/business/Configs/Swagger/swagger.json';

const registerSwaggerMiddleware = (app: express.Application) => {
	app.use(
		`${process.env.ROOT_PATH}/utils/docs`,
		swaggerUi.serve as unknown as RequestHandler,
		swaggerUi.setup(swagger) as unknown as RequestHandler
	);
};

export default registerSwaggerMiddleware;
