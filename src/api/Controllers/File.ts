/* eslint-disable @typescript-eslint/no-unused-vars */
import { EFile, EFileType } from "@prisma/client";
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import multer from "multer";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateFileViewModel } from "src/api/ViewModels/File/ICreateFile";
import { IListFileViewModel } from "src/api/ViewModels/File/IList";
import { IResultCreateFile } from "src/api/ViewModels/File/IResultCreateFile";
import { IUpdateFileViewModel } from "src/api/ViewModels/File/IUpdateFile";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { FilesMapper } from "src/business/Configs/Automapper/Profile/File";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IFile } from "src/business/Interfaces/Prisma/IFile";
import { IFileService } from "src/business/Interfaces/Service/IFile";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { uploadFileConfig } from "src/business/Middlewares/Attachment/UploadFileConfig";
import { Body, Delete, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/file")
@Route("file")
@Tags("File")
export class FileController extends BaseController {
  constructor(
    @inject(TOKENS.IFileService) private fileService: IFileService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/by-entity")
  @Get("/by-entity")
  public async getFileByEntityId(@Request() req: express.Request, @Query() entity: EFile, @Query() entityId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const entityQueryValue: EFile = req.query.entity as any;
    const entityIdQueryValue = req.query.entityId;

    let files: IFile[] = [];

    if (entityQueryValue && entityIdQueryValue) {
      files = await this.fileService.getFilesByEntityId(entityIdQueryValue.toString(), entityQueryValue);
    }

    const response = mapper.map(FilesMapper.IFileToIListFileViewModel, files);

    return this.customResponse<IListFileViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/by-entity-usage")
  @Get("/by-entity-usage")
  public async getFileByEntityAndUsage(
    @Request() req: express.Request,
    @Query() entity: EFile,
    @Query() entityId: string,
    @Query() type: EFileType,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const entityQ: EFile = req.query.entity as any;
    const entityIdQ = req.query.entityId;
    const typeQ = req.query.type;
    let files: IFile[] = [];

    if (entityQ && entityIdQ && typeQ) {
      files = await this.fileService.getFilesByEntityAndType(entityIdQ.toString(), entityQ, typeQ as EFileType);
    }

    const response = mapper.map(FilesMapper.IFileToIListFileViewModel, files);

    return this.customResponse<IListFileViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("/{fileId}")
  public async getFileById(@Request() req: express.Request, @Path() fileId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const file = await this.fileService.getById(id);

    const response = mapper.map(FilesMapper.IFileToIListFileViewModel, file);

    return this.customResponse<IListFileViewModel>(response);
  }

  @Security("api_key")
  @httpPost("/", multer(uploadFileConfig.getConfig()).single("file"))
  @Post("/")
  public async create(@Request() req: express.Request, @Body() file: ICreateFileViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const fileData: ICreateFileViewModel = JSON.parse(req.body.data);

    let response: IResultCreateFile | null = null;

    if (req.file) {
      response = await this.fileService.createFile(fileData, req.file);
    }

    return this.customResponse<IResultCreateFile>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() file: IUpdateFileViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;

    const input = mapper.map(FilesMapper.IUpdateFileViewModelToIFile, data);

    const response = await this.fileService.update(input);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{fileId}")
  public async delete(@Request() req: express.Request, @Path() fileId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result = await this.fileService.delete(id);

    return this.customResponse<boolean>(result);
  }
}
