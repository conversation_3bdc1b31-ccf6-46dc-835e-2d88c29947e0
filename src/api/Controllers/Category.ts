/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICategoryDetailsViewModel } from "src/api/ViewModels/Category/ICategoryDetails";
import { ICategorySubcategoryViewModel } from "src/api/ViewModels/Category/ICategorySubcategory";
import { ICategorySubcategorySelectedViewModel } from "src/api/ViewModels/Category/ICategorySubcategorySelected";
import { ICategoryWithIconViewModel } from "src/api/ViewModels/Category/ICategoryWithIcon";
import { ICreateCategoryViewModel } from "src/api/ViewModels/Category/ICreate";
import { IUpdateCategoryViewModel } from "src/api/ViewModels/Category/IUpdate";
import { CategorySubcategoryViewModel } from "src/api/ViewModels/CategorySubcategory.vm";
import { FilteredCategoryViewModel } from "src/api/ViewModels/FilteredCategory.vm";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { CategoryMapper } from "src/business/Configs/Automapper/Profile/Category";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ICategoryService } from "src/business/Interfaces/Service/ICategory";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/category")
@Route("category")
@Tags("Category")
export class CategoryController extends BaseController {
  constructor(
    @inject(TOKENS.ICategoryService) private categoryService: ICategoryService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/all")
  @Get("/all")
  public async getAllCategoriesWithSubcategories() {
    const categories = await this.categoryService.getAllCategoriesWithSubCategory();

    const response = mapper.map(CategoryMapper.ICategoryToICategorySubcategoryViewModel, categories);

    return this.customResponse<ICategorySubcategoryViewModel[]>(response);
  }

  // TODO método não utilizado
  // @Security("api_key")
  // @httpGet("/paged/")
  // @Get("/paged/")
  // public async getAllFilteredPaginated(
  //   @Request() req: express.Request,
  //   @Query() filter: string,
  //   @Query() page: number,
  //   @Query() pageSize: number
  // ) {
  //   const queryData = req.query as unknown as {
  //     page: number;
  //     pageSize: number;
  //     filter: string;
  //   };
  // FIXME
  @Security("api_key")
  @httpGet("/paged/")
  @Get("/paged/")
  public async getAllFilteredPaginated(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
    @Query() filterValue?: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      filterValue: string;
      orderBy: string;
      sortDirection: IOrder;
    };

    const { result, totalCount, totalPages } = await this.categoryService.getPagedWithSubcategory(
      queryData.currentPage,
      queryData.pageSize,
      queryData.filterValue,
      queryData.orderBy,
      queryData.sortDirection,
    );

    let responseMapped: PagedResult<ICategorySubcategoryViewModel> | null = null;

    if (result) {
      responseMapped = {
        result: mapper.map(CategoryMapper.ICategoryToICategorySubcategoryViewModel, result),
        totalCount,
        totalPages,
      };
    }

    return this.customResponse<{
      result: CategorySubcategoryViewModel[];
      totalCount: number;
      totalPages: number;
    }>(responseMapped);
  }

  @Security("api_key")
  @httpGet("/paged-with-icon/")
  @Get("/paged-with-icon/")
  public async getPagedWithIcon(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
    };

    const { result, totalCount, totalPages } = await this.categoryService.getPagedWithIcon(
      queryData.currentPage,
      queryData.pageSize,
    );

    let responseMapped: PagedResult<ICategoryWithIconViewModel> | null = null;

    if (result) {
      responseMapped = {
        result: mapper.map(CategoryMapper.ICategoryToICategoryWithIconViewModel, result),
        totalCount,
        totalPages,
      };
    }

    return this.customResponse<{
      result: ICategoryWithIconViewModel[];
      totalCount: number;
      totalPages: number;
    }>(responseMapped);
  }

  @Security("api_key")
  @httpGet("/paged-with-store-options/")
  @Get("/paged-with-store-options/")
  public async getPagedWithStoreOptions(
    @Request() req: express.Request,
    @Query() store: string,
    @Query() currentPage: number,
    @Query() pageSize: number,
    @Query() filter: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      storeId: string;
      currentPage: number;
      pageSize: number;
      filter: string;
    };

    const category = (await this.categoryService.getPagedWithStoreOptions(
      queryData.storeId,
      queryData.currentPage,
      queryData.pageSize,
      queryData.filter,
    )) as any;

    return this.customResponse<FilteredCategoryViewModel>(category);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() category: ICreateCategoryViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const categoryData: ICreateCategoryViewModel = req.body;
    const { attachments } = categoryData;

    const input = mapper.map(CategoryMapper.ICreateCategoryViewModelToICategory, categoryData);

    const response = await this.categoryService.create(input, attachments);

    return this.customResponse<boolean>(response);
  }

  // @Security("api_key")
  // @httpPost("/")
  // @Post("/")
  // public async createSeveralCategories(
  //   @Request() req: express.Request,
  //   @Body() category: ICreateCategoryViewModel
  // ) {
  //   const categoryQ = req.body;

  //   const categoryMapped = mapper.map(
  //     CategoryMapper.CreateCategoryViewModelArrayToICategoryArray,
  //     categoryQ
  //   );

  //   // TODO Implementar createMany
  //   const categoryCreated = await this.categoryService.createSeveralCategories(
  //     categoryMapped
  //   );

  //   return this.customResponse<ICategory[]>(categoryCreated);
  // }

  @Security("api_key")
  @httpGet("/with-subcategory-by-store")
  @Get("with-subcategory-by-store")
  public async getWithSubcategoryByStore(@Request() req: express.Request, @Query() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      storeId: string;
    };
    const category = await this.categoryService.getWithSubcategoryByStore(queryData.storeId);

    const response = mapper.map(CategoryMapper.ICategoryToICategorySubcategoryViewModel, category);

    return this.customResponse<ICategorySubcategoryViewModel[]>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("/{categoryId}")
  public async delete(@Request() req: express.Request, @Path() categoryId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result = await this.categoryService.deleteWithRelations(id);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() category: IUpdateCategoryViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: IUpdateCategoryViewModel = req.body;
    const subcategoryIds = data.subcategory;

    const input = mapper.map(CategoryMapper.IUpdateCategoryViewModelToICategory, data);

    const result = await this.categoryService.updateCategoryAndSubcategoryRelation(input, subcategoryIds);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/by-product-with-subcategory/")
  @Get("by-product-with-subcategory/")
  public async getCategoryByProductWithSubcategory(
    @Request() req: express.Request,
    @Query() storeId: string,
    @Query() productId?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      storeId: string;
      productId?: string;
    };
    const response = await this.categoryService.getCategoryByProductWithSubcategory(
      queryData.storeId,
      queryData.productId,
    );

    // TODO Create mapper

    return this.customResponse<ICategorySubcategorySelectedViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/select-front/")
  @Get("/select-front/")
  public async getStoresSelect(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      categoryName: string;
      currentPage: string;
      pageSize: string;
    };

    const categories = await this.categoryService.getSelect(
      queryData.categoryName,
      queryData.currentPage,
      queryData.pageSize,
    );

    return this.customResponse<PagedResult<ISelectDTO>>(categories);
  }

  @Security("api_key")
  @httpGet("/details/:id")
  @Get("/details/{id}")
  public async getDetails(@Request() req: express.Request, @Query() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const category = await this.categoryService.getCategoryDetails(req.params.id);

    const response = mapper.map(CategoryMapper.ICategoryToICategoryDetailsViewModel, category);

    return this.customResponse<ICategoryDetailsViewModel>(response);
  }
}
