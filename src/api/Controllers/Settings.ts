/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ISettingsViewModel } from "src/api/ViewModels/Settings/ISettings";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { SettingsMapper } from "src/business/Configs/Automapper/Profile/Settings";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ISettingsService } from "src/business/Interfaces/Service/ISettings";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Put, Request, Route, Security, Tags } from "tsoa";

@controller("/settings")
@Route("settings")
@Tags("Settings")
export class SettingsController extends BaseController {
  constructor(
    @inject(TOKENS.ISettingsService) private settingsService: ISettingsService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @httpGet("/")
  @Get("/")
  public async getAllSettings() {
    const result = await this.settingsService.getFormatted();
    const settingsMapped = mapper.map(SettingsMapper.ISettingsToISettingViewModel, result);

    return this.customResponse<ISettingsViewModel[]>(settingsMapped);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() settings: ISettingsViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const newSettings: ISettingsViewModel = req.body;

    const result = await this.settingsService.updateByUserId(this.userContext.userId, newSettings);
    return this.customResponse<boolean>(result);
  }
}
