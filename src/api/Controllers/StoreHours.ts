/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { IStoreHoursViewModel } from "src/api/ViewModels/StoreHours/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { StoreHoursMapper } from "src/business/Configs/Automapper/Profile/StoreHours";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IStoreHoursService } from "src/business/Interfaces/Service/IStoreHours";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Post, Put, Request, Route, Security, Tags } from "tsoa";

@controller("/store-hours")
@Route("store-hours")
@Tags("StoreHours")
export class StoreHoursController extends BaseController {
  constructor(
    @inject(TOKENS.IStoreHoursService)
    private storeHoursService: IStoreHoursService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() store: IStoreHoursViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: IStoreHoursViewModel = req.body;

    // TODO - Notificar app
    if (!data.storeId) return this.customResponse<number>(0);

    const input = mapper.map(StoreHoursMapper.IStoreHoursViewModelToIStoreHoursList, data);

    const response = await this.storeHoursService.create(data.storeId, input.list);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() store: IStoreHoursViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: IStoreHoursViewModel = req.body;

    // TODO - Notificar app
    if (!data.storeId) return this.customResponse<number>(0);

    const input = mapper.map(StoreHoursMapper.IStoreHoursViewModelToIStoreHoursList, data);

    const response = await this.storeHoursService.updateByStore(data.storeId, input.list);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{storeId}")
  public async delete(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.storeHoursService.deleteByStoreId(id);

    return this.customResponse<number>(response);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{storeId}")
  public async getStoreHoursByStoreId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const storeHours = await this.storeHoursService.getStoreHoursByStoreId(id);

    const storeHoursViewModel = mapper.map(StoreHoursMapper.IStoreHoursToIStoreHoursViewModel, storeHours);

    return this.customResponse<IStoreHoursViewModel>(storeHoursViewModel);
  }
}
