/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpPost } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateAttributeOptionViewModel } from "src/api/ViewModels/Attribute/ICreateAttributeOption";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAttributeOptionService } from "src/business/Interfaces/Service/IAttributeOption";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Post, Request, Route, Security, Tags } from "tsoa";

@controller("/attribute-option")
@Route("attribute-option")
@Tags("AttributeOption")
export class AttributeOptionController extends BaseController {
  constructor(
    @inject(TOKENS.IAttributeOptionService)
    private attributeOptionService: IAttributeOptionService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  // FIXME controller não utilizado
  // @Security("api_key")
  // @httpGet("/")
  // @Get("/")
  // public async getAll() {
  //   const attributesOptions: AttributeOptionListViewModel[] =
  //     await this.attributeOptionService.getAll();

  //   return this.customResponse<AttributeOptionListViewModel[]>(
  //     attributesOptions
  //   );
  // }

  // TODO Corrigir mapeamento para a inserção de listas de attributeOptions
  // FIXME controller não utilizado
  // @Security("api_key")
  // @httpPost("/queue")
  // @Post("/queue")
  // public async createQueue(
  //   @Request() req: express.Request,
  //   @Body() attributeOption: CreateAttributeOptionViewModel
  // ) {
  //   const optionQ = req.body;

  //   const optionMapped = mapper.mapArray(
  //     optionQ,
  //     CreateAttributeOptionViewModel,
  //     AttributeOption
  //   );
  //   const optionCreated = await this.attributeOptionService.createQueue(
  //     optionMapped
  //   );

  //   return this.customResponse<AttributeOption[]>(optionCreated);
  // }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() attributeOption: ICreateAttributeOptionViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;

    const response = await this.attributeOptionService.create(data);

    return this.customResponse<boolean>(response);
  }
}
