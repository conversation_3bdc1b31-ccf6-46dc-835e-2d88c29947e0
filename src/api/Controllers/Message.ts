/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { IListNotificationMessagesViewModel } from "src/api/ViewModels/Message/IListNotification";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { MessageMapper } from "src/business/Configs/Automapper/Profile/Message";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IMessageService } from "src/business/Interfaces/Service/IMessage";
import { IMessageContentService } from "src/business/Interfaces/Service/IMessageContent";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Get, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/message")
@Route("message")
@Tags("Message")
export class MessageController extends BaseController {
  constructor(
    @inject(TOKENS.IMessageService) private messageService: IMessageService,
    @inject(TOKENS.IMessageContentService)
    private messageContentService: IMessageContentService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  // NOTE Method is not used
  // @Security("api_key")
  // @httpGet("user/:id")
  // @Get("user/{messageId}")
  // public async getUserMessages(
  //   @Request() req: express.Request,
  //   @Path() messageId: string,
  //   @Query() filterType: "received" | "sended" | "all",
  //   @Query() sendingType: EMessageSendingType,
  // ) {
  //   const { id } = req.params;

  //   const sendingTypeQ = req.query.sendingType as EMessageSendingType;
  //   const filterTypeQ = req.query.filterType as "received" | "sended" | "all";

  //   const messages = await this.messageService.getUserMessages(id, filterTypeQ, sendingTypeQ);

  //   return this.customResponse<MessageListViewModel[]>(messages);
  // }

  // @Security("api_key")
  // @httpPost(
  //   "/",
  //   cognitoAccessTokenAuthenticate,
  //   multer(uploadFileConfig.getConfig(["png", "jpg", "jpeg", "pdf", "doc", "docx"])).array("file"),
  // )
  // @Post("/")
  // public async create(@Request() req: express.Request, @Body() message: ICreateMessageViewModel) {
  //   const data: ICreateMessageViewModel = JSON.parse(req.body.data);

  //   const input = mapper.map(MessageMapper.ICreateMessageViewModelToIMessage, data);

  //   const { files } = req;

  //   const response = await this.messageService.create(input, files as IFile[]);

  //   return this.customResponse<boolean>(response);
  // }

  // TODO Test
  @Security("api_key")
  @httpGet("/notifications")
  @Get("/notifications")
  public async getUserNotificationMessages(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: string;
      pageSize: string;
      profile: EProfile;
      filterByStatus?: string;
      dateToFilter?: string;
      orderBy?: "asc" | "desc";
      origin?: "web";
    };

    const { result, totalCount, totalPages } = await this.messageService.getUserNotificationMessages(
      this.userContext.userId,
      queryData.profile,
      queryData.page,
      queryData.pageSize,
      queryData.filterByStatus,
      queryData.dateToFilter,
      queryData.orderBy,
      queryData.origin,
    );

    const resultMapped = mapper.map(MessageMapper.IListNotificationMessageToIListNotificationMessagesViewModel, result);

    return this.customResponse<{
      result: IListNotificationMessagesViewModel[];
      totalCount: number;
      totalPages: number;
    }>({
      result: resultMapped,
      totalCount,
      totalPages,
    });
  }

  // TODO Test
  @Security("api_key")
  @httpPut("/notification-status/:id")
  @Put("/notification-status/{id}")
  public async updateNotificationMessageStatus(@Request() req: express.Request, @Query() profile: EProfile) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const queryData = req.query;

    const response = await this.messageService.updateNotificationMessageStatus(id, queryData.profile as EProfile);

    return this.customResponse<number>(response);
  }

  @Security("api_key")
  @httpPost("/multiple-notification-status/")
  @Post("/multiple-notification-status/")
  public async updateMultipleNotificationMessageStatus(@Request() req: express.Request, @Query() profile: EProfile) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const messageIds: string[] = req.body;
    const queryData = req.query;

    const response = await this.messageService.updateMultipleNotificationMessagesStatus(
      messageIds,
      queryData.profile as EProfile,
    );

    return this.customResponse<number>(response);
  }
}
