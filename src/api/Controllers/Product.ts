/* eslint-disable @typescript-eslint/no-unused-vars */
import { Xlsx } from "exceljs";
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { AttributesAndOptionsIdsViewModel } from "src/api/ViewModels/Attribute/IAttributesAndOptionsIds";
import { ICreateProductViewModel } from "src/api/ViewModels/Product/ICreate";
import { IProductsFilterDTO } from "src/api/ViewModels/Product/IFilterProducts";
import { IProductByStoreViewModel } from "src/api/ViewModels/Product/IProductByStore";
import { IUpdateProductViewModel } from "src/api/ViewModels/Product/IUpdate";
import { IUpdateCategorySubcategoryViewModel } from "src/api/ViewModels/Product/IUpdateCategorySubcategory";
import { IProductViewModel } from "src/api/ViewModels/Product/IViewModel";
import { ICreateProductModerationViewModel } from "src/api/ViewModels/ProductModeration/ICreate";
import { IStoreModerationViewModel } from "src/api/ViewModels/StoreModeration/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { ProductMapper } from "src/business/Configs/Automapper/Profile/Product";
import { ProductModerationMapper } from "src/business/Configs/Automapper/Profile/ProductModeration";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IProductService } from "src/business/Interfaces/Service/IProduct";
import { IProductModerationService } from "src/business/Interfaces/Service/IProductModerationService";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { handleProducts, IFormattedFavoriteProducts } from "src/business/Utils/formatUserFavoriteProducts";
import { Body, Delete, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/product")
@Route("product")
@Tags("Product")
export class ProductController extends BaseController {
  constructor(
    @inject(TOKENS.IProductService) private productService: IProductService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.IProductModerationService)
    private productModerationService: IProductModerationService,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/user-favorite-products")
  @Get("/user-favorite-products")
  public async getUserFavoriteProducts(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { userId } = this.userContext;

    const product = await this.productService.getUserFavoriteProducts(userId);

    const response = mapper.map(ProductMapper.IProductToIFavoriteProductViewModel, product);

    return this.customResponse<IFormattedFavoriteProducts[]>(handleProducts(response));
  }

  @Security("api_key")
  @httpGet("/with-category/:id")
  @Get("with-category/{productId}")
  public async getWithCategory(@Request() req: express.Request, @Path() productId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const data = await this.productService.getWithCategory(id);

    const response = mapper.map(ProductMapper.IProductToIProductViewModel, data);

    return this.customResponse<IProductViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/by-store/:id")
  @Get("by-store/{storeId}")
  public async getByStore(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const { userId } = this.userContext;

    const data = await this.productService.getByStore(id, userId);

    const response = mapper.map(ProductMapper.IProductByStoreWithFavoriteDTOToIProductViewModel, data);

    return this.customResponse<IProductByStoreViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/with-details/:id")
  @Get("with-details/{productId}")
  public async getWithAllDetails(@Request() req: express.Request, @Path() productId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const data = await this.productService.getWithAllDetails(id);

    const response = mapper.map(ProductMapper.IProductToIProductViewModel, data);

    return this.customResponse<IProductViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/paged")
  @Get("/paged")
  public async getPagedList(
    @Request() req: express.Request,
    @Query() page: number,
    @Query() pageSize: number,
    @Query() favoriteFilter: boolean,
    @Query() productFilter?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: number;
      pageSize: number;
      productFilter: string;
      favoriteFilter: string;
    };

    const { result, totalCount, totalPages } = await this.productService.getPagedProductList(
      queryData.productFilter,
      queryData.favoriteFilter === "true",
      Number(queryData.page),
      Number(queryData.pageSize),
    );

    const response: PagedResult<IProductViewModel> = {
      result: mapper.map(ProductMapper.IProductToIProductViewModel, result),
      totalCount,
      totalPages,
    };

    return this.customResponse<{
      result: IProductViewModel[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }

  @Security("api_key")
  @httpGet("/suggested")
  @Get("/suggested")
  public async getProductsSuggestions(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { userId } = this.userContext;

    const result = await this.productService.getProductsSuggestions(userId);

    const response = mapper.map(ProductMapper.IProductToIProductViewModel, result);

    return this.customResponse<IProductViewModel[]>(response);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() product: ICreateProductViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: ICreateProductViewModel = req.body;

    const input = mapper.map(ProductMapper.ICreateProductViewModelToIProduct, data);

    const response = await this.productService.create(input, data.attachments);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() product: IUpdateProductViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;

    const input = mapper.map(ProductMapper.IUpdateProductViewModelToIProduct, data);

    const response = await this.productService.updateWithRelations(input);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{productId}")
  public async delete(@Request() req: express.Request, @Path() productId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.productService.deleteWithRelations(id);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPost("/relate-attribute")
  @Post("/relate-attribute")
  public async relateAttribute(
    @Request() req: express.Request,
    @Body() data: { productId: string; attributeId: string },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.body as unknown as {
      productId: string;
      attributeId: string;
    };

    const result = await this.productService.relateProductAttribute(queryData.productId, queryData.attributeId);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpPost("/relate-attribute-option")
  @Post("/relate-attribute-option")
  public async relateAttributeOption(
    @Request() req: express.Request,
    @Body()
    data: { productId: string; attributeId: string; attributeOptionId: string },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.body as unknown as {
      productId: string;
      attributeId: string;
      attributeOptionId: string;
    };

    const result = await this.productService.relateProductAttributeOption(
      queryData.productId,
      queryData.attributeId,
      queryData.attributeOptionId,
    );

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/export/by-store/:id")
  @Get("/export/by-store/{storeId}")
  public async exportProdutsByStore(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const xlsx = await this.productService.exportByStore(id);

    this.httpContext.response.set({
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",

      // enabling CORS
      "Access-Control-Allow-Origin": process.env.CORS_ORIGIN,
      "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    });

    if (xlsx) {
      const xlsxBuffer = await xlsx.writeBuffer();
      const buff = Buffer.from(xlsxBuffer);
      const xlsx64 = buff.toString("base64");
      this.httpContext.response.write(`data: ${xlsx64}\n\n`);
      this.httpContext.response.send();
    }
    return this.customResponse<Xlsx>(xlsx);
  }
  // FIXME unused method in app
  // @Security("api_key")
  // @httpPut("/update-categories")
  // @Put("/update-categories")
  // public async updateCategories(
  //   @Request() req: express.Request,
  //   @Body() data: { productId: string; categoryIds: string[] },
  // ) {
  //   const { productId, categoryIds = [] } = req.body as unknown as {
  //     productId: string;
  //     categoryIds: string[];
  //   };

  //   let result = false;
  //   if (productId) {
  //     await this.productService.updateProductCategories(productId, categoryIds);
  //     result = true;
  //   }

  //   return this.customResponse<boolean>(result);
  // }
  // FIXME unused method in app
  // @Security("api_key")
  // @httpPut("/update-subcategories")
  // @Put("/update-subcategories")
  // public async updateSubCategories(
  //   @Request() req: express.Request,
  //   @Body() data: { productId: string; subCategoryIds: string[] },
  // ) {
  //   const { productId, subCategoryIds = [] } = req.body as unknown as {
  //     productId: string;
  //     subCategoryIds: string[];
  //   };

  //   let result = false;
  //   if (productId) {
  //     await this.productService.updateProductSubcategories(
  //       productId,
  //       subCategoryIds.map((item) => ({ productId, subcategoryId: item } as IProductSubcategory)),
  //     );
  //     result = true;
  //   }

  //   return this.customResponse<boolean>(result);
  // }

  @Security("api_key")
  @httpPut("/update-categories-subcategories")
  @Put("/update-categories-subcategories")
  public async updateCategoriesAndSubCategories(
    @Request() req: express.Request,
    @Body() data: IUpdateCategorySubcategoryViewModel,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const categoriesSubCategories: IUpdateCategorySubcategoryViewModel = req.body;

    const mapped = mapper.map(
      ProductMapper.IUpdateCategorySubCategoriesViewModelToIUpdateCategorySubCategoryDTO,
      categoriesSubCategories,
    );

    const result = await this.productService.updateCategoryAndSubcategory(mapped);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpPut("/update-attributes")
  @Put("/update-attributes")
  public async updateAttributes(
    @Request() req: express.Request,
    @Body()
    data: { productId: string; attributes: AttributesAndOptionsIdsViewModel[] },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { productId, attributes } = req.body as unknown as {
      productId: string;
      attributes: AttributesAndOptionsIdsViewModel[];
    };

    let result = false;
    if (productId) {
      result = await this.productService.updateProductAttributes(productId, attributes);
    }

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpPut("/update-active-status")
  @Put("/update-active-status")
  public async updateActiveStatus(
    @Request() req: express.Request,
    @Body() data: { productId: string; active: boolean },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { productId, active = false } = req.body as unknown as {
      productId: string;
      active: boolean;
    };

    let result = false;
    if (productId) {
      result = await this.productService.updateActiveStatus(productId, active);
    }

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/paged-front")
  @Get("/paged-front")
  public async getPagedListFront(
    @Request() req: express.Request,
    @Query() page: string,
    @Query() pageSize: string,
    @Query() storeId?: string,
    @Query() filterValue?: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
    @Query() categoryFilter?: string,
    @Query() storeFilter?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: string;
      pageSize: string;
      storeId: string;
      filterValue: string;
      orderBy: string;
      sortDirection: IOrder;
      categoryFilter: string;
      storeFilter: string;
    };

    const product = await this.productService.getPagedListFront(
      queryData.page,
      queryData.pageSize,
      queryData.storeId,
      queryData.filterValue,
      queryData.orderBy,
      queryData.sortDirection,
      queryData.categoryFilter,
      queryData.storeFilter,
    );

    const productDataMapped = mapper.map(ProductMapper.IProductToIProductViewModel, product.result);

    return this.customResponse<{
      result: IProductViewModel[];
      totalCount: number;
      totalPages: number;
    }>({
      result: productDataMapped,
      totalCount: product.totalCount,
      totalPages: product.totalPages,
    });
  }

  @Security("api_key")
  @httpGet("/search")
  @Get("/search")
  public async getProductSearch(
    @Request() req: express.Request,
    @Query() page: string,
    @Query() pageSize: string,
    @Query() filterValue?: string,
    @Query() latitude?: number,
    @Query() longitude?: number,
    @Query() locationFilter?: number,
    @Query() minPriceFilter?: string,
    @Query() maxPriceFilter?: string,
    @Query() category?: string[],
    @Query() subcategory?: string[],
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: string;
      pageSize: string;
      filterValue: string;
      latitude?: string;
      longitude?: string;
      locationFilter: string;
      minPriceFilter: string;
      maxPriceFilter: string;
      category?: string[];
      subcategory?: string[];
    };

    const response = await this.productService.getProductSearch(
      this.userContext.userId,
      queryData.page,
      queryData.pageSize,
      queryData.filterValue,
      Number(queryData.latitude),
      Number(queryData.longitude),
      Number(queryData.locationFilter),
      queryData.minPriceFilter,
      queryData.maxPriceFilter,
      queryData.category,
      queryData.subcategory,
    );

    return this.customResponse<{
      result: IProductsFilterDTO[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }

  @Security("api_key")
  @httpGet("/with-details-front/:id")
  @Get("with-details-front/{productId}")
  public async getWithAllDetailsFront(@Request() req: express.Request, @Path() productId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const productData = await this.productService.getWithAllDetailsFront(id);
    const productDataMapped = mapper.map(ProductMapper.IProductToIProductViewModel, productData);

    return this.customResponse<IProductViewModel>(productDataMapped);
  }

  @Security("api_key")
  @httpPost("/save-product-moderation/")
  @Post("/save-product-moderation/")
  public async saveProductModeration(
    @Request() req: express.Request,
    @Body()
    data: { productModeration: ICreateProductModerationViewModel },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { productModeration } = req.body;
    const result = await this.productService.saveProductModeration(productModeration);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{productId}")
  public async getById(@Request() req: express.Request, @Path() productId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result = await this.productService.getById(id);

    const response = mapper.map(ProductMapper.IProductToIProductViewModel, result);

    return this.customResponse<IProductViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/disabled-product-moderation/:id")
  @Get("/disabled-product-moderation/{productId}")
  public async getMostRecentProductModerationReason(@Request() req: express.Request, @Path() productId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const productModeration = await this.productModerationService.getMostRecentProductModeration(id);
    const data = mapper.map(ProductModerationMapper.IProductModerationToIProductModerationViewModel, productModeration);

    return this.customResponse<IStoreModerationViewModel>(data);
  }
}
