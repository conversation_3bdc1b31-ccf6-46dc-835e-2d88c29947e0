/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateTransactionViewModel } from "src/api/ViewModels/Transaction/ICreate";
import { IListTransactionByOrderViewModel } from "src/api/ViewModels/Transaction/IListTransactionByOrder";
import { ITransactionViewModel } from "src/api/ViewModels/Transaction/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { TransactionMapper } from "src/business/Configs/Automapper/Profile/Transaction";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ITransactionService } from "src/business/Interfaces/Service/ITransaction";
import { INotificationPagSeguro } from "src/business/Interfaces/Service/PagSeguro/INotification";
import { ISession } from "src/business/Interfaces/Service/PagSeguro/IObjects";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Path, Post, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/transaction")
@Route("/transaction")
@Tags("Transaction")
export class TransactionController extends BaseController {
  constructor(
    @inject(TOKENS.ITransactionService)
    private transactionService: ITransactionService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/by-order/:id")
  @Get("by-order/{orderId}")
  public async getByOrder(@Request() req: express.Request, @Path() orderId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const transactions = await this.transactionService.getTransactionByOrderId(id);

    const transactionMapped = mapper.map(
      TransactionMapper.IListTransactionByOrderDTOToIListTransactionByOrderViewModel,
      transactions,
    );

    return this.customResponse<IListTransactionByOrderViewModel[]>(transactionMapped);
  }

  @Security("api_key")
  @httpGet("/by-user/:id")
  @Get("by-user/{userId}")
  public async getByUser(@Request() req: express.Request, @Path() userId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const transactionData = await this.transactionService.getTransactionByUserId(id);

    const transactionMapped = mapper.map(TransactionMapper.ITransactionToITransactionViewModel, transactionData);

    return this.customResponse<ITransactionViewModel[]>(transactionMapped);
  }

  @Security("api_key")
  @httpGet("/last-by-user")
  @Get("last-by-user")
  public async getLastByUser(@Request() req: express.Request, @Query() userId: string, @Query() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const storeIdQ = req.query.storeId as string;
    const userIdQ = req.query.userId as string;

    const transactionData = await this.transactionService.getLastTransactionByUser(userIdQ, storeIdQ);

    const transactionMapped = transactionData
      ? mapper.map(TransactionMapper.ITransactionToITransactionViewModel, transactionData)
      : null;

    return this.customResponse<ITransactionViewModel>(transactionMapped);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(
    @Request() req: express.Request,
    @Body()
    require: {
      userId: string;
      orderId: string;
      transaction: ICreateTransactionViewModel;
    },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const userIdQ: string = req.body.userId;
    const orderIdQ: string = req.body.orderId;
    const transactionQ: ICreateTransactionViewModel = req.body.transaction;

    const isSuccess = await this.transactionService.createPayment(userIdQ, orderIdQ, transactionQ);

    return this.customResponse<any>(isSuccess);
  }

  @Security("api_key")
  @httpPost("/session")
  @Post("/session")
  public async createSession(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const session = await this.transactionService.createSessionPagSeguro();

    return this.customResponse<ISession>(session);
  }

  @Security("api_key")
  @httpPost("/notification/webhook")
  @Post("/notification/webhook")
  public async webhook(
    @Request() req: express.Request,
    @Body()
    require: INotificationPagSeguro,
  ) {
    const data: INotificationPagSeguro = req.body;

    await this.transactionService.handleWebhook(data);

    return this.customResponse<string>("OK");
  }

  // @httpPost("/notification/status")
  // @Post("/notification/status")
  // public async checkTransactionStatus(
  //   @Request() req: express.Request,
  //   @Body()
  //   require: IObjectNotification,
  // ) {
  //   const data: IObjectNotification = req.body;

  //   await this.transactionService.checkTransactionStatus(data);

  //   return this.customResponse<string>("OK");
  // }

  // @Security("api_key")
  // @httpPost("/cancel/:id")
  // @Post("/cancel/{transactionId}")
  // public async cancelPayment(@Request() req: express.Request, @Path() transactionId: string) {
  //   console.log("notification pix: ", req.params);
  //   const { id } = req.params;

  //   const transaction = await this.transactionService.getById(id);
  //   if (transaction) {
  //     const result = await this.transactionService.cancelPayment(transaction);
  //     return this.customResponse<any>(result);
  //   }

  //   return this.customResponse(null);
  // }
}
