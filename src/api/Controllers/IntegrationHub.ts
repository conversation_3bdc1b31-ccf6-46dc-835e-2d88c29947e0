import { inject } from "inversify";
import { controller, httpGet } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IIntegrationHubService } from "src/business/Interfaces/Service/IntegrationHub/IntegrationHub";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Get, Route, Security, Tags, Request, Query, Path } from "tsoa";
import express from "express";

@controller("/integrationHub")
@Route("integrationHub")
@Tags("integrationHub")
export class IntegrationHubController extends BaseController {
  constructor(
    @inject(TOKENS.IntegrationHubService)
    private integrationHubService: IIntegrationHubService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/states")
  @Get("/states")
  public async getAllStates(
    @Request() req: express.Request,
    @Query("limit") limit?: number,
    @Query("page") page?: number,
    @Query("name") name?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      limit?: number;
      page?: number;
      name?: string;
    };

    const result = await this.integrationHubService.getStates(queryData.limit, queryData.page, queryData.name);
    return this.customResponse(result);
  }

  @Security("api_key")
  @httpGet("/cities/:stateShortName")
  @Get("/cities/{stateName}")
  public async getCitiesByStateShortName(
    @Request() req: express.Request,
    @Path() stateName: string,
    @Query("limit") limit?: number,
    @Query("page") page?: number,
    @Query("name") name?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { stateShortName } = req.params;

    if (stateShortName) {
      const response = await this.integrationHubService.getCitiesByStateShortName(stateShortName);
      return this.customResponse(response);
    }

    return this.customResponse(false);
  }

  @Security("api_key")
  @httpGet("/districts/:id")
  @Get("/districts/{cityId}")
  public async getDistrictsByCityId(
    @Request() req: express.Request,
    @Path() cityId: string,
    @Query("limit") limit?: number,
    @Query("page") page?: number,
    @Query("name") name?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const queryData = req.query as unknown as {
      limit?: number;
      page?: number;
      name?: string;
    };

    if (id) {
      const response = await this.integrationHubService.getDistrictsByCityId(
        id,
        queryData.limit,
        queryData.page,
        queryData.name,
      );

      return this.customResponse(response);
    }

    return this.customResponse(false);
  }

  @Security("api_key")
  @httpGet("/getByZipCode/:zipCode")
  @Get("/getByZipCode/{zipCode}")
  public async getByZipCode(@Request() req: express.Request) {
    const { zipCode } = req.params;
    if (zipCode) {
      const response = await this.integrationHubService.getByZipCode(zipCode);
      return this.customResponse(response);
    }
    return this.customResponse(false);
  }
}
