/* eslint-disable @typescript-eslint/no-unused-vars */
import { EStoreModeratorStatus } from "@prisma/client";
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, interfaces } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { StoreMapper } from "src/business/Configs/Automapper/Profile/Store";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IStoreUserService } from "src/business/Interfaces/Service/IStoreUser";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Query, Request, Route, Security, Tags } from "tsoa";
import { IStoreModeratorViewModel } from "src/api/ViewModels/Store/IStoreModerator";
import { IStoreUserViewModel } from "src/api/ViewModels/StoreUser/IViewModel";
import { IValidateEmailDTO } from "src/business/DTOs/StoreUser/IValidateEmail";

@controller("/store-user")
@Route("store-user")
@Tags("StoreUser")
export class StoreUserController extends BaseController implements interfaces.Controller {
  constructor(
    @inject(TOKENS.IStoreUserService)
    private storeUserService: IStoreUserService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/validate")
  @Get("/validate")
  public async validateEmail(@Request() req: express.Request, @Query() email: string, @Query() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      email: string;
      storeId: string;
    };

    const response = await this.storeUserService.validateEmail(queryData.email, queryData.storeId);

    return this.customResponse<IValidateEmailDTO>(response);
  }

  @Security("api_key")
  @httpGet("/moderators/:id")
  @Get("/moderators/{storeId}")
  public async getModerators(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const storeModerators = await this.storeUserService.getModeratorsByStoreId(id);

    return this.customResponse<IStoreModeratorViewModel[]>(storeModerators);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{storeId}")
  public async get(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const storeUsers = await this.storeUserService.getStoreUsersByStoreId(id);

    const response = mapper.map(StoreMapper.IStoreUserToIStoreUserViewModel, storeUsers);

    return this.customResponse<IStoreUserViewModel[]>(response);
  }

  @Security("api_key")
  @httpPost("/set-status")
  @Post("/set-status")
  public async setStatus(
    @Request() req: express.Request,
    @Body() data: { storeUserId: string; status: EStoreModeratorStatus },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.body as unknown as {
      storeUserId: string;
      status: EStoreModeratorStatus;
    };

    const response = await this.storeUserService.setStatus(queryData.storeUserId, queryData.status);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPost("/handle-invite")
  @Post("/handle-invite")
  public async refuseInvite(
    @Request() req: express.Request,
    @Body() data: { storeUserId: string; isAccepted?: boolean },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const bodyData = req.body as unknown as {
      storeUserId: string;
      isAccepted?: boolean;
    };

    let response: boolean;

    if (bodyData.isAccepted) {
      response = await this.storeUserService.acceptInvite(bodyData.storeUserId);
    } else {
      response = await this.storeUserService.refuseInvite(bodyData.storeUserId);
    }

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() body: { storeId: string; email: string }) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body as unknown as { storeId: string; email: string };

    const response = await this.storeUserService.create(data.storeId, data.email);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{storeUserId}")
  public async delete(@Request() req: express.Request, @Path() storeUserId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.storeUserService.deleteStoreUser(id, this.userContext.userId);

    return this.customResponse<boolean>(response);
  }
}
