/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IOrder } from "src/business/DTOs/Order";
import { IPayoutService } from "src/business/Interfaces/Service/IPayout";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Put, Query, Request, Route, Security, Tags } from "tsoa";
import { PayoutMapper } from "src/business/Configs/Automapper/Profile/Payout";
import { EPayoutOwner, EPayoutStatus } from "@prisma/client";
import { IPayoutDetailsDTO } from "src/business/DTOs/Payout/IPayoutDetails";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { Xlsx } from "exceljs";
import { IPayoutAlertListViewModel } from "src/api/ViewModels/Payout/IALertList";
import { sub } from "date-fns";
import { IPayoutListViewModel } from "../ViewModels/Payout/IList";

@controller("/payout")
@Route("payout")
@Tags("Payout")
export class PayoutController extends BaseController {
  constructor(
    @inject(TOKENS.IPayoutService) private payoutService: IPayoutService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/paged/payout")
  @Get("/paged/payout")
  public async getAllFilteredPaginated(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
    @Query() filterValue?: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
    @Query() startDateFilter?: Date,
    @Query() endDateFilter?: Date,
    @Query() statusFilter?: EPayoutStatus,
    @Query() ownerFilter?: EPayoutOwner,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      filterValue: string;
      orderBy: string;
      sortDirection: IOrder;
      startDateFilter: Date;
      endDateFilter: Date;
      statusFilter?: EPayoutStatus;
      ownerFilter?: EPayoutOwner;
    };

    const { result, totalCount, totalPages, totalizer } = await this.payoutService.getPagedPayout(
      queryData.currentPage,
      queryData.pageSize,
      queryData.startDateFilter,
      queryData.endDateFilter,
      queryData.filterValue,
      queryData.orderBy,
      queryData.sortDirection,
      queryData.statusFilter,
      queryData.ownerFilter,
    );

    const response: PagedResult<IPayoutListViewModel> = {
      result: mapper.map(PayoutMapper.IPayoutToIPayoutListViewModel, result),
      totalCount,
      totalPages,
      totalizer,
    };

    return this.customResponse<{
      result: IPayoutListViewModel[];
      totalCount: number;
      totalPages: number;
      totalizer?: typeof totalizer;
    }>(response);
  }

  @Security("api_key")
  @httpGet("/paged/create-bordero")
  @Get("/paged/create-bordero")
  public async getPayoutsPaginatedToCreateBordero(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
    @Query() borderoId: string,
    @Query() ownerId: string,
    @Query() ownerFilter: EPayoutOwner,
    @Query() startDateFilter?: Date,
    @Query() endDateFilter?: Date,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
    @Query() filterValue?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      borderoId: string;
      ownerId: string;
      ownerFilter: EPayoutOwner;
      startDateFilter?: Date;
      endDateFilter?: Date;
      orderBy?: string;
      sortDirection?: IOrder;
      filterValue?: string;
    };

    const { result, totalCount, totalPages, totalizer } = await this.payoutService.getPayoutsPaginatedToCreateBordero(
      queryData.currentPage,
      queryData.pageSize,
      queryData.borderoId,
      queryData.ownerId,
      queryData.ownerFilter,
      queryData.startDateFilter,
      queryData.endDateFilter,
      queryData.orderBy,
      queryData.sortDirection,
      queryData.filterValue,
    );

    const response: PagedResult<IPayoutListViewModel> = {
      result: mapper.map(PayoutMapper.IPayoutListDTOToIPayoutListViewModel, result),
      totalCount,
      totalPages,
      totalizer,
    };

    return this.customResponse<{
      result: IPayoutListViewModel[];
      totalCount: number;
      totalPages: number;
      totalizer?: typeof totalizer;
    }>(response);
  }

  @Security("api_key")
  @httpGet("/all/by-bordero/:id")
  @Get("/all/by-bordero/borderoId")
  public async getAllPayoutsByBordero(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const data = await this.payoutService.getAllPayoutsByBordero(id);

    const response: IPayoutListViewModel[] = mapper.map(PayoutMapper.IPayoutListDTOToIPayoutListViewModel, data);

    return this.customResponse<IPayoutListViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/paged/by-bordero")
  @Get("/paged/by-bordero")
  public async getPayoutByBorderoId(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
    @Query() borderoId: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
    @Query() filterValue?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      borderoId: string;
      orderBy: string;
      sortDirection: IOrder;
      filterValue?: string;
    };

    const { result, totalCount, totalPages, totalizer } = await this.payoutService.getPagedPayoutByBordero(
      queryData.currentPage,
      queryData.pageSize,
      queryData.borderoId,
      queryData.orderBy,
      queryData.sortDirection,
      queryData.filterValue,
    );

    const response: PagedResult<IPayoutListViewModel> = {
      result: mapper.map(PayoutMapper.IPayoutToIPayoutListViewModel, result),
      totalCount,
      totalPages,
      totalizer,
    };

    return this.customResponse<{
      result: IPayoutListViewModel[];
      totalCount: number;
      totalPages: number;
      totalizer?: typeof totalizer;
    }>(response);
  }

  @Security("api_key")
  @httpPut("/update-bordero")
  @Put("/update-bordero")
  public async update(@Request() req: express.Request, @Body() body: { borderoId: string; payouts: string[] }) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;

    const response = await this.payoutService.updatePayoutsBordero(data.borderoId, data.payouts);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpGet("/report")
  @Get("/report")
  public async generateReport(
    @Request() req: express.Request,
    @Query() filterValue?: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
    @Query() startDateFilter?: Date,
    @Query() endDateFilter?: Date,
    @Query() statusFilter?: EPayoutStatus,
    @Query() ownerFilter?: EPayoutOwner,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      filterValue: string;
      orderBy: string;
      sortDirection: IOrder;
      startDateFilter: Date;
      endDateFilter: Date;
      statusFilter?: EPayoutStatus;
      ownerFilter?: EPayoutOwner;
    };

    const result = await this.payoutService.generatePayoutReport(
      queryData.startDateFilter,
      queryData.endDateFilter,
      queryData.filterValue,
      queryData.orderBy,
      queryData.sortDirection,
      queryData.statusFilter,
      queryData.ownerFilter,
    );

    this.httpContext.response.set({
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",

      // enabling CORS
      "Access-Control-Allow-Origin": process.env.CORS_ORIGIN,
      "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    });

    if (result) {
      const xlsxBuffer = await result.writeBuffer();
      const buff = Buffer.from(xlsxBuffer);
      const xlsx64 = buff.toString("base64");
      this.httpContext.response.write(`data: ${xlsx64}\n\n`);
      this.httpContext.response.send();
    }
    return this.customResponse<Xlsx>(result);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("/{payoutId}")
  public async getPayoutDetailsById(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result = await this.payoutService.getPayoutDetailsById(id);

    return this.customResponse<IPayoutDetailsDTO | null>(result);
  }

  @Security("api_key")
  @httpGet("/paged/alerts")
  @Get("/paged/alerts")
  public async getPayoutAlerts(
    @Request() req: express.Request,
    @Query() currentPage: number,
    @Query() pageSize: number,
    @Query() filterValue?: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
    @Query() statusFilter?: EPayoutStatus,
    @Query() periodFilter?: number,
    @Query() ownerFilter?: EPayoutOwner,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      filterValue?: string;
      orderBy?: string;
      sortDirection?: IOrder;
      statusFilter?: EPayoutStatus;
      periodFilter?: number;
      ownerFilter?: EPayoutOwner;
    };

    const endDateFilter = new Date();
    const startDateFilter = sub(endDateFilter, { days: queryData.periodFilter });

    const { result, totalCount, totalPages, totalizer } = await this.payoutService.getPagedPayout(
      queryData.currentPage,
      queryData.pageSize,
      startDateFilter,
      endDateFilter,
      queryData.filterValue || "",
      queryData.orderBy,
      queryData.sortDirection,
      queryData.statusFilter,
      queryData.ownerFilter,
      false,
      true,
    );

    const response: PagedResult<IPayoutAlertListViewModel> = {
      result: mapper.map(PayoutMapper.IPayoutToIPayoutAlertListViewModel, result),
      totalCount,
      totalPages,
      totalizer,
    };

    response.result.map((item) => {
      if (queryData.periodFilter) item.period = queryData.periodFilter;
      return item;
    });

    return this.customResponse<{
      result: IPayoutAlertListViewModel[];
      totalCount: number;
      totalPages: number;
      totalizer?: typeof totalizer;
    }>(response);
  }
}
