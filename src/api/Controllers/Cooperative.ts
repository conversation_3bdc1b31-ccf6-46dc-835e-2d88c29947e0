import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateOrUpdateCooperativeViewModel } from "src/api/ViewModels/Cooperative/ICreate";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICooperativeService } from "src/business/Interfaces/Service/ICooperative";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { Body, Post, Request, Route, Security, Tags, Delete, Path, Put, Get } from "tsoa";
import { CooperativeMapper } from "src/business/Configs/Automapper/Profile/Cooperative";
import { IUpdateCooperativeViewModel } from "src/api/ViewModels/Cooperative/IUpdate";
import { ICooperativeViewModel } from "src/api/ViewModels/Cooperative/IViewModel";

@controller("/cooperative")
@Route("cooperative")
@Tags("Cooperative")
export class CooperativeController extends BaseController {
  constructor(
    @inject(TOKENS.ICooperativeService) private cooperativeService: ICooperativeService,
    @inject(TOKENS.NotificationManager) notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async createOrUpdate(@Request() req: express.Request, @Body() body: ICreateOrUpdateCooperativeViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const cooperativeData: ICreateOrUpdateCooperativeViewModel = req.body;

    const input = mapper.map(CooperativeMapper.ICreateOrUpdateCooperativeViewModelToICooperative, cooperativeData);

    const response = await this.cooperativeService.createOrUpdate(input);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{cooperativeId}")
  public async delete(@Request() req: express.Request, @Path() cooperativeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.cooperativeService.delete(id);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() cooperative: IUpdateCooperativeViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);
    const data: IUpdateCooperativeViewModel = req.body;

    const input = mapper.map(CooperativeMapper.IUpdateCooperativeViewModelToICooperative, data);
    const response = await this.cooperativeService.update(input);
    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpGet("/")
  @Get("/")
  public async getOne(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const response = await this.cooperativeService.getCooperativeInfoWithAddress();

    const responseMapped = mapper.map(CooperativeMapper.ICooperativeToICooperativeViewModel, response);

    return this.customResponse<ICooperativeViewModel>(responseMapped);
  }
}
