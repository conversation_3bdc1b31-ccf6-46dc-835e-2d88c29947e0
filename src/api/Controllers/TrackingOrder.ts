/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateTrackingOrderViewModel } from "src/api/ViewModels/TrackingOrder/ICreate";
import { ITrackingOrderViewModel } from "src/api/ViewModels/TrackingOrder/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { TrackingOrderMapper } from "src/business/Configs/Automapper/Profile/TrackingOrder";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ITrackingOrderService } from "src/business/Interfaces/Service/ITrackingOrder";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Request, Route, Security, Tags } from "tsoa";

@controller("/tracking-orders")
@Route("tracking-orders")
@Tags("Tracking-orders")
export class TrackingOrderController extends BaseController {
  constructor(
    @inject(TOKENS.ITrackingOrderService)
    private trackingOrderService: ITrackingOrderService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/:orderId")
  @Get("{orderId}")
  public async getTrackingOrder(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { orderId } = req.params;

    const trackingOrder = await this.trackingOrderService.trackOrderDeliveryById(orderId);

    const trackingOrderMapped = mapper.map(TrackingOrderMapper.ITrackingOrderToITrackingOrderViewModel, trackingOrder);

    return this.customResponse<ITrackingOrderViewModel>(trackingOrderMapped);
  }

  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() store: ICreateTrackingOrderViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const trackingOrder: ICreateTrackingOrderViewModel = req.body;

    const input = mapper.map(TrackingOrderMapper.ICreateTrackingOrderToITrackingOrder, trackingOrder);

    const response = await this.trackingOrderService.create(input);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("/{orderId}")
  public async delete(@Request() req: express.Request, @Path() orderId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const result = await this.trackingOrderService.deleteByOrderId(id);
    return this.customResponse<boolean>(result);
  }
}
