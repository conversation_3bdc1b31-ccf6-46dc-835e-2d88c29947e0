/* eslint-disable @typescript-eslint/no-unused-vars */
import express from "express";
import { inject } from "inversify";
import { controller, httpGet } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ILoginSessionDashboardViewModel } from "src/api/ViewModels/LoginSession/IListDashboard";
import { ILoginSessionInfoViewModel } from "src/api/ViewModels/LoginSession/ILoginSessionInfo";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { LoginSessionMapper } from "src/business/Configs/Automapper/Profile/LoginSession";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ILoginSessionService } from "src/business/Interfaces/Service/ILoginSession";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Get, Query, Request, Route, Tags } from "tsoa";

@controller("/login-session")
@Route("login-session")
@Tags("LoginSession")
export class LoginSessionController extends BaseController {
  constructor(
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
    @inject(TOKENS.ILoginSessionService)
    private loginSessionService: ILoginSessionService,
  ) {
    super(notificationManager);
  }

  @httpGet("/access-by-day")
  @Get("/access-by-day")
  public async getNumberOfAccessByDay(
    @Request() req: express.Request,
    @Query() startDate: Date,
    @Query() endDate: Date,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const query = req.query as unknown as {
      startDate: Date;
      endDate: Date;
    };
    const response = await this.loginSessionService.getNumberOfAccessByDay(query.startDate, query.endDate);
    const output = mapper.map(LoginSessionMapper.ILoginSessionAggregateDTOToILoginSessionDashboardViewModel, response);

    return this.customResponse<ILoginSessionDashboardViewModel[]>(output);
  }

  @httpGet("/access-by-time")
  @Get("/access-by-time")
  public async getNumberOfAccessByTime(
    @Request() req: express.Request,
    @Query() startDate: Date,
    @Query() endDate: Date,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const query = req.query as unknown as {
      startDate: Date;
      endDate: Date;
    };
    const response = await this.loginSessionService.getNumberOfAccessByTime(query.startDate, query.endDate);
    const output = mapper.map(LoginSessionMapper.ILoginSessionAggregateDTOToILoginSessionDashboardViewModel, response);

    return this.customResponse<ILoginSessionDashboardViewModel[]>(output);
  }

  @httpGet("/access-info")
  @Get("/access-info")
  public async getNumberOfAccessAndMaxHour(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const response = await this.loginSessionService.getNumberOfAccessAndMaxHour();

    const output = mapper.map(LoginSessionMapper.ILoginSessionInfoDTOToILoginSessionInfoViewModel, response);

    return this.customResponse<ILoginSessionInfoViewModel>(output);
  }
}
