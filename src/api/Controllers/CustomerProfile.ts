import { inject } from "inversify";
import * as express from "express";
import { controller, httpGet } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICustomerProfile } from "src/business/Interfaces/Prisma/ICustomerProfile";
import { ICustomerProfileService } from "src/business/Interfaces/Service/ICustomerProfile";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Get, Request, Route, Security, Tags } from "tsoa";

@controller("/customer-profile")
@Route("customer-profile")
@Tags("customer-profile")
export class CustomerProfileController extends BaseController {
  constructor(
    @inject(TOKENS.ICustomerProfileService)
    private customerProfileService: ICustomerProfileService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/")
  @Get("/")
  public async getAll(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const profiles = await this.customerProfileService.getAll();

    return this.customResponse<ICustomerProfile[]>(profiles);
  }
}
