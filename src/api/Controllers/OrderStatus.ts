/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, interfaces } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateOrderStatusViewModel } from "src/api/ViewModels/OrderStatus/ICreate";
import { IListOrderStatusTypeViewModel } from "src/api/ViewModels/OrderStatus/IListOrderStatusType";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { OrderStatusMapper } from "src/business/Configs/Automapper/Profile/OrderStatus";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IOrderStatusService } from "src/business/Interfaces/Service/IOrderStatus";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Post, Request, Route, Security, Tags } from "tsoa";

@controller("/order-status")
@Route("order-status")
@Tags("OrderStatus")
export class OrderStatusController extends BaseController implements interfaces.Controller {
  constructor(
    @inject(TOKENS.IOrderStatusService)
    private orderStatusService: IOrderStatusService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("/{orderId}")
  public async getAllStatusByOrderId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result = await this.orderStatusService.getAllStatusByOrderId(id);

    const response = mapper.map(OrderStatusMapper.IOrderStatusToOrderStatusTypeListViewModel, result);

    return this.customResponse<IListOrderStatusTypeViewModel[]>(response);
  }

  @Security("api_key")
  @httpPost("/:id")
  @Post("/{orderId}")
  public async changeOrderStatus(@Request() req: express.Request, @Body() statusInfo: ICreateOrderStatusViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const profile = req.query.profile as EProfile;
    const orderStatus = req.body;

    const response = await this.orderStatusService.addOrderStatus(id, orderStatus, profile);

    return this.customResponse<boolean>(response);
  }
}
