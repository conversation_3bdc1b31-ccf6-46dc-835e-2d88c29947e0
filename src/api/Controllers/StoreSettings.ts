/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut, interfaces } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateStoreSettingsViewModel } from "src/api/ViewModels/StoreSettings/ICreate";
import { IStoreSettingsViewModel } from "src/api/ViewModels/StoreSettings/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { StoreSettingsMapper } from "src/business/Configs/Automapper/Profile/StoreSettings";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IStoreSettingsService } from "src/business/Interfaces/Service/IStoreSettings";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Put, Request, Route, Security, Tags } from "tsoa";

@controller("/store-settings")
@Route("store-settings")
@Tags("StoreSettings")
export class StoreSettingsController extends BaseController implements interfaces.Controller {
  constructor(
    @inject(TOKENS.IStoreSettingsService)
    private storeSettingsService: IStoreSettingsService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{storeId}")
  public async get(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const storeSettings = await this.storeSettingsService.getSettingsByStoreId(id);

    const response = mapper.map(StoreSettingsMapper.IStoreSettingsToStoreSettingsViewModel, storeSettings);

    return this.customResponse<IStoreSettingsViewModel>(response);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() settings: ICreateStoreSettingsViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: ICreateStoreSettingsViewModel = req.body;

    const dataMapped = mapper.map(StoreSettingsMapper.ICreateStoreSettingsViewModelToIStoreSettings, data);

    const response = await this.storeSettingsService.create(dataMapped);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() settings: ICreateStoreSettingsViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const input = req.body;

    const inputMapped = mapper.map(StoreSettingsMapper.ICreateStoreSettingsViewModelToIStoreSettings, input);

    const response = await this.storeSettingsService.update(inputMapped);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{storeSettingsId}")
  public async delete(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.storeSettingsService.delete(id);

    return this.customResponse<boolean>(response);
  }
}
