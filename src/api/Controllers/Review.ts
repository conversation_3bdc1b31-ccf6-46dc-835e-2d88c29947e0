/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateReviewViewModel } from "src/api/ViewModels/Review/ICreate";
import { IUpdateReviewViewModel } from "src/api/ViewModels/Review/IUpdate";
import { IReviewViewModel } from "src/api/ViewModels/Review/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { ReviewMapper } from "src/business/Configs/Automapper/Profile/Review";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IReviewService } from "src/business/Interfaces/Service/IReview";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/review")
@Route("review")
@Tags("Review")
export class ReviewController extends BaseController {
  constructor(
    @inject(TOKENS.IReviewService) private reviewService: IReviewService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/:storeId")
  @Get("{storeId}")
  public async getStoreReviews(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { storeId } = req.params;

    const reviews = await this.reviewService.getStoreReviews(storeId);

    const response = mapper.map(ReviewMapper.IReviewArrayToIReviewViewModelArray, reviews);

    return this.customResponse<IReviewViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/user/:userId")
  @Get("/user/{userId}")
  public async getUserReviews(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { userId } = req.params;

    const reviews = await this.reviewService.getUserReviews(userId);

    const response = mapper.map(ReviewMapper.IReviewArrayToIReviewViewModelArray, reviews);

    return this.customResponse<IReviewViewModel[]>(response);
  }

  // TODO Test
  @Security("api_key")
  @httpGet("/order/:orderId")
  @Get("/order/{orderId}")
  public async getOrderReviews(
    @Request() req: express.Request,
    @Path() orderId: string,
    @Query("deliverymanReview") deliverymanReview: boolean,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const id = req.params.orderId;
    const deliverymanQ = Boolean(req.query.deliverymanReview);

    const result = await this.reviewService.getOrderReviews(id, deliverymanQ);

    const response = mapper.map(ReviewMapper.IReviewToIReviewViewModel, result);

    return this.customResponse<IReviewViewModel>(response);
  }

  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() review: ICreateReviewViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const reviewData: ICreateReviewViewModel = req.body;
    const input = mapper.map(ReviewMapper.ICreateReviewViewModelToIReview, reviewData);
    const response = await this.reviewService.create(input);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() review: IUpdateReviewViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const reviewData: IUpdateReviewViewModel = req.body;
    const input = mapper.map(ReviewMapper.IUpdateReviewViewModelToIReview, reviewData);
    const response = await this.reviewService.update(input);

    return this.customResponse<boolean>(response);
  }
}
