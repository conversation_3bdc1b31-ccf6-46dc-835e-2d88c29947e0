import { inject } from "inversify";
import express from "express";
import { controller, httpGet } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Get, Query, Request, Route, Security, Tags } from "tsoa";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { UserMapper } from "src/business/Configs/Automapper/Profile/User";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IUserMailingViewModel } from "src/api/ViewModels/User/IUserMailing";
import { IResultMailingService } from "src/business/Interfaces/Service/IResultMailing";

@controller("/result-mailing")
@Route("result-mailing")
@Tags("ResultMailing")
export class ResultMailingController extends BaseController {
  constructor(
    @inject(TOKENS.IResultMailingService)
    private resultMailingService: IResultMailingService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/paged-front")
  @Get("/paged-front")
  public async getAllMailing(
    @Request() req: express.Request,
    @Query("page") page: number,
    @Query("pageSize") pageSize: number,
    @Query("mailingId") mailingId: string,
    @Query("filterName") filterName: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: number;
      pageSize: number;
      mailingId: string;
      filterName?: string;
    };

    const { result, totalCount, totalPages } = await this.resultMailingService.getUsersIdsByMailingIdPaged(
      queryData.page,
      queryData.pageSize,
      queryData.mailingId,
      queryData.filterName,
    );

    const response: PagedResult<IUserMailingViewModel> = {
      result: mapper.map(UserMapper.IUserMailingDTOToIUserMailingViewModel, result),
      totalCount,
      totalPages,
    };

    return this.customResponse<{
      result: IUserMailingViewModel[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }
}
