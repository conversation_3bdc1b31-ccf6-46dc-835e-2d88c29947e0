/* eslint-disable @typescript-eslint/no-unused-vars */
import { EBorderoStatus } from "@prisma/client";
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateBorderoRequestViewModel } from "src/api/ViewModels/Bordero/ICreateBorderoRequest";
import { ICreateBorderoResultViewModel } from "src/api/ViewModels/Bordero/ICreateBorderoResult";
import { IListBorderoViewModel } from "src/api/ViewModels/Bordero/IList";
import IBorderoPaymentViewModel from "src/api/ViewModels/Bordero/IPayment";
import { IUpdateBorderoViewModel } from "src/api/ViewModels/Bordero/IUpdate";
import { IValidateBorderoRequestViewModel } from "src/api/ViewModels/Bordero/IValidateBorderoRequest";
import { IValidateBorderoResultViewModel } from "src/api/ViewModels/Bordero/IValidateBorderoResult";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { BorderoMapper } from "src/business/Configs/Automapper/Profile/Bordero";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IBorderoService } from "src/business/Interfaces/Service/IBordero";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/bordero")
@Route("bordero")
@Tags("Bordero")
export class BorderoController extends BaseController {
  constructor(
    @inject(TOKENS.IBorderoService) private borderoService: IBorderoService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpPost("/validate")
  @Post("/validate")
  public async validateCpfCnpj(@Request() req: express.Request, @Body() bordero: IValidateBorderoRequestViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: IValidateBorderoRequestViewModel = req.body;
    const validateResponse = await this.borderoService.validate(data.cpfCnpj, data.payoutOwner);
    const response = mapper.map(
      BorderoMapper.IValidateBorderoResultDTOToIValidateBorderoResultViewModel,
      validateResponse,
    );

    return this.customResponse<IValidateBorderoResultViewModel>(response);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() bordero: ICreateBorderoRequestViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: ICreateBorderoRequestViewModel = req.body;

    const craeteResponse = await this.borderoService.create(data.cpfCnpj, data.payoutOwner);
    const response = mapper.map(BorderoMapper.ICreateBorderoResultDTOToICreateBorderoResultViewModel, craeteResponse);
    return this.customResponse<ICreateBorderoResultViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/paged/")
  @Get("/paged/")
  public async getAllFilteredPaginated(
    @Request() req: express.Request,
    @Query() currentPage: number,
    @Query() pageSize: number,
    @Query() filterStatus: EBorderoStatus,
    @Query() filterCpfCnpj: string,
    @Query() startDateFilter: Date,
    @Query() endDateFilter: Date,
    @Query() orderBy: string,
    @Query() sortDirection: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      filterStatus?: EBorderoStatus;
      filterCpfCnpj?: string;
      startDateFilter?: Date;
      endDateFilter?: Date;
      orderBy?: string;
      sortDirection?: string;
    };

    const { result, totalCount, totalPages } = await this.borderoService.getBorderoPaged(
      queryData.currentPage,
      queryData.pageSize,
      queryData.filterStatus,
      queryData.filterCpfCnpj,
      queryData.startDateFilter,
      queryData.endDateFilter,
      queryData.orderBy,
      queryData.sortDirection,
    );

    const response: PagedResult<IListBorderoViewModel> = {
      result: mapper.map(BorderoMapper.IBorderoListDTOToIListBorderoViewModel, result),
      totalCount,
      totalPages,
    };

    return this.customResponse<{
      result: IListBorderoViewModel[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() bordero: IUpdateBorderoViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: IUpdateBorderoViewModel = req.body;
    const input = mapper.map(BorderoMapper.IUpdateBorderoViewModelToIBordero, data);
    const response = await this.borderoService.updateBordero(input, data.payouts);
    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{borderoId}")
  public async delete(@Request() req: express.Request, @Path() borderoId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.borderoService.delete(id);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpGet("/details/:id")
  @Get("/details/{borderoId}")
  public async getDetails(@Request() req: express.Request, @Path() borderoId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const bordero = await this.borderoService.getBorderoDetails(id);

    const response = mapper.map(BorderoMapper.IBorderoPaymentDTOToIBorderoPaymentViewModel, bordero);

    return this.customResponse<IBorderoPaymentViewModel>(response);
  }

  @Security("api_key")
  @httpPost("/status")
  @Post("/status")
  public async changeStatus(@Request() req: express.Request, @Body() bordero: { id: string; status: EBorderoStatus }) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id, status } = req.body;

    const response = await this.borderoService.changeBorderoStatus(id, status);
    return this.customResponse<boolean>(response);
  }
}
