/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, interfaces } from "inversify-express-utils";
import { decode } from "jsonwebtoken";
import { BaseController } from "src/api/Controllers/Base";
import generateCookieMaxAge from "src/api/Utils/GenerateCookieMaxAge";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { UserAuthenticationResult } from "src/business/DTOs/UserAuthenticationResult";
import { CookiesFactory } from "src/business/Interfaces/Factory/ICookiesFactory";
import { ITokenService } from "src/business/Interfaces/Service/IToken";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Get, Path, Post, Request, Route, Security, Tags } from "tsoa";

@controller("/token")
@Tags("Token")
@Route("token")
export class TokenController extends BaseController implements interfaces.Controller {
  constructor(
    @inject(TOKENS.ITokenService) private tokenService: ITokenService,
    @inject(TOKENS.CookiesFactory) private cookiesFactory: CookiesFactory,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpPost("/login")
  @Post("login")
  public async login(@Request() req: express.Request, @Request() res: express.Response) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    return this.customResponse<boolean>(true);
  }

  @httpGet("/refresh")
  @Get("/refresh")
  public async refresh(@Request() req: express.Request) {
    const rawToken = req.headers.authorization;
    const token = rawToken?.replace("Bearer ", "");

    const queryData = req.query as unknown as {
      cognitoUsername?: string;
      cognitoId: string;
    };

    let result: UserAuthenticationResult | null = null;

    if (token) {
      result = await this.tokenService.refreshToken(queryData.cognitoId, token, queryData.cognitoUsername);
    }

    return this.customResponse<UserAuthenticationResult>(result);
  }

  @httpGet("/refresh-web")
  @Get("refresh-web")
  public async refreshWeb(@Request() req: express.Request) {
    let token: string | undefined;
    let cognitoId: string | undefined;

    if (req.cookies.refreshToken && req.cookies.idToken) {
      token = req.cookies.refreshToken;
      const decodedToken = decode(req.cookies.idToken) as any;
      cognitoId = decodedToken.sub;
    }

    let response: boolean = false;

    const cookies = this.cookiesFactory();

    if (token && cognitoId) {
      const result = await this.tokenService.refreshToken(cognitoId, token);

      if (result && "cognitoAuthenticationResult" in result) {
        if (result?.cognitoAuthenticationResult?.AccessToken && result.cognitoAuthenticationResult.ExpiresIn) {
          cookies.setCookie({
            name: "authorization",
            value: result.cognitoAuthenticationResult.AccessToken,
            options: {
              httpOnly: true,
              /* secure: true, */
              maxAge: generateCookieMaxAge(result.cognitoAuthenticationResult.ExpiresIn),
            },
          });

          cookies.setCookie({
            name: "idToken",
            value: result.cognitoAuthenticationResult.IdToken,
            options: {
              httpOnly: true,
            },
          });

          cookies.setCookie({
            name: "authenticated",
            value: "true",
            options: {
              httpOnly: false,
              /* secure: true, */
              maxAge: generateCookieMaxAge(result.cognitoAuthenticationResult.ExpiresIn),
            },
          });

          response = true;
        }
      }
    }

    if (response === false) {
      cookies.clearCookie({ name: "authorization", options: { httpOnly: true } });
      cookies.clearCookie({ name: "idToken", options: { httpOnly: true } });
      cookies.clearCookie({ name: "refreshToken", options: { httpOnly: true } });
    }

    return this.customResponse<boolean>(response, cookies.getAll());
  }

  @httpPost("/revoke")
  @Post("revoke")
  public async revoke(@Request() req: express.Request) {
    const rawToken = req.headers.authorization;
    const token = rawToken?.replace("Bearer ", "");

    if (token) {
      await this.tokenService.revokeAccess(token);
    }

    return this.customResponse<boolean>(true);
  }

  @httpPost("/exchange-code/:code")
  @Post("/exchange-code/{:code}")
  public async exchangeCode(@Request() req: express.Request, @Path() code: string) {
    const { code: authCode } = req.params;

    const result = await this.tokenService.exchangeCode(authCode);

    return this.customResponse<UserAuthenticationResult | null>(result);
  }
}
