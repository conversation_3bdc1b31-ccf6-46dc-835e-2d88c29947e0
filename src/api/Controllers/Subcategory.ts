/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateSubcategoryViewModel } from "src/api/ViewModels/Subcategory/ICreate";
import { IListSubcategoryViewModel } from "src/api/ViewModels/Subcategory/IList";
import { IUpdateSubcategoryViewModel } from "src/api/ViewModels/Subcategory/IUpdate";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { SubcategoryMapper } from "src/business/Configs/Automapper/Profile/Subcategory";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ISubcategoryService } from "src/business/Interfaces/Service/ISubcategory";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { IOrder } from "src/business/DTOs/Order";
import { Body, Delete, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ISubcategoryFrontViewModel } from "src/api/ViewModels/Subcategory/ISubcategoryFront";

@controller("/subcategory")
@Route("subcategory")
@Tags("Subcategory")
export class SubcategoryController extends BaseController {
  constructor(
    @inject(TOKENS.ISubcategoryService)
    private subcategoryService: ISubcategoryService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/")
  @Get("/")
  public async getAll() {
    const subcategory = await this.subcategoryService.getAll();

    const response = mapper.map(SubcategoryMapper.ISubcategoryToIListSubcategoryViewModel, subcategory);

    return this.customResponse<IListSubcategoryViewModel[]>(response);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() subcategory: ICreateSubcategoryViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const input: ICreateSubcategoryViewModel = req.body;

    const subcategoryDataMapped = mapper.map(SubcategoryMapper.ICreateSubcategoryViewModelToISubcategory, input);

    const response = await this.subcategoryService.create(subcategoryDataMapped);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("/{subcategoryId}")
  public async delete(@Request() req: express.Request, @Path() subcategoryId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const response = await this.subcategoryService.deleteWithRelations(id);
    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() subcategory: IUpdateSubcategoryViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const input = req.body;

    const response = await this.subcategoryService.update(input);

    return this.customResponse<boolean>(response);
  }

  // FIXME
  @Security("api_key")
  @httpGet("/paged/")
  @Get("/paged/")
  public async getAllFilteredPaginated(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
    @Query() filterValue?: string,    
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {      
      currentPage: string;
      pageSize: string;      
      filterValue: string;      
      orderBy: string;
      sortDirection: IOrder;
    };

    const subcategory = await this.subcategoryService.getPagedWithCategory(      
      queryData.currentPage,
      queryData.pageSize,
      queryData.filterValue,            
      queryData.orderBy,
      queryData.sortDirection,
    );

    let responseMapped: PagedResult<ISubcategoryFrontViewModel> | null = null;

    if (subcategory) {
      const subcategoryDataMapped = mapper.map(
        SubcategoryMapper.ISubcategoryToSubcategoryFrontViewModel,
        subcategory.result,
      );

      responseMapped = {
        result: subcategoryDataMapped,
        totalCount: subcategory.totalCount,
        totalPages: subcategory.totalPages,
      };
    }

    return this.customResponse<{
      result: ISubcategoryFrontViewModel[];
      totalCount: number;
      totalPages: number;
    }>(responseMapped);
  }
}
