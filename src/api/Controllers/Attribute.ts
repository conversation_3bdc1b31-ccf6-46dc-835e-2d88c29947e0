/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { IAttributeViewModel } from "src/api/ViewModels/Attribute/IAttribute";
import { ICreateAttributeViewModel } from "src/api/ViewModels/Attribute/ICreate";
import { IAttributeListViewModel } from "src/api/ViewModels/Attribute/IList";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { AttributeMapper } from "src/business/Configs/Automapper/Profile/Attribute";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAttributeService } from "src/business/Interfaces/Service/IAttribute";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Post, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/attribute")
@Route("attribute")
@Tags("Attribute")
export class AttributeController extends BaseController {
  constructor(
    @inject(TOKENS.IAttributeService)
    private attributeService: IAttributeService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/paged-with-product-attribute-options/")
  @Get("/paged-with-product-attribute-options/")
  public async getPagedWithAllAttributeOption(
    @Request() req: express.Request,
    @Query() productId: string,
    @Query() currentPage: number,
    @Query() pageSize: number,
    @Query() filter: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      productId: string;
      currentPage: string;
      pageSize: string;
      filter: string;
    };

    const result = await this.attributeService.getPagedWithAllAttributeOption(
      queryData.productId,
      queryData.currentPage,
      queryData.pageSize,
      queryData.filter,
    );

    return this.customResponse<{
      result: IAttributeViewModel[];
      totalCount: number;
      totalPages: number;
    }>(result);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() attribute: ICreateAttributeViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const attributeParam = req.body;

    const response = await this.attributeService.create(attributeParam);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpGet("/with-product-attribute-option")
  @Get("/with-product-attribute-option")
  public async getWithProductAttributeOption(@Request() req: express.Request, @Query() productId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const result = await this.attributeService.getWithProductAttributeOption(req.query.productId as string);

    const response = mapper.map(AttributeMapper.IAttributeToIAttributeListViewModel, result);

    return this.customResponse<IAttributeListViewModel[]>(response);
  }
}
