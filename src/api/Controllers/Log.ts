/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ILogListViewModel } from "src/api/ViewModels/Log/IList";
import { ILogDetailsViewModel } from "src/api/ViewModels/Log/ILogDetails";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { LogMapper } from "src/business/Configs/Automapper/Profile/Log";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { ILogService } from "src/business/Interfaces/Service/ILog";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Get, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/log")
@Route("log")
@Tags("Log")
export class LogController extends BaseController {
  constructor(
    @inject(TOKENS.ILogService) private logService: ILogService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/paged/log")
  @Get("/paged/log")
  public async getAllFilteredPaginated(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
    @Query() filterValue?: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
    @Query() startDateFilter?: Date,
    @Query() endDateFilter?: Date,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      filterValue: string;
      orderBy: string;
      sortDirection: IOrder;
      startDateFilter: Date;
      endDateFilter: Date;
    };

    const { result, totalCount, totalPages } = await this.logService.getPagedLog(
      queryData.currentPage,
      queryData.pageSize,
      queryData.startDateFilter,
      queryData.endDateFilter,
      queryData.filterValue,
      queryData.orderBy,
      queryData.sortDirection,
    );

    let responseMapped: PagedResult<ILogListViewModel> | null = null;

    const resultMapped = mapper.map(LogMapper.ILogToILogListViewModel, result);

    if (result) {
      responseMapped = {
        result: resultMapped,
        totalCount,
        totalPages,
      };
    }

    return this.customResponse<{
      result: ILogListViewModel[];
      totalCount: number;
      totalPages: number;
    }>(responseMapped);
  }

  @Security("api_key")
  @httpGet("/by-id/:id")
  @Get("/by-id/{logId}")
  public async getLogDetailsById(
    @Request() req: express.Request,
    @Query() createdAt: Date,
    @Query() entity: string,
    @Query() entityId: string,
    @Query() action: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const queryData = req.query as unknown as {
      createdAt: Date;
      entity: string;
      entityId: string;
      action: string;
    };

    const result = await this.logService.getLogDetailsById(
      id,
      queryData.entity,
      queryData.entityId,
      queryData.createdAt,
      queryData.action,
    );

    const resultMapped = mapper.map(LogMapper.ILogDTOToILogDetailsViewModel, result);

    return this.customResponse<ILogDetailsViewModel>(resultMapped);
  }
}
