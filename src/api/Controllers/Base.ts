/* eslint-disable no-dupe-class-members */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request } from "express";
import { inject, injectable } from "inversify";
import { BaseHttpController, interfaces } from "inversify-express-utils";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICookie } from "src/business/Interfaces/Controllers/ICookie";
import { ICustomError, ICustomResponse } from "src/business/Interfaces/Controllers/ICustomResponse";
import { IAuthenticateRequest } from "src/business/Interfaces/Tools/IAuthenticateRequest";
import { INotification, INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { IUserContext } from "src/business/Interfaces/Tools/IUserContext";

@injectable()
export abstract class BaseController extends BaseHttpController implements interfaces.Controller {
  protected notificationManager: INotificationManager;

  @inject(TOKENS.AuthenticateRequest)
  private _authenticateRequest: IAuthenticateRequest;

  @inject(TOKENS.UserContext)
  protected userContext: IUserContext;

  constructor(notificationManager: INotificationManager) {
    super();
    this.notificationManager = notificationManager;
  }

  async authenticateRequest(req: Request) {
    const response = await this._authenticateRequest.handle(req);

    return response;
  }

  customResponse<R>(
    content?: R | null,
    cookies?: ICookie[],
    status?: number,
  ): {
    error: ICustomError;
    data?: R | null;
    status: number;
  } {
    const notificationList = this.notificationManager.getList();

    const response: ICustomResponse<R> = {
      status: 400,
      data: {} as R | null,
      error: {} as ICustomError,
    };

    if (cookies && cookies.length > 0) {
      cookies.forEach(({ name, value, options, remove }) => {
        if (remove) {
          this.httpContext.response.clearCookie(name, options);
          return;
        }

        if (value) this.httpContext.response.cookie(name, value, options);
      });
    }

    if (notificationList.length > 0) {
      response.error = this.normalizeList(notificationList);
      response.status = status || 400;

      console.log("Notification encountered", response.error);
    } else {
      response.status = 200;
    }

    if (typeof content === "object" && content !== null && !Array.isArray(content)) {
      if (Object.keys(content).length === 0) {
        content = null;
      }
    }
    response.data = content;

    this.httpContext.response.status(response.status);
    return response;
  }

  validateId(id: string | undefined) {
    if (id) {
      return id;
    }

    this.notificationManager.add("generic.errors", "user_notFound");
    return null;
  }

  private normalizeList(list: INotification[]) {
    const data = list.reduce(
      (acc, i) => {
        const [key, value] = Object.entries(i)[0];
        if (key.includes("generic")) {
          acc.generic[key] = acc.generic[key] ? [...acc.generic[key], value] : [value];
        } else {
          acc.fields[key] = acc.fields[key] ? [...acc.fields[key], value] : [value];
        }
        return acc;
      },
      { generic: {}, fields: {} } as {
        generic: { [key: string]: string[] };
        fields: { [key: string]: string[] };
      },
    );
    return data;
  }
}
