/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { IAddressOptionViewModel } from "src/api/ViewModels/Address/IAddressOption";
import { ICreateAddressViewModel } from "src/api/ViewModels/Address/ICreate";
import { ICreateAddressWithoutRelationViewModel } from "src/api/ViewModels/Address/ICreateWithoutRelation";
import { IListAddressViewModel } from "src/api/ViewModels/Address/IList";
import { IUpdateAddressViewModel } from "src/api/ViewModels/Address/IUpdate";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { AddressMapper } from "src/business/Configs/Automapper/Profile/Address";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IAddress } from "src/business/Interfaces/Prisma/IAddress";
import { IAddressRepository } from "src/business/Interfaces/Repository/IAddress";
import { IAddressService } from "src/business/Interfaces/Service/IAddress";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Put, Request, Route, Security, Tags } from "tsoa";

@controller("/address")
@Route("address")
@Tags("Address")
export class AddressController extends BaseController {
  constructor(
    @inject(TOKENS.IAddressService) private addressService: IAddressService,
    @inject(TOKENS.IAddressRepository) private addressRepository: IAddressRepository,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  // TODO Testar com usuário
  @Security("api_key")
  @httpGet("/by-user/")
  @Get("by-user/")
  public async getAddressByUserId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const result = await this.addressService.getAddressesByUserId(this.userContext.userId);

    const response = mapper.map(AddressMapper.IAddressToIListAddressViewModel, result);

    return this.customResponse<IListAddressViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/options/by-user/")
  @Get("options/by-user/")
  public async getAddressOptionsByUserId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const result = await this.addressService.getAddresseOptionsByUserId(this.userContext.userId);

    const response = mapper.map(AddressMapper.IAddressOptionToIAddressOptionViewModel, result);

    return this.customResponse<IAddressOptionViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{addressId}")
  public async get(@Request() req: express.Request, @Path() addressId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result: IAddress | null = await this.addressService.getById(id);

    const response = mapper.map(AddressMapper.IAddressToIListAddressViewModel, result);

    return this.customResponse<IListAddressViewModel>(response);
  }

  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() address: ICreateAddressViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;
    const { userId } = this.userContext;

    const input = mapper.map(AddressMapper.ICreateAddressViewModelToIAddress, data);

    const result = await this.addressService.create(input, userId);

    return this.customResponse<boolean>(!!result);
  }

  @httpPost("/create-without-relating")
  @Post("/create-without-relating")
  public async createWithoutRelating(
    @Request() req: express.Request,
    @Body() address: ICreateAddressWithoutRelationViewModel,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;

    const input = mapper.map(AddressMapper.ICreateAddressViewModelToIAddress, data);

    const id = await this.addressService.create(input);

    return this.customResponse<string>(id);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() address: IUpdateAddressViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;
    const { userId } = this.userContext;

    const input = mapper.map(AddressMapper.IUpdateAddressViewModelToIAddress, data);

    const response = await this.addressService.updateSwitchDefaulAddress(input, userId);

    return this.customResponse<number>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{addressId}")
  public async delete(@Request() req: express.Request, @Path() addressId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.addressService.delete(id);

    return this.customResponse(response);
  }
}
