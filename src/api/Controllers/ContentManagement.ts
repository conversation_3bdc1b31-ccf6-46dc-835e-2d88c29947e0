/* eslint-disable @typescript-eslint/no-unused-vars */
import { EContentManagementType } from "@prisma/client";
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateContentManagementViewModel } from "src/api/ViewModels/ContentManagement/ICreate";
import { IUpdateContentManagementViewModel } from "src/api/ViewModels/ContentManagement/IUpdate";
import { IContentManagementViewModel } from "src/api/ViewModels/ContentManagement/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { ContentManagementMapper } from "src/business/Configs/Automapper/Profile/ContentManagement";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IContentManagementService } from "src/business/Interfaces/Service/IContentManagement";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/content-management")
@Route("content-management")
@Tags("ContentManagement")
export class ContentManagementController extends BaseController {
  constructor(
    @inject(TOKENS.IContentManagementService) private contentManagementService: IContentManagementService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/by_type/")
  @Get("by_type/")
  public async getByType(@Request() req: express.Request, @Query() contentType: EContentManagementType) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const contentTypeQ: EContentManagementType = req.query.contentType as any;
    const content: IContentManagementViewModel | null = await this.contentManagementService.getByType(contentTypeQ);
    return this.customResponse<IContentManagementViewModel>(content);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{contentId}")
  public async get(@Request() req: express.Request, @Path() contentId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const content: IContentManagementViewModel | null = await this.contentManagementService.getById(id);

    return this.customResponse<IContentManagementViewModel>(content);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() content: ICreateContentManagementViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const contentData: ICreateContentManagementViewModel = req.body;
    contentData.userId = this.userContext.userId;

    const contentMapped = mapper.map(
      ContentManagementMapper.ICreateContentManagementViewModelToIContentManagement,
      contentData,
    );

    const contentCreated = await this.contentManagementService.create(contentMapped);

    return this.customResponse<boolean>(!!contentCreated);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("")
  public async update(@Request() req: express.Request, @Body() content: IUpdateContentManagementViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const contentData: IUpdateContentManagementViewModel = req.body;
    contentData.userId = this.userContext.userId;

    const contentMapped = mapper.map(
      ContentManagementMapper.IUpdateContentManagementViewModelToIContentManagement,
      contentData,
    );

    const result = await this.contentManagementService.update(contentMapped);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("/{contentId}")
  public async delete(@Request() req: express.Request, @Path() contentId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const response = await this.contentManagementService.delete(id);
    return this.customResponse<boolean>(response);
  }
}
