/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { IShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/IShopkeeper";
import { IUserShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/IUserShopkeeper";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { ShopkeeperMapper } from "src/business/Configs/Automapper/Profile/Shopkeeper";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IShopkeeperService } from "src/business/Interfaces/Service/IShopkeeper";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Path, Put, Query, Request, Route, Security, Tags } from "tsoa";
import { IUpdateShopkeeperViewModel } from "../ViewModels/Shopkeeper/IUpdateShopkeeper";

@controller("/shopkeeper")
@Route("shopkeeper")
@Tags("shopkeeper")
export class ShopkeeperController extends BaseController {
  constructor(
    @inject(TOKENS.IShopkeeperService)
    private shopkeeperService: IShopkeeperService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/")
  @Get("/")
  public async getAll() {
    const result = await this.shopkeeperService.getAll();

    const response = mapper.map(ShopkeeperMapper.IShopkeeperToIShopkeeperViewModel, result);

    return this.customResponse<IShopkeeperViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/status" /*  */)
  @Get("status")
  public async getByStatus(
    @Request() req: express.Request,
    @Query() page: string,
    @Query() pageSize: string,
    @Query() status: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: string;
      pageSize: string;
      status: string;
    };

    const { result, totalCount, totalPages } = await this.shopkeeperService.getShopkeeperByStatus(
      queryData.page,
      queryData.pageSize,
      queryData.status,
    );

    const response = {
      result: mapper.map(ShopkeeperMapper.IShopkeeperToIUserShopkeeperViewModel, result),
      totalCount,
      totalPages,
    };

    return this.customResponse<{
      result: IUserShopkeeperViewModel[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() user: IUpdateShopkeeperViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: IUpdateShopkeeperViewModel = req.body;

    const input = mapper.map(ShopkeeperMapper.IShopkeeperUpdateViewModelToIShopkeeper, data);

    const result = await this.shopkeeperService.update(input);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/:id" /* cognitoAccessTokenAuthenticate */)
  @Get("/:userId")
  public async getShopkeeper(@Request() req: express.Request, @Path() userId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const data = await this.shopkeeperService.getShopkeeper(id);

    const result = mapper.map(ShopkeeperMapper.IShopkeeperToIShopkeeperViewModel, data);

    return this.customResponse<IShopkeeperViewModel>(result);
  }
}
