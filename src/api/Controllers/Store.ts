/* eslint-disable @typescript-eslint/no-unused-vars */
import { EDayOfWeek, EStoreModeratorStatus } from "@prisma/client";
import { Xlsx } from "exceljs";
import * as express from "express";
import { inject } from "inversify";
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateStoreViewModel } from "src/api/ViewModels/Store/ICreate";
import { IListStoreViewModel } from "src/api/ViewModels/Store/IList";
import { IListUserStoreViewModel } from "src/api/ViewModels/Store/IListUserStore";
import { IPaymentMethodsViewModel } from "src/api/ViewModels/Store/IPaymentMethods";
import { IStoreNameViewModel } from "src/api/ViewModels/Store/IStoreName";
import { IStoreShowcaseViewModel } from "src/api/ViewModels/Store/IStoreShowcase";
import { IUpdateStoreViewModel } from "src/api/ViewModels/Store/IUpdate";
import { IStoreViewModel } from "src/api/ViewModels/Store/IViewModel";
import { ICreateStoreModerationViewModel } from "src/api/ViewModels/StoreModeration/ICreate";
import { IStoreModerationViewModel } from "src/api/ViewModels/StoreModeration/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { StoreMapper } from "src/business/Configs/Automapper/Profile/Store";
import { StoreModerationMapper } from "src/business/Configs/Automapper/Profile/StoreModeration";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ISelectDTO } from "src/business/DTOs/ISelect";
import { IOrder } from "src/business/DTOs/Order";
import { PagedResult } from "src/business/DTOs/PagedResult";
import { IStoreCategory } from "src/business/Interfaces/Prisma/IStoreCategory";
import { IStoreService } from "src/business/Interfaces/Service/IStore";
import { IStoreModerationService } from "src/business/Interfaces/Service/IStoreModeration";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";

@controller("/store")
@Route("store")
@Tags("Store")
export class StoreController extends BaseController {
  constructor(
    @inject(TOKENS.IStoreService) private storeService: IStoreService,
    @inject(TOKENS.IStoreModerationService)
    private storeModerationService: IStoreModerationService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() store: ICreateStoreViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: any = req.body;
    data.storeUsers = [{ userId: this.userContext.userId, owner: true, status: EStoreModeratorStatus.active }];

    const input = mapper.map(StoreMapper.ICreateStoreViewModelToIStore, data);
    const response = await this.storeService.create(input, data.attachments);
    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpGet("/paged/")
  @Get("/paged/")
  public async getAllFilteredPaginated(
    @Request() req: express.Request,
    @Query() page: number,
    @Query() pageSize: number,
    @Query() currentDay: EDayOfWeek,
    @Query() currentTime: Date,
    @Query() storeFilter: string,
    @Query() categoryIdFilter: string,
    @Query() favoriteFilter: boolean,
    @Query() latitude?: number,
    @Query() longitude?: number,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: number;
      pageSize: number;
      currentDay: EDayOfWeek;
      currentTime: Date;
      storeFilter: string;
      categoryIdFilter: string;
      favoriteFilter: string;
      latitude: number;
      longitude: number;
    };

    const { result, totalCount, totalPages } = await this.storeService.getPagedListWithFavoriteStores(
      queryData.currentDay,
      queryData.currentTime,
      queryData.categoryIdFilter,
      queryData.storeFilter,
      queryData.favoriteFilter === "true",
      Number(queryData.page),
      Number(queryData.pageSize),
      Number(queryData.latitude),
      Number(queryData.longitude),
    );

    const response: PagedResult<IListStoreViewModel> = {
      result: mapper.map(StoreMapper.IListStoresDTOToIListStoreViewModel, result),
      totalCount,
      totalPages,
    };

    return this.customResponse<{
      result: IListStoreViewModel[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }

  @Security("api_key")
  @httpGet("/info/:id")
  @Get("/info/{storeId}")
  public async getStoreInfo(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const store = await this.storeService.getStoreInfoById(id);

    const response = mapper.map(StoreMapper.IStoreToIStoreViewModel, store);

    return this.customResponse<IStoreViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/stores-user/")
  @Get("/stores-user/")
  public async getStoresByUserId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const stores = await this.storeService.getStoresByUserId(this.userContext.userId);

    const response = mapper.map(StoreMapper.IListUserStoreDTOToIListUserStoreViewModel, stores);
    
    return this.customResponse<IListUserStoreViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/stores-name/")
  @Get("/stores-name/")
  public async getStoresNameByUserId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const storesNames = await this.storeService.getStoresNameByUserId(this.userContext.userId);

    const response = mapper.map(StoreMapper.IStoreNameDTOToIStoreNameViewModel, storesNames);

    return this.customResponse<IStoreNameViewModel[]>(response);
  }

  @Security("api_key")
  @httpPost("/relate-user")
  @Post("relate-user")
  public async relateStoreUser(@Request() req: express.Request, @Body() require: { storeId: string; userId: string }) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { storeId, userId } = req.body;

    const result = await this.storeService.relateStoreUser(storeId, userId);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() store: IUpdateStoreViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);
    const data: IUpdateStoreViewModel = req.body;

    const input = mapper.map(StoreMapper.IUpdateStoreViewModelToIStore, data);
    const response = await this.storeService.update(input);
    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{storeId}")
  public async delete(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.storeService.deleteWithRelations(id);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpGet("/payment-methods-by-user")
  @Get("/payment-methods-by-user")
  public async getPaymentMethodsByStoreAndUser(
    @Request() req: express.Request,
    @Query() storeId: string,
    @Query() userId: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const storeIdQ = req.query.storeId as string;
    const userIdQ = req.query.userId as string;

    const paymentMethods = await this.storeService.getPaymentMethodsByStoreAndUser(storeIdQ, userIdQ);
    const result = mapper.map(StoreMapper.IPaymentMethodsDTOToIPaymentMethodsViewModel, paymentMethods);

    return this.customResponse<IPaymentMethodsViewModel>(result);
  }

  @Security("api_key")
  @httpGet("/paged-front/")
  @Get("/paged-front/")
  public async getPagedListFront(
    @Request() req: express.Request,
    @Query() page: string,
    @Query() pageSize: string,
    @Query() filterValue?: string,
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: string;
      pageSize: string;
      filterValue: string;
      orderBy: string;
      sortDirection: IOrder;
    };

    const store = await this.storeService.getPagedListFront(
      queryData.page,
      queryData.pageSize,
      queryData.filterValue,
      queryData.orderBy,
      queryData.sortDirection,
    );

    const storeDataMapped: IStoreViewModel[] = mapper.map(StoreMapper.IStoreToIStoreViewModel, store.result);

    return this.customResponse<{
      result: IStoreViewModel[];
      totalCount: number;
      totalPages: number;
    }>({
      result: storeDataMapped,
      totalCount: store.totalCount,
      totalPages: store.totalPages,
    });
  }

  @Security("api_key")
  @httpGet("/export-stores/")
  @Get("/export-stores/")
  public async exportStores(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const xlsx = await this.storeService.exportStores();

    this.httpContext.response.set({
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",

      // enabling CORS
      "Access-Control-Allow-Origin": process.env.CORS_ORIGIN,
      "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    });

    if (xlsx) {
      const xlsxBuffer = await xlsx.writeBuffer();
      const buff = Buffer.from(xlsxBuffer);
      const xlsx64 = buff.toString("base64");
      this.httpContext.response.write(`data: ${xlsx64}\n\n`);
      this.httpContext.response.send();
    }
    return this.customResponse<Xlsx>(xlsx);
  }

  @Security("api_key")
  @httpPost("/save-store-moderation/")
  @Post("/save-store-moderation/")
  public async saveStoreModeration(
    @Request() req: express.Request,
    @Body()
    data: { storeModeration: ICreateStoreModerationViewModel },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { storeModeration } = req.body;

    const result = await this.storeService.saveStoreModeration(storeModeration);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/with-details-front/:id")
  @Get("/with-details-front/{storeId}")
  public async getWithAllDetailsFront(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const storeData = await this.storeService.getWithAllDetailsFront(id);
    const storeDataMapped = mapper.map(StoreMapper.IStoreToIStoreViewModel, storeData);

    return this.customResponse<IStoreViewModel>(storeDataMapped);
  }

  @Security("api_key")
  @httpPut("/update/categories")
  @Put("/update/categories")
  public async updateCategories(
    @Request() req: express.Request,
    @Body() data: { storeId: string; categoriesIds: string[] },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { storeId, categoriesIds = [] } = req.body as unknown as {
      storeId: string;
      categoriesIds: string[];
    };
    const result = await this.storeService.updateStoreCategories(
      storeId,
      categoriesIds.map((id) => ({ storeId, categoryId: id } as IStoreCategory)),
    );
    return this.customResponse<any>(result);
  }

  @httpGet("/check-availability/:id")
  @Get("/check-availability/{storeId}")
  public async checkAvailabilityByStoreId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const isAvailable = await this.storeService.checkAvailabilityByStoreId(id);

    return this.customResponse<boolean>(isAvailable);
  }

  @Security("api_key")
  @httpGet("/disabled-store-moderation/:id")
  @Get("/disabled-store-moderation/{storeId}")
  public async getMostRecentStoreModerationReason(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const storeData = await this.storeModerationService.getMostRecentStoreModeration(id);
    const storeDataMapped = mapper.map(StoreModerationMapper.IStoreModerationToIStoreModerationViewModel, storeData);

    return this.customResponse<IStoreModerationViewModel>(storeDataMapped);
  }

  @Security("api_key")
  @httpGet("/showcase/:id")
  @Get("/showcase/{storeId}")
  public async getStoreShowcase(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const data = await this.storeService.getStoreShowcase(id);

    const response = mapper.map(StoreMapper.IStoreShowcaseWithUrlsDTOToIStoreShowcaseViewModel, data);

    return this.customResponse<IStoreShowcaseViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/select-front/")
  @Get("/select-front/")
  public async getStoresSelect(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      storeName: string;
      currentPage: string;
      pageSize: string;
    };

    const stores = await this.storeService.getSelect(
      queryData.storeName,
      Number(queryData.currentPage),
      Number(queryData.pageSize),
    );

    return this.customResponse<PagedResult<ISelectDTO>>(stores);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{storeId}")
  public async get(@Request() req: express.Request, @Path() storeId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const store = await this.storeService.getById(id);

    const response = mapper.map(StoreMapper.IStoreToIStoreViewModel, store);

    return this.customResponse<IStoreViewModel>(response);
  }
  
 
  @httpGet("/check-pix/:userId")
  @Get("/check-pix/{userId}")
  public async getCheckPix(@Request() req: express.Request){
    const {userId} = req.params;
    const hasStoreMissingPixKey = await this.storeService.getHasStoreMissingPixKey(userId);
    return this.customResponse<boolean>(hasStoreMissingPixKey)
  }
}
