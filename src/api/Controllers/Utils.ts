import { inject } from "inversify";
import { controller, httpPost } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Post, Route, Tags } from "tsoa";

@controller("/utils")
@Route("utils")
@Tags("Utils")
export class UtilsController extends BaseController {
  constructor(
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @httpPost("/ping")
  @Post("ping")
  public async healthCheck() {
    this.httpContext.response.send("pong");
  }
}
