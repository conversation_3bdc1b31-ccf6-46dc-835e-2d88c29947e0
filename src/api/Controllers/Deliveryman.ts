/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { IDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IDeliveryman";
import { IDeliverymanLocationViewModel } from "src/api/ViewModels/Deliveryman/IDeliverymanLocation";
import { IUserDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IUserDeliveryman";
import { ILocationViewModel } from "src/api/ViewModels/Location/IViewModel";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { DeliverymanMapper } from "src/business/Configs/Automapper/Profile/Deliveryman";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IDeliverymanService } from "src/business/Interfaces/Service/IDeliveryman";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";
import { IUpdateDeliverymanViewModel } from "../ViewModels/Deliveryman/IUpdateDeliveryman";


@controller("/deliveryman")
@Route("deliveryman")
@Tags("deliveryman")
export class DeliverymanController extends BaseController {
  constructor(
    @inject(TOKENS.IDeliverymanService)
    private deliverymanService: IDeliverymanService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/")
  @Get("/")
  public async getAll() {
    const result = await this.deliverymanService.getAll();

    // TODO Check this
    const response = mapper.map(DeliverymanMapper.IDeliverymanToIDeliverymanViewModel, result);

    return this.customResponse<IDeliverymanViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/status")
  @Get("status")
  public async getByStatus(
    @Request() req: express.Request,
    @Query() page: string,
    @Query() pageSize: string,
    @Query() status: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      page: string;
      pageSize: string;
      status: string;
    };

    const { result, totalCount, totalPages } = await this.deliverymanService.getDeliverymanByStatus(
      queryData.page,
      queryData.pageSize,
      queryData.status,
    );

    const response = {
      result: mapper.map(DeliverymanMapper.IDeliverymanToIUserDeliverymanViewModel, result),
      totalCount,
      totalPages,
    };

    return this.customResponse<{
      result: IUserDeliverymanViewModel[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async update(@Request() req: express.Request, @Body() user: IUpdateDeliverymanViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data: IUpdateDeliverymanViewModel = req.body;

    const deliverymanMapped = mapper.map(DeliverymanMapper.IDeliverymanUpdateViewModelToIShopkeeper, data);

    const result = await this.deliverymanService.update(deliverymanMapped);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/:id" /* cognitoAccessTokenAuthenticate */)
  @Get("/:userId")
  public async getDeliveryman(@Request() req: express.Request, @Path() userId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const data = await this.deliverymanService.getDeliveryman(id);
    const result = mapper.map(DeliverymanMapper.IDeliverymanToIDeliverymanViewModel, data);

    return this.customResponse<IDeliverymanViewModel>(result);
  }

  @Security("api_key")
  @httpPost("/location")
  @Post("/location")
  public async getDeliverymanLocation(@Request() req: express.Request, @Body() body: { cpf: string }) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const {
      body: { cpf },
    } = req;

    const input = await this.deliverymanService.getDeliverymanLocation(cpf);
    const output = mapper.map(DeliverymanMapper.IDeliverymanToIDeliverymanLocationViewModel, input);

    return this.customResponse<IDeliverymanLocationViewModel>(output);
  }

  @Security("api_key")
  @httpPut("/location")
  @Put("/location")
  public async updateDeliverymanLocation(@Request() req: express.Request, @Body() body: ILocationViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const location = req.body;

    const response = await this.deliverymanService.updateDeliverymanLocation(this.userContext.userId, location);

    return this.customResponse<boolean>(response);


  }

  @httpGet("/check-pix/:userId")
  @Get("/check-pix/{userId}")
  public async getCheckPix(@Request() req: express.Request){
    const {userId} = req.params;
    const hasPixKey = await this.deliverymanService.getCheckPixKey(userId)
    
    return this.customResponse<boolean>(hasPixKey)
  }
}
