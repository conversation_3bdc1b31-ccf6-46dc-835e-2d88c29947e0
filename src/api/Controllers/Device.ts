/* eslint-disable @typescript-eslint/no-unused-vars */
import { ELanguageOptions } from "@prisma/client";
import express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { IRegisterDeviceViewModel } from "src/api/ViewModels/Device/IRegisterDevice";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IDeviceService } from "src/business/Interfaces/Service/IDevice";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Path, Post, Put, Request, Route, Security, Tags } from "tsoa";

@controller("/device")
@Route("/device")
@Tags("Device")
export class DeviceController extends BaseController {
  constructor(
    @inject(TOKENS.IDeviceService) private deviceService: IDeviceService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
    this.deviceService = deviceService;
  }

  @Security("api_key")
  @httpPost("/register-notification")
  @Post("/register-notification")
  public async registerNotificationService(@Request() req: express.Request, @Body() body: IRegisterDeviceViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const {
      body: { deviceToken, deviceUniqueId, deviceManufacturer },
    } = req;

    const { userId } = this.userContext;

    let response: boolean | null = null;

    if (userId && deviceToken && deviceUniqueId && deviceManufacturer) {
      response = await this.deviceService.registerNotificationService(
        userId,
        deviceToken,
        deviceUniqueId,
        deviceManufacturer,
      );
    }

    return this.customResponse(response);
  }

  // TODO Test with real device
  @Security("api_key")
  @httpGet("/notification-endpoint-attributes/:deviceUniqueId")
  @Get("/notification-endpoint-attributes/:deviceId")
  public async getNotificationEndpointAttributes(@Request() req: express.Request, @Path() deviceId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { deviceUniqueId } = req.params;

    const response = await this.deviceService.getNotificationEndpointAttributes(
      this.userContext.userId,
      deviceUniqueId!,
    );

    return this.customResponse<Record<string, string>>(response);
  }

  // TODO Test
  @Security("api_key")
  @httpPut("/disable-device/:deviceId")
  @Put("/disable-device/{deviceId}")
  public async getUserDevice(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { deviceId } = req.params;

    const response = await this.deviceService.disableUserDevice(this.userContext.userId, deviceId);

    return this.customResponse<boolean>(response);
  }

  // TODO Test
  @httpGet("/device-language/:id")
  @Get("/device-language/{deviceId}")
  public async getDeviceLanguage(@Request() req: express.Request, @Path() deviceId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const language = await this.deviceService.getDeviceLanguage(this.userContext.userId, id);

    return this.customResponse<ELanguageOptions>(language);
  }

  @httpPut("/device-language/:id")
  @Put("/device-language/{deviceId}")
  public async updateDeviceLanguage(
    @Request() req: express.Request,
    @Path() deviceId: string,
    @Body() data: { language: ELanguageOptions },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const { language } = req.body;

    const response = await this.deviceService.updateDeviceLanguage(id, language);

    return this.customResponse<boolean>(response);
  }

  @httpPut("/default-profile/:id")
  @Put("/default-profile/{deviceId}")
  public async updateDeviceDefaultProfile(
    @Request() req: express.Request,
    @Path() deviceId: string,
    @Body() data: { profile: EProfile },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const { profile } = req.body;
    const response = await this.deviceService.updateDeviceDefaultProfile(id, profile);

    return this.customResponse<boolean>(response);
  }
}
