/* eslint-disable @typescript-eslint/no-unused-vars */
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, httpPut } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICardViewModel } from "src/api/ViewModels/Card/ICard";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { CardMapper } from "src/business/Configs/Automapper/Profile/Card";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { ICardService } from "src/business/Interfaces/Service/ICard";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Path, Post, Put, Request, Route, Security, Tags } from "tsoa";

@controller("/card")
@Route("card")
@Tags("Card")
export class CardController extends BaseController {
  constructor(
    @inject(TOKENS.ICardService) private cardService: ICardService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  // TODO Testar com usuário
  @Security("api_key")
  @httpGet("/by_user/")
  @Get("by_user/")
  public async getCardByUserId(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const cards: ICardViewModel[] | null = await this.cardService.getCardsByUserId(this.userContext.userId);
    return this.customResponse<ICardViewModel[]>(cards);
  }

  @Security("api_key")
  @httpGet("/:id")
  @Get("{cardId}")
  public async get(@Request() req: express.Request, @Path() cardId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const card: ICardViewModel | null = await this.cardService.getById(id);

    return this.customResponse<ICardViewModel>(card);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() card: ICardViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const cardData: ICardViewModel = req.body;

    const result = mapper.map(CardMapper.ICreateCardViewModelToICard, cardData);

    const cardCreated = await this.cardService.create(result);

    return this.customResponse<boolean>(!!cardCreated);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("")
  public async update(@Request() req: express.Request, @Body() card: ICardViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const newCard = req.body;

    const mapped = mapper.map(CardMapper.IUpdateCardViewModelToICard, newCard);

    const result = await this.cardService.update(mapped);

    return this.customResponse<boolean>(result);
  }
}
