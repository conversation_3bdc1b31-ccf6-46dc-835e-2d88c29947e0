/* eslint-disable prefer-const */
import { inject } from "inversify";
import express from "express";
import { controller, httpDelete, httpGet, httpPost } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IMailingService } from "src/business/Interfaces/Service/IMailing";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Delete, Get, Path, Post, Query, Request, Route, Security, Tags } from "tsoa";
import { ICreateMailingViewModel } from "src/api/ViewModels/Mailing/ICreateMailing";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { MailingMapper } from "src/business/Configs/Automapper/Profile/Mailing";
import { IMailingViewModel } from "src/api/ViewModels/Mailing/IViewModel";
import { IOrder } from "src/business/DTOs/Order";

@controller("/mailing")
@Route("mailing")
@Tags("Mailing")
export class MailingController extends BaseController {
  constructor(
    @inject(TOKENS.IMailingService)
    private MailingService: IMailingService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpGet("/paged/")
  @Get("/paged/")
  public async getAllMailingPaged(
    @Request() req: express.Request,
    @Query() currentPage: string,
    @Query() pageSize: string,
    @Query() filterValue?: string,    
    @Query() orderBy?: string,
    @Query() sortDirection?: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      currentPage: string;
      pageSize: string;      
      filterValue: string;      
      orderBy: string;
      sortDirection: IOrder;
    };

    const includes = {
      filtersMailing: true
    }

    const {data} = await this.MailingService.getPaged( 
      "title,messageContent",   
      queryData.filterValue,    
      queryData.currentPage,
      queryData.pageSize,                
      queryData.orderBy,
      queryData.sortDirection,
      includes
    );

    return this.customResponse<{
      result: IMailingViewModel[],
      totalCount: number;
      totalPages: number;
    }>({
      result: mapper.map(MailingMapper.IMailingToIMailingViewModel, data.result),
      totalCount: data.totalCount,
      totalPages: data.totalPages
    });
  }

  @Security("api_key")
  @httpGet("/details/:id")
  @Get("/details/{MailingId}")
  public async getMailingDetails(@Request() req: express.Request, @Path() MailingId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const data = await this.MailingService.getMailingDetailsById(id);

    const response = mapper.map(MailingMapper.IMailingToIMailingViewModel, data);

    return this.customResponse<IMailingViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/execute/:id")
  @Get("/execute/{MailingId}")
  public async executeMailing(@Request() req: express.Request, @Path() MailingId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    if (id) {
      const response = await this.MailingService.executeMailing(id);

      return this.customResponse(response);
    }

    return this.customResponse(false);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async createMailing(@Request() req: express.Request, @Body() body: ICreateMailingViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const Mailing: ICreateMailingViewModel = {
      ...req.body,
      filtersMailing: {
        ...req.body.filtersMailing,
        birthDayEndDate: req.body.filtersMailing.birthDayEndDate ? new Date(req.body.filtersMailing.birthDayEndDate) : undefined,
        birthDayStartDate: req.body.filtersMailing.birthDayStartDate ? new Date(req.body.filtersMailing.birthDayStartDate) : undefined,
      }
    }

    const MailingMapped = mapper.map(MailingMapper.ICreateMailingViewModelToIMailing, Mailing);

    const response = await this.MailingService.create(MailingMapped);

    return this.customResponse(response);
  }

  @Security("api_key")
  @httpDelete("/:id")
  @Delete("{MailingId}")
  public async delete(@Request() req: express.Request, @Path() MailingId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const response = await this.MailingService.delete(id);

    return this.customResponse(response);
  }
}
