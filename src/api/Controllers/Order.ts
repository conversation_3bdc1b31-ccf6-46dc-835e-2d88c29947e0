import { IOrderDetailsViewModel } from "src/api/ViewModels/Order/IOrderDetails";
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Xlsx } from "exceljs";
import * as express from "express";
import { inject } from "inversify";
import { controller, httpGet, httpPost, httpPut, interfaces } from "inversify-express-utils";
import { BaseController } from "src/api/Controllers/Base";
import { ICreateOrderViewModel } from "src/api/ViewModels/Order/ICreateOrder";
import { ICreateOrderResultViewModel } from "src/api/ViewModels/Order/ICreateOrderResult";
import { IDeliverymanSalesViewModel } from "src/api/ViewModels/Order/IDeliverymanSales";
import { IOrderFinancialConsolidation } from "src/api/ViewModels/Order/IFinancialConsolidation";
import { IFinancialDetailsViewModel } from "src/api/ViewModels/Order/IFinancialConsolidationDetails";
import { IListOrderViewModel } from "src/api/ViewModels/Order/IListOrder";
import { ISalesTotalizerViewModel } from "src/api/ViewModels/Order/ISalesTotalizer";
import { IShopkeeperSalesViewModel } from "src/api/ViewModels/Sales/IShopkeeperSales";
import { mapper } from "src/business/Configs/Automapper/Mapper";
import { OrderMapper } from "src/business/Configs/Automapper/Profile/Order";
import TOKENS from "src/business/Configs/Inversify/Tokens";
import { IOrder } from "src/business/DTOs/Order";
import { EFinancialConsolidation } from "src/business/Enums/EFinancialConsolidation";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";
import { EProfile } from "src/business/Enums/Models/EProfile";
import { IChatService } from "src/business/Interfaces/Service/IChat";
import { IOrderService } from "src/business/Interfaces/Service/IOrder";
import { INotificationManager } from "src/business/Interfaces/Tools/INotificationManager";
import { Body, Get, Path, Post, Put, Query, Request, Route, Security, Tags } from "tsoa";
import { set } from "date-fns";
import { IListOrderByStatusViewModel } from "src/api/ViewModels/Order/IListOrderByStatus";
import { IListUserOrdersViewModel } from "src/api/ViewModels/Order/IListUserOrders";
import { IOrderDetailsBackOfficeViewModel } from "src/api/ViewModels/Order/IOrderDetailsBackOffice";
import { IUpdateOrderViewModel } from "src/api/ViewModels/Order/IUpdateOrder";

@controller("/order")
@Route("order")
@Tags("Order")
export class OrderController extends BaseController implements interfaces.Controller {
  constructor(
    @inject(TOKENS.IOrderService)
    private orderService: IOrderService,
    @inject(TOKENS.IChatService)
    private chatService: IChatService,
    @inject(TOKENS.NotificationManager)
    notificationManager: INotificationManager,
  ) {
    super(notificationManager);
  }

  @Security("api_key")
  @httpPost("/")
  @Post("/")
  public async create(@Request() req: express.Request, @Body() body: ICreateOrderViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const data = req.body;

    const response = await this.orderService.create(data);

    const result = mapper.map(OrderMapper.ICreateOrderResultDTOToICreateOrderResultViewModel, response);

    return this.customResponse<ICreateOrderResultViewModel>(result);
  }

  // TODO Test
  @Security("api_key")
  @httpPut("/customer-code/:id")
  @Put("/customer-code/{orderId}")
  public async update(
    @Request() req: express.Request,
    @Path() orderId: string,
    @Body() body: { customerCode: string },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const { customerCode } = req.body;

    const response = await this.orderService.updateCustomerCode(id, customerCode);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpPut("/deliveryman/:id")
  @Put("/deliveryman/{orderId}")
  public async updateDeliverymanId(@Request() req: express.Request, @Path() orderId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const queryData = req.query as unknown as {
      userId: string;
    };

    const response = await this.orderService.updateDeliverymanId(id, queryData.userId);

    return this.customResponse<boolean>(response);
  }

  @Security("api_key")
  @httpGet("/by-status")
  @Get("/by-status")
  public async getByStatus(
    @Request() req: express.Request,
    @Query() statusValue: EOrderStatusValue,
    @Query() storeId: string,
    @Query() pageSize: string,
    @Query() page: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      storeId: string;
      statusValue?: EOrderStatusValue;
      page: string;
      pageSize: string;
    };

    const { result, totalCount, totalPages } = await this.orderService.getOrdersByStatus(
      queryData.storeId,
      queryData.page,
      queryData.pageSize,
      queryData.statusValue,
    );

    return this.customResponse<{
      result: IListOrderByStatusViewModel[];
      totalCount: number;
      totalPages: number;
    }>({
      result: mapper.map(OrderMapper.IOrderByStatusDTOToIListOrderViewModel, result),
      totalCount,
      totalPages,
    });
  }

  @Security("api_key")
  @httpGet("/by-deliveryman-status")
  @Get("/by-deliveryman-status")
  public async getByStatusAndByDeliverymanId(
    @Request() req: express.Request,
    @Query() statusValue: EOrderStatusValue,
    @Query() userId: string,
    @Query() pageSize: string,
    @Query() page: string,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      statusValue?: EOrderStatusValue;
      userId: string;
      page: string;
      pageSize: string;
    };

    const { id } = req.params;

    const { result, totalCount, totalPages } = await this.orderService.getOrdersByStatusAndDeliverymanId(
      queryData.userId,
      queryData.page,
      queryData.pageSize,
      queryData.statusValue,
    );

    // const response = mapper.map(OrderMapper.IOrderToIListOrderViewModel, orders);

    return this.customResponse<{
      result: IListOrderByStatusViewModel[];
      totalCount: number;
      totalPages: number;
    }>({
      result: mapper.map(OrderMapper.IOrderByStatusDTOToIListOrderViewModel, result),
      totalCount,
      totalPages,
    });

    // return this.customResponse<IListOrderViewModel[]>(response);
  }

  @Security("api_key")
  @httpGet("/by-user")
  @Get("/by-user")
  public async getOrdersByUserId(
    @Request() req: express.Request,
    @Query() userId: string,
    @Query() page: number,
    @Query() pageSize: number,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      userId: string;
      page: string;
      pageSize: string;
    };

    const { result, totalCount, totalPages } = await this.orderService.getOrdersByUserId(
      queryData.userId,
      Number(queryData.page),
      Number(queryData.pageSize),
    );

    const response = {
      result: mapper.map(OrderMapper.IUserOrdersDTOToIListOrderViewModel, result),
      totalCount,
      totalPages,
    };

    return this.customResponse<{
      result: IListUserOrdersViewModel[];
      totalCount: number;
      totalPages: number;
    }>(response);
  }

  // TODO Test
  @Security("api_key")
  @httpGet("/validate-customer-code/:id")
  @Get("/validate-customer-code/{orderId}")
  public async validateCustomerCode(@Request() req: express.Request, @Query() customerCode: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const queryData = req.query as unknown as {
      customerCode: string;
    };

    const response = await this.orderService.validateCustomerCode(id, queryData.customerCode);

    return this.customResponse<boolean>(response);
  }

  // TODO Test
  @Security("api_key")
  @httpGet("/paged-filtered-by-store")
  @Get("/paged-filtered-by-store")
  public async getOrders(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const query = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      startDateFilter: Date;
      endDateFilter: Date;
      filterValue: string;
      storeFilter: string;
      delivered: string;
      orderBy: string;
      sortDirection: IOrder;
    };

    const { result, totalCount, totalPages } = await this.orderService.getOrdersPaged(
      query.currentPage,
      query.pageSize,
      query.startDateFilter,
      query.endDateFilter,
      query.filterValue,
      query.storeFilter,
      query.delivered === "true",
      query.orderBy,
      query.sortDirection,
    );

    return this.customResponse<{
      result: IListOrderViewModel[];
      totalCount: number;
      totalPages: number;
    }>({
      result: mapper.map(OrderMapper.IOrderToIListOrderViewModel, result),
      totalCount,
      totalPages,
    });
  }

  @Security("api_key")
  @httpPost("/cancel/:id")
  @Post("/cancel/{orderId}")
  public async cancel(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const orderCancel = await this.orderService.cancelOrder(id);

    return this.customResponse<boolean>(orderCancel);
  }

  // TODO Test
  @Security("api_key")
  @httpGet("/report/")
  @Get("/report")
  public async getSalesReport(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const saleReported = await this.orderService.createOrderReportFile();

    this.httpContext.response.set({
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",

      "Access-Control-Allow-Origin": process.env.CORS_ORIGIN,
      "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept",
    });

    if (saleReported) {
      const xlsxBuffer = await saleReported.writeBuffer();
      const buff = Buffer.from(xlsxBuffer);
      const xlsx64 = buff.toString("base64");
      this.httpContext.response.write(`data: ${xlsx64}\n\n`);
      this.httpContext.response.send();
    }

    return this.customResponse<Xlsx>(saleReported);
  }

  // NOTE Not used
  // @httpGet("/paged/")
  // @Get("/paged/")
  // public async getAllFilteredPaginated(
  //   @Request() req: express.Request,
  //   @Query() page: number,
  //   @Query() pageSize: number,
  //   @Query() columnFilter: string,
  //   @Query() filterOperator: string,
  //   @Query() filterValue: string,
  // ) {
  //   const queryData = req.query as unknown as {
  //     columnFilter: string;
  //     filterValue: string;
  //     currentPage: string;
  //     pageSize: string;
  //     orderBy: string;
  //     sortDirection: IOrder;
  //   };

  //   const order = await this.orderService.getPaged(
  //     queryData.columnFilter,
  //     queryData.filterValue,
  //     queryData.currentPage,
  //     queryData.pageSize,
  //     { field: queryData.orderBy, order: queryData.sortDirection },
  //     [
  //       { name: "address", alias: "address" },
  //       { name: "entity.user", alias: "user" },
  //       { name: "entity.store", alias: "store" },
  //       { name: "entity.deliveryman", alias: "deliveryman" },
  //     ],
  //   );

  //   let responseMapped: PagedResult<OrderListFrontViewModel> | null = null;

  //   if (order) {
  //     const orderDataMapped: OrderListFrontViewModel[] = mapper.mapArray(order.result, Order, OrderListFrontViewModel);

  //   return this.customResponse<{
  //     result: OrderListFrontViewModel[];
  //     totalCount: number;
  //     totalPages: number;
  //   }>({
  //     result: ordersMapped,
  //     totalCount: orders.totalCount,
  //     totalPages: orders.totalPages,
  //   });
  // }

  @Security("api_key")
  @httpGet("/chat-messages/:id")
  @Get("/chat-messages/{orderId}")
  public async chat(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const orderChat = await this.chatService.getChatMessages(id);

    return this.customResponse(orderChat);
  }

  @Security("api_key")
  @httpPut("/deliveryman-route-length/:id")
  @Put("/deliveryman-route-length/{orderId}")
  public async updateDeliverymanRouteLength(
    @Request() req: express.Request,
    @Path() orderId: string,
    @Body() order: { routeLength: number },
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const { routeLength } = req.body;
    const result = await this.orderService.updateDeliverymanRouteLength(id, routeLength);

    return this.customResponse<boolean>(result);
  }

  @Security("api_key")
  @httpGet("/financial-consolidation")
  @Get("/financial-consolidation")
  public async getFinancialConsolidation(@Request() req: express.Request) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const query = req.query as unknown as {
      currentPage: string;
      pageSize: string;
      startDateFilter: Date;
      endDateFilter: Date;
      filterValue?: string;
      orderBy?: string;
      sortDirection?: string;
      statusFilter?: EFinancialConsolidation;
    };

    const { result, totalCount, totalPages, totalizer } = await this.orderService.getFinancialConsolidationPaged(
      query.currentPage,
      query.pageSize,
      query.startDateFilter,
      query.endDateFilter,
      query.filterValue,
      query.orderBy,
      query.sortDirection,
      query.statusFilter,
    );

    return this.customResponse<{
      result: IOrderFinancialConsolidation[];
      totalCount: number;
      totalPages: number;
      totalizer: typeof totalizer;
    }>({
      result: mapper.map(OrderMapper.IOrderToIOrderFinancialConsolidation, result),
      totalCount,
      totalPages,
      totalizer,
    });
  }

  @Security("api_key")
  @httpGet("/order-details/:id")
  @Get("/order-details/{orderId}")
  public async getOrderDetailsById(@Request() req: express.Request, @Path() orderId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result = await this.orderService.getOrderDetailsById(id);

    const response = mapper.map(OrderMapper.IOrderDetailsDTOToIOrderDetailsViewModel, result);

    return this.customResponse<IOrderDetailsViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/financial-consolidation-details/:id")
  @Get("/financial-consolidation-details/{orderId}")
  public async getFinancialConsolidationOrder(@Request() req: express.Request, @Path() orderId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;

    const result = await this.orderService.getOneFinancialConsolidationOrder(id);
    const mappedResult = mapper.map(OrderMapper.IOrderToIFinancialDetailsViewModel, result);

    return this.customResponse<IFinancialDetailsViewModel>(mappedResult);
  }

  @Security("api_key")
  @httpGet("/deliveryman-shopkeeper-sales/:id")
  @Get("/deliveryman-shopkeeper-sales/{id}")
  public async getDeliverymanAndShopkeeperSalesById(
    @Request() req: express.Request,
    @Query() startDate: Date,
    @Query() endDate: Date,
    @Query() pageSize: string,
    @Query() page: string,
    @Query() profile: EProfile,
    @Query() statusValue: EOrderStatusValue,
  ) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const queryData = req.query as unknown as {
      statusValue?: EOrderStatusValue;
      startDate: Date;
      endDate: Date;
      page: string;
      pageSize: string;
      profile: EProfile;
    };

    const { id } = req.params;

    const { result, totalCount, totalPages, totalizer } = await this.orderService.getDeliverymanAndShopkeeperSales(
      id,
      set(new Date(queryData.startDate), { hours: 0, minutes: 0, seconds: 0 }),
      set(new Date(queryData.endDate), { hours: 23, minutes: 59, seconds: 59 }),
      queryData.page,
      queryData.pageSize,
      queryData.profile,
      queryData.statusValue,
    );

    let formattedTotalizers: ISalesTotalizerViewModel[] = [];
    if (totalizer) {
      formattedTotalizers = mapper.map(
        OrderMapper.ITotalizerToISalesTotalizerViewModel,
        Object.entries(totalizer).map(([key, value]) => ({ [key]: value })),
      );
    }

    const mappedResult =
      queryData.profile === EProfile.deliveryman
        ? mapper.map(OrderMapper.IOrderSalesDTOToIDeliverymanSalesViewModel, result)
        : mapper.map(OrderMapper.IOrderSalesDTOToIShopkeeperSalesViewModel, result);

    return this.customResponse<{
      result: IShopkeeperSalesViewModel[] | IDeliverymanSalesViewModel[];
      totalCount: number;
      totalPages: number;
      totalizer?: ISalesTotalizerViewModel[];
    }>({
      result: mappedResult,
      totalCount,
      totalPages,
      totalizer: formattedTotalizers,
    });
  }

  @Security("api_key")
  @httpGet("/details/:id")
  @Get("/details/{orderId}")
  public async getOrderBackOfficeDetails(@Request() req: express.Request, @Path() orderId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const { isSales }: { isSales?: boolean } = req.query;

    const result = await this.orderService.getOrderBackOfficeDetails(id, isSales);

    const response = mapper.map(OrderMapper.IOrderDetailsBackOfficeDTOToIOrderDetailsBackOfficeViewModel, result);

    return this.customResponse<IOrderDetailsBackOfficeViewModel>(response);
  }

  @Security("api_key")
  @httpGet("/last-order/:id")
  @Get("/last-order/{userId}")
  public async getLastOrderIdByUserIdAndStoreId(@Request() req: express.Request, @Path() userId: string) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

    const { id } = req.params;
    const { storeId } = req.body;

    const result = await this.orderService.getLastOrderIdByUserIdAndStoreId(id, storeId);

    return this.customResponse<string>(result);
  }

  @Security("api_key")
  @httpPut("/")
  @Put("/")
  public async updateOrderOnFailTransaction(@Request() req: express.Request, @Body() body: IUpdateOrderViewModel) {
    const authenticateResponse = await this.authenticateRequest(req);
    if (!authenticateResponse) return this.customResponse(undefined, undefined, 401); 

    
    const data = req.body;

    const result = await this.orderService.updateOrderOnFailTransaction(data as IUpdateOrderViewModel );

    return this.customResponse<any>(result);
  }
  
}
