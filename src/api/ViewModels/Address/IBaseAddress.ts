import { EAddress } from "@prisma/client";
import { IBaseViewModel } from "src/api/ViewModels/Address/IBase";
import { ICreateUserAddressViewModel } from "src/api/ViewModels/Address/ICreateUserAddress";

export interface IBaseAddressViewModel extends IBaseViewModel {
  id: string;
  street: string;
  number?: string;
  isDefault?: boolean;
  complement?: string;
  district: string;
  country: string;
  city: string;
  state: string;
  postcode: string;
  type: EAddress;
  nickname?: string;
  latitude: number;
  longitude: number;
  userAddress?: ICreateUserAddressViewModel[];
  //userId?: string; 
}
