import { IListOrderItemViewModel } from "src/api/ViewModels/Order/IListOrderItem";
import { IListOrderStatusViewModel } from "src/api/ViewModels/Order/IListOrderStatus";

export interface IOrderDetailsViewModel {
  id: string;
  code: number;
  totalPrice: number;
  shippingPrice: number;
  customerCode: string | null;
  estimatedDeliveryTime: number;
  orderStatus: IListOrderStatusViewModel[];
  user: {
    firstName: string;
    lastName: string;
  };
  deliveryman: {
    firstName?: string;
    lastName?: string;
    rating?: string;
  };
  address: {
    nickname: string | null;
    street: string;
    number: string | null;
    district: string;
    city: string;
    state: string;
    complement: string | null;
  } | null;
  store: {
    name: string;
  };
  orderItem: IListOrderItemViewModel[];
}
