import { IListDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IListDeliveryman";
import { IListOrderStatusViewModel } from "src/api/ViewModels/Order/IListOrderStatus";
import { IUserViewModel } from "src/api/ViewModels/User/IUser";

export interface IListOrderViewModel {
  id: string;
  code: number;
  storeId: string;
  totalPrice: number;
  price: number;
  createdAt: Date;
  orderStatus?: IListOrderStatusViewModel[];
  store: { id: string; name: string };
  deliveryman?: IListDeliverymanViewModel;
  user?: IUserViewModel;
}

// export class OrderListViewModel {
//   id: string;

//   code: number;

//   storeId: string;

//   store: StoreViewModel;

//   address: AddressViewModel;

//   totalPrice: number;

//   quantityItems: number;

//   price: number;

//   quantityProducts: number;

//   shippingPrice: number;

//   orderStatus?: OrderStatusListViewModel[];

//    orderItem?: OrderItemListViewModel[];

//   rating?: number;

//   createdAt: Date;
// }
