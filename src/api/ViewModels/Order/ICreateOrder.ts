import { ICreateOrderItemViewModel } from "src/api/ViewModels/Order/ICreateOrderItem";
import { IOrderStatusViewModel } from "src/api/ViewModels/Order/IOrderStatus";
import { ICreateTransactionViewModel } from "src/api/ViewModels/Transaction/ICreate";

export interface ICreateOrderViewModel {
  userId: string;
  storeId: string;
  addressId: string;
  deliverymanId?: string;
  price: number;
  totalPrice: number;
  quantityItems: number;
  quantityProducts: number;
  estimatedDeliveryTime: number;
  shippingPrice: number;
  orderItem: ICreateOrderItemViewModel[];
  orderStatus: IOrderStatusViewModel[];
  orderTransactionData: ICreateTransactionViewModel;
}
