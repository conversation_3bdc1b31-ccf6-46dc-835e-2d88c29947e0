import { IUserDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IUserDeliveryman";
import { IOrderFinancialConsolidation } from "src/api/ViewModels/Order/IFinancialConsolidation";
import { IStoreViewModel } from "src/api/ViewModels/Store/IViewModel";

export interface IFinancialDetailsViewModel {
  financial: IOrderFinancialConsolidation;
  store: IStoreViewModel;
  deliveryman: IUserDeliverymanViewModel;
}
