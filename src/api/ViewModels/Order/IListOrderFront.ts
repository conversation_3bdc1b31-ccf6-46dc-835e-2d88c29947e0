import { IListAddressViewModel } from "src/api/ViewModels/Address/IList";
import { IListDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IListDeliveryman";
import { IListOrderItemViewModel } from "src/api/ViewModels/Order/IListOrderItem";
import { IStoreViewModel } from "src/api/ViewModels/Store/IViewModel";
import { IUserViewModel } from "src/api/ViewModels/User/IUser";
import { EOrderStatusValue } from "src/business/Enums/Models/EOrderStatusValue";

export interface IListOrderFrontViewModel {
  id: string;
  user: IUserViewModel;
  store: IStoreViewModel;
  address: IListAddressViewModel;
  deliveryman: IListDeliverymanViewModel;
  price: number;
  totalPrice: number;
  quantityItems: number;
  quantityProducts: number;
  shippingPrice: number;
  createdAt: Date;
  // FIXME
  orderStatus: EOrderStatusValue;
  code: number;
  orderItem?: IListOrderItemViewModel[];
}
