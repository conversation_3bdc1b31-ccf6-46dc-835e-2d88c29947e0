import { IBaseOrderItemViewModel } from "src/api/ViewModels/Order/IBaseOrderItem";
import { IProductListViewModel } from "src/api/ViewModels/Product/IList";

export interface IListOrderItemViewModel extends IBaseOrderItemViewModel {
  productName: string;
  storeId: string;
  product: IProductListViewModel;
  // attributeOptions?: AttributeOptionListViewModel[];
}

// export class OrderItemListViewModel {
//   orderId: string;

//   product: ProductViewModel;

//   attribute?: AttributeViewModel[];

//   attributeOptions?: AttributeOptionListViewModel[];

//   quantity: number;

//   totalPrice: number;

//   unityPrice: number;

//   observation?: string;
// }
