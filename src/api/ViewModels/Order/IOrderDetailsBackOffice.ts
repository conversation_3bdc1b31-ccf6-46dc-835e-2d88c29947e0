import { IListOrderStatusViewModel } from "src/api/ViewModels/Order/IListOrderStatus";

export interface IOrderDetailsBackOfficeViewModel {
  id: string;
  code: number;
  totalPrice: number;
  price: number;
  shippingPrice: number;
  orderStatus: IListOrderStatusViewModel[];
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  deliveryman: {
    id: string;
    firstName: string;
    lastName: string;
    cpf: string;
  };
  address: {
    street: string;
    number: string | null;
    district: string;
    city: string;
    state: string;
    complement: string | null;
    postcode: string;
    country: string;
  } | null;
  store: {
    id: string;
    name: string;
    cnpj: string;
    phone: string;
    email: string;
  };
}
