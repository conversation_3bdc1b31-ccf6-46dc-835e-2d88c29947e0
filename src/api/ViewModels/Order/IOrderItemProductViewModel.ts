import { IAddress } from "src/business/Interfaces/Prisma/IAddress";

export interface SimpleProduct {
  id: string;
  name: string;
}

export interface ItemProduct {
  id: string;
  quantity: number;
  unityPrice: number;
  totalPrice: number;
  product: SimpleProduct;
}

export interface IOrderItemProductViewModel {
  id: string;

  code: number;

  storeId: string;

  storeName: string;

  price: number;

  shippingPrice: number;

  totalPrice: number;

  quantityItems: number;

  quantityProducts: number;

  // FIXME Usar viewmodel
  address: IAddress;

  orderItems: ItemProduct[];
}
