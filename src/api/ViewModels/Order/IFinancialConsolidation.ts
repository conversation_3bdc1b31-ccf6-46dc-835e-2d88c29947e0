import { EFinancialTransactions } from "@prisma/client";

export interface IOrderFinancialConsolidation {
  id: string;
  status: EFinancialTransactions;
  totalPrice: number;
  price?: number;
  shippingPrice?: number;
  paymentType: string;
  paymentRate: number;
  storeName?: string;
  firstName?: string;
  cooperativeRate: number;
  financeCompanyPayment?: number;
  financeCompanyId?: string;
  financeCompanyStore?: string;
  createdAt: Date;
}
