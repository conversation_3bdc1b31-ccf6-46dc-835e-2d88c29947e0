import { IListAddressViewModel } from "src/api/ViewModels/Address/IList";
import { ICategoryViewModel } from "src/api/ViewModels/Category/ICategory";
import { IBaseViewModel } from "src/api/ViewModels/Store/IBase";
import { IStoreHoursViewModel } from "src/api/ViewModels/StoreHours/IViewModel";
import { IStoreModerationViewModel } from "src/api/ViewModels/StoreModeration/IViewModel";
import { IStoreSettingsViewModel } from "src/api/ViewModels/StoreSettings/IViewModel";
import { IStoreUserViewModel } from "src/api/ViewModels/StoreUser/IViewModel";

export interface IStoreViewModel extends IBaseViewModel {
  id: string;
  storeCategory?: ICategoryViewModel[];
  address?: IListAddressViewModel;
  storeUsers?: IStoreUserViewModel[];
  storeHours?: IStoreHoursViewModel;
  storeSettings?: IStoreSettingsViewModel;
  attachments?: { id: string }[];
  storeModeration?: IStoreModerationViewModel;
  storeReviewAverage?: string;
  userFavoriteStoreId?: string;
}
