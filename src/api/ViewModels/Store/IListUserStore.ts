import { EStoreModeratorStatus } from "@prisma/client";
import { IStoreHoursViewModel } from "src/api/ViewModels/StoreHours/IViewModel";

export interface IListUserStoreViewModel {
  id: string;
  name: string;
  phone: string;
  cnpj: string;
  email: string;
  active: boolean;
  activeByAdmin: boolean;
  open: boolean;
  iconUrl?: string;
  addressId?: string;
  storeHours?: IStoreHoursViewModel;
  owner: boolean;
  status: EStoreModeratorStatus;
  storeUserId: string;
  pixKey?: string | null
}
