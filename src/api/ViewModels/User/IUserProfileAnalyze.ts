import { EProfileStatus } from "@prisma/client";
import { IProfileViewModel } from "src/api/ViewModels/Profile/IProfile";

export interface IUserProfileAnalyzeViewModel {
  id: string;
  firstName: string;
  lastName: string;
  cpf: string;
  email: string;
  phone: string;
  profiles: IProfileViewModel[];
  deleted: boolean;
  disabled: boolean;
  status: {
    deliveryman?: EProfileStatus;
    shopkeeper?: EProfileStatus;
  };
  isDefaultAdmin?: boolean;
}
