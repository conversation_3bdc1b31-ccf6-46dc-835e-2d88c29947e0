import { IClientViewModel } from "src/api/ViewModels/Client/IClient";
import { IDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/IDeliveryman";
import { IListFileViewModel } from "src/api/ViewModels/File/IList";
import { IProfileViewModel } from "src/api/ViewModels/Profile/IProfile";
import { IShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/IShopkeeper";

export interface IUserWithProfileDataViewModel {
  id: string;
  client?: IClientViewModel;
  deliveryman?: IDeliverymanViewModel;
  shopkeeper?: IShopkeeperViewModel;
  profiles?: IProfileViewModel[];
  files?: IListFileViewModel[];
}
