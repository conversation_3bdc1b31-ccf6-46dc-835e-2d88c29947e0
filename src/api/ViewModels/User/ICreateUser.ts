import { ICreateAddressViewModel } from "src/api/ViewModels/Address/ICreate";
import { CreateClientViewModel } from "src/api/ViewModels/Client/ICreateClient";
import { ICreateDeliverymanViewModel } from "src/api/ViewModels/Deliveryman/ICreateDeliveryman";
import { CreateShopkeeperViewModel } from "src/api/ViewModels/Shopkeeper/ICreateShopkeeper";
import { IBaseUserViewModel } from "src/api/ViewModels/User/IBaseUser";

export interface ICreateUserViewModel extends IBaseUserViewModel {
  password: string;
  userProfiles?: { profileId: string }[];
  userPermissions?: { permissionId: string }[];
  client?: CreateClientViewModel;
  shopkeeper?: CreateShopkeeperViewModel;
  deliveryman?: ICreateDeliverymanViewModel;
  userAddress?: ICreateAddressViewModel;
}
