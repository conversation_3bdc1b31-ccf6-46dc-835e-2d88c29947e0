import { ChallengeNameType } from "@aws-sdk/client-cognito-identity-provider";

export interface ICreateUserWebViewModel {
  firstName: string;
  lastName: string;
  cpf: string;
  email: string;
  password: string;
  phone: string;
  dateOfBirth: string;
  authChallengeData: {
    ChallengeName: ChallengeNameType;
    ChallengeParameters: { [key: string]: string };
    ChallengeResponses?: { [key: string]: string };
    Session: string;
  };
}
