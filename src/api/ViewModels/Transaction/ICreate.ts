import { EPaymentMethod, ETransactionType } from "@prisma/client";

export interface ICreateTransactionViewModel {
  type: ETransactionType;
  paymentMethod: EPaymentMethod;
  amount: number;
  installments: number;
  cardHolder?: string;
  flag?: string;
  cardNumberLastDigits?: string;
  hashCardPaymentPlatform?: string;
  savedCard?: {
    cardId: string;
    securityCode: string;
    idCardPaymentPlatform?: string;
  };
  newCard?: {
    cardNumber: string;
    cardHolder: string;
    expiration: string;
    saveCard: boolean;
    lastDigits: string;
  };
}
