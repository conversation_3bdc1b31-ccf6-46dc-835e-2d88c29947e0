import { EPaymentMethod, ETransactionStatus, ETransactionType } from "@prisma/client";
import { ICardViewModel } from "src/api/ViewModels/Card/ICard";

export class ITransactionViewModel {
  id: string;

  transactionPlatformId?: string;

  type: ETransactionType;

  status: ETransactionStatus;

  paymentMethod: EPaymentMethod;

  amount: number;

  authorizationCode?: string;

  installments: number;

  statusDetail?: string;

  customerId?: string;

  chargeId?: string;

  createdAt: Date;

  updatedAt: Date;

  orderId: string;

  cards?: ICardViewModel[];
}
