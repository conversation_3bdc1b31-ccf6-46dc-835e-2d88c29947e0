import { EPayoutOwner, EPayoutStatus } from "@prisma/client";

export interface IPayoutAlertListViewModel {
  id: string;
  period: number;
  status: EPayoutStatus;
  payoutOwner: EPayoutOwner;
  storeName: string;
  storeCnpj: string;
  cooperativeName: string;
  cooperativeCnpj: string;
  deliverymanFirstName: string;
  deliverymanLastName: string;
  deliverymanCpf: string;
  transferValue: number;
  storeId?: string;
  userId?: string;
}
