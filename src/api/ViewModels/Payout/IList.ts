import { EPayoutOwner, EPayoutStatus } from "@prisma/client";

export interface IPayoutListViewModel {
  id: string,
  orderId: string,
  administrativeFeePercent: number,
  administrativeFeeValue: number,
  createdAt: Date,
  transferValue: number,
  statusDate: Date,
  status: EPayoutStatus,
  payoutOwner: EPayoutOwner,
  price: number,
  shippingPrice: number,
  totalPrice: number,
  storeName: string,
  storeCnpj: string,
  deliverymanFirstName: string,
  deliverymanLastName: string,
  deliverymanCpf: string,
}
  