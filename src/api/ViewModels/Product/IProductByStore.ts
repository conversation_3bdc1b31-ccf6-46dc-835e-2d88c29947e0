import { IFileViewModel } from "src/api/ViewModels/File/IFile";

export interface IProductByStoreViewModel {
  id: string;
  name: string;
  shortDescription: string;
  price: number;
  salePrice: number;
  storeId: string;
  preparationTime: number;
  sku: string;
  active: boolean;
  activeByAdmin: boolean;
  files?: IFileViewModel[] | null;
  isFavorite?: boolean;
  storeName: string;
  category: {
    name: string;
  }[];
}
