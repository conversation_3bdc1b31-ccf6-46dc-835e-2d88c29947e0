import { ICategoryViewModel } from "src/api/ViewModels/Category/ICategory";
import { IListFileViewModel } from "src/api/ViewModels/File/IList";
import { IUserFavoriteProductsViewModel } from "src/api/ViewModels/UserFavorite/IUserFavoriteProducts";
import { IStoreViewModel } from "src/api/ViewModels/Store/IViewModel";
import { ISubcategoryViewModel } from "src/api/ViewModels/Subcategory/ISubcategory";

export interface IFavoriteProductViewModel {
  id: string;
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  salePrice: number;
  sku: string;
  active: boolean;
  category?: ICategoryViewModel[];
  subcategory?: ISubcategoryViewModel[];
  userFavoriteProducts: IUserFavoriteProductsViewModel[];
  store: IStoreViewModel;
  icon?: IListFileViewModel;
}
