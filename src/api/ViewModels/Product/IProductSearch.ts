interface IProductSearchStoreAddressViewModel {
  latitude: string;
  longitude: string;
}

export interface IProductSearchStoreViewModel {
  id: string;
  name: string;
  isFavorite?: boolean;
  address: IProductSearchStoreAddressViewModel;
  iconUrl?: string;
  reviewAverage?: number;
}

export interface IProductSearchViewModel {
  id: string;
  name: string;
  description: string;
  price: string;
  salePrice: string;
  preparationTime: string;
  iconUrl?: string;
}
