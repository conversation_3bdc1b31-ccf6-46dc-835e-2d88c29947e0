import { IAttributeViewModel } from "src/api/ViewModels/Attribute/IAttribute";
import { ICreateCategorySubcategoryViewModel } from "src/api/ViewModels/Category/ICreateCategorySubcategory";
import { IBaseViewModel } from "src/api/ViewModels/Product/IBase";

export interface ICreateProductViewModel extends IBaseViewModel {
  category: ICreateCategorySubcategoryViewModel[];
  attribute?: IAttributeViewModel[];
  attachments?: { id: string }[];
}
