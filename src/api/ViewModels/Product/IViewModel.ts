import { IAttributeViewModel } from "src/api/ViewModels/Attribute/IAttribute";
import { ICategoryViewModel } from "src/api/ViewModels/Category/ICategory";
import { IListFileViewModel } from "src/api/ViewModels/File/IList";
import { IBaseViewModel } from "src/api/ViewModels/Product/IBase";
import { ProductModerationViewModel } from "src/api/ViewModels/ProductModeration.vm";
import { IStoreViewModel } from "src/api/ViewModels/Store/IViewModel";
import { ISubcategoryViewModel } from "src/api/ViewModels/Subcategory/ISubcategory";
import { IUserFavoriteProductsViewModel } from "src/api/ViewModels/UserFavorite/IUserFavoriteProducts";

export interface IProductViewModel extends IBaseViewModel {
  id: string;
  category: ICategoryViewModel[];
  subcategory: ISubcategoryViewModel[];
  userFavoriteProducts?: IUserFavoriteProductsViewModel[];
  attribute?: IAttributeViewModel[];
  storeName: string;
  files?: IListFileViewModel;
  moderation?: ProductModerationViewModel;
  store?: IStoreViewModel;
}
