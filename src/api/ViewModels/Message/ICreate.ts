import { EMessageType } from "@prisma/client";
import { ICreateMessageContentViewModel } from "src/api/ViewModels/Message/ICreateMessageContent";
import { EMessageSendingType } from "src/business/Enums/Models/Message/EMessageSendingType";

export interface ICreateMessageViewModel {
  title: string;
  type: EMessageType;
  sendingType?: EMessageSendingType[];
  senderId?: string;
  directMailId?: string;
  usersId: string[];
  profileId: string;
  content: ICreateMessageContentViewModel[];
}
