import { IBaseViewModel } from "src/api/ViewModels/Bordero/IBase";
import { IPayoutListViewModel } from "src/api/ViewModels/Payout/IList";
import { IStoreViewModel } from "src/api/ViewModels/Store/IViewModel";
import { IUserViewModel } from "src/api/ViewModels/User/IUser";

export interface IBorderoViewModel extends IBaseViewModel {
  id: string;
  user?: IUserViewModel;
  userAdmin: IUserViewModel;
  store?: IStoreViewModel;
  payout?: IPayoutListViewModel[];
}
