import { EBorderoStatus, EPayoutOwner } from "@prisma/client";
import { IFileViewModel } from "src/api/ViewModels/File/IFile";

interface IBorderoPaymentViewModel {
  id: string;
  userId?: string | null;
  storeId?: string | null;
  cooperativeId?: string | null;
  status: EBorderoStatus;
  payoutOwner: EPayoutOwner;
  createdAt: Date;
  quantityOrders: number;
  sumOrderValue: number;
  sumAdministrativeFeeValue: number;
  sumTransferValue: number;
  cooperative: {
    name: string;
    cnpj: string;
    pixKey?: string | null;
  } | null;
  user?: {
    firstName: string;
    lastName: string;
    cpf: string;
    pixKey?: string | null;
  } | null;
  store?: {
    name: string;
    cnpj: string;
    pixKey?: string | null;
  } | null;
  userAdmin?: {
    firstName: string;
    lastName: string;
  };
  files?: IFileViewModel[];
}

export default IBorderoPaymentViewModel;
