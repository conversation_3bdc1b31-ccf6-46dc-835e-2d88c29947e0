import { ICardViewModel } from "src/api/ViewModels/Card/ICard";
import { EPaymentMethod } from "src/business/Enums/Models/EPaymentMethod";
import { ETransactionStatus } from "src/business/Enums/Models/ETransactionStatus";
import { ETransactionType } from "src/business/Enums/Models/ETransactionType";

export class TransactionViewModel {
  id: string;

  transactionPlatformId?: string;

  type: ETransactionType;

  status: ETransactionStatus;

  paymentMethod: EPaymentMethod;

  amount: number;

  authorizationCode?: string;

  installments: number;

  statusDetail?: string;

  customerId?: string;

  chargeId?: string;

  createdAt: Date;

  updatedAt: Date;

  orderId: string;

  cards?: ICardViewModel[];
}
