import { Prisma } from "@prisma/client";

export const createCustomerProfileView = Prisma.sql`
  create or replace view customers_profile as
  select 
    row_number() over () as "id",
    o.id as "orderId",
    oi.id as "orderItemId",
    o."userId",
    o.price as "orderPrice",
    o."createdAt" as "orderTime",
    a.district as "orderDistrict",
    a."districtHash" as "orderDistrictHash",
    a.city as "orderCity",
    a."cityHash" as "orderCityHash",
    a.state as "orderState",
    a."stateHash" as "orderStateHash",
    o."shippingPrice",
    o."storeId",
    p.id as "productId",
    p."salePrice" as "productPrice",
    oi.quantity as "productQuantity",
    pc."categoryId",
    ps."subcategoryId"
  from
    orders o
  left join order_items oi on
    oi."orderId" = o.id
  left join addresses a on
    a.id = o."addressId"
  left join products p on
    p.id = oi."productId"
  left join products_categories pc on
    pc."productId" = p.id
  left join products_subcategories ps on
    ps."productId" = p.id
  left join users u on
    u.id = o."userId"
  where u."permanentlyDeleted"= false
`;
