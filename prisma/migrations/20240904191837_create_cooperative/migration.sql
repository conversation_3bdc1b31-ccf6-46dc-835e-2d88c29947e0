-- CreateTable
CREATE TABLE "cooperative" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" VARCHAR NOT NULL,
    "cnpj" VARCHAR NOT NULL,
    "email" VARCHAR NOT NULL,
    "pix<PERSON>ey" VARCHAR,
    "addressId" UUID,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cooperative_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cooperative_cnpj_key" ON "cooperative"("cnpj");

-- AddForeignKey
ALTER TABLE "cooperative" ADD CONSTRAINT "cooperative_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "addresses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
