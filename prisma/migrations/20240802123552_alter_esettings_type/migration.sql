/*
  Warnings:

  - The values [float] on the enum `ESettingsType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ESettingsType_new" AS ENUM ('string', 'int', 'decimal', 'money');
ALTER TABLE "settings" ALTER COLUMN "type" DROP DEFAULT;
ALTER TABLE "settings" ALTER COLUMN "type" TYPE "ESettingsType_new" USING ("type"::text::"ESettingsType_new");
ALTER TYPE "ESettingsType" RENAME TO "ESettingsType_old";
ALTER TYPE "ESettingsType_new" RENAME TO "ESettingsType";
DROP TYPE "ESettingsType_old";
ALTER TABLE "settings" ALTER COLUMN "type" SET DEFAULT 'string';
COMMIT;
