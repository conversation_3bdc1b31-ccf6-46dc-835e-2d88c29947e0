/*
  Warnings:

  - Made the column `orderId` on table `reviews` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "reviews" DROP CONSTRAINT "reviews_orderId_fkey";

-- DropIndex
DROP INDEX "reviews_orderId_key";

-- AlterTable
ALTER TABLE "reviews" ALTER COLUMN "storeId" DROP NOT NULL,
ALTER COLUMN "orderId" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
