/*
  Warnings:

  - You are about to drop the column `order` on the `content_management` table. All the data in the column will be lost.
  - You are about to drop the column `title` on the `content_management` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "content_management" DROP COLUMN "order",
DROP COLUMN "title",
ADD COLUMN     "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "userId" UUID;

-- AddForeignKey
ALTER TABLE "content_management" ADD CONSTRAINT "content_management_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
