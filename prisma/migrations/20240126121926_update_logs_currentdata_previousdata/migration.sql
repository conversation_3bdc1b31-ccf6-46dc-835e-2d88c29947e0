/*
  Warnings:

  - You are about to alter the column `currentData` on the `logs` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `previousData` on the `logs` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `currentDataHash` on the `logs` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `previousDataHash` on the `logs` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.

*/
-- AlterTable
ALTER TABLE "logs" ALTER COLUMN "currentData" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "previousData" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "currentDataHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "previousDataHash" SET DATA TYPE VARCHAR(250);
