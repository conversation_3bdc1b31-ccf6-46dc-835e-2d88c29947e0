/*
  Warnings:

  - Changed the type of `status` on the `payouts` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "EPayoutStatus" AS ENUM ('waiting', 'available', 'returned', 'temporaryRetention', 'executed');

-- AlterTable
ALTER TABLE "payouts" DROP COLUMN "status",
ADD COLUMN     "status" "EPayoutStatus" NOT NULL;
