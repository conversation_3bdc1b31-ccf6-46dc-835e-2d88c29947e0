/*
  Warnings:

  - You are about to alter the column `street` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `number` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(250)`.
  - You are about to alter the column `complement` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `district` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `country` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `city` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `state` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `postcode` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `nickname` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `cityHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `complementHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `countryHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `districtHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `nicknameHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `numberHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(250)`.
  - You are about to alter the column `postcodeHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `stateHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `streetHash` on the `addresses` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `firstName` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `lastName` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `email` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `phone` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `firstNameHash` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `lastNameHash` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.
  - You are about to alter the column `phoneHash` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar` to `VarChar(250)`.

*/
-- AlterTable
ALTER TABLE "addresses" ALTER COLUMN "street" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "number" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "complement" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "district" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "country" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "city" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "state" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "postcode" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "nickname" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "cityHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "complementHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "countryHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "districtHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "nicknameHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "numberHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "postcodeHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "stateHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "streetHash" SET DATA TYPE VARCHAR(250);

-- AlterTable
ALTER TABLE "users" ALTER COLUMN "firstName" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "lastName" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "cpf" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "email" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "phone" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "firstNameHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "lastNameHash" SET DATA TYPE VARCHAR(250),
ALTER COLUMN "phoneHash" SET DATA TYPE VARCHAR(250);
