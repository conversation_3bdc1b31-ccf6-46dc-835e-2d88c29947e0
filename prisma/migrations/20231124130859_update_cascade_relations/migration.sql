-- DropFore<PERSON><PERSON>ey
ALTER TABLE "products_attributes" DROP CONSTRAINT "products_attributes_productId_fkey";

-- DropForeignKey
ALTER TABLE "products_attributes_options" DROP CONSTRAINT "products_attributes_options_productAttributeId_fkey";

-- DropForeignKey
ALTER TABLE "products_subcategories" DROP CONSTRAINT "products_subcategories_productId_fkey";

-- AddForeignKey
ALTER TABLE "products_attributes" ADD CONSTRAINT "products_attributes_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "products_attributes_options" ADD CONSTRAINT "products_attributes_options_productAttributeId_fkey" FOREIGN KEY ("productAttributeId") REFERENCES "products_attributes"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddF<PERSON><PERSON><PERSON>ey
ALTER TABLE "products_subcategories" ADD CONSTRAINT "products_subcategories_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
