/*
  Warnings:

  - You are about to drop the column `firstAccess` on the `login_sessions` table. All the data in the column will be lost.
  - You are about to drop the column `lastAccess` on the `login_sessions` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "login_sessions" DROP COLUMN "firstAccess",
DROP COLUMN "lastAccess",
ADD COLUMN     "endAccess" DATE NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "startAccessDate" DATE NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "startAccessDay" "EDayOfWeek" NOT NULL DEFAULT 'sunday',
ADD COLUMN     "startAccessTime" TIME NOT NULL DEFAULT CURRENT_TIMESTAMP;
