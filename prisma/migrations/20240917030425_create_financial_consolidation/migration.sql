-- CreateEnum
CREATE TYPE "EFinancialStatus" AS ENUM ('failed', 'success');

-- CreateEnum
CREATE TYPE "EFinancialTransactions" AS ENUM ('correctValues', 'divergentValues', 'onlyFinanceCompany', 'onlyLocalDB');

-- CreateTable
CREATE TABLE "financial_consolidation" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "EFinancialStatus" NOT NULL,

    CONSTRAINT "financial_consolidation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "financial_consolidation_transactions" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "EFinancialTransactions" NOT NULL,
    "storeName" VARCHAR,
    "platformId" VARCHAR,
    "chargeId" VARCHAR,
    "totalPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "administrativeFee" DOUBLE PRECISION DEFAULT 0,
    "consolidationId" UUID NOT NULL,
    "orderId" UUID NOT NULL,

    CONSTRAINT "financial_consolidation_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "financial_consolidation_transactions_orderId_key" ON "financial_consolidation_transactions"("orderId");

-- AddForeignKey
ALTER TABLE "financial_consolidation_transactions" ADD CONSTRAINT "financial_consolidation_transactions_consolidationId_fkey" FOREIGN KEY ("consolidationId") REFERENCES "financial_consolidation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "financial_consolidation_transactions" ADD CONSTRAINT "financial_consolidation_transactions_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
