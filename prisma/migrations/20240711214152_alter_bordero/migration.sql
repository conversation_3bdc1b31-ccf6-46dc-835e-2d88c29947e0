/*
  Warnings:

  - Changed the type of `status` on the `borderos` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "EBorderoStatus" AS ENUM ('open', 'closed', 'paid');

-- AlterTable
ALTER TABLE "borderos" DROP COLUMN "status",
ADD COLUMN     "status" "EBorderoStatus" NOT NULL;

-- DropEnum
DROP TYPE "EBorderStatus";
