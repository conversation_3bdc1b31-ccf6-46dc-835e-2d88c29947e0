-- CreateEnum
CREATE TYPE "DatabaseAction" AS ENUM ('insert', 'delete', 'create', 'update', 'error');

-- CreateTable
CREATE TABLE "logs" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "entityId" UUID NOT NULL,
    "entity" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "datails" TEXT NOT NULL,
    "message" TEXT NOT NULL,

    CONSTRAINT "logs_pkey" PRIMARY KEY ("id")
);
