-- CreateEnum
CREATE TYPE "EContentManagementType" AS ENUM ('faq', 'privacyPolicy', 'termsOfUse', 'contract', 'about');

-- CreateTable
CREATE TABLE "content_management" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "type" "EContentManagementType" NOT NULL,

    CONSTRAINT "content_management_pkey" PRIMARY KEY ("id")
);
