/*
 Warnings:
 
 - You are about to drop the column `usage` on the `files` table. All the data in the column will be lost.
 - Added the required column `extension` to the `files` table without a default value. This is not possible if the table is not empty.
 - Changed the type of `type` on the `files` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
 
 */
-- CreateEnum
CREATE TYPE "EFileType" AS ENUM (
  'photo',
  'document',
  'receipt',
  'bannerWeb',
  'bannerMobile',
  'logo',
  'icon',
  'profilePhoto',
  'rg',
  'cnh',
  'criminalRecord',
  'vehicleDocument'
);

ALTER TABLE
  "files" RENAME COLUMN "type" TO "extension";

ALTER TABLE
  "files"
ADD
  COLUMN "type" "EFileType";

-- export enum EUsageFile {
--   photo = 1,
--   document = 2,
--   receipt = 3,
--   bannerWeb = 4,
--   bannerMobile = 5,
--   logo = 6,
--   icon = 7,
--   profilePhoto = 8,
--   rg = 9,
--   cnh = 10,
-- }
UPDATE
  "files"
SET
  "type" = 'photo'
WHERE
  "usage" = 1;

UPDATE
  "files"
SET
  "type" = 'document'
WHERE
  "usage" = 2;

UPDATE
  "files"
SET
  "type" = 'receipt'
WHERE
  "usage" = 3;

UPDATE
  "files"
SET
  "type" = 'bannerWeb'
WHERE
  "usage" = 4;

UPDATE
  "files"
SET
  "type" = 'bannerMobile'
WHERE
  "usage" = 5;

UPDATE
  "files"
SET
  "type" = 'logo'
WHERE
  "usage" = 6;

UPDATE
  "files"
SET
  "type" = 'icon'
WHERE
  "usage" = 7;

UPDATE
  "files"
SET
  "type" = 'profilePhoto'
WHERE
  "usage" = 8;

UPDATE
  "files"
SET
  "type" = 'rg'
WHERE
  "usage" = 9;

UPDATE
  "files"
SET
  "type" = 'cnh'
WHERE
  "usage" = 10;

ALTER TABLE
  "files" DROP COLUMN "usage";

ALTER TABLE
  "files"
ALTER COLUMN
  "type"
SET
  NOT NULL;