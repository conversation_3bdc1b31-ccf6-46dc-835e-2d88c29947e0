-- CreateEnum
CREATE TYPE "EBorderStatus" AS ENUM ('open', 'closed', 'paid');

-- AlterTable
ALTER TABLE "payouts" ADD COLUMN     "borderoId" UUID;

-- CreateTable
CREATE TABLE "borderos" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "status" "EBorderStatus" NOT NULL,
    "cpfCnpj" VARCHAR NOT NULL,
    "userId" UUID NOT NULL,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "statusDate" TIMESTAMP(6) NOT NULL,
    "quantityOrders" INTEGER NOT NULL DEFAULT 0,
    "sumOrderValue" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "sumAdministrativeFeeValue" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "sumTransferValue" DOUBLE PRECISION NOT NULL DEFAULT 0,

    CONSTRAINT "borderos_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "payouts" ADD CONSTRAINT "payouts_borderoId_fkey" FOREIGN KEY ("borderoId") REFERENCES "borderos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "borderos" ADD CONSTRAINT "borderos_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
