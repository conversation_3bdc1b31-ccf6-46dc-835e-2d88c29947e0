/*
  Warnings:

  - You are about to drop the column `cpfCnpj` on the `payouts` table. All the data in the column will be lost.
  - You are about to drop the column `deliverymanId` on the `payouts` table. All the data in the column will be lost.
  - You are about to drop the column `shopkeeperId` on the `payouts` table. All the data in the column will be lost.
  - Made the column `statusDate` on table `payouts` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "payouts" DROP CONSTRAINT "payouts_deliverymanId_fkey";

-- DropForeignKey
ALTER TABLE "payouts" DROP CONSTRAINT "payouts_shopkeeperId_fkey";

-- AlterTable
ALTER TABLE "payouts" DROP COLUMN "cpfCnpj",
DROP COLUMN "deliverymanId",
DROP COLUMN "shopkeeperId",
ALTER COLUMN "statusDate" SET NOT NULL,
ALTER COLUMN "statusDate" SET DATA TYPE TIMESTAMP(6);
