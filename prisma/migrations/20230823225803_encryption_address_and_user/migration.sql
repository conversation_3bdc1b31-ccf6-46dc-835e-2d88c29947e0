/*
  Warnings:

  - A unique constraint covering the columns `[cpfHash]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[emailHash]` on the table `users` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "addresses" ADD COLUMN     "cityHash" VARCHAR,
ADD COLUMN     "complementHash" VARCHAR,
ADD COLUMN     "countryHash" VARCHAR,
ADD COLUMN     "districtHash" VARCHAR,
ADD COLUMN     "nicknameHash" VARCHAR,
ADD COLUMN     "numberHash" TEXT,
ADD COLUMN     "postcodeHash" VARCHAR,
ADD COLUMN     "stateHash" VARCHAR,
ADD COLUMN     "streetHash" VARCHAR;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "cpfHash" VARCHAR(250),
ADD COLUMN     "emailHash" VARCHAR(250),
ADD COLUMN     "firstNameHash" VARCHAR,
ADD COLUMN     "lastNameHash" VARCHAR,
ADD COLUMN     "phoneHash" VARCHAR;

-- CreateIndex
CREATE UNIQUE INDEX "users_cpfHash_key" ON "users"("cpfHash");

-- CreateIndex
CREATE UNIQUE INDEX "users_emailHash_key" ON "users"("emailHash");
