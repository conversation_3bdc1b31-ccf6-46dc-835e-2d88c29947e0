-- CreateEnum
CREATE TYPE "ENumberComparison" AS ENUM ('equal', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual');

-- CreateEnum
CREATE TYPE "EMailingSendingType" AS ENUM ('notification', 'email', 'notification_email');

-- AlterEnum
ALTER TYPE "EMessageType" ADD VALUE 'mailing';

-- CreateTable
CREATE TABLE "mailling" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "title" VARCHAR NOT NULL,
    "messageContent" VARCHAR NOT NULL,
    "topicArn" VARCHAR,
    "description" VARCHAR,
    "sendingType" "EMailingSendingType" NOT NULL DEFAULT 'notification',
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "mailling_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "filters_mailing" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "district" VARCHAR,
    "city" VARCHAR,
    "state" VARCHAR,
    "paymentMethod" "EPaymentMethod",
    "orderPrice" INTEGER,
    "orderPriceComparison" "ENumberComparison",
    "birthDayStartDate" TIMESTAMP(3),
    "birthDayEndDate" TIMESTAMP(3),
    "mailingId" UUID NOT NULL,
    "category" TEXT[],

    CONSTRAINT "filters_mailing_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "result_mailing" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "runTimeDate" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" UUID,
    "mailingId" UUID,

    CONSTRAINT "result_mailing_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "filters_mailing_mailingId_key" ON "filters_mailing"("mailingId");

-- AddForeignKey
ALTER TABLE "filters_mailing" ADD CONSTRAINT "filters_mailing_mailingId_fkey" FOREIGN KEY ("mailingId") REFERENCES "mailling"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "result_mailing" ADD CONSTRAINT "result_mailing_mailingId_fkey" FOREIGN KEY ("mailingId") REFERENCES "mailling"("id") ON DELETE SET NULL ON UPDATE CASCADE;
