generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["clientExtensions", "extendedWhereUnique", "views"]
  binaryTargets   = ["native", "darwin-arm64", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Address {
  id             String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  street         String        @db.VarChar(250) /// @encrypted
  streetHash     String?       @db.VarChar(250) /// @encryption:hash(street)
  number         String?       @db.VarChar(250) /// @encrypted
  numberHash     String?       @db.VarChar(250) /// @encryption:hash(number)
  complement     String?       @db.VarChar(250) /// @encrypted
  complementHash String?       @db.VarChar(250) /// @encryption:hash(complement)
  district       String        @db.VarChar(250) /// @encrypted
  districtHash   String?       @db.VarChar(250) /// @encryption:hash(district)
  country        String        @db.VarChar(250) /// @encrypted
  countryHash    String?       @db.VarChar(250) /// @encryption:hash(country)
  city           String        @db.VarChar(250) /// @encrypted
  cityHash       String?       @db.VarChar(250) /// @encryption:hash(city)
  state          String        @db.VarChar(250) /// @encrypted
  stateHash      String?       @db.VarChar(250) /// @encryption:hash(state)
  latitude       Float         @default(-14.786102719393547) @db.DoublePrecision
  longitude      Float         @default(-39.038121914749716) @db.DoublePrecision
  postcode       String        @db.VarChar(250) /// @encrypted
  postcodeHash   String?       @db.VarChar(250) /// @encryption:hash(postcode)
  nickname       String?       @db.VarChar(250) /// @encrypted
  nicknameHash   String?       @db.VarChar(250) /// @encryption:hash(nickname)
  createdAt      DateTime      @default(now()) @db.Timestamp(6)
  updatedAt      DateTime      @default(now()) @db.Timestamp(6)
  type           EAddress      @default(delivery)
  order          Order[]
  userAddress    UserAddress[]
  stores         Store[]
  cooperative    Cooperative[]
  isDefault      Boolean       @default(false)

  @@map("addresses")
}

model Attribute {
  id               String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name             String             @db.VarChar
  shortDescription String             @db.VarChar
  required         Boolean            @default(false)
  type             EAttributeType     @default(simpleSelection)
  createdAt        DateTime           @default(now()) @db.Timestamp(6)
  updatedAt        DateTime           @default(now()) @db.Timestamp(6)
  attributeOption  AttributeOption[]
  productAttribute ProductAttribute[]

  @@map("attributes")
}

model AttributeOption {
  id                     String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  value                  String                   @db.VarChar
  attributeId            String                   @db.Uuid
  attribute              Attribute                @relation(fields: [attributeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  productAttributeOption ProductAttributeOption[]

  @@map("attributes_options")
}

model Card {
  id                    String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                String            @db.Uuid
  idCardPaymentPlatform String?           @db.VarChar
  method                ECardMethod
  cardHolder            String?           @db.VarChar
  cardNumberLastDigits  String            @db.VarChar
  cardNumber            String?           @db.VarChar
  expiration            String?           @db.VarChar
  flag                  ECardFlag
  status                ECardStatus
  saved                 Boolean           @default(false)
  createdAt             DateTime          @default(now()) @db.Timestamp(6)
  updatedAt             DateTime          @default(now()) @updatedAt @db.Timestamp(6)
  user                  User?             @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  transactionCard       TransactionCard[]

  @@map("cards")
}

model Category {
  id                  String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                String                @unique @db.VarChar
  description         String                @db.VarChar
  createdAt           DateTime              @default(now()) @db.Timestamp(6)
  updatedAt           DateTime              @default(now()) @db.Timestamp(6)
  categorySubcategory CategorySubcategory[]
  productCategories   ProductCategory[]
  storeCategory       StoreCategory[]

  @@map("categories")
}

model CategorySubcategory {
  id            String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  subcategoryId String      @db.Uuid
  categoryId    String      @db.Uuid
  subcategory   Subcategory @relation(fields: [subcategoryId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  category      Category    @relation(fields: [categoryId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("categories_subcategories")
}

model Client {
  id         String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  status     EProfileStatus? @default(review)
  evaluation String?         @db.VarChar
  userId     String          @unique @db.Uuid
  createdAt  DateTime        @default(now()) @db.Timestamp(6)
  updatedAt  DateTime        @default(now()) @db.Timestamp(6)
  user       User            @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("clients")
}

model Deliveryman {
  id         String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  type       String          @db.VarChar
  modality   String          @db.VarChar
  status     EProfileStatus? @default(review)
  annotation String?
  latitude   Float?          @db.DoublePrecision
  longitude  Float?          @db.DoublePrecision
  userId     String          @unique @db.Uuid
  createdAt  DateTime        @default(now()) @db.Timestamp(6)
  updatedAt  DateTime        @default(now()) @db.Timestamp(6)
  user       User            @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  orders     Order[]
  review     Review[]
  pixKey     String?         @db.VarChar()

  @@map("deliverymen")
}

model Device {
  id                         String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  deviceId                   String
  deviceToken                String
  endpointArn                String
  subscriptionArn            String?
  type                       EDeviceType
  active                     Boolean
  userId                     String           @db.Uuid
  language                   ELanguageOptions @default(PT)
  profileId                  String           @db.Uuid
  deliverymanSubscriptionArn String?
  createdAt                  DateTime         @default(now())
  updatedAt                  DateTime         @updatedAt
  user                       User?            @relation(fields: [userId], references: [id])
  profile                    Profile?         @relation(fields: [profileId], references: [id])

  @@unique([deviceId, userId])
  @@map("devices")
}

model File {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  url          String    @db.VarChar
  key          String    @db.VarChar
  bucket       String    @db.VarChar
  originalName String    @db.VarChar
  entity       EFile     @default(product)
  extension    String    @db.VarChar
  type         EFileType
  size         Int
  master       Boolean
  entityId     String?   @db.Uuid
  createdAt    DateTime  @default(now()) @db.Timestamp(6)
  updatedAt    DateTime  @default(now()) @db.Timestamp(6)

  @@map("files")
}

model OrderItem {
  id                              String                            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  orderId                         String                            @db.Uuid
  productId                       String                            @db.Uuid
  quantity                        Int
  unitPrice                       Float                             @default(0) @db.DoublePrecision
  totalPrice                      Float                             @default(0) @db.DoublePrecision
  observation                     String?                           @db.VarChar
  product                         Product                           @relation(fields: [productId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  order                           Order                             @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  orderItemProductAttributeOption OrderItemProductAttributeOption[]

  @@map("order_items")
}

model OrderStatusType {
  id          String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  value       String        @db.VarChar
  orderStatus OrderStatus[]

  @@map("order_status_types")
}

model Order {
  id                     String                              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                 String                              @db.Uuid
  code                   Int                                 @default(autoincrement())
  storeId                String                              @db.Uuid
  addressId              String                              @db.Uuid
  deliverymanId          String?                             @db.Uuid
  createdAt              DateTime                            @default(now()) @db.Timestamp(6)
  price                  Float                               @default(0) @db.DoublePrecision
  shippingPrice          Float                               @default(0) @db.DoublePrecision
  totalPrice             Float                               @default(0) @db.DoublePrecision
  quantityItems          Int
  quantityProducts       Int
  estimatedDeliveryTime  Int                                 @default(0)
  customerCode           String?                             @db.VarChar
  routeLength            Float                               @default(0) @db.Real
  orderItem              OrderItem[]
  store                  Store                               @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user                   User                                @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  address                Address?                            @relation(fields: [addressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  deliveryman            Deliveryman?                        @relation(fields: [deliverymanId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  Chat                   Chat?
  orderStatus            OrderStatus[]
  trackingOrder          TrackingOrder[]
  review                 Review[]
  transaction            Transaction[]
  payout                 Payout[]
  financialConsolidation FinancialConsolidationTransactions?

  @@map("orders")
}

model OrderItemProductAttributeOption {
  id                       String                  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  orderItemId              String                  @db.Uuid
  productAttributeOptionId String                  @db.Uuid
  productAttributeOption   ProductAttributeOption? @relation(fields: [productAttributeOptionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  orderItem                OrderItem?              @relation(fields: [orderItemId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("orders_items_products_attributes_options")
}

model OrderStatus {
  id                String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  orderId           String          @db.Uuid
  orderStatusTypeId String          @db.Uuid
  current           Boolean         @default(true)
  observation       String?         @db.VarChar
  createdAt         DateTime        @default(now()) @db.Timestamp(6)
  order             Order           @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  orderStatusType   OrderStatusType @relation(fields: [orderStatusTypeId], references: [id], onDelete: NoAction)

  @@map("orders_status")
}

model Permission {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  auth_module        String              @db.VarChar
  app_module         String              @db.VarChar
  profilePermissions ProfilePermission[]
  userPermissions    UserPermission[]

  @@map("permissions")
}

model Product {
  id                   String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                 String                 @db.VarChar
  shortDescription     String                 @db.VarChar
  description          String                 @db.VarChar
  price                Float                  @default(0) @db.DoublePrecision
  salePrice            Float                  @default(0) @db.DoublePrecision
  sku                  String                 @db.Char(8)
  active               Boolean
  activeByAdmin        Boolean                @default(true)
  createdAt            DateTime               @default(now()) @db.Timestamp(6)
  updatedAt            DateTime               @default(now()) @db.Timestamp(6)
  storeId              String                 @db.Uuid
  preparationTime      Int                    @default(0)
  orderItem            OrderItem[]
  store                Store                  @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  productAttribute     ProductAttribute[]
  productCategory      ProductCategory[]
  productSubcategory   ProductSubcategory[]
  userFavoriteProducts UserFavoriteProducts[]
  productModeration    ProductModeration[]

  @@map("products")
}

model ProductModeration {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  reason    String?  @db.VarChar
  createdAt DateTime @default(now()) @db.Timestamp(6)
  productId String   @db.Uuid
  product   Product  @relation(fields: [productId], references: [id])

  @@map("product_moderation")
}

model ProductAttribute {
  id                     String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  productId              String                   @db.Uuid
  attributeId            String                   @db.Uuid
  active                 Boolean                  @default(true)
  attribute              Attribute                @relation(fields: [attributeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product                Product                  @relation(fields: [productId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  productAttributeOption ProductAttributeOption[]

  @@map("products_attributes")
}

model ProductAttributeOption {
  id                              String                            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  productAttributeId              String                            @db.Uuid
  attributeOptionId               String                            @db.Uuid
  active                          Boolean                           @default(true)
  orderItemProductAttributeOption OrderItemProductAttributeOption[]
  attributeOption                 AttributeOption                   @relation(fields: [attributeOptionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  productAttribute                ProductAttribute                  @relation(fields: [productAttributeId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@map("products_attributes_options")
}

model ProductCategory {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  productId  String   @db.Uuid
  categoryId String   @db.Uuid
  category   Category @relation(fields: [categoryId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@map("products_categories")
}

model ProductSubcategory {
  id            String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  productId     String      @db.Uuid
  subcategoryId String      @db.Uuid
  product       Product     @relation(fields: [productId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  subcategory   Subcategory @relation(fields: [subcategoryId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("products_subcategories")
}

model Profile {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name               String              @db.VarChar
  profilePermissions ProfilePermission[]
  userProfiles       UserProfile[]
  device             Device[]
  userMessage        UserMessage[]
  chatMessage        ChatMessage[]

  @@map("profiles")
}

model ProfilePermission {
  id           String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  profileId    String     @db.Uuid
  permissionId String     @db.Uuid
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  profile      Profile    @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("profiles_permissions")
}

model Review {
  id             String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  rate           Int
  customerReview String       @db.VarChar
  storeResponse  String?      @db.VarChar
  createdAt      DateTime     @default(now()) @db.Timestamp(6)
  updatedAt      DateTime     @default(now()) @db.Timestamp(6)
  userId         String       @db.Uuid
  storeId        String?      @db.Uuid
  orderId        String?      @db.Uuid
  store          Store?       @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user           User?        @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  order          Order?       @relation(fields: [orderId], references: [id])
  deliveryman    Deliveryman? @relation(fields: [deliverymanId], references: [id])
  deliverymanId  String?      @db.Uuid

  @@map("reviews")
}

model Shopkeeper {
  id         String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  status     EProfileStatus? @default(review)
  annotation String?
  userId     String          @unique @db.Uuid
  createdAt  DateTime        @default(now()) @db.Timestamp(6)
  updatedAt  DateTime        @default(now()) @db.Timestamp(6)
  user       User            @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("shopkeepers")
}

model StoreHours {
  id        String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  open      DateTime   @db.Time()
  close     DateTime   @db.Time()
  dayOfWeek EDayOfWeek @default(sunday)
  storeId   String     @db.Uuid
  store     Store      @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("store_hours")
}

model Store {
  id                 String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name               String               @db.VarChar
  cnpj               String               @unique() @db.VarChar
  slug               String?              @db.VarChar
  email              String               @db.VarChar
  phone              String               @db.VarChar
  description        String               @db.VarChar
  open               Boolean              @default(true)
  active             Boolean              @default(true)
  activeByAdmin      Boolean              @default(true)
  addressId          String?              @db.Uuid
  createdAt          DateTime             @default(now()) @db.Timestamp(6)
  updatedAt          DateTime             @default(now()) @db.Timestamp(6)
  pixKey             String?              @db.VarChar()
  order              Order[]
  products           Product[]
  reviews            Review[]
  storeHours         StoreHours[]
  address            Address?             @relation(fields: [addressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  storeCategory      StoreCategory[]
  storeUsers         StoreUser[]
  userFavoriteStores UserFavoriteStores[]
  storeSettings      StoreSettings?
  storeModeration    StoreModeration[]
  Bordero            Bordero[]

  @@map("stores")
}

model Cooperative {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name      String    @db.VarChar
  cnpj      String    @unique() @db.VarChar
  email     String    @db.VarChar
  pixKey    String?   @db.VarChar()
  addressId String?   @db.Uuid
  createdAt DateTime  @default(now()) @db.Timestamp(6)
  updatedAt DateTime  @default(now()) @db.Timestamp(6)
  address   Address?  @relation(fields: [addressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  payout    Payout[]
  bordero   Bordero[]

  @@map("cooperative")
}

model StoreSettings {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  toleranceOrder Int?     @default(0)
  deliveryRange  Float    @default(2) @db.Real
  cash           Boolean  @default(false)
  debt           Boolean  @default(false)
  credit         Boolean  @default(false)
  ticket         Boolean  @default(false)
  pix            Boolean  @default(false)
  facebook       String?  @db.VarChar
  instagram      String?  @db.VarChar
  tiktok         String?  @db.VarChar
  createdAt      DateTime @default(now()) @db.Timestamp(6)
  updatedAt      DateTime @default(now()) @db.Timestamp(6)
  storeId        String   @unique @db.Uuid
  store          Store    @relation(fields: [storeId], references: [id])

  @@map("store_settings")
}

model StoreModeration {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  reason    String?  @db.VarChar
  createdAt DateTime @default(now()) @db.Timestamp(6)
  storeId   String   @db.Uuid
  store     Store    @relation(fields: [storeId], references: [id])

  @@map("store_moderation")
}

model StoreCategory {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  storeId    String   @db.Uuid
  categoryId String   @db.Uuid
  category   Category @relation(fields: [categoryId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  store      Store    @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("stores_categories")
}

model StoreUser {
  id      String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  storeId String                @db.Uuid
  userId  String                @db.Uuid
  user    User                  @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  store   Store                 @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  owner   Boolean               @default(false)
  status  EStoreModeratorStatus @default(pending)

  @@map("stores_users")
}

model Subcategory {
  id                  String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                String                @unique @db.VarChar
  description         String                @db.VarChar
  createdAt           DateTime              @default(now()) @db.Timestamp(6)
  updatedAt           DateTime              @default(now()) @updatedAt @db.Timestamp(6)
  categorySubcategory CategorySubcategory[]
  productSubcategory  ProductSubcategory[]

  @@map("subcategories")
}

model User {
  id                   String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  cognitoId            String?                @unique() @db.Uuid
  cognitoIdGoogle      String?                @unique() @db.Uuid
  firstName            String                 @db.VarChar(250) /// @encrypted
  firstNameHash        String?                @db.VarChar(250) /// @encryption:hash(firstName)
  lastName             String                 @db.VarChar(250) /// @encrypted
  lastNameHash         String?                @db.VarChar(250) /// @encryption:hash(lastName)
  cpf                  String                 @unique() @db.VarChar(250) /// @encrypted
  cpfHash              String?                @unique() @db.VarChar(250) /// @encryption:hash(cpf)
  email                String                 @unique() @db.VarChar(250) /// @encrypted
  emailHash            String?                @unique() @db.VarChar(250) /// @encryption:hash(email)
  phone                String                 @db.VarChar(250) /// @encrypted
  phoneHash            String?                @db.VarChar(250) /// @encryption:hash(phone)
  contactByEmail       Boolean                @default(true)
  contactBySms         Boolean                @default(true)
  contactByWhatsapp    Boolean                @default(true)
  disabled             Boolean                @default(false)
  deleted              Boolean                @default(false)
  permanentlyDeleted   Boolean                @default(false)
  deletedAt            DateTime?              @db.Timestamp(6)
  createdAt            DateTime               @default(now()) @db.Timestamp(6)
  updatedAt            DateTime               @default(now()) @db.Timestamp(6)
  dateOfBirth          DateTime               @default(now()) @db.Date()
  reactivationCode     String?                @db.VarChar(6)
  client               Client?
  deliveryman          Deliveryman?
  order                Order[]
  reviewUser           Review[]
  shopkeeper           Shopkeeper?
  storeUsers           StoreUser[]
  userAddress          UserAddress[]
  userFavoriteProducts UserFavoriteProducts[]
  userFavoriteStores   UserFavoriteStores[]
  userPermissions      UserPermission[]
  userProfiles         UserProfile[]
  devices              Device[]
  message              Message?
  userMessage          UserMessage[]
  card                 Card[]
  chatMessage          ChatMessage[]
  loginSession         LoginSession[]
  Settings             Settings[]
  log                  Log[]
  ContentManagement    ContentManagement[]
  Bordero              Bordero[]              @relation("user")
  BorderoAdmin         Bordero[]              @relation("admin")

  @@map("users")
}

model UserAddress {
  id        String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String  @db.Uuid
  addressId String  @db.Uuid
  address   Address @relation(fields: [addressId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@map("users_addresses")
}

model UserFavoriteProducts {
  id        String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String  @db.Uuid
  productId String  @db.Uuid
  product   Product @relation(fields: [productId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user      User    @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("users_favorites_products")
}

model UserFavoriteStores {
  id      String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId  String @db.Uuid
  storeId String @db.Uuid
  store   Store  @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user    User   @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("users_favorites_stores")
}

model UserPermission {
  id           String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId       String     @db.Uuid
  permissionId String     @db.Uuid
  user         User       @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_4180c1a3fdc77c74e9ad3a387f2")
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_a7fc06687add3a4da3ddbbb406f")

  @@map("users_permissions")
}

model UserProfile {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String   @db.Uuid
  profileId String   @db.Uuid
  user      User?    @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  profile   Profile? @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("users_profiles")
}

model TrackingOrder {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  orderId   String   @db.Uuid
  latitude  Float    @db.Real
  longitude Float    @db.Real
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  order     Order    @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("tracking_orders")
}

model Message {
  id           String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title        String
  type         EMessageType
  sendingType  EMessageSendingType[]
  senderId     String?               @unique @db.Uuid
  directMailId String?               @db.Uuid
  createdAt    DateTime              @default(now()) @db.Timestamp(6)
  updatedAt    DateTime              @default(now()) @db.Timestamp(6)
  sender       User?                 @relation(fields: [senderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  userMessage  UserMessage[]
  content      MessageContent[]

  @@map("messages")
}

model UserMessage {
  id        String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String         @db.Uuid
  messageId String         @db.Uuid
  status    EMessageStatus @default(created)
  profileId String?        @db.Uuid
  user      User           @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  message   Message        @relation(fields: [messageId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  profile   Profile?       @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("users_messages")
}

model MessageContent {
  id              String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  body            String              @db.VarChar
  status          EMessageStatus      @default(created)
  sendingType     EMessageSendingType
  messageId       String              @db.Uuid
  message         Message             @relation(fields: [messageId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  emailAttachment EmailAttachment[]

  @@map("messages_content")
}

model EmailAttachment {
  id               String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  url              String
  messageContentId String         @db.Uuid
  createdAt        DateTime       @default(now()) @db.Timestamp(6)
  updatedAt        DateTime       @default(now()) @db.Timestamp(6)
  messageContent   MessageContent @relation(fields: [messageContentId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@map("emails_attachments")
}

model Transaction {
  id                    String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  transactionPlatformId String?            @db.VarChar
  type                  ETransactionType
  status                ETransactionStatus
  paymentMethod         EPaymentMethod
  amount                Float?             @default(0) @db.DoublePrecision
  authorizationCode     String?            @db.VarChar
  installments          Int?
  statusDetail          String?            @db.VarChar
  traceId               String?            @db.VarChar
  chargeId              String?            @db.VarChar
  customerId            String?            @db.VarChar
  pixKey                String?            @db.VarChar
  orderId               String             @db.Uuid
  createdAt             DateTime           @default(now()) @db.Timestamp(6)
  updatedAt             DateTime           @default(now()) @db.Timestamp(6)
  order                 Order?             @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  transactionCard       TransactionCard[]

  @@map("transactions")
}

model TransactionCard {
  id            String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  transactionId String      @db.Uuid
  cardId        String      @db.Uuid
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  card          Card        @relation(fields: [cardId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("transactions_cards")
}

model Log {
  id              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId          String?  @db.Uuid
  entityId        String?  @db.Uuid
  entity          String?
  level           String
  action          String?
  createdAt       DateTime @default(now()) @db.Timestamp(6)
  details         String   @db.Text /// @encrypted
  detailsHash     String?  @db.Text /// @encryption:hash(details)
  message         String
  currentData     String?  @db.Text /// @encrypted
  currentDataHash String?  @db.Text /// @encryption:hash(currentData)
  user            User?    @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("logs")
}

model Chat {
  id          String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  orderId     String        @unique @db.Uuid
  createdAt   DateTime      @default(now()) @db.Timestamp(6)
  updatedAt   DateTime      @default(now()) @db.Timestamp(6)
  order       Order         @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  chatMessage ChatMessage[]

  @@map("chats")
}

model ChatMessage {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  chatId    String   @db.Uuid
  userId    String   @db.Uuid
  profileId String   @db.Uuid
  message   String   @db.VarChar
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  profile   Profile  @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user      User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("chat_messages")
}

model LoginSession {
  id              String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId          String     @db.Uuid
  startAccessDate DateTime   @default(now()) @db.Date()
  startAccessTime DateTime   @default(now()) @db.Time()
  startAccessHour Int        @default(0)
  startAccessDay  EDayOfWeek @default(sunday)
  endAccess       DateTime   @default(now()) @db.Timestamp(6)
  user            User       @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("login_sessions")
}

model Mailing {
  id             String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title          String              @db.VarChar
  messageContent String              @db.VarChar
  topicArn       String?             @db.VarChar
  description    String?             @db.VarChar
  sendingType    EMailingSendingType @default(notification)
  createdAt      DateTime            @default(now()) @db.Timestamp(6)
  filtersMailing FiltersMailing?
  resultMailing  ResultMailing[]

  @@map("mailling")
}

model FiltersMailing {
  id                   String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  district             String?            @db.VarChar
  city                 String?            @db.VarChar
  state                String?            @db.VarChar
  paymentMethod        EPaymentMethod?
  orderPrice           Float?             @default(0) @db.DoublePrecision
  orderPriceComparison ENumberComparison?
  birthDayStartDate    DateTime?
  birthDayEndDate      DateTime?
  Mailing              Mailing            @relation(fields: [mailingId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  mailingId            String             @unique @db.Uuid
  category             String[]

  @@map("filters_mailing")
}

model ResultMailing {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  runTimeDate DateTime @default(now()) @db.Timestamp(6)
  userId      String?  @db.Uuid
  Mailing     Mailing? @relation(fields: [mailingId], references: [id])
  mailingId   String?  @db.Uuid

  @@map("result_mailing")
}

model Settings {
  id     String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name   String        @unique @db.VarChar
  value  String
  type   ESettingsType @default(string)
  userId String?       @db.Uuid
  user   User?         @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("settings")
}

model ContentManagement {
  id        String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  content   String
  type      EContentManagementType
  createdAt DateTime               @default(now()) @db.Timestamp(6)
  updatedAt DateTime               @default(now()) @db.Timestamp(6)
  userId    String?                @db.Uuid
  user      User?                  @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("content_management")
}

model Payout {
  id                       String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  orderId                  String        @db.Uuid
  cooperativeId            String?       @db.Uuid
  administrativeFeePercent Float         @default(0) @db.DoublePrecision
  administrativeFeeValue   Float         @default(0) @db.DoublePrecision
  transferValue            Float         @default(0) @db.DoublePrecision
  createdAt                DateTime      @default(now()) @db.Timestamp(6)
  statusDate               DateTime      @db.Timestamp(6)
  status                   EPayoutStatus
  borderoId                String?       @db.Uuid
  payoutOwner              EPayoutOwner
  order                    Order?        @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  cooperative              Cooperative?  @relation(fields: [cooperativeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  bordero                  Bordero?      @relation(fields: [borderoId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("payouts")
}

model Bordero {
  id                        String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  status                    EBorderoStatus @default(open)
  payoutOwner               EPayoutOwner
  userId                    String?        @db.Uuid
  userIdAdmin               String         @db.Uuid
  storeId                   String?        @db.Uuid
  cooperativeId             String?        @db.Uuid
  createdAt                 DateTime       @default(now()) @db.Timestamp(6)
  statusDate                DateTime       @default(now()) @db.Timestamp(6)
  quantityOrders            Int            @default(0) @db.Integer
  sumOrderValue             Float          @default(0) @db.DoublePrecision
  sumAdministrativeFeeValue Float          @default(0) @db.DoublePrecision
  sumTransferValue          Float          @default(0) @db.DoublePrecision
  user                      User?          @relation(name: "user", fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  userAdmin                 User           @relation(name: "admin", fields: [userIdAdmin], references: [id], onDelete: NoAction, onUpdate: NoAction)
  store                     Store?         @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  cooperative               Cooperative?   @relation(fields: [cooperativeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  payout                    Payout[]

  @@map("borderos")
}

model FinancialConsolidation {
  id           String                               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt    DateTime                             @default(now()) @db.Timestamp(6)
  status       EFinancialStatus
  transactions FinancialConsolidationTransactions[]

  @@map("financial_consolidation")
}

model FinancialConsolidationTransactions {
  id                String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt         DateTime               @default(now()) @db.Timestamp(6)
  status            EFinancialTransactions
  storeName         String?                @db.VarChar
  platformId        String?                @db.VarChar
  chargeId          String?                @db.VarChar
  totalPrice        Float                  @default(0) @db.DoublePrecision
  administrativeFee Float?                 @default(0) @db.DoublePrecision
  consolidationId   String                 @db.Uuid
  orderId           String                 @unique @db.Uuid
  consolidation     FinancialConsolidation @relation(fields: [consolidationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  order             Order                  @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("financial_consolidation_transactions")
}

view CustomerProfile {
  id              Int      @unique
  orderId         String
  orderItemId     String
  userId          String
  orderPrice      Int
  orderTime       DateTime
  orderDistrict   String
  orderCity       String
  orderState      String
  shippingPrice   Int
  storeId         String
  productId       String
  productPrice    Int
  productQuantity Int
  categoryId      String
  subcategoryId   String

  @@map("customers_profile")
}

enum EFileType {
  photo
  document
  receipt
  bannerWeb
  bannerMobile
  logo
  icon
  profilePhoto
  rg
  cnh
  criminalRecord
  vehicleDocument
}

enum EMessageStatus {
  created
  sended
  read
  failed
}

enum ENumberComparison {
  equal
  lessThan
  lessThanOrEqual
  greaterThan
  greaterThanOrEqual
}

enum ETransactionType {
  payment
  cancellation
}

enum ETransactionStatus {
  created
  pending
  approved
  authorized
  in_process
  in_mediation
  rejected
  cancelled
  refunded
  charged_back
}

enum EPaymentMethod {
  credit
  debt
  pix
  ticket
}

enum ELanguageOptions {
  EN
  PT
}

enum EMessageSendingType {
  sms
  email
  notification
}

enum EMessageType {
  order_status
  sale
  voucher
  birthdays
  registration
  chat
  mailing
  profile_status
  moderation
  moderator
  moderator_blocked
}

enum EAddress {
  charge
  delivery
}

enum EFile {
  product
  store
  user
  shopkeeper
  deliveryman
  category
  order
  bordero
}

enum EDayOfWeek {
  sunday
  monday
  tuesday
  wednesday
  thursday
  friday
  saturday
}

enum EAttributeType {
  simpleSelection
  multipleSelection
}

enum EProfileStatus {
  review
  approved
  disapproved
  pendingDocuments
}

enum EDeviceType {
  IOS
  ANDROID
}

enum ECardMethod {
  credit
  debt
}

enum ECardFlag {
  visa
  mastercard
  elo
  hipercard
  hiper
  americanExpress
}

enum ECardStatus {
  active
  inactive
}

enum EMailingSendingType {
  notification
  email
  notification_email
}

enum EContentManagementType {
  faq
  privacyPolicy
  termsOfUse
  contract
  about
}

enum EPayoutStatus {
  waiting
  available
  returned
  temporaryRetention
  executed
}

enum EStoreModeratorStatus {
  active
  blocked
  pending
}

enum EBorderoStatus {
  open
  closed
  paid
}

enum EPayoutOwner {
  store
  deliveryman
  cooperative
}

enum ESettingsType {
  string
  int
  decimal
  money
}

enum EFinancialStatus {
  failed
  success
}

enum EFinancialTransactions {
  correctValues
  divergentValues
  onlyFinanceCompany
  onlyLocalDB
}
