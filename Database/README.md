# Instalar servidor de banco de dados postgres em container

*O servidor de banco de dados pode ser conteinerizado com Docker, para isso, efetuar a instalação do docker desktop e subir o container com o arquivo docker compose.yaml que já está com o usuário e senha definidos conforme o projeto.*

### Instalação do docker

* Siga os procedimentos para instalar o Docker Desktop para Windows conforme link: https://docs.docker.com/desktop/setup/install/windows-install/
* Após instalado, abra o Docker Desktop, este precisará estar sempre em execução para que o container continue ativo e mantenha conexão do banco de dados ativa.

### Subindo o banco de dados no docker

* Abra o seu promt de comando CMD e acesse a pasta database do projeto.
* Nesta pasta, já está disponível o docker-compose.yaml para instalar o banco de dados.

### Execute o comando abaixo para subir o container do banco de dados e aguarde finalizar.

docker compose up --build -d

#### Para verificar se o container está em execução, excute:

docker ps

Se tudo estiver ok, o container será exibido desta forma.	
