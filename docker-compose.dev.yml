services:
  postgres:
    image: postgres:13
    container_name: local-postgres
    restart: always
    environment:
      POSTGRES_USER: mobile
      POSTGRES_PASSWORD: c3p3d1
      POSTGRES_DB: c3p3d1
      POSTGRES_NON_ROOT_USER: mobile
      POSTGRES_NON_ROOT_PASSWORD: c3p3d1
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    networks:
      - api-network

  api:
    container_name: api-dev
    image: delivery_api_dev
    build:
      target: api-dev
      context: ./
      dockerfile: Dockerfile.dev
    ports:
      - 4000:4000
    restart: 'no'
    depends_on:
      - postgres
    networks:
      - api-network

  n8n:
    container_name: n8n-dev
    image: n8nio/n8n
    restart: always
    ports:
      - '5678:5678'
    environment:
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PORT=5678
      - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
      - NODE_ENV=development
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=postgres
      - DB_POSTGRESDB_USER=mobile
      - DB_POSTGRESDB_PASSWORD=c3p3d1
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n-backups:/backups
    depends_on:
      - postgres
    networks:
      - api-network

  evolution-api:
    container_name: evolution_api
    image: atendai/evolution-api:v2.2.2
    restart: always
    ports:
      - '8080:8080'
    env_file:
      - evolution.env
    volumes:
      - evolution_instances:/evolution/instances
    depends_on:
      - postgres
      - redis
    networks:
      - api-network

  redis:
    container_name: redis-dev
    image: redis:7
    restart: always
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - api-network

networks:
  api-network:
    driver: bridge

volumes:
  postgres-data:
    name: postgres-data
    driver: local
  n8n_data:
    name: n8n_data
    driver: local
  evolution_instances:
    name: evolution_instances
    driver: local
  redis_data:
    name: redis_data
    driver: local
